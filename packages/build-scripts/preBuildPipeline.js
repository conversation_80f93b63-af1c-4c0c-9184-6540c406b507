import { generateFirebaseFiles } from "./revamped-firebase.js";
import { createOneSignal } from "./utils/one-signal/index.js";
import { downloadFromS3 } from "./utils/s3/index.js";
import config from "./config/index.js";
import path from "path";
import fs from "fs";
import axios from "axios";

const preBuildConfigString = process.env.PREBUILD_CONFIG;

if (!preBuildConfigString || preBuildConfigString.trim() === "") {
  throw new Error("NO BUILD CONFIG PASSED IN ENV");
}

const preBuildConfig = JSON.parse(preBuildConfigString);

console.log("Resolved config:", JSON.stringify(preBuildConfig, null, 2));

const currentWrkDir = path.resolve(process.cwd());
const {
  platform,
  bundleId,
  serviceAccountFileUploaderKey,
  appId: apptileAppId,
  appName,
  pipelines,
} = preBuildConfig;
const pipelineResult = { platform };

(async () => {
  try {
    if (!serviceAccountFileUploaderKey) {
      throw new Error(
        "No Service Account File Found for creating Firebase app."
      );
    }

    const serviceAccountPath = path.join(
      currentWrkDir,
      "assets",
      apptileAppId,
      "firebaseServiceAccountKeyFile.json"
    );
    await downloadFromS3(
      serviceAccountFileUploaderKey,
      serviceAccountPath,
      config.buildAssetsBucket
    );

    let firebasePipelineData = {};
    if (pipelines.generateFirebaseFiles) {
      firebasePipelineData = await generateFirebaseFiles(
        apptileAppId,
        appName,
        platform,
        bundleId,
        serviceAccountPath
      );
      pipelineResult["firebase"] = firebasePipelineData;
    }

    if (pipelines.createOneSignal) {
      const serviceAccountBuffer = fs.readFileSync(serviceAccountPath);
      const serviceAccountFileInBase64 =
        serviceAccountBuffer.toString("base64");
      const onesignal_appId = await createOneSignal(
        appName,
        apptileAppId,
        serviceAccountFileInBase64
      );

      console.log(onesignal_appId, "OneSignal App Id");
      if (onesignal_appId) {
        pipelineResult["onesignal"] = { onesignal_appId };
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    await axios.post(
      `${config.apiBaseUrl}/internal-build-system/webhook/preBuildPhase/${apptileAppId}`,
      pipelineResult,
      {
        headers: {
          "X-App-Id": apptileAppId,
          "X-Api-Token": config.buildManager.apiToken,
        },
      }
    );
    console.log("Webhook sent successfully");
  }
})();
