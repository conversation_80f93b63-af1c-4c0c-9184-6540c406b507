import shell from "shelljs";
import fs from "fs";
import config from "./config/index.js";
import _ from "lodash";
import { downloadFromS3, uploadToS3 } from "./utils/s3/index.js";
import axios from "axios";
import path from "path";
import moment from "moment";
import {
  sendSlackAlerts,
  generateBuildFailureAlert,
  generateBuildSuccessAlert,
} from "./utils/slack/index.js";
import QRCode from "qrcode";
import { registerDevice } from "./utils/ios/registerDevice.js";

import { switchBranchOrTag, resetGITChanges } from "./utils/git/index.js";

import { generateJKS } from "./utils/android/generateJKS.js";
import { enablePublishFlow } from "./utils/publishFlow/index.js";
import {
  QuickSightClient,
  CreateDashboardCommand,
  DescribeDashboardCommand,
  DescribeAnalysisDefinitionCommand,
} from "@aws-sdk/client-quicksight";
const quickSightClient = new QuickSightClient({
  region: "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

async function main() {
  try {
    //TODO: HANDLE MOST COMMON ERRORS WHILE EXECUTING distribution.sh

    /* THIS SCRIPT DOES THE FOLLOWING:

  1. GET BUILD_CONFIG from env and parse it as JSON
  2. Download Assets from S3,(build_config contains s3 paths for respective asset)
  3. Construct distribution.config.json by replacing uploader key with filepaths after download
  4. Replace distribution.config.json in ReactNativeTSProjeect/devops folder
  5. Run ./distribution.build.sh
  6. Once the build is generated upload to S3 and trigger webhook in Build Manager with build assets CDN Url

  *******!!!THIS SCRIPT ASSUMES ReactNativeTSProjeect is in the SAME DIRECTORY in which build-scripts are present**************

  */

    var platform = process.env.PLATFORM;
    var jenkinsBuildNumber = process.env.BUILD_NUMBER;
    const buildConfigString = process.env.BUILD_CONFIG;
    if (!buildConfigString || buildConfigString.trim() === "") {
      throw new Error("NO BUILD CONFIG PASSED IN ENV");
    }

    // Parse the trimmed JSON string

    const buildConfig =
      platform === "android"
        ? JSON.parse(decodeURIComponent(buildConfigString))
        : JSON.parse(buildConfigString);
    console.log("resolved config1: ", JSON.stringify(buildConfig, null, 2));

    const currentWrkDir = path.resolve(process.cwd());
    const projectPath = path.join(currentWrkDir, "..", "apptile-app");
    const iosPlistPath = path.join(
      projectPath,
      "ios",
      "ReactNativeTSProject",
      "Info.plist"
    );
    const xcodeProjFilePath = path.join(
      projectPath,
      "ios",
      "ReactNativeTSProject.xcodeproj",
      "project.pbxproj"
    );

    const destinationFilePath = path.join(projectPath, "devops");
    const buildTweaksPath = path.join(currentWrkDir, "utils", "tweaks");
    const ipadTweaksPath = path.join(buildTweaksPath, "ipadSupport");

    var appId = _.get(buildConfig, "app_id", "");
    var build_android = _.get(buildConfig, "build_android", false);
    var build_ios = _.get(buildConfig, "build_ios", false);
    var appName = _.get(buildConfig, "app_name", null);
    var bundleName = _.get(
      buildConfig,
      `${platform.toLowerCase()}.bundle_id`,
      null
    );
    var webhook_url = _.get(
      buildConfig,
      `${platform.toLowerCase()}.webhook_url`,
      null
    );

    var publishOnApptile = _.get(buildConfig, "ios.publishOnApptile", false);

    //   const { apiKey: customerApiKey, issuerId: customerIssuerId } =
    //     buildConfig.ios;

    var version = _.get(
      buildConfig,
      `${platform.toLowerCase()}.version_number`,
      null
    );
    var semver = _.get(
      buildConfig,
      `${platform.toLowerCase()}.version_semver`,
      null
    );

    var branchOrTag = _.get(
      buildConfig,
      `${platform.toLowerCase()}.buildSourceGitHeadName`,
      "v0.17.5"
    );

    var minFrameworkVersion = _.get(
      buildConfig,
      `${platform.toLowerCase()}.minFrameworkVersion`,
      "v0.13.0"
    );
    var frameworkVersion = _.get(
      buildConfig,
      `${platform.toLowerCase()}.frameworkVersion`,
      "unknown framework version"
    );
    var teamId = _.get(buildConfig, `ios.team_id`);
    var isIndividualAccount = _.get(
      buildConfig,
      `ios.isIndividualAccount`,
      false
    );
    var onesignal_appId = _.get(buildConfig, `onesignal_appId`, null);
    var skipAppGroupCreation = _.get(
      buildConfig,
      `${platform.toLowerCase()}.skipAppGroupCreation`,
      false
    );
    var enableOneSignal = _.get(
      buildConfig,
      `${platform.toLowerCase()}.enableOneSignal`,
      false
    );

    var forceAppGroupCreation = _.get(
      buildConfig,
      `${platform}.forceAppGroupCreation`,
      false
    );

    // firstname + lastname of the person that triggered build

    var triggeredBy = _.get(buildConfig, `${platform}.triggeredBy`, "Unknown");

    var enableIpadSupport = _.get(
      buildConfig,
      `${platform}.enableIpadSupport`,
      false
    );

    var isBuildUploaded = false;

    shell.cd(buildTweaksPath);

    if (build_ios) {
      console.log("Running Tweaks");

      const appgroupsFlag =
        !isIndividualAccount && (forceAppGroupCreation || enableOneSignal);

      if (appgroupsFlag) {
        console.log("Enabling App Groups..");
        shell.exec(
          `./tweaks.sh ${buildTweaksPath} ${projectPath} --enable-appgroups --disable-applepay`
        );
      } else {
        console.log("Disabling Appgroups..");
        shell.exec(
          `./tweaks.sh ${buildTweaksPath} ${projectPath} --disable-appgroups --disable-applepay`
        );
      }

      if (enableIpadSupport) {
        console.log("Enabling Ipad Support...");
        shell.cd(ipadTweaksPath);
        const ipadXcodeProjFile = path.join(ipadTweaksPath, "project.pbxproj");
        shell.exec(
          `./ipadSupport.sh "${iosPlistPath}" "${ipadXcodeProjFile}" "${xcodeProjFilePath}"`
        );
        console.log("Ipad Support Enabled");
      }
    }

    //!! ON CODEBUILD PASSING FLAGS FAIL SOMEHOW. NEED TO DEBUG

    // if (build_android) {
    //   console.log("Running Tweaks");
    //   shell.exec(`./tweaks.sh ${buildTweaksPath} ${projectPath}`);
    // }

    shell.cd(currentWrkDir);

    // if (build_ios && version !== "1" && semver !== "1.0.0") {
    //   buildConfig.ios.uploadToTestflight = true;
    //   const { apiKeyId: apptileApiKey, issuerId: apptileIssuerId } =
    //     config.appstore.credentials;

    //   const { apiKey: customerApiKey, issuerId: customerIssuerId } =
    //     buildConfig.ios;

    //   shell.env["apiKey"] = publishOnApptile ? apptileApiKey : customerApiKey;
    //   shell.env["apiIssuerId"] = publishOnApptile
    //     ? apptileIssuerId
    //     : customerIssuerId;
    // }

    console.log(buildConfig.ios);

    console.log("Downloading Build Assets From S3...");

    const requiredFiles = ["icon_path", "splash_path", "service_file_path"];

    let fileNamesMap = {
      icon_path: "icon.png",
      splash_path: "splash.png",
    };

    if (buildConfig[platform].splash_path.includes(".gif")) {
      fileNamesMap["splash_path"] = "splash.gif";
    }

    if (build_android) {
      fileNamesMap["service_file_path"] = "google-services.json";

      if (!buildConfig.android.store_file_path) {
        const { filePath, alias, password } = await generateJKS(
          appId,
          appName,
          bundleName
        );
        buildConfig.android.store_file_path = filePath;
        buildConfig.android.key_alias = alias;
        buildConfig.android.store_password = password;
        buildConfig.android.key_password = password;
        console.log(buildConfig.android);
      } else {
        requiredFiles.push("store_file_path");
        const storeFilePathSuffix =
          buildConfig.android.store_file_path.includes("keystore")
            ? ".keystore"
            : ".jks";

        fileNamesMap = {
          ...fileNamesMap,
          store_file_path: `androidStoreFile${storeFilePathSuffix}`,
        };
      }
    }

    if (build_ios) {
      fileNamesMap["service_file_path"] = "GoogleService-Info.plist";

      if (!publishOnApptile) {
        requiredFiles.push("appStorePrivateKey");
        fileNamesMap["appStorePrivateKey"] = "appStorePrivateKey.p8";
      }
    }

    if (onesignal_appId && buildConfig[platform]?.onesignal_icon_path) {
      requiredFiles.push("onesignal_icon_path");
      fileNamesMap["onesignal_icon_path"] = "onesignal-icon.png";
    }

    await Promise.all(
      requiredFiles.map(async (key) => {
        const downloadPath = path.join(
          currentWrkDir,
          "assets",
          fileNamesMap[key]
        );

        // Download the files from S3

        await downloadFromS3(
          _.get(buildConfig, [platform, key]),
          downloadPath,
          config.buildAssetsBucket
        );

        // Replace Uploader Keys With Downloaded File Paths in distribution.config.json

        _.set(buildConfig, [platform, key], path.resolve(downloadPath));
      })
    );

    const enableApptileAnalytics = _.get(
      buildConfig,
      "enable_apptile_analytics",
      false
    );
    if (enableApptileAnalytics) {
      console.log("Phase 1 - Apptile Analytics - setup start");
      const listSources = {
        method: "get",
        maxBodyLength: Infinity,
        url: "https://api.segmentapis.com/sources?pagination[count]=200",
        headers: {
          Authorization: `Bearer ${process.env.SEGMENT_API_KEY}`,
        },
      };

      console.log("Phase 1 - Apptile Analytics - Fetching all sources");
      let sources = await axios.request(listSources);
      sources = sources?.data?.data?.sources;
      const existingSource = sources.find((e) => e.name == appId);
      if (!existingSource) {
        console.log(
          "Phase 1 - Apptile Analytics - Existing source not found, creating new source"
        );
        const data = JSON.stringify({
          slug: appId.replace(/[-]/gim, "_"),
          name: appId,
          enabled: true,
          metadataId: "B0X0QmvMny",
          settings: {},
        });

        let createSource = {
          method: "post",
          maxBodyLength: Infinity,
          url: "https://api.segmentapis.com/sources",
          headers: {
            Authorization: `Bearer ${process.env.SEGMENT_API_KEY}`,
            "Content-Type": "application/json",
          },
          data: data,
        };

        let newSource = await axios.request(createSource);
        newSource = newSource?.data?.data?.source;
        _.set(
          buildConfig,
          "apptile_analytics_segment_key",
          newSource.writeKeys[0]
        );
      } else {
        _.set(
          buildConfig,
          "apptile_analytics_segment_key",
          existingSource.writeKeys[0]
        );
        console.log("Phase 1 - Apptile Analytics - Existing source found");
        console.log("Phase 1 - Apptile Analytics - setup end");
      }
      console.log("Phase 2 - Apptile Analytics - setup start");
      try {
        const ExistingDashboardInfo = {
          AwsAccountId: process.env.AWS_ACCOUNT_ID,
          DashboardId: `${appId}-dashboard`,
        };
        const checkDashboard = new DescribeDashboardCommand(
          ExistingDashboardInfo
        );
        await quickSightClient.send(checkDashboard);
        console.log(
          "Phase 2 - Apptile Analytics - found existing dashboard ending this phase"
        );
      } catch (err) {
        try {
          console.log("Phase 2 - Apptile Analytics - fetching og analysis");
          const ogAnalysisInfo = {
            AwsAccountId: process.env.AWS_ACCOUNT_ID,
            AnalysisId: "OG-ANALYSIS",
          };
          const getOgAnalysis = new DescribeAnalysisDefinitionCommand(
            ogAnalysisInfo
          );
          const ogAnalysis = await quickSightClient.send(getOgAnalysis);
          if (!ogAnalysis) throw new Error("OG analysis not found");
          ogAnalysis.Definition.ParameterDeclarations =
            ogAnalysis.Definition.ParameterDeclarations.map((e) => {
              if (e?.StringParameterDeclaration?.Name == "SchemaName") {
                e.StringParameterDeclaration.DefaultValues.StaticValues = [
                  `${appId[0].match(/[\d]/gim) ? "_" : ""}${appId.replace(
                    /[-]/gim,
                    "_"
                  )}`,
                ];
              }
              return e;
            });
          console.log(
            "Phase 2 - Apptile Analytics - og analysis fetched successfully"
          );
          const newDashboardInfo = {
            AwsAccountId: process.env.AWS_ACCOUNT_ID,
            DashboardId: `${appId}-dashboard`,
            Name: `${appId}-dashboard`,
            Permissions: [
              {
                Actions: [
                  "quicksight:DescribeDashboard",
                  "quicksight:ListDashboardVersions",
                  "quicksight:UpdateDashboardPermissions",
                  "quicksight:QueryDashboard",
                  "quicksight:UpdateDashboard",
                  "quicksight:DeleteDashboard",
                  "quicksight:UpdateDashboardPublishedVersion",
                  "quicksight:DescribeDashboardPermissions",
                ],
                Principal:
                  "arn:aws:quicksight:us-east-1:************:user/default/AWSReservedSSO_PowerUserAccess_608fb0bf87b02a48/<EMAIL>",
              },
            ],
            Definition: ogAnalysis.Definition,
            ThemeArn: ogAnalysis.ThemeArn,
          };
          const createNewDashboard = new CreateDashboardCommand(
            newDashboardInfo
          );
          const newDashboard = await quickSightClient.send(createNewDashboard);
          if (newDashboard)
            console.log(
              "Phase 2 - Apptile Analytics - dashboard created successfully"
            );
          else
            console.error(
              "ERROR!!!! Phase 2 - Apptile Analytics - dashboard creation failed"
            );
        } catch (err) {
          console.error(
            "ERROR!!!! Phase 2 - Apptile Analytics - dashboard creation failed"
          );
        }
      }
      console.log("Phase 2 - Apptile Analytics - setup end");
    }
    // Convert buildConfig to JSON string
    const buildConfigJSON = JSON.stringify(buildConfig, null, 2);
    console.log(
      "resolved buildConfigJSON: ",
      JSON.stringify(buildConfig, null, 2)
    );

    // Write the build config to disk as distribution.config.json

    fs.writeFileSync(
      path.join(currentWrkDir, "assets", "distribution.config.json"),
      buildConfigJSON,
      "utf-8"
    );

    if (build_ios) {
      const {
        apiKeyId: apptileApiKey,
        issuerId: apptileIssuerId,
        privateKeyPath: apptilePrivateKeyPath,
      } = config.appstore.credentials;

      const {
        apiKey: customerApiKey,
        issuerId: customerIssuerId,
        appStorePrivateKey,
      } = buildConfig.ios;

      console.log(customerApiKey, customerIssuerId, appStorePrivateKey);

      shell.env["apiKey"] = publishOnApptile ? apptileApiKey : customerApiKey;
      shell.env["apiIssuerId"] = publishOnApptile
        ? apptileIssuerId
        : customerIssuerId;

      shell.env["authKeyPath"] = publishOnApptile
        ? apptilePrivateKeyPath
        : appStorePrivateKey;
    }

    if (build_ios && !publishOnApptile) {
      config.appstore.credentials.apiKeyId = _.get(
        buildConfig,
        "ios.apiKey",
        null
      );
      config.appstore.credentials.issuerId = _.get(
        buildConfig,
        "ios.issuerId",
        null
      );
      config.appstore.credentials.privateKeyPath = _.get(
        buildConfig,
        "ios.appStorePrivateKey",
        null
      );

      console.log(config.appstore.credentials);

      await registerDevice(
        config.appstore.testDeviceName,
        config.appstore.testDeviceUdid
      );
    }

    if (build_ios) {
      config.appstore.credentials.teamId = teamId;
      const { createBundleIdentifier } = await import(
        "./utils/ios/bundleIds.js"
      );

      const { createBundleCapabilities } = await import(
        "./utils/ios/bundleCapabilities.js"
      );

      //!! Handle AppGroup Created more than once.

      var appGroupId;

      if (!skipAppGroupCreation && !isIndividualAccount) {
        const { createAppGroup } = await import("./utils/ios/appGroups.js");

        appGroupId = await createAppGroup(
          `${appName}`,
          `group.${bundleName}.notification`,
          teamId,
          isIndividualAccount
        );
      }

      const appBundleId = await createBundleIdentifier(
        appName,
        bundleName,
        appGroupId,
        teamId
      );
      const imageNotificationBundleId = await createBundleIdentifier(
        `${appName} Image Notification`,
        `${bundleName}.ImageNotification`,
        appGroupId,
        teamId
      );

      const notificationContentBundleId = await createBundleIdentifier(
        `${appName} NotificationContentExtension`,
        `${bundleName}.NotificationContentExtension`,
        appGroupId,
        teamId
      );

      console.log(
        appBundleId,
        imageNotificationBundleId,
        notificationContentBundleId,
        "Bundle Ids"
      );
      if (appBundleId) await createBundleCapabilities(appBundleId);

      if (imageNotificationBundleId)
        await createBundleCapabilities(imageNotificationBundleId);

      if (notificationContentBundleId)
        await createBundleCapabilities(notificationContentBundleId);
    }

    const sourceFilePath = path.join(
      currentWrkDir,
      "assets",
      "distribution.config.json"
    );

    shell.cd(destinationFilePath);
    shell.cp("-f", sourceFilePath, destinationFilePath);

    console.log(`Generating Build for ${platform}`);

    shell.env["build_environment"] = "ci";

    const result = shell.exec("./distribution.build.sh");

    // //!! Handle Most Common Android Errors
    // //!! Upload to S3 Logs for build system
    // //!! Try this in silent false

    console.log(result.code, "CODE");
    if (result.code !== 0) {
      if (result.stdout.includes("Error fetching app info"))
        throw new Error(
          "App needs to be Published atleast once inorder to build!"
        );
    }

    if (result.code === 0) {
      if (build_ios && result.stderr.includes("ARCHIVE FAILED")) {
        throw new Error(
          "Failed Because .entitlements files containing Appgroups .Build System Doesnt Support Appgroups for now! Remove that and try again!"
        );
      }
      //TODO: IMPROVE THIS LOGIC. NEED TO CHECK WETHER BUILD UPLOADED OR NOT FROM LOGS
      if (build_ios && result.stdout.includes("UPLOAD SUCCEEDED")) {
        isBuildUploaded = true;
        console.log("TESTFLIGHT UPLOAD SUCCEDED");
      }
    }

    const buildAssetsPath = path.join(projectPath, "build");

    if (!fs.existsSync(buildAssetsPath))
      throw Error(`${platform} Build Failed. Check Logs on Jenkins!`);

    shell.cd(projectPath);

    // ZIP THE BUILD ASSETS FOLDER

    shell.exec("zip -r build.zip build/");

    const currentTime = moment().format("MMMM_Do_YYYY_h_mm_ss").toLowerCase();

    const appNameNoSpace = appName
      .replaceAll(/\s/g, "")
      .replace(/[^a-zA-Z]/g, "")
      .toLowerCase();

    const s3FolderPath = `${appId}/builds/${platform.toLowerCase()}/${appNameNoSpace}_${currentTime}`;
    const s3zipFileName = `${s3FolderPath}/build.zip`;

    let qrCodeImageLink = null;
    if (platform.toLowerCase() === "android") {
      await uploadToS3(
        path.join(projectPath, "build/outputs/apk/release/app-release.apk"),
        config.artifactsBucket,
        `${s3FolderPath}/app-release.apk`
      );

      // GENERATE THE QR CODE
      const apkUrl = encodeURI(
        `https://artefacts-demo.apptile.io/${appId}/builds/${platform.toLowerCase()}/${appNameNoSpace}_${currentTime}/app-release.apk`
      );
      console.log("apkUrl", apkUrl);

      const qrFile = await new Promise((resolve, reject) => {
        const qrPath = path.join(projectPath, "build/qr.png");
        QRCode.toFile(
          qrPath,
          apkUrl,
          {
            autoCorrelationLevel: "H",
          },
          function (err) {
            if (err) {
              resolve("");
              console.error("Qr code generation failed");
            } else {
              resolve(qrPath);
            }
          }
        );
      });

      await uploadToS3(
        qrFile,
        config.artifactsBucket,
        `${s3FolderPath}/qr.png`
      );

      qrCodeImageLink = encodeURI(
        `https://artefacts-demo.apptile.io/${appId}/builds/${platform.toLowerCase()}/${appNameNoSpace}_${currentTime}/qr.png`
      );
      console.log("qrCodeImageLink: ", qrCodeImageLink);
    } else {
      await uploadToS3(
        path.join(projectPath, "build/ReactNativeTSProject.ipa"),
        config.artifactsBucket,
        `${s3FolderPath}/ReactNativeTSProject.ipa`
      );
      const ipaUrl = `https://artefacts-demo.apptile.io/${appId}/builds/${platform.toLowerCase()}/${appNameNoSpace}_${currentTime}/ReactNativeTSProject.ipa`;
      console.log(ipaUrl, "IPA URL");
    }

    // UPLOAD THE BUILD ASSETS TO S3
    const uploaderKey = await uploadToS3(
      path.join(projectPath, "build.zip"),
      config.artifactsBucket,
      s3zipFileName
    );

    console.log(uploaderKey, "Uploader Key for S3 Bucket");

    if (uploaderKey) {
      const artefactUrl = encodeURI(config.buildCdnUrl + "/" + uploaderKey);
      console.log(artefactUrl);

      const alertData = {
        appName,
        platform,
        version,
        semver,
        artefactUrl,
        qrCodeImageLink,
        triggeredBy,
        branchOrTag,
        frameworkVersion,
        jenkinsBuildNumber,
      };
      const alertMessage = generateBuildSuccessAlert(alertData);
      await sendSlackAlerts(alertMessage);

      await axios.post(
        webhook_url,
        {
          success: true,
          artefactUrl,
          uploaded: isBuildUploaded,
        },
        {
          headers: {
            "X-App-Id": appId,
            "X-Api-Token": config.buildManager.apiToken,
          },
        }
      );
    } else {
      throw Error("Web Hook Failed to Apptile Server!!");
    }

    await enablePublishFlow(appId);
    process.exit(0);
  } catch (err) {
    console.log("Build Failed !!" + err.stack ?? "");
    const alertData = {
      appName,
      platform,
      version,
      semver,
      branchOrTag,
      triggeredBy,
      frameworkVersion,
      jenkinsBuildNumber,
    };
    const alertMessage = generateBuildFailureAlert(alertData, err.stack);
    await sendSlackAlerts(alertMessage);

    await axios.post(
      webhook_url,
      {
        success: false,
      },
      {
        headers: {
          "X-App-Id": appId,
          "X-Api-Token": config.buildManager.apiToken,
        },
      }
    );

    process.exit(1);
  }
}

main();
