AWS_REGION=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
BUILD_ASSETS_BUCKET=
API_BASE_URL="http://api.apptile.local"
ARTIFACTS_BUCKET=
BUILD_CDN_URL=""

#Appstore account creds for ClearSight Private Limited Account
APPSTORE_API_KEY=
APPSTORE_ISSUER_ID=
PRIVATE_KEY_PATH=
APPSTORE_TEAM_ID=

#Required for puppeteer to update apns cert
APPSTORE_CONNECT_USERNAME=
APPSTORE_CONNECT_PASSWORD=

#Puppeteer folder to save cookies
USER_DATA_DIR="~/user_dir"

#Slack webhook to send build alerts
SLACK_BUILD_WEBHOOK=""

NOTIFIER_BUCKET=""

# Segment analytics key required to create segment analytics when enableAnalytics=true in distribution.config
SEGMENT_API_KEY=""

JENKINS_BASE_URL=
JENKINS_IOS_JOB_TITLE= "IOS-Build-Pipeline"
JENKINS_ANDROID_JOB_TITLE="android-code-build"
JENKINS_FIREBASE_JOB_TITLE="Firebase-Creation"

# Apptile server elevated tokens for build system
ONESIGNAL_PROXY_AUTH_TOKEN=

BUILD_MANAGER_API_TOKEN=

