import {connectPlugin, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {PluginEditorsConfig} from 'apptile-core';
import docs from './docs';
import _ from 'lodash';

interface ApptileColorSwatchConfigParams {
  value: any;
  colorsConfig: any;
  getColorOption: (colorKey: string) => any;
}

export const ApptileColorSwatchConfig: ApptileColorSwatchConfigParams = {
  value: null,
  colorsConfig: '',
  getColorOption: 'function',
};

const fuzzySearchColor = (colorKey: string, colorMapping: any) => {
  let colorHex = colorMapping[colorKey.toLowerCase()] ? colorMapping[colorKey.toLowerCase()] : null;

  if (!colorHex) {
    const derivedColorKey = colorKey.toLowerCase().replace('/', ' ').split(' ').splice(-1).pop();
    if (derivedColorKey) {
      colorHex = _.get(colorMapping, derivedColorKey);
    }
  }

  if (!colorHex) {
    const possibleMatches = colorKey.toLowerCase().replace('/', ' ').split(' ');
    for (let possibleMatch of possibleMatches) {
      colorHex = _.get(colorMapping, possibleMatch);
      if (colorHex) break;
    }
  }
  return colorHex;
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  getColorOption: {
    getValue(model, renderedValue, selector) {
      const colorsConfig = model?.colorsConfig;
      return (colorKey: string) => {
        return fuzzySearchColor(colorKey, colorsConfig);
      };
    },
  },
};

export const shopifyEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'colorsConfig',
      props: {
        label: 'Color Swatch Config',
        placeholder: `{{{
          "default": {
            "colorHex": "#ffffff"
          },
          "ash": {
            "colorHex": "#a3a3a3"
          },
          "sky": {
            "colorHex": "#4cbbb8"
          }
        }}}`,
      },
    },
  ],
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'apptileColorSwatch',
  type: 'datasource',
  name: 'Apptile Color Swatch',
  description: 'Manage color swatches for Product.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'state',
};
const emptyOnupdate = null;

export default connectPlugin('ApptileColorSwatch', null, ApptileColorSwatchConfig, emptyOnupdate, shopifyEditors, {
  propertySettings,
  pluginListing: pluginListing,
  docs,
});
