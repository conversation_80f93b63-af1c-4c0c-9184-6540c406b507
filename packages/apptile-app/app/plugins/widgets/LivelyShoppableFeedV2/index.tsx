import {createDeepEqualSelector} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import {datasourceByIdModelSel, datasourceTypeModelSel} from 'apptile-core';
import _, {debounce} from 'lodash';
import React, {useCallback} from 'react';
import {View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {createSelector} from 'reselect';
import {PluginEditorsConfig} from 'apptile-core';
import {CartProductVariantQuantityChangeParam} from '../../datasource/ShopifyV_22_10/actions/checkoutAction';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import LivelyControl from './LivelyControl';
import docs from './docs';

const SHOPIFY_PV_GID_MATCH_REGEX = /^gid:\/\/shopify\/ProductVariant\/\d+/; // matches productId with pattern gid://shopify/Product/{any id}
const SHOPIFY_PV_GID_PREFIX = 'gid://shopify/ProductVariant/';

export const resolveNumericVariantId = (variantId: string | number): number => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return _.parseInt(_.replace(`${variantId}`, SHOPIFY_PV_GID_PREFIX, ''));
  }
  return _.parseInt(`${variantId}`);
};

export const resolveVariantGid = (variantId: string | number): string => {
  if (SHOPIFY_PV_GID_MATCH_REGEX.test(`${variantId}`)) {
    return `${variantId}`;
  }
  return `${SHOPIFY_PV_GID_PREFIX}` + variantId;
};

export interface IApptileCartDetails {
  id: string;
  subtotalAmount: number;
  lines: {
    id: string;
    quantity: number;
    variant: {
      product: {
        title: string;
        id: string;
      };
      id: string;
      title: string;
      featuredImage: string;
      price: number;
      salePrice: number;
    };
  }[];
}

type LivelyWidgetConfigType = {
  value: string;
  productHandle: string;
  cartDetails: IApptileCartDetails | string;
  onCheckoutTap: string;
  onNavigateToProductTap: string;
  navigateToVariantSelector: string;
};

const LivelyWidgetConfig: LivelyWidgetConfigType = {
  value: '',
  productHandle: '',
  cartDetails: '{{shopify.currentCart}}',
  onCheckoutTap: '',
  onNavigateToProductTap: '',
  navigateToVariantSelector: '',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'LivelyShoppableFeedV2',
  type: 'widget',
  name: 'Lively Shoppable Feeds2',
  description: 'Load Lively Widget in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
  defaultHeight: 'auto',
  defaultWidth: 'auto',
};

const makeShopifyConfigSelector = (pluginId: string) => {
  const shopifyDSConfigSel = state => selectPluginConfig(state, null, pluginId);
  return shopifyDSConfigSel;
};

const Lively = React.forwardRef((props, ref) => {
  const {modelStyles, config, model, triggerEvent, modelUpdate} = props;

  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const brandId = model.get('brandId');
  const isLiveStreamFeed = model.get('liveStreamFeed');
  const isChunkStream = model.get('chunkStream');
  const cartDetails = model.get('cartDetails');
  const showCart = model.get('showCart');
  const themeCode = model.get('themeCode');
  const widgetId = model.get('widgetId');
  const shopifyDatasourceId = model.get('shopifyDatasourceId');

  const shopifyDSSel = (state: any) => datasourceByIdModelSel(state, shopifyDatasourceId);
  const shopifyDsModel = useSelector(shopifyDSSel);
  const shopifyDsConfigSel = makeShopifyConfigSelector(shopifyDatasourceId);
  const shopifyDsConfig = useSelector(shopifyDsConfigSel);

  const shopifyLineItemIncreaseAction = shopifyDsModel?.get('increaseCartLineItemQuantity');
  const shopifyLineItemDecreaseAction = shopifyDsModel?.get('decreaseCartLineItemQuantity');
  const isLoading = shopifyDsModel?.get('syncingCartStatus');
  const dispatch = useDispatch();

  const increaseLineItem = (variantId: string) => {
    const params = {
      merchandiseId: resolveVariantGid(variantId),
      quantity: 1,
      syncWithShopify: true,
      itemPrice: 0,
      successToastText: '',
      sellingPlanId: '',
    } as CartProductVariantQuantityChangeParam;

    if (!shopifyLineItemIncreaseAction) {
      logger.error('increaseCartLineItemQuantity action not defined on shopify datasource');
      return;
    }
    shopifyLineItemIncreaseAction(dispatch, shopifyDsConfig, shopifyDsModel, [shopifyDatasourceId], params);
  };

  const decreaseLineItem = (variantId: string) => {
    const params = {
      merchandiseId: resolveVariantGid(variantId),
      quantity: 1,
      syncWithShopify: true,
      itemPrice: 0,
      successToastText: '',
      sellingPlanId: '',
    } as CartProductVariantQuantityChangeParam;

    if (!shopifyLineItemIncreaseAction) {
      logger.error('increaseCartLineItemQuantity action not defined on shopify datasource');
      return;
    }
    shopifyLineItemDecreaseAction(dispatch, shopifyDsConfig, shopifyDsModel, [shopifyDatasourceId], params);
  };

  const onCartUpdateHandler = (type: string, data: {quantity: number; variantId: string}) => {
    if (!data?.variantId) {
      console.error(`No variant Id available data ${JSON.stringify(data, null, 2)}`);
    }

    if (type === 'add') {
      //increase qty
      increaseLineItem(data?.variantId);
    } else if (type === 'update') {
      if (data?.quantity < 0) {
        // decrease qty
        decreaseLineItem(data?.variantId);
      } else {
        //increase qty
        increaseLineItem(data?.variantId);
      }
    }
  };

  const handleChange = useCallback(
    (value: any) => {
      modelUpdate([{selector: ['productHandle'], newValue: value}]);
      setTimeout(() => triggerEvent('onNavigateToProductTap'), 100);
    },
    [modelUpdate, triggerEvent],
  );
  const debouncedHandleChange = useCallback(debounce(handleChange, 340), [handleChange]);

  const onCallback = (event, data: {url: string; id: string; handle: string}) => {
    const {url} = data ?? {};
    if (event === 'CART_UPDATE') {
      onCartUpdateHandler('update', data);
    } else if (event == 'ATC') {
      onCartUpdateHandler('add', data);
    } else if (event === 'CHECKOUT') {
      triggerEvent('onCheckoutTap');
    } else if (event === 'PRODUCT_HOME_CLICK') {
      triggerEvent('navigateToVariantSelector', data);
    } else if (event === 'BUY_NOW') {
      triggerEvent('navigateToVariantSelector', data);
      console.log('event');
    } else {
      const productHandle = _.isEmpty(data) ? null : data?.handle;
      if (!_.isEmpty(productHandle)) {
        debouncedHandleChange(productHandle);
      } else {
        console.log(`Could not trigger onNavigateToProductTap as productHandle is null ${productHandle}`);
      }
    }
  };

  const LivelyConfiguration = {
    widgetId: widgetId, // replace with widget id
    sfLimit: modelStyles?.sfLimit || 10,
    home: {
      type: modelStyles.type,
      header: {
        showHeader: false,
        headerText: 'Watch and shop',
        style: {},
      },
      video: {
        autoPlay: !!modelStyles?.autoPlay,
        hoverOnPlay: !!modelStyles?.hoverOnPlay,
        showViewCount: !!modelStyles?.showViewCount,
        playGif: !!modelStyles?.playGif,
        viewIcon: modelStyles?.viewIcon,
        playIcon: modelStyles?.playIcon,
        columnCount: modelStyles?.columnCount,
        showPlayButton: !!modelStyles?.showPlayButton,
        showViewCountIcon: !!modelStyles?.showViewCountIcon,
        showProduct: !!modelStyles?.showProduct,
        productPlacement: !modelStyles.showProduct ? 'inside' : modelStyles?.productPlacement,
        buyNowButtonText: modelStyles?.buyNowButtonText,
        showProductPrice: !!modelStyles?.showProductPrice,
        showProductActualPrice: !!modelStyles?.showProductActualPrice,
        showProductImage: !!modelStyles?.showProductImage,
        showBuyNowButton: !!modelStyles?.showBuyNowButton,
        showNavigationButton: !!modelStyles?.showNavigationButton,
        navigationButtonLeft: {}, // No mapping found
        navigationButtonRight: {}, // No mapping found
        navigationButtonText: {}, // No mapping found
        containerStyle: {flex: 1, padding: 4}, // No mapping found
        videoContainerStyle: {
          borderRadius: modelStyles?.videoContainerStyleborderRadius || 10,
        },
        viewCountTextStyle: {}, // No mapping found
        viewCountContainerStyle: {}, // No mapping found
        playButtonStyle: {}, // No mapping found
        productCardContainerStyle: {
          backgroundColor: modelStyles?.productCardContainerStyleBackgrooundColor,
        },
        productImageStyle: {
          height: modelStyles?.productImageStyleheight,
        },
        productNameStyle: {
          color: modelStyles?.productNameStylecolor || 'white', // fallback to white
        },
        productSellingPriceStyle: {
          color: modelStyles.productSellingPriceStyleColor || 'white', // fallback to white
        },
        productActualPriceStyle: {
          color: modelStyles?.productActualPriceStylecolor || 'white', // fallback to white
        },
        buyNowButtonStyle: {
          backgroundColor: modelStyles?.buyNowButtonStylebackgroundColor,
          borderRadius: modelStyles?.buyNowButtonStyleborderRadius,
          marginTop: modelStyles?.buyNowButtonStylemarginTop || 5,
        },
        buyNowButtonTextStyle: {
          color: modelStyles?.buyNowButtonTextStylecolor,
        },
      },
    },
    feeds: {
      video: {
        customActionOnBuyNow: true,
        showControls: modelStyles?.feedshowControls,
        seekDuration: 10,
        controlContainerStyle: {},
        videoDurationTextStyle: {},
        seekButtonStyle: {},
        restartVideoButtonStyle: {},
        playPauseButtonStyle: {},
        seekableDurationTextStyle: {},
        sliderProps: {},
        muted: modelStyles?.feedmuted,
        showActionContainer: modelStyles?.feedshowActionContainer,
        showViewsCount: modelStyles?.feedshowViewsCount,
        showLikes: modelStyles?.feedshowLikes,
        showShare: modelStyles?.feedshowShare,
        likeIcon: null,
        likeIconActive: null,
        shareIcon: null,
        cancelIcon: null,
        muteIcon: null,
        unMuteIcon: null,
        showProductList: modelStyles?.feedshowProductList,
        loadingWidget: null,
        buyNowButtonText: null,
        showProductPrice: modelStyles?.feedshowProductPrice,
        showProductActualPrice: modelStyles?.feedshowProductActualPrice,
        showProductImage: modelStyles?.feedshowProductImage,
        showBuyNowButton: modelStyles?.feedshowBuyNowButton,
        cancelButtonStyle: {},
        muteUnMuteButtonStyle: {},
        actionContainerStyle: {},
        viewsStyle: {},
        likesStyle: {},
        shareStyle: {},
        productListOnVideoStyle: {},
        productCardContainerStyle: {
          backgroundColor: modelStyles?.feedproductCardContainerStylebackgroundColor,
        },
        productImageStyle: {
          height: modelStyles?.feedproductImageStyleheight,
        },
        productNameStyle: {},
        productSellingPriceStyle: {},
        productActualPriceStyle: {},
        buyNowButtonStyle: {
          backgroundColor: modelStyles?.feedbuyNowButtonStylebackgroundColor,
          borderRadius: modelStyles?.feedbuyNowButtonStyleborderRadius,
        },
        buyNowButtonTextStyle: {
          color: modelStyles?.feedbuyNowButtonTextStylecolor,
        },
      },
    },

    productDetails: {
      showSellingPrice: true,
      showActualPrice: true,
      showDiscount: true,
      showDescription: true,
      showMoreInfoButton: true,
      showATCButton: true,
      showViewCartButton: true,

      descriptionHeadingText: null,
      moreInfoText: null,
      atcText: null,
      notifyText: null,

      closeIcon: null,
      descExpandIcon: null,
      descCloseIcon: null,

      containerStyle: {},
      bottomContainerStyle: {},
      productImageStyle: {},
      productNameStyle: {},
      sellingPriceStyle: {},
      actualPriceStyle: {},
      offerStyle: {},
      variantNameStyle: {},
      variantsOptionStyle: {},
      variantSelectedOptionStyle: {},
      variantsOptionTextStyle: {},
      variantSelectedOptionTextStyle: {},

      descriptionDetailsAccordion: {},
      descriptionContainerStyle: {},
      descriptionImageStyle: {},
      descriptionHeadingStyle: {},

      moreInfoTextStyle: {},
      moreInfoButtonStyle: {},
      atcTextStyle: {},
      atcButtonStyle: {},

      showCartButtonStyle: {},
    },

    cartDetails: {
      cartHeading: 'Cart',
      closeIcon: null,
      buyNowText: null,

      containerStyle: {},
      cartHeadingStyle: {},
      cartItemContainerStyle: {},
      productImageStyle: {},
      productNameStyle: {},

      countContainerStyle: {},
      countActionStyle: {},
      countTextStyle: {},
      priceTextStyle: {},
      buyNowButton: {},
      buyNowButtonTextStyle: {},
    },
  };

  return (
    <View style={[layoutStyles, modelStyles]} ref={ref}>
      <LivelyControl
        key={widgetId + '-' + brandId}
        brandId={brandId}
        onCartUpdate={onCartUpdateHandler}
        cartDetails={cartDetails}
        onCallback={onCallback}
        onShopNow={() => triggerEvent('onCheckoutTap')}
        isLoading={isLoading}
        config={LivelyConfiguration}
        isLiveStreamFeed={isLiveStreamFeed}
        showChunks={isChunkStream}
      />
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'cartDetails',
      props: {
        label: 'Cart Details',
      },
    },
    {
      type: 'codeInput',
      name: 'widgetId',
      props: {
        label: 'Widget Id',
      },
    },
    {
      type: 'codeInput',
      name: 'brandId',
      props: {
        label: 'Brand Id',
      },
    },
    {
      type: 'codeInput',
      name: 'shopifyDatasourceId',
      props: {
        label: 'Shopify Datasource Id',
      },
    },
    {
      type: 'codeInput',
      name: 'themeCode',
      props: {
        label: 'themeCode',
      },
    },
    {
      type: 'codeInput',
      name: 'showCart',
      props: {
        label: 'Show Cart',
      },
    },
    {
      type: 'checkbox',
      name: 'liveStreamFeed',
      props: {
        label: 'Show Live Stream Feed',
      },
    },
    {
      type: 'checkbox',
      name: 'chunkStream',
      props: {
        label: 'Show  Chunk Stream',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onCheckoutTap: {
    type: EventTriggerIdentifier,
  },
  onNavigateToProductTap: {
    type: EventTriggerIdentifier,
  },
  navigateToVariantSelector: {
    type: EventTriggerIdentifier,
  },
};
const emptyOnupdate = null;

const widegetConfigurationStyles: any = [
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Home Videos',
    },
  },
  {
    type: 'numericInput',
    name: 'sfLimit',
    props: {
      label: 'SF Limit',
    },
  },
  {
    type: 'radioGroup',
    name: 'type',
    props: {
      label: 'Type',
      options: ['horizontal', 'grid'],
    },
  },
  {
    type: 'checkbox',
    name: 'autoPlay',
    props: {
      label: 'AutoPlay',
    },
  },
  {
    type: 'checkbox',
    name: 'hoverOnPlay',
    props: {
      label: 'Hover On Play',
    },
  },
  {
    type: 'checkbox',
    name: 'showViewCount',
    props: {
      label: 'Show View Count',
    },
  },
  {
    type: 'checkbox',
    name: 'playGif',
    props: {
      label: 'Play GIF',
    },
  },
  {
    type: 'codeInput',
    name: 'viewIcon',
    props: {
      label: 'View Icon',
    },
  },
  {
    type: 'codeInput',
    name: 'playIcon',
    props: {
      label: 'Play Icon',
    },
  },
  {
    type: 'numericInput',
    name: 'columnCount',
    props: {
      label: 'Column Count',
    },
  },
  {
    type: 'checkbox',
    name: 'showPlayButton',
    props: {
      label: 'Show Play Button',
    },
  },
  {
    type: 'checkbox',
    name: 'showViewCountIcon',
    props: {
      label: 'Show View Count Icon',
    },
  },
  {
    type: 'checkbox',
    name: 'showProduct',
    props: {
      label: 'Show Product',
    },
  },
  {
    type: 'radioGroup',
    name: 'productPlacement',
    props: {
      label: 'Product Placement',
      options: ['inside', 'below'],
    },
  },
  {
    type: 'codeInput',
    name: 'buyNowButtonText',
    props: {
      label: 'Buy Now Button Text',
    },
  },
  {
    type: 'checkbox',
    name: 'showProductPrice',
    props: {
      label: 'Show Product Price',
    },
  },
  {
    type: 'checkbox',
    name: 'showProductActualPrice',
    props: {
      label: 'Show Product Actual Price',
    },
  },
  {
    type: 'checkbox',
    name: 'showProductImage',
    props: {
      label: 'Show Product Image',
    },
  },
  {
    type: 'checkbox',
    name: 'showBuyNowButton',
    props: {
      label: 'Show Buy Now Button',
    },
  },
  {
    type: 'checkbox',
    name: 'showNavigationButton',
    props: {
      label: 'Show Navigation Button',
    },
  },
  {
    type: 'colorInput',
    name: 'productCardContainerStyleBackgrooundColor',
    props: {
      label: 'Product Card Background Color',
    },
  },
  {
    type: 'colorInput',
    name: 'productSellingPriceStyleColor',
    props: {
      label: 'Product Selling Price Color',
    },
  },
  {
    type: 'colorInput',
    name: 'productActualPriceStylecolor',
    props: {
      label: 'Product Actual Price Color',
    },
  },
  {
    type: 'codeInput',
    name: 'productImageStyleheight',
    props: {
      label: 'Product Image Height',
    },
  },
  {
    type: 'numericInput',
    name: 'videoContainerStyleborderRadius',
    props: {
      label: 'Container Border Radius',
    },
  },
  {
    type: 'numericInput',
    name: '  buyNowButtonStylemarginTop',
    props: {
      label: 'Buy Now Button Margin Top',
    },
  },
  {
    type: 'colorInput',
    name: 'productNameStylecolor',
    props: {
      label: 'Product Name Color',
    },
  },
  {
    type: 'colorInput',
    name: 'buyNowButtonStylebackgroundColor',
    props: {
      label: 'Buy Now Button Background Color',
    },
  },
  {
    type: 'numericInput',
    name: 'buyNowButtonStyleborderRadius',
    props: {
      label: 'Buy Now Button Border Radius',
    },
  },
  {
    type: 'colorInput',
    name: 'buyNowButtonTextStylecolor',
    props: {
      label: 'Buy Now Button TextStyle Color',
    },
  },
  {
    type: 'editorSectionHeader',
    name: '',
    props: {
      label: 'Feed Videos',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowControls',
    props: {
      label: 'Show Controls',
    },
  },
  {
    type: 'checkbox',
    name: 'feedmuted',
    props: {
      label: 'Muted',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowActionContainer',
    props: {
      label: 'Show Action Container',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowViewsCount',
    props: {
      label: 'Show Views Count',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowLikes',
    props: {
      label: 'Show Likes',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowShare',
    props: {
      label: 'Show Share',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowProductList',
    props: {
      label: 'Show Product List',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowProductPrice',
    props: {
      label: 'Show Product Price',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowProductActualPrice',
    props: {
      label: 'Show Product Actual Price',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowProductImage',
    props: {
      label: 'Show Product Image',
    },
  },
  {
    type: 'checkbox',
    name: 'feedshowBuyNowButton',
    props: {
      label: 'Show Buy Now Button',
    },
  },
  {
    type: 'colorInput',
    name: 'feedproductCardContainerStylebackgroundColor',
    props: {
      label: 'Product Card Container Background Color',
    },
  },
  {
    type: 'codeInput',
    name: 'feedproductImageStyleheight',
    props: {
      label: 'Product Image Height',
    },
  },
  {
    type: 'colorInput',
    name: 'feedbuyNowButtonStylebackgroundColor',
    props: {
      label: 'Buy Now Button Background Color',
    },
  },
  {
    type: 'numericInput',
    name: 'feedbuyNowButtonStyleborderRadius',
    props: {
      label: 'Buy Now Button Border Radius',
    },
  },
  {
    type: 'colorInput',
    name: 'feedbuyNowButtonTextStylecolor',
    props: {
      label: 'Buy Now Button Text Style Color',
    },
  },
];

export default connectWidget('Livelyshoppablefeed2', Lively, LivelyWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: [...defaultStyleEditors, ...widegetConfigurationStyles],
  pluginListing,
  docs,
});
