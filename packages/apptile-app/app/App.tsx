import React, {useEffect, useMemo, useRef, useState} from 'react';
import {AppState, InteractionManager, Linking, Platform, StyleSheet, View} from 'react-native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Provider} from 'react-redux';

// Import Local storage here so it inits libraries for async storage before any anything else.
import {LocalStorage} from 'apptile-core';
logger.info('LocalStorage initialized: ');

import {FileSystem, logger} from 'apptile-core';
logger.info('FileSystem and logger initialized');

import {JSSplashScreen, setSplashScreenMessage} from 'apptile-core';
import {AppUpdaterOverlay} from 'apptile-core';
import {initPlugins} from './plugins/initPlugins';
import {store} from 'apptile-core';
import {getAppConstants} from 'apptile-core';
import {getConfigValue as RNGetValues} from 'apptile-core';
import {initApptileConfig} from './common/apptile/ApptileConfigManager';
import {processPreviewURL, processCampaignParameters} from './common/Linking/LinkingManager';
import {requestNotificationPermission} from './common/firebase/firebaseHandler';
import {ApptileLocalSettings} from 'apptile-core';
import {initApptileUserIdentifier} from 'apptile-core';
import {updateFCMToken} from 'apptile-core';
import {FontsProvider} from 'apptile-core';
import {AppContainer} from 'apptile-core';
import {ThemeContainer} from 'apptile-core';
import {CustomIconProvider} from 'apptile-core';
import {PreviewAppOverlay} from 'apptile-core';
import {ApptileAnimationsContextProvider} from 'apptile-core';
import checkATTPermission from './common/AppleATTPermission';
import {ApptileAnalytics} from './common/ApptileAnalytics';
import defaultEventManager from './common/ApptileAnalytics/DefaultEvents';
import useMoEngage, {registerForMoengageNotification} from './common/ApptileAnalytics/moengageAnalytics/useMoEngage';
import initAppsFlyer from './common/ApptileAnalytics/appsflyerAnalytics/initAppsFlyer';
import {initOneSignal, getNotificationInfo} from './common/ApptileAnalytics/onesignalAnalytics/initOneSignal';
import {SentryHelper} from 'apptile-core';
import {initCleverTap} from './common/ApptileAnalytics/cleverTapAnalytics/initCleverTap';
import initKlaviyo from './common/ApptileAnalytics/klaviyoAnalytics/initKlaviyo';
import {Segment} from './common/ApptileAnalytics/segmentAnalytics';
import {addCustomEventListener} from 'apptile-core';
// import NativeDevSettings from 'react-native/Libraries/NativeModules/specs/NativeDevSettings';

import {GetRegisteredNativePage} from '../app/views/prebuilt';
import {WEB_API_SERVER_ENDPOINT} from '../.env.json';
import EditableWidget, {EditableHandlePositionContext} from '../app/plugins/widgets/common/components/EditableWidget';
import {getLinkingPrefixesInDevAndWeb} from '../app/common/utils/getLinkingPrefixes';
import {getLinkingPrefixesInDistributedApp} from '../app/common/utils/mobile-only';
import {checkBundleUpdate} from './common/Bundle/BundleManager';
global.ENABLE_LOGROCKET = process.env.ENABLE_LOGROCKET;
global.WEB_API_SERVER_ENDPOINT = WEB_API_SERVER_ENDPOINT;
global.GetRegisteredNativePage = GetRegisteredNativePage;
global.EditableWidget = EditableWidget;
global.EditableHandlePositionContext = EditableHandlePositionContext;
global.getLinkingPrefixesInDevAndWeb = getLinkingPrefixesInDevAndWeb;
global.getLinkingPrefixesInDistributedApp = getLinkingPrefixesInDistributedApp;

initPlugins();

addCustomEventListener('ApptileAnalyticsSendEvent', (type, name, params) => {
  ApptileAnalytics.sendEvent(type, name, params);
});

addCustomEventListener('ApptileAnalyticsSendInternalEvent', (type, name, params) => {
  ApptileAnalytics.sendInternalEvent(type, name, params);
});

addCustomEventListener('registerForMoengageNotification', token => {
  registerForMoengageNotification(token);
});

addCustomEventListener('ApptileGetNotificationInfo', () => {
  getNotificationInfo();
});

addCustomEventListener('markStart', (label: string, meta: any) => {
  ApptileAnalytics.markStart(label, meta);
});

addCustomEventListener('markEnd', (label: string, meta: any) => {
  ApptileAnalytics.markEnd(label, meta);
});

/**
 * ThemeContainer will be wrapping entire Editor in case if isEditable otherwise it will be only wrapping the App
 * @param param0
 * @returns
 */

const App: React.FC<Record<string, any>> = props => {
  // logger.info('[DEBUG] RERENDER App.tsx');
  const [apptileInit, setApptileInit] = useState(false);
  const onLaunchRef = React.useRef<boolean>();
  const appState = useRef(AppState.currentState);

  useMemo(() => {
    initApptileConfig().then(async () => {
      // prioritize preview url processing in preview app
      const isDistributedApp = !(Platform.OS === 'web' || !(await RNGetValues('APPTILE_IS_DISTRIBUTED_APP')));
      if (!isDistributedApp) {
        const initialUrl = (await Linking.getInitialURL()) || props.initialUrl;
        if (initialUrl) await processPreviewURL(initialUrl);
      }
      logger.info('Initiating with App Constants:', getAppConstants());
      setApptileInit(true);

      // logger.info('[DEBUG ]App.tsx initApptileConfig Called');
      if (!__DEV__) {
        SentryHelper.init();
        (async () => {
          setSplashScreenMessage?.('Checking for JS bundle update...');
        })();
      }
      InteractionManager.setDeadline(15);
      // logger.info('[DEBUG ]App.tsx initApptileConfig Finished');
    });
  }, []);

  useEffect(() => {
    // NativeDevSettings.setIsDebuggingRemotely(false);
    setTimeout(
      () =>
        ApptileLocalSettings.initialize().then(async () => {
          await InteractionManager.runAfterInteractions();
          const newUserRegistered = await initApptileUserIdentifier();
          Segment.initSegment();

          await checkATTPermission();

          await initOneSignal();

          /* OneSignalRequiresItToRemove */
          await requestNotificationPermission();
          if (ApptileLocalSettings.pushToken) {
            await updateFCMToken();
          }
          /* OneSignalRequiresItToRemoveEnd */

          // if (ApptileLocalSettings.pushToken) {
          //   initKlaviyo();
          // }
          initKlaviyo()

          await ApptileAnalytics.initialize(); //<== [do not alter] this need to be after att permission check

          const initialUrl = (await Linking.getInitialURL()) || props.initialUrl;
          const campaignParams = await processCampaignParameters(initialUrl);
          if (newUserRegistered) {
            await defaultEventManager.sendFirstOpenEvent(campaignParams);
          } else {
            if (onLaunchRef.current) {
              return;
            }
            if (appState.current !== 'active') {
              return;
            }
            await defaultEventManager.sendAppOpenEvent(campaignParams);
            onLaunchRef.current = true;
          }

          initAppsFlyer();
          initCleverTap();
        }),
      500,
    );
    // logger.info('App.tsx useEffect Initializer Done');
  }, []);

  useMoEngage();

  const lastUpdateCheck = useRef(+new Date());
  useEffect(() => {
    let subscription: any;
    const init = async () => {
      let nextUpdateCheck: number = Number(await RNGetValues('APPTILE_NEXT_BUNDLE_UPDATE_CHECK_ON_APP_FOCUS'));
      nextUpdateCheck = isNaN(nextUpdateCheck) ? 24 : nextUpdateCheck;
      subscription = AppState.addEventListener('change', nextAppState => {
        if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
          if (!__DEV__ && +new Date() - lastUpdateCheck.current > 1000 * 60 * 60 * (nextUpdateCheck || 24)) {
            Platform.select({native: checkBundleUpdate(false, true)});
          }
          // on refocus check for new app config if it's been more than one day in background BLOCKER: appConfig update not applied correctly using changeAppConfig
        }
        appState.current = nextAppState;
      });
    }
    init();
    return () => {
      subscription.remove();
    };
  }, []);

  // logger.info('[DEBUG] Rendering App.tsx');
  return (
    <SafeAreaProvider>
      <Provider store={store}>
        <FontsProvider>
          <CustomIconProvider>
            <ThemeContainer>
              <ApptileAnimationsContextProvider>
                <AppContainer apptileInit={apptileInit} />
              </ApptileAnimationsContextProvider>
            </ThemeContainer>
          </CustomIconProvider>
        </FontsProvider>
      </Provider>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

let AppWithJSSplash: React.FC<Record<string, any>> = props => (
  <JSSplashScreen splashImageSource={require('./assets/splash.png')}>
    <View style={styles.container}>
      <App {...props} />
      <PreviewAppOverlay {...props} />
      <AppUpdaterOverlay imageSource={require('./assets/update-logo.gif')} />
    </View>
  </JSSplashScreen>
);

AppWithJSSplash = SentryHelper.wrapWithTouchBoundary(AppWithJSSplash);

export default AppWithJSSplash;
