import React, {useEffect, useState} from 'react';
import {Modal, StyleSheet, Text, View, Image} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigate, useParams} from 'react-router-dom';

import Button from '../components-v2/base/Button';
import Icon from '../components-v2/base/Icon';
// import {BuildManagerApi} from '../api/BuildApi';
import {selectMandatoryCheck} from '../selectors/EditorModuleSelectors';
import PublishFlow from '../views/publishFlow';
import {selectPaymentStatus} from '../selectors/BillingSelector';
import Analytics from '@/root/web/lib/segment';
import {checkApptileEmailSelector} from '../selectors/FeatureGatingSelector';
import {SnapshotSchedulerForm} from '../views/snapshotScheduler';
import {overlappingOtasSelector} from '../selectors/SnapshotSelector';
import {checkOverlappingOtas} from '../actions/editorActions';
import {apptileStateSelector} from 'apptile-core';
import _ from 'lodash';
import {SnapshotOverlapHandler} from '../views/snapshotScheduler/SnapshotOverlapHandler';
import {defaultBranchNameSelector} from '../selectors/EditorSelectors';

type SaveAndPublishProps = {
  onSave: () => void;
  onPublish: () => void;
  fromPaymentConfirmation: boolean;
  hideSave?: boolean;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  saveButton: {
    // width: 120,
    height: 30,
    paddingHorizontal: 16,
    backgroundColor: '#F3F3F3',
  },
  saveButtonText: {
    fontSize: 13,
    lineHeight: 16,
    fontWeight: '500',
    color: '#000000',
  },
  publishButtonTopBar: {
    height: 30,
    paddingHorizontal: 16,
    backgroundColor: '#1060E0',
    borderWidth: 0,
  },
  publishButtonTextTopBar: {
    fontSize: 13,
    lineHeight: 16,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  backdrop: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    backgroundColor: '#00000050',
  },
  modal: {
    padding: 42,
    borderWidth: 1,
    borderColor: '#BEBEBE',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderRadius: 30,
    backgroundColor: 'white',
    // minWidth: '40%',
    // minHeight: '40%',
    alignItems: 'center',
    maxHeight: 500,
  },
  heading: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 25,
    marginBottom: 20,
    fontFamily: 'Work Sans',
  },
  subheading: {
    fontSize: 20,
    width: 460,
    fontFamily: 'Work Sans',
  },
  actionArea: {
    flexDirection: 'row',
    marginTop: 40,
    width: '100%',
  },
  cancelButton: {
    flex: 1,
    marginRight: 4,
  },
  publishButton: {
    flex: 1,
    marginLeft: 4,
  },
});

const SaveAndPublish = React.forwardRef<View, SaveAndPublishProps>((props, ref) => {
  const [showPublishButton, setShowPublishButton] = useState(false);
  const currentBranchName = useParams().branchName;
  const defaultBranchName = useSelector(defaultBranchNameSelector);
  if (!defaultBranchName) {
    logger.error('Default branch not found');
  }
  const apptileState = useSelector(apptileStateSelector);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showPublishModal, setShowPublishModal] = useState(props.fromPaymentConfirmation ?? false);
  const isPaidCustomer = useSelector(selectPaymentStatus);
  const isApptileUser = useSelector(checkApptileEmailSelector);

  const onCancel = () => {
    setShowPublishModal(false);
    setShowPublishButton(false);
  };

  const onPublish = () => {
    props.onPublish();
    setShowPublishModal(false);
    setShowPublishButton(false);
  };

  const onPublishV2 = () => {
    setShowPublishModal(false);
    setShowPublishButton(false);
  };

  const checkOverlappingOtaFunction = () => dispatch(checkOverlappingOtas(new Date()));
  useEffect(() => {
    checkOverlappingOtaFunction();
  }, [apptileState.forkId]);

  return (
    <>
      <View style={styles.container}>
        {props?.hideSave ? null : (
          <Button
            id="saveButton"
            onPress={props.onSave}
            textStyles={styles.saveButtonText}
            containerStyles={styles.saveButton}>
            Save
          </Button>
        )}
        {currentBranchName === defaultBranchName ? (
          <Button
            id="publishButton"
            color="PRIMARY"
            variant="PILL"
            containerStyles={styles.publishButtonTopBar}
            textStyles={styles.publishButtonTextTopBar}
            onPress={() => {
              Analytics.track('editor:publish_publishButtonClicked');
              checkOverlappingOtaFunction();
              return isPaidCustomer || isApptileUser ? setShowPublishModal(true) : navigate('../pricing');
            }}>
            Publish
          </Button>
        ) : (
          <Button
            id="publishButton"
            color="PRIMARY"
            variant="PILL"
            containerStyles={styles.publishButtonTopBar}
            textStyles={styles.publishButtonTextTopBar}
            onPress={() => {
              Analytics.track('editor:publish_publishButtonClicked');
              return isPaidCustomer || isApptileUser ? setShowPublishModal(true) : navigate('../pricing');
            }}>
            Schedule
          </Button>
        )}
      </View>

      {showPublishModal && (
        <PublishModal
          onCancel={onCancel}
          onPublish={onPublish}
          onPublishV2={onPublishV2}
          hasSchedule={currentBranchName !== defaultBranchName}
        />
      )}
    </>
  );
});

export const PublishModal = ({
  onCancel,
  onPublish,
  onPublishV2,
  hasSchedule,
}: {
  onCancel: () => void;
  onPublish: () => void;
  onPublishV2: () => void;
  hasSchedule: boolean;
}) => {
  const apptileState = useSelector(state => state.apptile);
  const navigate = useNavigate();
  const mandatoryFields = useSelector(selectMandatoryCheck());
  const getOverlappingOtas = useSelector(overlappingOtasSelector);

  const [hasSomeBuild, setHasSomeBuild] = useState(false);
  useEffect(() => {
    if (apptileState.appId) {
      // BuildManagerApi.listBuild(apptileState.appId).then(res => {
      //   const data = res.data as {status: 'error' | 'completed'}[];
      //   const _hasSomeBuild = data.some(i => i.status === 'completed');
      //   setHasSomeBuild(_hasSomeBuild);
      // });
      setHasSomeBuild(apptileState.isPublished);

      if (!apptileState.isPublished && !mandatoryFields.check) {
        navigate('../dashboard/publish');
        onPublishV2();
      }
    }
  }, [apptileState]);

  if (!hasSomeBuild && !mandatoryFields.check) return null;

  return (
    <Dialog>
      {!hasSomeBuild && !mandatoryFields.check ? (
        <PublishFlow onCancel={onCancel} />
      ) : (
        <>
          {hasSchedule && !mandatoryFields.check ? (
            <SnapshotSchedulerForm onCancel={onCancel} />
          ) : !mandatoryFields.check && !_.isEmpty(getOverlappingOtas) ? (
            <SnapshotOverlapHandler
              onCancel={onCancel}
              overlappingSnapshot={getOverlappingOtas[0]}
              onPublish={onPublish}
            />
          ) : (
            <>
              {mandatoryFields.check ? (
                <Icon name="alert-circle-outline" size="9xl" color="SECONDARY" />
              ) : (
                <Icon name="alert" size="9xl" color="DISABLED" />
              )}
              <Text style={styles.heading}>
                {mandatoryFields.check ? 'Tile Setup Incomplete' : 'Confirm App Update'}
              </Text>
              {mandatoryFields.check ? (
                <View style={{width: 500, flexDirection: 'row'}}>
                  <View style={{width: '50%'}}>
                    <Image
                      source={{uri: require('../assets/images/platform-left-panel.png')}}
                      resizeMode="contain"
                      style={{height: 250, width: '100%'}}
                    />
                  </View>
                  <View style={{width: '50%', paddingVertical: 11, justifyContent: 'space-between'}}>
                    <Text>
                      Kindly ensure the tile setup is finalized on the pages marked with the Red dot before you publish
                      the app.
                    </Text>
                    <Button containerStyles={{alignSelf: 'flex-end'}} color="CTA" onPress={onCancel}>
                      Continue
                    </Button>
                  </View>
                </View>
              ) : (
                <Text style={styles.subheading}>
                  Your changes will be published immediately to all app users. Please double check before proceeding.
                </Text>
              )}

              {!mandatoryFields.check && (
                <View style={styles.actionArea}>
                  <Button
                    containerStyles={styles.cancelButton}
                    variant="FILLED-PILL"
                    color="SECONDARY"
                    size="LARGE"
                    onPress={onCancel}>
                    {!hasSomeBuild || mandatoryFields.check ? 'Dismiss' : 'Cancel'}
                  </Button>

                  <Button
                    containerStyles={styles.publishButton}
                    variant="FILLED-PILL"
                    color="CTA"
                    size="LARGE"
                    icon="cloud-upload"
                    onPress={onPublish}>
                    Publish App Update
                  </Button>
                </View>
              )}
            </>
          )}
        </>
      )}
    </Dialog>
  );
};

type DialogProps = {};
export const Dialog: React.FC<DialogProps> = props => {
  const {children} = props;
  return (
    <Modal visible transparent>
      <View style={styles.backdrop}>
        <View style={styles.modal}>{children}</View>
      </View>
    </Modal>
  );
};

export default SaveAndPublish;
