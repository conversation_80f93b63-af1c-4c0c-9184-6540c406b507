import React, {useEffect, useState, useCallback} from 'react';
import {Image, Pressable, StyleSheet, View, Text, ScrollView} from 'react-native';
import TextElement from '../components-v2/base/TextElement';
import SaveAndPublish from './SaveAndPublish';
import {
  EDITOR_SELECTED_PAGE_TYPE,
  EDITOR_SELECT_NAV_COMPONENT,
  editorSetActiveAttachmentId,
  editorSetActiveAttachmentKey,
  saveAppState,
  fetchAppBranchesWithScheduledOta,
} from '../actions/editorActions';
import {useCallbackRef, apptileStateSelector, allAvailablePlans} from 'apptile-core';
import AppConfigApi from '../api/AppConfigApi';
import {useAppBridge} from '@shopify/app-bridge-react';
import {Redirect} from '@shopify/app-bridge/actions';
import {batch, useDispatch, useSelector} from 'react-redux';
import {initApptileIsEditable, initApptileIsPreview} from 'apptile-core';
import {selectScreensInNav, defaultBranchNameSelector} from '../selectors/EditorSelectors';
import Button from '../components-v2/base/Button';
import {changeOnboardingMetadata} from '../actions/onboardingActions';
import {getOnboardingMetadataWithKey} from '../selectors/OnboardingSelector';
import {APP_PREVIEW_CLICKED} from '../common/onboardingConstants';
import {EditorRootState} from '../store/EditorRootState';
import {useNavigate, useParams} from '../routing.web';
import {useSearchParams} from 'react-router-dom';
import PopoverComponent from '../components-v2/base/Popover';
import {MaterialCommunityIcons} from 'apptile-core';
import {getNavigationContext} from 'apptile-core';
import {useIsPreview} from 'apptile-core';
import {ScreenConfig} from 'apptile-core';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import Analytics from '@/root/web/lib/segment';
import {IAppBranchesWithScheduledOta, ScheduledOtaStatusEnum} from '../api/ApiTypes';
import theme from '../styles-v2/theme';
import commonStyles from '../styles-v2/commonStyles';
import ScreenEntityPicker from '../components-v2/ScreenEntityPicker';
import _ from 'lodash';
import {languageOptions} from '../common/languageForkUtils';
import {checkApptileEmailSelector, currentPlanFeaturesSelector} from '../selectors/FeatureGatingSelector';
import {useLocation} from 'react-router';
import CompatibilityPopup from './components/CompatibilityPopup';

// Helper function to format date as "Mon, 3rd March @ 3:30 PM"
const formatDate = (dateString?: string) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const day = date.getDate();
  const dayStr =
    day +
    (day % 10 === 1 && day !== 11
      ? 'st'
      : day % 10 === 2 && day !== 12
      ? 'nd'
      : day % 10 === 3 && day !== 13
      ? 'rd'
      : 'th');

  const hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const formattedHours = hours % 12 || 12;
  const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;

  return `${days[date.getDay()]}, ${dayStr} ${months[date.getMonth()]} @ ${formattedHours}:${formattedMinutes} ${ampm}`;
};

const currentBranchTitleMapping = {
  Main: 'Primary App',
};

export const TopBar = () => {
  const dispatch = useDispatch();
  const isPreview = useIsPreview();
  const [showCompatibilityModal, setShowCompatibilityModal] = useState(false);
  const onSave = useCallbackRef(() => {
    dispatch(saveAppState(false, true));
  });
  const params = useParams();
  const {panelName} = params;
  const [searchParams] = useSearchParams();
  const fromPaymentConfirmation = searchParams.get('fromPaymentConfirmation');
  const apptileState = useSelector(apptileStateSelector);

  const location = useLocation();
  const queryParams: URLSearchParams = new URLSearchParams(location.search);
  const isPreviewParam = queryParams.get('preview') === 'true';

  const [hasSeenPreviewPopover, setHasSeenPreviewPopover] = useState(false);

  const [showLanguageNudge, setShowLanguageNudge] = useState(false);

  const navigate = useNavigate();
  const previewClicked = useSelector((state: EditorRootState) =>
    getOnboardingMetadataWithKey(state, APP_PREVIEW_CLICKED),
  );
  const toggleIsPreview = (_isPreview: boolean) => {
    dispatch(initApptileIsPreview(_isPreview));
    dispatch(initApptileIsEditable(!_isPreview));
    if (typeof previewClicked !== undefined && !previewClicked) {
      dispatch(changeOnboardingMetadata({[APP_PREVIEW_CLICKED]: true}));
    }
  };
  const context = getNavigationContext();
  const onPublish = useCallbackRef(() => {
    dispatch(saveAppState(true, true, 'Updated template'));
    setShowLanguageNudge(true);
  });
  const [showPopover, setShowPopover] = useState(false);

  const isApptileEmail = useSelector(checkApptileEmailSelector) ?? false;
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isMultiLanguageDisabled = !currentPlanFeatures.includes(allAvailablePlans.PRO);

  // Fetch branches with scheduled OTA
  const defaultBranchName = useSelector(defaultBranchNameSelector);

  const fetchBranchesWithScheduledOta = useCallback(() => {
    if (apptileState?.appId && apptileState?.forkId) {
      dispatch(fetchAppBranchesWithScheduledOta(apptileState.appId, apptileState.forkId));
    }
  }, [apptileState?.appId, apptileState?.forkId, dispatch]);

  useEffect(() => {
    fetchBranchesWithScheduledOta();
  }, [fetchBranchesWithScheduledOta]);

  // Get branches data from Redux store
  const branchesByNameWithOta = {
    ...useSelector((state: EditorRootState) => state.branches.branchesByNameWithOta || {}),
  };
  // Include default branch in the list (don't remove it)
  const branches: IAppBranchesWithScheduledOta[] = Object.values(branchesByNameWithOta);

  // Define interface for branch with scheduled time
  interface IBranchWithScheduledTime extends IAppBranchesWithScheduledOta {
    scheduledTime?: string | null;
    publishCommitId?: string | null;
  }

  // Categorize branches
  const draftBranches: IBranchWithScheduledTime[] = [];
  const upcomingBranches: IBranchWithScheduledTime[] = [];
  let liveBranch: IBranchWithScheduledTime | null = null;

  // Check if default branch has a publish commit ID
  const defaultBranch = branches.find(branch => branch.branchName === defaultBranchName) as
    | IBranchWithScheduledTime
    | undefined;
  const defaultBranchHasPublishCommit = defaultBranch && defaultBranch.headCommitId;

  branches.forEach(branch => {
    if (branch.scheduledOtas.length === 0) {
      // If it's the default branch with a publish commit, consider it live
      if (branch.branchName === defaultBranchName && defaultBranchHasPublishCommit) {
        liveBranch = branch;
      } else {
        draftBranches.push(branch);
      }
    } else {
      const revertSnapshots = branch.scheduledOtas.filter(scheduledOta => scheduledOta.revertSnapshot);
      const nonRevertSnapshots = branch.scheduledOtas.filter(scheduledOta => !scheduledOta.revertSnapshot);
      const hasPendingRevertSnapshot =
        revertSnapshots.length > 0 &&
        revertSnapshots.find(scheduledOta => scheduledOta.status === ScheduledOtaStatusEnum.PENDING);
      const hasPendingNonRevertSnapshot =
        nonRevertSnapshots.length > 0 &&
        nonRevertSnapshots.find(scheduledOta => scheduledOta.status === ScheduledOtaStatusEnum.PENDING);

      if (hasPendingRevertSnapshot && hasPendingNonRevertSnapshot) {
        // Get the scheduled time for upcoming branches
        const pendingOta = nonRevertSnapshots.find(
          scheduledOta => scheduledOta.status === ScheduledOtaStatusEnum.PENDING,
        );
        branch.scheduledTime = pendingOta?.publishDate || null;
        upcomingBranches.push(branch);
      }
      if (hasPendingRevertSnapshot && !hasPendingNonRevertSnapshot) {
        liveBranch = branch;
      }
    }
  });

  const slideAnimationHeight = useSharedValue(0);
  useEffect(() => {
    slideAnimationHeight.value = withTiming(showPopover ? 198 : 0, {
      duration: 100,
      easing: Easing.ease,
    });
  }, [showPopover, slideAnimationHeight]);

  const fadeEffect = useSharedValue(1);
  useEffect(() => {
    fadeEffect.value = withTiming(showPopover ? 1 : 0, {
      duration: 120,
      easing: Easing.ease,
    });
  }, [showPopover, fadeEffect]);

  const popOverAnimation = useAnimatedStyle(() => ({
    height: slideAnimationHeight.value,
    opacity: fadeEffect.value,
  }));

  const screens: ScreenConfig[] = useSelector(selectScreensInNav);
  const activeNavigation = useSelector(s => (s as any).activeNavigation);
  const activeScreen = screens?.filter((e: ScreenConfig) => e.screen == activeNavigation?.activePageId);

  //For animating topbar
  const topBarHeight = useSharedValue(50);
  useEffect(() => {
    topBarHeight.value = withTiming(isPreview ? 0 : 50, {
      duration: 680,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPreview, topBarHeight]);
  const topBarTopAnimation = useAnimatedStyle(() => ({
    height: topBarHeight.value,
  }));

  const editorPreviewClicked = () => {
    Analytics.track('editor:editor_previewClicked');
    toggleIsPreview(true);
  };
  const isPDP = activeScreen && !_.isEmpty(activeScreen.filter(s => s?.name === 'Product'));
  const isPLP = activeScreen && !_.isEmpty(activeScreen.filter(s => s?.name === 'Collection'));

  const currentBranch = useSelector((state: EditorRootState) => state.branches.branchesByNameWithOta)[
    params.branchName
  ];

  // Get all forks from Redux state
  const allForks = useSelector(
    (state: EditorRootState) => (state.forks?.appForksById as Record<string, {title: string}>) || {},
  );

  // Get the current fork ID
  const currentForkId = useSelector((state: EditorRootState) => state.apptile?.forkId as string);

  // Get the active fork
  const activeFork = currentForkId && allForks ? allForks[currentForkId] : null;

  // Determine the display title for the active fork
  const activeForkLanguage = languageOptions.find(lang => lang.name === activeFork?.title);

  const activeForkTitle = activeFork
    ? activeFork.title === 'Main'
      ? 'En'
      : activeForkLanguage
      ? _.capitalize(activeForkLanguage.code)
      : _.capitalize(activeFork.title)
    : 'En';

  // State for showing/hiding the fork dropdown
  const [showForkDropdown, setShowForkDropdown] = useState(false);
  const [showBranchPopover, setShowBranchPopover] = useState(false);

  // Get platform state for Shopify embedded app detection
  const platformState = useSelector((state: EditorRootState) => state.platform);
  const {isEmbeddedInShopify} = platformState || {};

  // Custom hook for getting app bridge
  const useGetAppBridge = (isEmbeddedInShopify: boolean): any => {
    const voidFn = () => false;
    const getAppBridge = isEmbeddedInShopify ? useAppBridge : voidFn;
    return getAppBridge();
  };

  // Get app bridge for Shopify embedded app navigation
  const appBridge = useGetAppBridge(isEmbeddedInShopify);

  // Helper function for navigation that works both in Shopify embedded app and standalone
  const redirectRemote = useCallback(
    (url: string) => {
      if (appBridge) {
        const redirect = Redirect.create(appBridge);
        redirect.dispatch(Redirect.Action.APP, url);
      } else {
        window.location.href = url;
      }
    },
    [appBridge],
  );

  // Handle fork change - optimized implementation matching LanguageAndRegion.tsx
  const handleForkChange = useCallback(
    async (forkId: string) => {
      if (forkId === currentForkId) {
        // If clicking the current fork, just close the dropdown
        setShowForkDropdown(false);
        return;
      }

      try {
        // Get the branches for the selected fork - passing forkId directly without conversion
        const appId = params.id;
        const {data} = (await AppConfigApi.fetchAppBranches(appId, forkId)) as any;
        const branchName = data.branches?.[0]?.branchName;

        // Direct redirect without additional processing
        redirectRemote(`/dashboard/${params.orgId}/app/${appId}/f/${forkId}/b/${branchName}/app-editor`);

        // Close the dropdown
        setShowForkDropdown(false);
      } catch (error) {
        console.error('Error switching fork:', error);
      }
    },
    [currentForkId, params, redirectRemote],
  );
  useEffect(() => {
    if (location.pathname.includes('/app-editor') && !window.CompressionStream) {
      setShowCompatibilityModal(true);
    }
  }, [location.pathname]);

  return (
    <>
      {showCompatibilityModal && <CompatibilityPopup onClose={() => setShowCompatibilityModal(false)} />}
    <Animated.View style={[styles.wrapper, topBarTopAnimation]}>
      <View style={[styles.leftSectionWrapper]}>
        <View style={styles.sidebarHeader}>
          <Pressable
            style={{flex: 1, justifyContent: 'center'}}
            onPress={() => {
              navigate(`/dashboard/${params.orgId}/app/${params.id}`);
            }}>
            <Image
              style={styles.logo}
              source={require('@/root/web/assets/images/apptile_icon.png')}
              resizeMode="cover"
            />
          </Pressable>
        </View>
        <View style={styles.languagePagesWrapper}>
          {(isApptileEmail || !isMultiLanguageDisabled) && (
            <>
              <View style={styles.forkContainer}>
                {/* Display the active fork title with dropdown using PopoverComponent */}
                <PopoverComponent
                  visible={showForkDropdown}
                  onVisibleChange={setShowForkDropdown}
                  trigger={
                    <Pressable
                      style={{flexDirection: 'row', alignItems: 'center'}}
                      onPress={() => {
                        setShowForkDropdown(!showForkDropdown);
                      }}>
                      <MaterialCommunityIcons name="web" size={16} color={'#000000'} style={{marginRight: 4}} />
                      <TextElement style={styles.forkTitle}>{activeForkTitle}</TextElement>
                      <MaterialCommunityIcons name="chevron-down" size={16} color="#9D9D9D" style={{marginLeft: 8}} />
                    </Pressable>
                  }>
                  {Object.entries(allForks).length && (
                    <Animated.View style={[styles.topbarDropdownPopover]}>
                      <View style={{}}>
                        <View style={styles.topbarDropdownHeader}>
                          <TextElement fontWeight="600" style={styles.topbarDropdownHeaderTitle}>
                            LANGUAGES
                          </TextElement>
                        </View>
                        {Object.entries(allForks).map(([forkId, fork]) => (
                          <Pressable
                            key={forkId}
                            style={[
                              styles.topbarDropdownOptions,
                              currentForkId === forkId ? {backgroundColor: '#E6F0FF'} : {},
                            ]}
                            onPress={() => {
                              handleForkChange(forkId);
                              setShowForkDropdown(false);
                            }}>
                            <TextElement
                              style={[
                                styles.topbarDropdownOptionText,
                                currentForkId === forkId ? {color: theme.PRIMARY_COLOR} : {},
                              ]}>
                              {fork.title === 'Main'
                                ? 'Default (en)'
                                : (() => {
                                    const forkLanguage = languageOptions.find(lang => lang.name === fork.title);
                                    return forkLanguage
                                      ? `${_.capitalize(fork.title)} (${_.toLower(forkLanguage.code)})`
                                      : `${_.capitalize(fork.title)}`;
                                  })()}
                            </TextElement>
                            {currentForkId === forkId && (
                              <MaterialCommunityIcons name="check" size={16} color={theme.PRIMARY_COLOR} />
                            )}
                          </Pressable>
                        ))}
                      </View>
                    </Animated.View>
                  )}
                </PopoverComponent>
                {Object.entries(allForks).length > 1 && (
                  <PopoverComponent
                    visible={showLanguageNudge}
                    trigger={<View style={{width: 1, height: '100%', backgroundColor: '#000'}} />}>
                    <View style={styles.forkReminderPopover}>
                      <TextElement
                        style={{
                          color: theme.PRIMARY_COLOR,
                          fontSize: 13,
                          lineHeight: 15,
                          fontWeight: '500',
                          marginBottom: 8,
                        }}>
                        Reminder!
                      </TextElement>
                      <TextElement style={{color: '#535353', fontSize: 12, lineHeight: 18, fontWeight: '400'}}>
                        You've published a language! Don't forget to publish other languages to keep your app
                        consistent.
                      </TextElement>
                      <Pressable
                        style={{position: 'absolute', top: 8, right: 8}}
                        onPress={() => setShowLanguageNudge(false)}>
                        <MaterialCommunityIcons name="close" size={14} />
                      </Pressable>
                    </View>
                  </PopoverComponent>
                )}
              </View>

              <View style={styles.separatorLine} />
            </>
          )}

          <View style={[styles.appContainerHeader]}>
            {activeScreen.length > 0 ? (
              <PopoverComponent
                visible={showPopover}
                onVisibleChange={setShowPopover}
                trigger={
                  <Pressable
                    style={[styles.popoverPressableStyles]}
                    onPress={() => {
                      setShowPopover(!showPopover);
                    }}>
                    <MaterialCommunityIcons name="file-outline" size={16} color={'#000000'} style={{marginRight: 4}} />
                    <Text style={[commonStyles.baseText, styles.appContainerHeaderText]}>
                      {_.capitalize(activeScreen[0].title || activeScreen[0].name || 'Loading')}
                    </Text>
                    <MaterialCommunityIcons name="chevron-down" size={16} color="#9D9D9D" style={{marginLeft: 8}} />
                  </Pressable>
                }>
                {screens && (
                  <Animated.View
                    style={[styles.topbarDropdownPopover, {minWidth: 200, marginLeft: 0}, popOverAnimation]}>
                    {/* style={[styles.appContainerHeader, styles.appContainerHeaderPopover, popOverAnimation]}> */}
                    {/* <View style={{flex: 1, overflow: 'scroll', padding: 12}}> */}
                    <View style={styles.topbarDropdownHeader}>
                      <TextElement fontWeight="600" style={styles.topbarDropdownHeaderTitle}>
                        PAGES
                      </TextElement>
                    </View>
                    <div className="pagesScrollWrapper" style={{height: '100%', overflow: 'scroll'}}>
                      {screens?.map((s: ScreenConfig, index: number) => (
                        <View key={s.name + '_' + index}>
                          <Pressable
                            // style={styles.appPopoverScreenText}
                            style={[
                              styles.topbarDropdownOptions,
                              activeScreen.length && activeScreen[0].name == s.name ? {backgroundColor: '#E6F0FF'} : {},
                            ]}
                            onPress={() => {
                              batch(() => {
                                dispatch(editorSetActiveAttachmentId(''));
                                dispatch(editorSetActiveAttachmentKey(''));
                                dispatch({type: EDITOR_SELECT_NAV_COMPONENT, payload: s.name});
                                dispatch({type: EDITOR_SELECTED_PAGE_TYPE, payload: s.type});
                              });
                              context.navigate(s.name);
                              setShowPopover(false);
                            }}>
                            {/* <View style={{width: 20}}>
                              <MaterialCommunityIcons
                                name={activeScreen.length && activeScreen[0].name == s.name ? 'check' : 'blank'}
                                size={16}
                                color={theme.PRIMARY_COLOR}
                              />
                            </View> */}
                            <TextElement
                              style={[
                                commonStyles.baseText,
                                styles.topbarDropdownOptionText,
                                activeScreen.length && activeScreen[0].name == s.name
                                  ? {color: theme.PRIMARY_COLOR}
                                  : {},
                              ]}>
                              {_.capitalize(s.title || s.name)}
                            </TextElement>
                            {activeScreen.length && activeScreen[0].name == s.name && (
                              <MaterialCommunityIcons name="check" size={16} color={theme.PRIMARY_COLOR} />
                            )}
                            {/* <Text style={[commonStyles.baseText]}>{_.capitalize(s.title || s.name)}</Text> */}
                          </Pressable>
                        </View>
                      ))}
                    </div>
                  </Animated.View>
                )}
              </PopoverComponent>
            ) : (
              <Text style={[styles.appContainerHeaderText]}>Loading</Text>
            )}
          </View>
        </View>

        {/* // TODO: fix this styling */}
        {(isPDP || isPLP) && (
          <View style={[styles.absolutePicker]}>
            <View style={styles.separatorLine} />
            <View style={[{maxWidth: 175}]}>
              <ScreenEntityPicker isV2={true} />
            </View>
          </View>
        )}
      </View>

      <View
        style={{
          position: 'absolute',
          left: 0,
          marginLeft: 'auto',
          marginRight: 'auto',
          gap: 10,
          width: '100%',
          alignItems: 'center',
          paddingRight: 90,
          zIndex: -1,
        }}>
        <View
          style={{
            flexGrow: 0,
            flexShrink: 1,
            flexBasis: 'auto',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'row',
          }}>
          <PopoverComponent
            visible={showBranchPopover}
            onVisibleChange={setShowBranchPopover}
            trigger={
              <Pressable
                style={styles.currentBranchWrapper}
                onPress={() => {
                  setShowBranchPopover(!showBranchPopover);
                }}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  {currentBranch && (
                    <View style={styles.branchTagWrapper}>
                      <MaterialCommunityIcons
                        name="circle"
                        size={12}
                        color={
                          liveBranch && currentBranch.branchName === liveBranch.branchName
                            ? '#3BA720'
                            : upcomingBranches.some(branch => branch.branchName === currentBranch.branchName)
                            ? '#EAA92A'
                            : '#9D9D9D'
                        }
                      />
                      <TextElement style={styles.branchTagText}>
                        {liveBranch && currentBranch.branchName === liveBranch.branchName
                          ? 'Live'
                          : upcomingBranches.some(branch => branch.branchName === currentBranch.branchName)
                          ? 'Scheduled'
                          : 'Draft'}
                      </TextElement>
                    </View>
                  )}
                  <TextElement style={[commonStyles.baseText, styles.currentItemText]}>
                    {currentBranch?.title
                      ? currentBranchTitleMapping[currentBranch.title as keyof typeof currentBranchTitleMapping] ??
                        currentBranch.title
                      : ''}
                  </TextElement>
                  <MaterialCommunityIcons name="chevron-down" size={16} color="#9D9D9D" style={{}} />
                </View>
              </Pressable>
            }>
            {/* <Animated.View style={[styles.branchPopover]}> */}
            <Animated.View style={[styles.topbarDropdownPopover, {width: 312, marginLeft: 0, maxHeight: 266}]}>
              {/* style={[styles.appContainerHeader, styles.appContainerHeaderPopover, popOverAnimation]}> */}
              {/* <View style={{flex: 1, overflow: 'scroll', padding: 12}}> */}
              <View style={styles.topbarDropdownHeader}>
                <TextElement fontWeight="600" style={styles.topbarDropdownHeaderTitle}>
                  VERSIONS
                </TextElement>
              </View>
              <div
                className="pagesScrollWrapper"
                style={{height: '100%', overflow: 'scroll', padding: 12, paddingBottom: 0}}>
                {/* Branch selection options */}

                {/* Live Branch */}
                {liveBranch && (
                  <Pressable
                    key={liveBranch.id}
                    style={[
                      styles.branchItem,
                      currentBranch?.branchName === liveBranch.branchName
                        ? {borderColor: theme.PRIMARY_COLOR, borderWidth: 1}
                        : {},
                    ]}
                    onPress={() => {
                      if (params.orgId && params.id && params.forkId && liveBranch) {
                        window.location.href = `/dashboard/${params.orgId}/app/${params.id}/f/${params.forkId}/b/${liveBranch.branchName}/app-editor`;
                      }
                      setShowBranchPopover(false);
                    }}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <View style={styles.branchTagWrapper}>
                        <MaterialCommunityIcons name="circle" size={12} color="#3BA720" />
                        <TextElement style={styles.branchTagText}>Live</TextElement>
                      </View>
                      <TextElement style={styles.branchDropdownText}>
                        {liveBranch && liveBranch.branchName === defaultBranchName
                          ? 'Primary App'
                          : liveBranch?.title || liveBranch?.branchName || ''}
                      </TextElement>
                    </View>
                    {liveBranch?.updatedAt && (
                      <View style={styles.branchDropdownTime}>
                        <TextElement style={styles.branchDropdownTimeText}>
                          {/* Last saved {new Date().toLocaleString()} */}
                          Last saved {formatDate(liveBranch?.updatedAt)}
                        </TextElement>
                      </View>
                    )}
                  </Pressable>
                )}

                {/* Upcoming Branches */}
                {upcomingBranches.length > 0 &&
                  upcomingBranches.map((branch: IBranchWithScheduledTime) => (
                    <Pressable
                      key={branch.id}
                      style={[
                        styles.branchItem,
                        currentBranch?.branchName === branch.branchName && {
                          borderColor: theme.PRIMARY_COLOR,
                          borderWidth: 1,
                        },
                      ]}
                      onPress={() => {
                        window.location.href = `/dashboard/${params.orgId}/app/${params.id}/f/${params.forkId}/b/${branch.branchName}/app-editor`;
                        setShowBranchPopover(false);
                      }}>
                      <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        <View style={styles.branchTagWrapper}>
                          <MaterialCommunityIcons name="circle" size={12} color="#EAA92A" />
                          <TextElement style={styles.branchTagText}>Scheduled</TextElement>
                        </View>
                        <TextElement style={styles.branchDropdownText}>
                          {branch.branchName === defaultBranchName ? 'Primary App' : branch.title || branch.branchName}
                        </TextElement>
                      </View>
                      {branch.scheduledOtas.find(ota => !ota.revertSnapshot && ota.status === 'PENDING')
                        ?.publishDate && (
                        <View style={styles.branchDropdownTime}>
                          <TextElement style={styles.branchDropdownTimeText}>
                            Scheduled for{' '}
                            {formatDate(
                              branch.scheduledOtas.find(ota => !ota.revertSnapshot && ota.status === 'PENDING')
                                ?.publishDate,
                            )}
                          </TextElement>
                        </View>
                      )}
                    </Pressable>
                  ))}

                {/* Draft Branches */}
                {draftBranches.length > 0
                  ? draftBranches.map((branch: IBranchWithScheduledTime) => (
                      <Pressable
                        key={branch.id}
                        style={[
                          styles.branchItem,
                          currentBranch?.branchName === branch.branchName
                            ? {borderColor: theme.PRIMARY_COLOR, borderWidth: 1}
                            : {},
                        ]}
                        onPress={() => {
                          if (params.orgId && params.id && params.forkId && branch.branchName) {
                            window.location.href = `/dashboard/${params.orgId}/app/${params.id}/f/${params.forkId}/b/${branch.branchName}/app-editor`;
                            setShowBranchPopover(false);
                          }
                        }}>
                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                          <View style={styles.branchTagWrapper}>
                            <MaterialCommunityIcons name="circle" size={12} color="#9D9D9D" />
                            <TextElement style={styles.branchTagText}>Draft</TextElement>
                          </View>
                          <TextElement style={styles.branchDropdownText}>
                            {branch.branchName === defaultBranchName
                              ? 'Primary App'
                              : branch.title || branch.branchName}
                          </TextElement>
                        </View>
                        <View style={styles.branchDropdownTime}>
                          <TextElement style={styles.branchDropdownTimeText}>
                            Last saved {formatDate(branch.updatedAt)}
                          </TextElement>
                        </View>
                      </Pressable>
                    ))
                  : null}

                {!liveBranch && upcomingBranches.length === 0 && draftBranches.length === 0 && (
                  <View style={{padding: 12, alignItems: 'center'}}>
                    <TextElement>No versions available</TextElement>
                  </View>
                )}
              </div>
            </Animated.View>
          </PopoverComponent>

          <View style={styles.separatorLine} />

          <Button
            icon="play"
            color="TILE"
            size="LARGE"
            containerStyles={styles.previewButtonWrapper}
            textStyles={styles.previewButtonText}
            onPress={editorPreviewClicked}>
            Preview
          </Button>
        </View>
      </View>
      <SaveAndPublish fromPaymentConfirmation={fromPaymentConfirmation} onSave={onSave} onPublish={onPublish} />
      <View style={{width: 2, height: 2, backgroundColor: '#00000000', position: 'absolute', right: 0, top: 0}}>
        <PopoverComponent
          trigger={<View style={{width: 2, height: 2, backgroundColor: '#00000000'}} />}
          positions={['bottom']}
          containerStyle={{zIndex: 10}}
          disableClickOutside={true}
          visible={isPreview && isPreviewParam && !hasSeenPreviewPopover}>
          <View
            style={{
              width: 336,
              padding: 24,
              backgroundColor: '#FFFFFF',
              display: 'flex',
              flexDirection: 'column',
              gap: 13,
              marginTop: 0,
              marginLeft: 0,
              borderRadius: 8,
              boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.15)',
              zIndex: 10,
              position: 'absolute',
              right: 20,
              top: 500,
            }}>
            <Pressable
              onPress={() => setHasSeenPreviewPopover(true)}
              style={{position: 'absolute', right: 14, top: 14}}>
              <MaterialCommunityIcons name="close" size={14} color="#000000" />
            </Pressable>
            <TextElement style={{fontSize: 16, lineHeight: 20, fontWeight: '600', color: '#000000'}}>
              Preview your app!
            </TextElement>
            <TextElement style={{fontSize: 14, lineHeight: 18, fontWeight: '400', color: '#000000'}}>
              Everything looks good? You can edit translations before publishing.
            </TextElement>
            <View style={{display: 'flex', flexDirection: 'row', gap: 13}}>
              <SaveAndPublish
                hideSave={true}
                fromPaymentConfirmation={fromPaymentConfirmation}
                onSave={onSave}
                onPublish={onPublish}
              />
              <Button
                onPress={() => {
                  toggleIsPreview(false);
                  setHasSeenPreviewPopover(true);
                }}
                containerStyles={{borderWidth: 0, paddingHorizontal: 20}}
                textStyles={{fontSize: 13, lineHeight: 16, fontWeight: '500'}}
                color="SECONDARY">
                Edit Translations
              </Button>
            </View>
          </View>
        </PopoverComponent>
      </View>
      <View
        style={{width: '100%', borderBottomWidth: 1, borderColor: '#E5E5E5', position: 'absolute', bottom: 0, left: 80}}
      />
    </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  absolutePicker: {
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
    left: '100%',
  },
  languagePagesWrapper: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    position: 'relative',
    zIndex: 2,
  },
  forkContainer: {
    paddingLeft: 12,
    position: 'relative',
    zIndex: 2,
  },
  separatorLine: {
    width: 1,
    height: 18,
    backgroundColor: '#E5E5E5',
    marginHorizontal: 12,
  },
  dropdownContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    minWidth: 150,
    marginTop: 4,
    padding: 0,
  },
  forkReminderPopover: {
    padding: 16,
    width: 248,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 1,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.10)',
    opacity: 1,
    marginTop: 10,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    marginLeft: 100,
  },
  topbarDropdownPopover: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    minWidth: 160,
    overflow: 'hidden',
    zIndex: 1,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.10)',
    opacity: 1,
    marginTop: 10,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    marginLeft: 80,
  },
  branchPopover: {
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    minWidth: 250,
    maxHeight: 400,
    overflow: 'hidden',
    zIndex: 1,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.25)',
    opacity: 1,
    marginTop: 10,
  },
  branchItem: {
    padding: '12px',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    marginBottom: 12,
  },
  branchTagWrapper: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
    height: 24,
    gap: 4,
    backgroundColor: '#F3F3F3',
    borderRadius: 12,
  },
  branchTagText: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: '500',
    color: '#000000',
  },
  branchDropdownText: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '500',
    color: '#000000',
    marginLeft: 8,
  },
  branchDropdownTime: {
    marginTop: 8,
  },
  branchDropdownTimeText: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '400',
    color: '#535353',
    textWrap: 'wrap',
  },
  branchSeparator: {
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 4,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  topbarDropdownHeader: {
    height: 32,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    display: 'flex',
    justifyContent: 'center',
  },
  topbarDropdownHeaderTitle: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '600',
    color: '#000000',
  },
  topbarDropdownOptions: {
    height: 36,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  topbarDropdownOptionText: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '400',
    color: '#000000',
  },
  forkTitle: {
    fontSize: 13,
    lineHeight: 16,
    fontWeight: '500',
    color: '#000000',
  },
  wrapper: {
    height: 50,
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    overflow: 'hidden',
  },
  leftSectionWrapper: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
  },
  previewButtonWrapper: {
    borderRadius: 40,
    borderWidth: 0,
    backgroundColor: '#F3F3F3',
    height: 30,
    paddingHorizontal: 16,
    paddingLeft: 12,
  },
  previewButtonText: {
    fontSize: 13,
    lineHeight: 16,
    fontWeight: '500',
    color: '#000000',
  },
  logo: {
    width: 40,
    height: 40,
  },
  pageText: {
    fontWeight: '500',
    color: '#000',
    fontSize: 14,
  },
  sidebarHeader: {
    paddingRight: 23,
    overflow: 'hidden',
    borderRightWidth: 1,
    borderColor: '#E5E5E5',
    height: '100%',
  },
  appPopoverScreenText: {
    flexDirection: 'row',
    paddingVertical: 10,
    textAlign: 'left',
  },
  popoverPressableStyles: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appContainerHeaderText: {
    whiteSpace: 'nowrap',
    wordBreak: 'break-all',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    color: theme.SECONDARY_COLOR,
    fontSize: 13,
    fontWeight: '500',
    lineHeight: 16,
    maxWidth: 80,
    // paddingVertical: 8,
  },
  appContainerHeaderPreText: {
    whiteSpace: 'nowrap',
    color: theme.SECONDARY_COLOR,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 14,
    paddingVertical: 8,
  },
  appContainerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    // paddingLeft: 12,
    // paddingRight: 4,
    // height: 38,
    zIndex: 2,
    // width: 131,
  },
  appContainerHeaderPopover: {
    zIndex: 1,
    width: 200,
    flexDirection: 'column',
    alignItems: 'flex-start',
    height: 175,
    overflow: 'hidden',
    padding: 0,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.25)',
    opacity: 1,
    marginLeft: 45,
    marginTop: 10,
  },
  flexRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
  currentBranchWrapper: {
    // width: 119,
    // height: 34,
    overflow: 'hidden',
    justifyContent: 'center',
    // paddingLeft: 16,
    // alignItems: 'center',
  },
  currentItemText: {
    whiteSpace: 'nowrap',
    wordBreak: 'break-all',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    justifyContent: 'center',
    fontWeight: '500',
    fontSize: 13,
    lineHeight: 16,
    color: '#000',
    marginHorizontal: 8,
  },
});

const webStyles = `
.pagesScrollWrapper::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
`;

document.head.insertAdjacentHTML('beforeend', `<style>${webStyles}</style>`);
