import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Image, Pressable, ScrollView, StyleSheet, Text, View} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {useParams} from 'react-router';

import theme from '../styles-v2/theme';
import {NavigatorConfig, PluginNamespaceImpl} from 'apptile-core';
import {
  selectActiveScreen,
  selectSelectedPluginConfig,
  selectSelectedNavComponent,
  selectSelectedPluginPageId,
  selectNavComponentSelector,
  selectSettingtSelector,
} from '../selectors/EditorSelectors';
import {
  DevicePlatformType,
  pluginDelete,
  saveAppState,
  sendTileAnalytics,
  setOpenDevicePreviewQRModal,
  setQRModalPlatformType,
} from '../actions/editorActions';
import {selectModuleByUUID} from 'apptile-core';
import {useCallbackRef} from 'apptile-core';
import generatePreviewURL from '../common/generatePreviewURL';
import TileEditor from '../components-v2/TileEditor';
import MandatoryTileEditor from '../components-v2/MandatoryTileEditor';
import Button from '../components-v2/base/Button';
import commonStyles from '../styles-v2/commonStyles';
import {initApptileIsEditable, initApptileIsPreview} from 'apptile-core';
import {useIsPreview} from 'apptile-core';
import SaveAndPublish from './SaveAndPublish';
import {selectModuleMandatoryCheck} from '../selectors/EditorModuleSelectors';
import TilesApi from '../api/TilesApi';
import {Feather} from 'apptile-core';
import {EditorRootState} from '../store/EditorRootState';
import ModalComponent from '../components-v2/base/Modal';
import PreviewQRFlow from '../views/previewQRFlow';
import {currentPlanFeaturesSelector} from '../selectors/FeatureGatingSelector';
import {Plan, allAvailablePlans} from 'apptile-core';
import EditorSectionHeader from '../components/controls/EditorSectionHeader';
import AspectRatioControl from '../components/controls/AspectRatioControl';
import {activeNavigationSelector} from 'apptile-core';
import {
  PDP_BORDER_RADIUS,
  PDP_HIDE_WISHLIST,
  PDP_IMAGE_ASPECT_RATIO_KEY,
  PDP_IMAGE_RESIZE_KEY,
  PDP_SETTINGS_KEY,
  PLP_CARD_HEIGHT_KEY,
  PLP_IMAGE_ASPECT_RATIO_KEY,
  PLP_NUM_COLS_KEY,
  PLP_SETTINGS_KEY,
} from 'apptile-core';
import {selectAppSettingsForKey} from 'apptile-core';
import {updateSettingsValue} from 'apptile-core';
import RangeSliderControl from '../components/controls/RangeSliderControl';
import RadioGroupControl from '../components/controls/RadioGroupControl';
import CheckboxControl from '../components/controls/CheckboxControl';
import {changeOnboardingMetadata} from '../actions/onboardingActions';
import {APP_PREVIEW_CLICKED} from '../common/onboardingConstants';
import {getOnboardingMetadataWithKey} from '../selectors/OnboardingSelector';
import Tooltip from '../components-v2/base/SimpleTooltip';
import {ApptileWebIcon} from '../icons/ApptileWebIcon';
// import Tooltip from '../components-v2/base/Tooltip/Index';
import BottomNavigationEditor from '../components-v2/BottomNavigationEditor';
import {AI_TILE_PAGE_ID} from '../common/tileConstants';
import TopNavigationEditor from '../components-v2/TopNavigationEditor';
import AlertandToasts from '../components-v2/AlertandToasts';
import BrandSetting from '../../web/views/settings/brand';
import CustomAppSettings from '../components-v2/customAppSetting';
import {navConfigSelector} from '../../../apptile-core/selectors/AppConfigSelector';

const RightSidebar = () => {
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));

  const pluginId = moduleConfig?.get('id');
  const pageId = useSelector(state => selectSelectedPluginPageId(state));
  const activeNavigation = useSelector(state => activeNavigationSelector(state));
  const isSuperPageCompatible =
    activeNavigation.activePageId == 'Product' || activeNavigation.activePageId == 'Collection';
  const activePage = useSelector(state => selectActiveScreen(state))[0];
  const isSuperPage = isSuperPageCompatible && activePage.nativeTemplate;
  const navConfig = useSelector(state => selectNavComponentSelector(state));
  const selectedNavConfig: NavigatorConfig = useSelector(state => selectSelectedNavComponent(state));
  const selectedSettings: any = useSelector(state => selectSettingtSelector(state));
  const NavSelector: NavigatorConfig = useSelector(navConfigSelector)?.get('rootNavigator');
  const mainTab = useMemo(() => {
    if (!NavSelector) return null;
    const navConfigJS = NavSelector.toJS();
    return Object.values(navConfigJS.screens).find(
      screen => screen?.type === 'navigator' && screen?.navigatorType === 'tab',
    );
  }, [NavSelector]);
  const previewClicked = useSelector((state: EditorRootState) =>
    getOnboardingMetadataWithKey(state, APP_PREVIEW_CLICKED),
  );

  const dispatch = useDispatch();

  const isPreview = useIsPreview();
  const toggleIsPreview = (_isPreview: boolean) => {
    dispatch(initApptileIsPreview(_isPreview));
    dispatch(initApptileIsEditable(!_isPreview));
    if (typeof previewClicked !== undefined && !previewClicked) {
      dispatch(changeOnboardingMetadata({[APP_PREVIEW_CLICKED]: true}));
    }
  };

  const params = useParams();
  const {panelName} = params;

  const apptileState = useSelector(state => state.apptile);
  const orgs = useSelector(state => state.orgs);
  const [previewURL, setPreviewURL] = useState<string>();
  useEffect(() => {
    const appId = apptileState?.appId as string;
    const orgId = apptileState?.orgId as string;
    const forkId = apptileState?.forkId;
    const branchName = apptileState?.appBranch;
    const appName = orgs?.appsById[params.id]?.name;
    const orgName = orgs?.orgsById[params.orgId]?.name;
    setPreviewURL(generatePreviewURL(orgId, appId, forkId, branchName, appName, orgName));
  }, [apptileState, orgs, params.id, params.orgId]);

  const sidebarRightTranslateX = useSharedValue(0);
  useEffect(() => {
    sidebarRightTranslateX.value = withTiming(!isPreview ? 0 : 370, {
      duration: 680,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPreview, sidebarRightTranslateX]);
  const sidebarRightAnimation = useAnimatedStyle(() => ({
    transform: [{translateX: sidebarRightTranslateX.value}],
  }));
  const sidebarRightTranslateXRevert = useSharedValue(370);
  useEffect(() => {
    sidebarRightTranslateXRevert.value = withTiming(!isPreview ? 370 : 0, {
      duration: 680,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPreview, sidebarRightTranslateXRevert]);
  const sidebarRightAnimationRevert = useAnimatedStyle(() => ({
    transform: [{translateX: sidebarRightTranslateXRevert.value}],
  }));

  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  const moduleRecord = useSelector((state: any) => selectModuleByUUID(state, moduleUUID));
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const currentPageId = useSelector((state: EditorRootState) => state.activeNavigation?.activePageId);
  const isDeletable =
    (currentPlanFeatures.includes(allAvailablePlans[moduleRecord?.isDeletable as Plan]) &&
      currentPageId != AI_TILE_PAGE_ID) ??
    true;

  const editors = moduleRecord?.get('editors')?.toKeyedSeq().toList().toJS();
  const childNamespace = moduleInstanceConfig?.get('childNamespace');
  const moduleNamespace = new PluginNamespaceImpl(
    (moduleConfig?.namespace?.getNamespace() ?? []).concat([childNamespace]),
    moduleConfig?.id,
  );
  const mandatoryFieldsEmpty = useSelector(selectModuleMandatoryCheck(pageId, pluginId));

  const {openModal, platformType} = useSelector((state: EditorRootState) => state.platform.devicePreviewQRModal);
  const onDevicePreviewVisibilityChange = (value: boolean) => {
    if (!value) dispatch(setOpenDevicePreviewQRModal(false));
  };

  const setOpenQRModal = (platform: DevicePlatformType) => {
    dispatch(setOpenDevicePreviewQRModal(true));
    dispatch(setQRModalPlatformType(platform));
  };
  const onDelete = useCallbackRef(() => {
    dispatch(pluginDelete(pluginId, pageId));
    dispatch(sendTileAnalytics(moduleUUID ?? '', 'editor:design_tileDeleted', {}));
  });

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const plpSettings: SettingsConfig = useSelector(settingsSelector(PLP_SETTINGS_KEY));
  const plpImageAspectRatio: string | null = plpSettings.getSettingValue(PLP_IMAGE_ASPECT_RATIO_KEY);
  const plpCardHeight: string | null = plpSettings.getSettingValue(PLP_CARD_HEIGHT_KEY);
  const plpNoOfCols: string | null = plpSettings.getSettingValue(PLP_NUM_COLS_KEY);
  const pdpSettings: SettingsConfig = useSelector(settingsSelector(PDP_SETTINGS_KEY));
  const pdpImageAspectRatio: string | null = pdpSettings.getSettingValue(PDP_IMAGE_ASPECT_RATIO_KEY);
  const pdpImageResize: string | null = pdpSettings.getSettingValue(PDP_IMAGE_RESIZE_KEY);
  const pdpHideWishlist: boolean = pdpSettings.getSettingValue(PDP_HIDE_WISHLIST) ?? false;
  const pdpBorderRadius: string | null = pdpSettings.getSettingValue(PDP_BORDER_RADIUS);
  const [plpImageAspect, setPLPImageAspect] = useState(plpImageAspectRatio);
  const [plpCardHeightValue, setPLPCardHeightValue] = useState(plpCardHeight);
  const [plpNoOfCol, setPLPNoOfCol] = useState(plpNoOfCols);
  const [pdpImageAspect, setPDPImageAspect] = useState(pdpImageAspectRatio);
  const [pdpImageResizeMode, setPDPImageResize] = useState(pdpImageResize);
  const [pdpHideWishlistIcon, setPDPHideWishlist] = useState(pdpHideWishlist);
  const [pdpBorderRadiusValue, setPDPBorderRadius] = useState(pdpBorderRadius);

  const onPLPAspectUpdate = useCallback(
    value => {
      setPLPImageAspect(`${!Number(value) ? '' : Number(value)}`);
      dispatch(
        updateSettingsValue(PLP_SETTINGS_KEY, PLP_IMAGE_ASPECT_RATIO_KEY, `${!Number(value) ? '' : Number(value)}`),
      );
    },
    [dispatch],
  );
  const onPLPHeightUpdate = useCallback(
    value => {
      setPLPCardHeightValue(`${!Number(value) ? '' : Number(value)}`);
      dispatch(updateSettingsValue(PLP_SETTINGS_KEY, PLP_CARD_HEIGHT_KEY, `${!Number(value) ? '' : Number(value)}`));
    },
    [dispatch, setPLPCardHeightValue],
  );
  const onPLPColsUpdate = useCallback(
    value => {
      setPLPNoOfCol(`${!Number(value) ? '' : Number(value)}`);
      dispatch(updateSettingsValue(PLP_SETTINGS_KEY, PLP_NUM_COLS_KEY, `${!Number(value) ? '' : Number(value)}`));
    },
    [dispatch],
  );
  const onPDPAspectUpdate = useCallback(
    value => {
      setPDPImageAspect(`${!Number(value) ? '' : Number(value)}`);
      dispatch(
        updateSettingsValue(PDP_SETTINGS_KEY, PDP_IMAGE_ASPECT_RATIO_KEY, `${!Number(value) ? '' : Number(value)}`),
      );
    },
    [dispatch],
  );
  const onPDPResizeUpdate = useCallback(
    value => {
      setPDPImageResize(value);
      dispatch(updateSettingsValue(PDP_SETTINGS_KEY, PDP_IMAGE_RESIZE_KEY, value));
    },
    [dispatch],
  );
  const onPDPHideWishlistUpdate = useCallback(
    value => {
      setPDPHideWishlist(value);
      dispatch(updateSettingsValue(PDP_SETTINGS_KEY, PDP_HIDE_WISHLIST, value));
    },
    [dispatch],
  );
  const onPDPBorderRadiusUpdate = useCallback(
    value => {
      setPDPBorderRadius(value);
      dispatch(updateSettingsValue(PDP_SETTINGS_KEY, PDP_BORDER_RADIUS, value));
    },
    [dispatch],
  );
  return panelName == 'AI-Tile-Creation' && !moduleUUID ? (
    <></>
  ) : (
    <View style={[styles.rowContainer, commonStyles.rightSideBar]}>
      <Animated.View
        style={[StyleSheet.absoluteFillObject, styles.sidebarRight, styles.QRContainer, sidebarRightAnimationRevert]}>
        <Button
          containerStyles={{backgroundColor: '#FFF', borderColor: 'transparent'}}
          variant="PILL"
          color="SECONDARY"
          size="MEDIUM"
          icon="close"
          onPress={() => toggleIsPreview(false)}>
          Exit
        </Button>

        <>
          <Text style={styles.QRCaption}>To preview the app on your own phone, choose your device type:</Text>
          <View style={styles.platformIconsContainer}>
            <Pressable onPress={() => setOpenQRModal('IOS')}>
              <View style={styles.platformIconWrapper}>
                <Image style={styles.platformIcon} source={require('@/root/web/assets/images/apple-icon.png')} />
              </View>
            </Pressable>
            <Pressable onPress={() => setOpenQRModal('ANDROID')}>
              <View style={styles.platformIconWrapper}>
                <Image style={styles.platformIcon} source={require('@/root/web/assets/images/android-icon.png')} />
              </View>
            </Pressable>
          </View>
          {openModal && previewURL && (
            <ModalComponent
              onVisibleChange={onDevicePreviewVisibilityChange}
              visible={openModal}
              content={<PreviewQRFlow platformType={platformType} previewURL={previewURL} />}
            />
          )}
        </>
      </Animated.View>

      <Animated.View
        style={[
          StyleSheet.absoluteFillObject,
          styles.sidebarRight,
          styles.propertyEditorContainer,
          sidebarRightAnimation,
        ]}>
        {/* <Tooltip
          visible={mandatoryFields.check}
          tooltip={
            <View style={[styles.sidebarHeader, styles.tooltip, styles.propertyEditorContainer]}>
              <Text style={[commonStyles.baseText, commonStyles.errorText]}>
                Please Fill Mandatory Fields in Following Tile
              </Text>
              <Text style={commonStyles.baseText}>Page Name - {mandatoryFields.page}</Text>
              <Text style={commonStyles.baseText}>Tile Name - {mandatoryFields.plugin}</Text>
            </View>
          }> */}
        {/* </Tooltip> */}
        {isSuperPage && activeNavigation.activePageId === 'Collection' && (
          <View style={[styles.contentContainer, pluginId && isDeletable ? {} : {paddingBottom: 0}]}>
            <EditorSectionHeader label={'PLP Configuration'} name={'PLP'} />
            <View style={{flexBasis: 'auto'}}>
              <AspectRatioControl
                value={plpImageAspect ?? ''}
                onChange={onPLPAspectUpdate}
                defaultValue={''}
                label={'Image Aspect Ratio'}
              />
            </View>
            <View style={{flexBasis: 'auto'}}>
              <RangeSliderControl
                value={plpCardHeightValue ?? ''}
                onChange={onPLPHeightUpdate}
                defaultValue={''}
                label={'Card Height'}
                minRange="280"
                maxRange="600"
                steps="10"
              />
            </View>
            <View>
              <RadioGroupControl
                value={plpNoOfCol ?? ''}
                onChange={onPLPColsUpdate}
                defaultValue={''}
                options={[
                  {value: '1', text: '1'},
                  {value: '2', text: '2'},
                ]}
                disableBinding={true}
                label={'No Of Columns'}
              />
            </View>
          </View>
        )}
        {isSuperPage && activeNavigation.activePageId === 'Product' && (
          <View style={[styles.contentContainer, pluginId && isDeletable ? {} : {paddingBottom: 0}]}>
            <EditorSectionHeader label={'PDP Configuration'} name={'PDP'} />
            <View>
              <AspectRatioControl
                value={pdpImageAspect ?? ''}
                onChange={onPDPAspectUpdate}
                defaultValue={''}
                label={'Image Aspect Ratio'}
              />
              <RadioGroupControl
                value={pdpImageResizeMode ?? ''}
                onChange={onPDPResizeUpdate}
                options={[
                  {value: 'cover', text: 'Fill'},
                  {value: 'contain', text: 'Fit'},
                ]}
                label={'Image Aspect Ratio'}
                disableBinding={true}
              />
              <CheckboxControl
                value={pdpHideWishlistIcon ?? false}
                onChange={onPDPHideWishlistUpdate}
                label={'Hide Wishlist'}
              />
              <RangeSliderControl
                value={pdpBorderRadiusValue ?? '0'}
                onChange={onPDPBorderRadiusUpdate}
                label={'CTA Border Radius'}
                minRange="0"
                maxRange="25"
              />
            </View>
          </View>
        )}
        {moduleConfig?.subtype === 'ModuleInstance' &&
          (!navConfig || navConfig[1] !== mainTab?.name) &&
          !selectedSettings && (
            <View style={[styles.contentSettingsContainer, pluginId && isDeletable ? {} : {paddingBottom: 0}]}>
              {mandatoryFieldsEmpty ? (
                <MandatoryTileEditor key={moduleUUID} isDeletable={isDeletable} onDelete={onDelete} />
              ) : (
                <TileEditor key={moduleUUID} isDeletable={isDeletable} onDelete={onDelete} />
              )}
            </View>
          )}
        {!isSuperPage && !moduleConfig && (!navConfig || navConfig[1] !== mainTab?.name) && !selectedSettings && (
          <View style={styles.tileSelectionCont}>
            <View style={styles.tileSelection}>
              <ApptileWebIcon name={'select'} size={48} />
              <Text style={[commonStyles.heading, {marginVertical: 5}]}>SELECT A TILE</Text>
              <Text style={[commonStyles.baseText, {color: theme.TILE_COLOR, textAlign: 'center'}]}>
                Select a tile in canvas to activate this panel.
              </Text>
            </View>
          </View>
        )}
        {navConfig &&
          selectedNavConfig &&
          (selectedNavConfig?.navigatorType === 'tab' ||
            (selectedNavConfig?.type !== 'navigator' && navConfig.length >= 3 && navConfig[1] === mainTab?.name)) && (
            <BottomNavigationEditor />
          )}
        {navConfig && (!selectedNavConfig || (selectedNavConfig && selectedNavConfig?.navigatorType === 'topTab')) && (
          <TopNavigationEditor />
        )}

        {!navConfig && selectedSettings === 'Toasts' && <AlertandToasts />}
        {!navConfig && selectedSettings === 'AppSettings' && <CustomAppSettings />}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    width: '100vw',
    height: '100vh',
    flexDirection: 'row',
    backgroundColor: theme.PRIMARY_BACKGROUND,
  },
  platformIconsContainer: {
    flexDirection: 'row',
    width: 185,
    justifyContent: 'center',
    marginTop: 20,
    gap: 20,
  },
  platformIcon: {
    width: 32,
    height: 32,
  },
  platformIconWrapper: {
    borderWidth: 1,
    borderColor: '#000000',
    padding: 14,
    borderRadius: 8,
  },
  rowContainer: {
    flexDirection: 'row',
  },
  sidebarHeader: {
    paddingTop: '1vh',
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  sidebarHeaderRight: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'center',
    zIndex: 1,
  },
  previewButton: {
    marginRight: 4,
    alignItems: 'center',
  },
  topButtons: {
    flex: 1,
  },
  bottomButtons: {
    width: '40%',
  },
  sidebarRight: {
    width: '100%',
    height: '100%',
    paddingLeft: 12,
    paddingRight: 6,
  },
  QRContainer: {
    alignItems: 'flex-end',
    paddingVertical: '1vh',
  },
  QRCaption: {
    fontSize: 14,
    width: 184,
    textAlign: 'center',
    marginTop: 20,
    lineHeight: 20,
  },
  propertyEditorContainer: {
    backgroundColor: '#FFFFFF',
    flex: 1,
  },
  bottomButtonsWrapper: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    paddingLeft: 20,
    paddingRight: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    boxShadow: '0px -1px 8px rgba(0, 0, 0, 0.09)',
  },
  contentSettingsContainer: {
    flex: 1,
    paddingBottom: 5,
    paddingTop: '1vh',
  },
  contentContainer: {
    flexGrow: 0,
    flexShrink: 0,
    paddingBottom: 20,
    paddingTop: '1vh',
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
  },
  errorStrip: {
    backgroundColor: theme.INPUT_BACKGROUND,
    minHeight: 114,
    marginTop: 10,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    gap: 10,
  },
  variantsStrip: {
    marginTop: 20,
    marginBottom: 15,
    gap: 10,
  },
  variantsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 10,
    borderColor: theme.INPUT_BORDER,
    borderWidth: 1,
    padding: 5,
    borderRadius: 8,
    backgroundColor: theme.INPUT_BACKGROUND,
    alignItems: 'flex-start',
  },
  variantsPopover: {
    width: 400,
    height: 400,
    marginTop: 60,
    marginRight: 20,
    overflowY: 'scroll',
    paddingVertical: 15,
  },
  variantItem: {
    width: '30%',
    aspectRatio: 1,
    borderRadius: 8,
    backgroundColor: theme.QUATERNARY_BACKGROUND,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    flexDirection: 'column',
  },
  image: {
    width: '100%',
    height: '100%',
    flex: 1,
  },
  tileSelectionCont: {
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tileSelection: {
    width: 229,
    height: 219,
    backgroundColor: theme.TILE_LABEL_BACKGROUND,
    paddingVertical: 58,
    paddingHorizontal: 13,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.CONTROL_BORDER,
    textAlign: 'center',
  },
});
export default RightSidebar;
