const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const path = require('path');
const IconfontPlugin = require('iconfont-plugin-webpack');
const WatchExternalFilesPlugin = require('webpack-watch-files-plugin').default;
const rootDir = path.join(__dirname, '..');
const nodeModulesDir = path.resolve(__dirname, '../node_modules');
const coreDir = path.join(__dirname, '../../apptile-core');
console.log('apptile-core entry path: ', coreDir);
console.log('nodeModulesPath: ', nodeModulesDir);
console.log('WatchExternalFilesPlugin: ', WatchExternalFilesPlugin);
require('dotenv').config();
const resolve = path.resolve.bind(path, __dirname);

const webpackEnv = process.env.NODE_ENV || 'development';

let mainEntry = path.join(rootDir, 'web', 'index.web.ts');

module.exports = {
  entry: {
    main: [mainEntry],
    preview: [path.join(rootDir, 'web', 'preview.web.ts')],
    appclip: [path.join(rootDir, 'web', 'appclip.web.ts')],
  },
  output: {
    path: path.join(rootDir, 'dist'),
    filename: '[name].[contenthash].js',
    chunkFilename: '[name].bundle.js',
    publicPath: '/',
  },
  optimization: {
    splitChunks: {
      cacheGroups: {
        apptileApp: {
          test: /[\\/]app[\\/]/,
          name: 'apptile-app',
          chunks: 'all',
        },
      },
      chunks: 'all',
    },
  },
  mode: webpackEnv,
  devtool: webpackEnv === 'development' ? 'inline-source-map' : 'source-map',
  devServer: {
    port: 3024,
    hot: true,
    historyApiFallback: true,
    static: {
      directory: path.join(rootDir, 'web', 'assets'),
    },
    allowedHosts: 'all',
    host: '127.0.0.1',
  },
  module: {
    rules: [
      {
        test: /\.(ts|tsx|js|jsx)$/,
        exclude:
          /node_modules\/(?!(react-native-circular-progress-indicator|react-native-reanimated|react-native-json-tree|react-native-vector-icons|zego-express-engine-reactnative)\/).*/,
        use: {
          loader: 'babel-loader',
          options: {
            cacheDirectory: true,
            cacheCompression: false,
            presets: ['module:@react-native/babel-preset'],
            plugins: [
              '@babel/plugin-proposal-export-namespace-from',
              'react-native-reanimated/plugin',
              ['@babel/plugin-proposal-class-properties', { loose: true }],
              ['@babel/plugin-transform-private-methods', { loose: true }],
              ['@babel/plugin-transform-private-property-in-object', { loose: true }],
            ],
          },
        },
      },
      {
        type: 'asset/resource',
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
      },
      {
        test: /\.(ttf|otf|eot|woff|woff2)$/,
        use: {
          loader: 'url-loader',
        },
        include: [
          path.join(rootDir, 'node_modules', 'react-native-vector-icons'),
          path.join(rootDir, 'web', 'assets', 'webfonts'),
        ],
      },
      {
        test: /\.css$/i,
        use: ['style-loader', 'css-loader'],
        exclude: /main\.css/i,
      },
      {
        test: /\.html$/i,
        use: 'html-loader',
      },
      {
        test: /\.(png|jpg|gif)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'images/[name]-[hash][ext]',
        },
      },
      {
        test: /main\.css/i,
        type: 'asset/resource',
      },
      {
        test: /\.txt$/i,
        use: 'raw-loader',
      },
    ],
  },
  plugins: [
    new IconfontPlugin({
      src: resolve('assets/icons'),
      family: 'ApptileWebIcons',
      dest: {
        font: resolve('assets/webfonts/[family].[type]'),
        css: resolve('assets/stylesheets/_[family].scss'),
      },
      watch: {
        cwd: __dirname,
        pattern: 'icons/*.svg',
      },
    }),
    new HtmlWebpackPlugin({
      filename: 'index.html',
      template: path.join(rootDir, 'web', 'public', 'index.html'),
      chunks: ['main'],
      excludeChunks: ['preview', 'SA', 'appclip'],
    }),
    new HtmlWebpackPlugin({
      filename: 'preview.html',
      template: path.join(rootDir, 'web', 'public', 'preview.html'),
      chunks: ['preview'],
      excludeChunks: ['main', 'SA', 'appclip'],
    }),
    new HtmlWebpackPlugin({
      filename: 'app-clip.html',
      template: path.join(rootDir, 'web', 'public', 'app-clip.html'),
      chunks: ['appclip'],
      excludeChunks: ['preview','main', 'SA'],
    }),
    new HtmlWebpackPlugin({
      filename: 'SA.html',
      template: path.join(rootDir, 'web', 'public', 'SA.html'),
      chunks: ['SA'],
      excludeChunks: ['main', 'preview', 'appclip'],
    }),
    new HtmlWebpackPlugin({
      filename: 'superactions.html',
      template: path.join(rootDir, 'web', 'public', 'SA.html'),
      chunks: ['SA'],
      excludeChunks: ['main', 'preview', 'appclip'],
    }),
    new webpack.DefinePlugin({
      process: { env: JSON.stringify(process.env) },
      __DEV__: process.env.NODE_ENV !== 'production',
    }),
    new WatchExternalFilesPlugin({
      files: [coreDir],
    }),
  ],
  resolve: {
    modules: [nodeModulesDir, 'node_modules'],
    extensions: ['.web.tsx', '.web.ts', '.tsx', '.ts', '.web.jsx', '.web.js', '.jsx', '.js', '.css'],
    alias: Object.assign({
      'react-native$': 'react-native-web',
      'react-native-webview': 'react-native-web-webview',
      '@/root': rootDir,
      'apptile-core': coreDir,
      'asset_placeholder-image': path.join(rootDir, 'app', 'assets', 'image-placeholder.png'),
    }),
    fallback: {
      buffer: require.resolve('buffer/'),
    },
  },
};
