import {ILiveStream, ILiveStreamRecording} from '../api/LivelyApiTypes';

export const LIVELY_INTEGRATION_CODE = 'lively';
export const FETCH_LIVELY_CREDS = 'FETCH_LIVELY_CREDS';
export const FETCHED_LIVELY_CREDS = 'FETCHED_LIVELY_CREDS';

export const LIVE_SELLING_INIT = 'LIVE_SELLING_INIT';

export const LIVELY_REGISTER = 'LIVELY_REGISTER';
export const LIVELY_REGISTER_STATUS = 'LIVELY_REGISTER_STATUS';
export const LIVELY_REGISTER_SUCCESS = 'LIVELY_REGISTER_SUCCESS';
export const LIVELY_REGISTER_FAILED = 'LIVELY_REGISTER_FAILED';
export const LIVELY_LOGIN = 'LIVELY_LOGIN';
export const LIVELY_LOGIN_SUCCESS = 'LIVELY_LOGIN_SUCCESS';
export const LIVELY_LOGIN_FAILED = 'LIVELY_LOGIN_FAILED';
export const LIVELY_LOGOUT = 'LIVELY_LOGOUT';
export const LIVELY_LOGOUT_SUCCESS = 'LIVELY_LOGOUT_SUCCESS';
export const LIVELY_LOGIN_TRIGGERED = 'LIVELY_LOGIN_TRIGGERED';
export const FETCH_FACEBOOK_PAGES = 'FETCH_FACEBOOK_PAGES';
export const FETCH_FACEBOOK_PAGES_SUCCESS = 'FETCH_FACEBOOK_PAGES_SUCCESS';
export const FETCH_FACEBOOK_PAGES_FAILED = 'FETCH_FACEBOOK_PAGES_FAILED';
export const FETCH_FACEBOOK_TOKEN = 'FETCH_FACEBOOK_TOKEN';
export const FETCH_FACEBOOK_TOKEN_SUCCESS = 'FETCH_FACEBOOK_TOKEN_SUCCESS';
export const FETCH_FACEBOOK_TOKEN_FAILED = 'FETCH_FACEBOOK_TOKEN_FAILED';
export const FETCH_INSTAGRAM_PAGES = 'FETCH_INSTAGRAM_PAGES';
export const FETCH_INSTAGRAM_PAGES_SUCCESS = 'FETCH_INSTAGRAM_PAGES_SUCCESS';
export const FETCH_INSTAGRAM_PAGES_FAILED = 'FETCH_INSTAGRAM_PAGES_FAILED';
export const FETCH_INSTAGRAM_TOKEN = 'FETCH_INSTAGRAM_TOKEN';
export const FETCH_INSTAGRAM_TOKEN_SUCCESS = 'FETCH_INSTAGRAM_TOKEN_SUCCESS';
export const FETCH_INSTAGRAM_TOKEN_FAILED = 'FETCH_INSTAGRAM_TOKEN_FAILED';
export const CREATE_STREAM = 'CREATE_STREAM';
export const CREATE_STREAM_SUCCESS = 'CREATE_STREAM_SUCCESS';
export const CREATE_STREAM_FAILED = 'CREATE_STREAM_FAILED';
export const UPDATE_CREATE_STREAM_META = 'UPDATE_CREATE_STREAM_META';
export const ADD_PINNED_COMMENT = 'ADD_PINNED_COMMENT';
export const REMOVE_PINNED_COMMENT = 'REMOVE_PINNED_COMMENT';

export const DISPLAY_STREAM_DELETE_CONFIRM = 'DISPLAY_STREAM_DELETE_CONFIRM';
export const CLOSE_STREAM_DELETE_CONFIRM = 'CLOSE_STREAM_DELETE_CONFIRM';

export const DELETE_STREAM = 'DELETE_STREAM';
export const DELETE_UPCOMING_STREAM = 'DELETE_UPCOMING_STREAM';
export const DELETE_STREAM_SUCCESS = 'DELETE_STREAM_SUCCESS';
export const DELETE_STREAM_FAILED = 'DELETE_STREAM_FAILED';

export const LIVELY_FETCH_PAST_STREAMS = 'LIVELY_FETCH_PAST_STREAMS';
export const LIVELY_FETCH_PAST_STREAMS_SUCCESS = 'LIVELY_FETCH_PAST_STREAMS_SUCCESS';
export const LIVELY_FETCH_PAST_STREAMS_FAILED = 'LIVELY_FETCH_PAST_STREAMS_FAILED';

export const LIVELY_FETCH_UPCOMING_STREAMS = 'LIVELY_FETCH_UPCOMING_STREAMS';
export const LIVELY_FETCH_UPCOMING_STREAMS_SUCCESS = 'LIVELY_FETCH_UPCOMING_STREAMS_SUCCESS';
export const LIVELY_FETCH_UPCOMING_STREAMS_FAILED = 'LIVELY_FETCH_UPCOMING_STREAMS_FAILED';

export const LIVELY_FETCH_IN_PROGRESS_STREAMS = 'LIVELY_FETCH_IN_PROGRESS_STREAMS';
export const LIVELY_FETCH_IN_PROGRESS_STREAMS_SUCCESS = 'LIVELY_FETCH_IN_PROGRESS_STREAMS_SUCCESS';
export const LIVELY_FETCH_IN_PROGRESS_STREAMS_FAILED = 'LIVELY_FETCH_IN_PROGRESS_STREAMS_FAILED';

export const LIVELY_FETCH_ACTIVE_STREAMS = 'LIVELY_FETCH_ACTIVE_STREAMS';

export type livelyLoginProps = {
  email: string;
  password: string;
  appId: string;
  directAuth?: boolean;
  buildRedirection?:boolean; // if true, it will redirects to the scion-v3-roadmap build number, this is added for other partners apart from shopify
};

export enum ILivelyWebsite {
  SHOPIFY = '1',
  SHOPLAZZA = '2'
}

export const loginToLively = (payload: livelyLoginProps) => {
  return {
    type: LIVELY_LOGIN,
    payload,
  };
};

export const logoutFromLively = () => {
  return {
    type: LIVELY_LOGOUT,
  };
};

export type livelyRegisterProps = {
  email: string;
  password: string;
  appId: string;
};

export const registerToLively = (payload: livelyRegisterProps) => {
  return {
    type: LIVELY_REGISTER,
    payload,
  };
};

export type streamCreationProps = {
  topic: string;
  description: string;
  thumbnailUrl: string;
  platforms: string[];
  fbAuthId: string | null;
  instaAuthId: string | null;
  streamProducts: any[];
  scheduleDate: Date;
  hasNotification: boolean;
  notificationInfo?: NotificationInfo | {};
  hasStoreCredit: boolean;
  storeCreditInfo?: any;
};

export type NotificationInfo = {
  title: string;
  description: string;
  thumbnailUrl: string;
};

export const createStream = (payload: streamCreationProps) => {
  return {
    type: CREATE_STREAM,
    payload,
  };
};

export interface UpdateCreateStreamMetaProps {
  data: string;
  error: string | null;
}

export const updateCreateStreamMeta = (payload: UpdateCreateStreamMetaProps) => {
  return {
    type: UPDATE_CREATE_STREAM_META,
    payload,
  };
};

export const fetchFacebookPages = () => {
  return {
    type: FETCH_FACEBOOK_PAGES,
  };
};

export const fetchFacebookToken = (streamingId: string) => {
  return {
    type: FETCH_FACEBOOK_TOKEN,
    payload: {streamingId},
  };
};

export const fetchInstagramToken = (streamingId: string, instaAuthId: string) => {
  return {
    type: FETCH_INSTAGRAM_TOKEN,
    payload: {streamingId, instaAuthId},
  };
};

export const fetchInstagramPages = () => {
  return {
    type: FETCH_INSTAGRAM_PAGES,
  };
};

export type CommentProp = {
  name: string;
  comment: string;
  imgUrl: string;
  id: string;
  source: 'MODERATOR' | 'HOST';
  isPinned: boolean;
};

export const addPinnedComment = (comment: CommentProp, streamingId: string) => {
  return {
    type: ADD_PINNED_COMMENT,
    payload: {
      comment,
      streamingId,
    },
  };
};

export const removePinnedComment = (streamingId: string) => {
  return {
    type: REMOVE_PINNED_COMMENT,
    payload: {
      streamingId,
    },
  };
};

export type IDisplayStreamDeleteConfirmPayload = {
  streamId: string;
  feedFileId: string | undefined;
  onCreateClose: (() => void) | undefined;
};

export const displayStreamDeleteConfirm = (
  streamId: string,
  feedFileId: string,
  onCreateClose?: (() => void) | undefined,
) => {
  return {
    type: DISPLAY_STREAM_DELETE_CONFIRM,
    payload: {streamId, feedFileId, onCreateClose},
  };
};

export const closeStreamDeleteConfirm = () => {
  return {
    type: CLOSE_STREAM_DELETE_CONFIRM,
  };
};

export const deleteStream = (streamId: string, feedFileId: string) => {
  return {
    type: DELETE_STREAM,
    payload: {streamId, feedFileId},
  };
};

export const deleteUpcomingStream = (streamId: string, onCreateClose?: () => void) => {
  return {
    type: DELETE_UPCOMING_STREAM,
    payload: {streamId, onCreateClose},
  };
};

export const fetchPastStreams = () => {
  return {
    type: LIVELY_FETCH_PAST_STREAMS,
  };
};

export const fetchUpcomingStreams = () => {
  return {
    type: LIVELY_FETCH_UPCOMING_STREAMS,
  };
};

export type IPastStreamsResponse = {
  recordings: ILiveStreamRecording[];
  data: ILiveStream[];
};

export const fetchActiveStreams = () => {
  return {
    type: LIVELY_FETCH_ACTIVE_STREAMS,
  };
};

export type IActiveStreamsResponse = {
  data: ILiveStream[];
};

export type errorPayload = {
  errorMessage: string;
  isNetworkError: boolean;
};
