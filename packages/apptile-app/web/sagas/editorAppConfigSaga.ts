import {
  DispatchAction,
  DispatchActions,
  generatePurePageModelFromConfig,
  hydratePluginModelCachesForPage,
  NAMESPACE_SPERATOR,
  versionMatchAppConfig,
} from 'apptile-core';
import {CreateWidgetPayload} from 'apptile-core';
import {PluginConfig, ModuleRecord, LayoutRecord, PluginNamespaceImpl} from 'apptile-core';
import {addNamespace} from 'apptile-core';
import {GetRegisteredConfig, resolvePluginListing} from 'apptile-core';
import {selectAppConfig, SentryHelper} from 'apptile-core';
import Immutable from 'immutable';
import _, {cloneDeep} from 'lodash';
import {AppConfig, InteractionManager} from 'react-native';
import {SagaIterator} from 'redux-saga';
import {all, call, delay, put, putResolve, select, spawn, take, takeEvery, takeLatest} from 'redux-saga/effects';
import {makeToast} from '../actions/toastActions';
import {TilesCacheType} from '../common/webDatatypes';
import {selectTilesCache} from '../selectors/TileSelector';
import {allAvailablePlans} from 'apptile-core';
import {
  selectPluginConfig,
  validateNaming,
  PluginConfigType,
  triggerCustomEventListener,
  AppModelType,
  selectAppModel,
  getPluginsContained,
  PluginNamespace,
  getPluginSelector,
  getJSBindingVariables,
  pluginConfigSetPathValue,
  MoveWidgetPayload,
  PageConfig,
  NavigatorConfig,
  UpdatePageId,
  JSModel,
} from 'apptile-core';
import {
  PluginConfigUpdate,
  PluginConfigValueUpdate,
  PluginConfigUpdatePath,
  PluginConfigSetPathValue,
  BulkPluginUpdate,
  PluginConfigEventDelete,
  AddPage,
  DeletePageConfig,
} from 'apptile-core';
import {PluginRenamePayload} from 'apptile-core';
import {
  recalculatePluginConfig,
  commitStageModel,
  initGlobalPageModels,
  removeNamespace,
  getPluginIdIndexInSelector,
  getNamespaces,
  updateProperty,
  addPluginDone,
  eventDeleteDependencyHandler,
  evaluateJSBindingString,
  apptileStateSelector,
} from 'apptile-core';
import {generateNewModuleId, deleteModulePlugins, createModulePlugin} from './editorModuleActionsSaga';
import {Selector} from 'react-redux';
import {PluginDeletePayload} from '../actions/editorActions';
import AppConfigApi from '../api/AppConfigApi';
import type {IAppBranch} from '../api/ApiTypes';
import {toggleGeneralPurposeModal} from '../actions/editorActions';

export function* fetchAppConfigSagaWeb(action: DispatchAction<FetchAppConfigPayload>): SagaIterator {
  try {
    const apptileState = yield select(apptileStateSelector);
    const appManifest = yield call(AppConfigApi.fetchAppManifest, action.payload.appId);

    let appBranch: IAppBranch;
    appBranch = yield call(
      AppConfigApi.fetchAppBranch,
      action.payload.appId,
      apptileState.forkId,
      apptileState.appBranch,
    );

    let appConfig: AppConfig;
    let isLegacyConfig = false;
    let currentAppSaveId = 0;

    currentAppSaveId = appBranch.headCommitId;
    appConfig = yield call(
      AppConfigApi.fetchAppConfigWeb,
      action.payload.appId,
      apptileState.forkId,
      apptileState.appBranch,
    );

    if (!appConfig) {
      yield put(
        toggleGeneralPurposeModal(
          true,
          `
A network error is blocking the app configuration from loading. Don't worry all your data is safe. Here are the things you can try to solve this issue:
- Disable browser extensions that may be inadvertently blocking apptile's apis
- Change your internet connection as there may be a firewall on the current one preventing our configuration api from being reachable
- Contact support
`,
        ),
      );
      SentryHelper.captureMessage('WEB_ALERT: Appconfig failed to load');
    } else {
      yield put(toggleGeneralPurposeModal(false));
    }

    const versionMatchedAppConfig = versionMatchAppConfig(appConfig);

    yield put({
      type: DispatchActions.FETCH_APPCONFIG_FINISHED,
      payload: {
        isPublished: appManifest?.published,
        appId: action.payload.appId,
        appSaveId: currentAppSaveId,
        appConfig: versionMatchedAppConfig,
        appSaveUpdatedAt: appBranch.updatedAt,
        isLegacy: isLegacyConfig,
      },
    });

    logger.info(`[DEBUG] fetchAppConfigSaga: Dispatching INIT_APP_MODEL`);
    yield put({
      type: DispatchActions.INIT_APP_MODEL,
    });
    yield spawn(function* () {
      yield take(DispatchActions.INIT_UPDATE_PAGE_MODEL);
      yield call(InteractionManager.runAfterInteractions);
      yield call(initGlobalPageModels);
    });
  } catch (e) {
    yield put({
      type: DispatchActions.INIT_APP_MODEL_ERROR,
      payload: {
        e,
      },
    });
  }
}

export function* addPluginSaga(action: DispatchAction<CreateWidgetPayload>): SagaIterator {
  try {
    const {pluginType, configType, pageId, refWidget, afterRefWidget, layout, moduleUUID, bFetchTileFromServer} =
      action.payload;
    let {container} = action.payload;
    const pluginConfigMap = GetRegisteredConfig(pluginType)({});
    const pluginPrefix = resolvePluginListing(pluginType)?.labelPrefix ?? 'UnknownPlugin';
    const appConfig: AppConfig = yield select(selectAppConfig);
    const isGlobalPlugin = pageId === undefined;
    const pageConfig = appConfig.getPage(pageId);
    const pluginsMap: Immutable.OrderedMap<string, PluginConfig> = isGlobalPlugin
      ? appConfig.plugins
      : pageConfig.plugins;
    const appModulesCache = appConfig.modules;
    let containerConfig: PluginConfig = pluginsMap?.get(container);

    let pluginId: string;
    let config: PluginConfig;

    // Tiles & Modules Addition.
    if (pluginType === 'ModuleInstance' && !isGlobalPlugin) {
      pluginId = generateNewModuleId(pageConfig);
      let moduleRecord: ModuleRecord = appModulesCache.get(moduleUUID);
      if (bFetchTileFromServer || !moduleRecord) {
        moduleRecord = yield call(ensureModuleRecord, moduleUUID);
      }
      if (moduleRecord && moduleRecord.isRootLevelTile) {
        containerConfig = undefined;
        container = '';
      }
      let moduleConfigFields = {};
      const bPersistInputs = moduleRecord.persistInputBindings;
      moduleRecord.inputs.forEach(key =>
        _.set(moduleConfigFields, key, bPersistInputs ? moduleRecord.inputBindings?.get(key) : ''),
      );
      moduleRecord.outputs.forEach(key => _.set(moduleConfigFields, key, ''));
      moduleRecord.queries.forEach(key => _.set(moduleConfigFields, key, ''));
      config = new PluginConfig({
        id: pluginId,
        type: configType,
        subtype: pluginType,
        config: Immutable.Map({
          moduleUUID,
          moduleName: moduleRecord.moduleName,
          childNamespace: pluginId,
          variantSelected: 'custom',
          changesDone: false,
          variantGating: allAvailablePlans.CORE,
          //FIXME: TODO: Remove this hack and only make eventhandler be used.
          events: moduleRecord.defaultEventHandlers.map(e => e.get('eventHandler') || e),
          ...moduleConfigFields,
        }),
        layout: new LayoutRecord({container, ...layout}),
        namespace: containerConfig?.namespace,
      });
    } else {
      // Component Addition
      pluginId = generateUniquePluginId(pluginsMap, pluginPrefix);
      const namespace = containerConfig?.namespace;
      config = new PluginConfig({
        id: addNamespace(namespace, pluginId),
        type: configType,
        subtype: pluginType,
        config: pluginConfigMap,
        layout: new LayoutRecord({container, ...layout}),
        namespace: namespace ? new PluginNamespaceImpl(namespace?.getNamespace(), pluginId) : undefined,
      });
    }

    yield* addPluginConfig(config, pageId, refWidget, afterRefWidget);
  } catch (e) {
    logger.error(e);
  }
}

export function* addDatasourcePluginSaga(action: DispatchAction<CreateWidgetPayload>): SagaIterator {
  try {
    const {pluginType, configType, pageId, container, refWidget, afterRefWidget, layout} = action.payload;
    const pluginConfigMap = GetRegisteredConfig(pluginType)({});
    const pluginPrefix = resolvePluginListing(pluginType)?.labelPrefix ?? 'UnknownPlugin';
    const appConfig: AppConfig = yield select(selectAppConfig);
    const pluginsMap: Immutable.OrderedMap<string, PluginConfig> = appConfig.plugins;
    const containerConfig: PluginConfig = pluginsMap?.get(container);

    let pluginId: string;
    let config: PluginConfig;

    pluginId = generateUniqueDatasourcePluginId(pluginsMap, pluginPrefix);
    const namespace = containerConfig?.namespace;
    config = new PluginConfig({
      id: addNamespace(namespace, pluginId),
      type: configType,
      subtype: pluginType,
      config: pluginConfigMap,
      layout: new LayoutRecord({container, ...layout}),
      namespace: namespace ? new PluginNamespaceImpl(namespace?.getNamespace(), pluginId) : undefined,
    });

    yield* addPluginConfig(config, pageId, refWidget, afterRefWidget);
  } catch (e) {
    logger.error(e);
  }
}

function* ensureModuleRecord(moduleUUID: string): SagaIterator<ModuleRecord> {
  let tilesCache: TilesCacheType = yield select(selectTilesCache);
  let moduleRecord: ModuleRecord | undefined;
  let retryCount = 0;
  while (moduleRecord === undefined) {
    tilesCache = yield select(selectTilesCache);
    if (tilesCache.isTileCached(moduleUUID)) {
      moduleRecord = tilesCache.getTileCacheRecord(moduleUUID);
      let newAppConfig = yield select(selectAppConfig);
      newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);
      yield put({
        type: DispatchActions.UPDATE_APP_CONFIG,
        payload: newAppConfig,
      });
      yield put(makeToast({content: `Updated tile details for ${moduleRecord?.moduleName}`, appearances: 'info'}));
      if (moduleRecord?.moduleConfig) {
        for (const [pid, pluginConfig] of moduleRecord.moduleConfig) {
          if (pluginConfig.subtype === 'ModuleInstance') {
            yield call(ensureModuleRecord, pluginConfig.config.get('moduleUUID'));
          }
        }
      }
    } else {
      if (!retryCount) yield put(makeToast({content: 'Fetching tile details!', appearances: 'info'}));
      retryCount++;
      if (retryCount > 5) {
        yield put(makeToast({content: 'Error Fetching tile details!', appearances: 'error'}));
        throw `module not found ${moduleUUID}`;
      }
      yield delay(500);
    }
  }
  return moduleRecord;
}

export function* updatePluginConfig(action: DispatchAction<PluginConfigUpdate>): SagaIterator {
  try {
    const {pluginId, pageId, update} = action.payload;
    const pluginConfig = yield select(selectPluginConfig, pageId, pluginId);
    yield put({
      type: DispatchActions.PLUGIN_UPDATE_PROPERTY,
      payload: {
        pluginId,
        pageId,
        update,
      },
    });

    yield call(recalculatePluginConfig, pageId, pluginId, pluginConfig, update);
    yield call(hydratePluginModelCachesForPage, pageId);
  } catch (e) {}
}

export function* updatePluginConfigPropertyValue(action: DispatchAction<PluginConfigValueUpdate>): SagaIterator {
  try {
    const {pluginId, pageId, propName, value} = action.payload;
    const pluginConfig = yield select(selectPluginConfig, pageId, pluginId);
    yield put({
      type: DispatchActions.PLUGIN_UPDATE_PROPERTY_VALUE,
      payload: {
        pluginId,
        pageId,
        propName,
        value,
      },
    });

    yield call(recalculatePluginConfig, pageId, pluginId, pluginConfig, Immutable.Map().set(propName, value));
    yield call(hydratePluginModelCachesForPage, pageId);
  } catch (e) {}
}
export function* updatePluginConfigPath(action: DispatchAction<PluginConfigUpdatePath>): SagaIterator {
  try {
    const {pluginId, pageId, selector, update, remove} = action.payload;
    const pluginConfig = yield select(selectPluginConfig, pageId, pluginId);
    let configUpdate = Object.assign({}, update);
    if (!_.isEmpty(remove)) {
      remove?.map(prop => _.set(configUpdate, [prop], ''));
    }
    if (selector[0] === 'config') {
      configUpdate = _.isEmpty(selector.slice(1)) ? configUpdate : _.set({}, selector.slice(1), configUpdate);
      yield call(recalculatePluginConfig, pageId, pluginId, pluginConfig, configUpdate);
      if (selector?.[1] !== 'style') yield call(hydratePluginModelCachesForPage, pageId);
    }
    if (selector[0] === 'layout' && _.has(update, ['hidden'])) {
      yield call(recalculatePluginConfig, pageId, pluginId, pluginConfig, configUpdate);
    }
  } catch (e) {}
}

export function* updatePluginConfigPathValue(action: DispatchAction<PluginConfigSetPathValue>): SagaIterator {
  try {
    const {pluginId, pageId, selector, value} = action.payload;
    const pluginConfig = yield select(selectPluginConfig, pageId, pluginId);
    if (selector[0] === 'config') {
      yield call(
        recalculatePluginConfig,
        pageId,
        pluginId,
        pluginConfig,
        Immutable.Map().setIn(selector.slice(1), value),
      );
      yield call(hydratePluginModelCachesForPage, pageId);
    }
  } catch (e) {}
}

export function* bulkUpdatePlugins(action: DispatchAction<BulkPluginUpdate>): SagaIterator {
  try {
    const {updates} = action.payload;
    const allCalls = [];
    for (let i in updates) {
      const {pageId, pluginId, layout, style, config} = updates[i];
      let pluginConfig = yield select(selectPluginConfig, pageId, pluginId);
      let updateMap = Immutable.Map();
      if (layout) {
        // allCalls.push(
        //   call(recalculatePluginConfig, pageId, pluginId, pluginConfig, Immutable.Map().setIn(['layout'], layout)),
        // );
        updateMap = updateMap.setIn(['layout'], layout);
      }
      if (config) {
        // allCalls.push(call(recalculatePluginConfig, pageId, pluginId, pluginConfig, config));
        updateMap = updateMap.mergeDeep(config);
      }
      if (style) {
        // allCalls.push(
        //   call(recalculatePluginConfig, pageId, pluginId, pluginConfig, Immutable.Map().setIn(['style'], style)),
        // );
        updateMap = updateMap.setIn(['style'], style);
      }
      if (style || config || layout)
        allCalls.push(call(recalculatePluginConfig, pageId, pluginId, pluginConfig, updateMap));
    }
    yield all(allCalls);
    yield call(hydratePluginModelCachesForPage, pageId);
  } catch (e) {
    logger.error('Bulk update failed: ' + e?.message);
    logger.error(e);
  }
}

export function* updatePluginLayout(action: DispatchAction<PluginConfigUpdate>): SagaIterator {
  try {
    const {pluginId, pageId, update} = action.payload;
    const pluginConfig = yield select(selectPluginConfig, pageId, pluginId);
    yield put({
      type: DispatchActions.PLUGIN_UPDATE_LAYOUT,
      payload: {
        pluginId,
        pageId,
        update,
      },
    });

    yield call(recalculatePluginConfig, pageId, pluginId, pluginConfig, update);
  } catch (e) {}
}

export function* updatePluginStyle(action: DispatchAction<PluginConfigUpdate>): SagaIterator {
  try {
    const {pluginId, pageId, update} = action.payload;
    yield put({
      type: DispatchActions.PLUGIN_UPDATE_STYLE,
      payload: {
        pluginId,
        pageId,
        update,
      },
    });
  } catch (e) {}
}

function* handleRenamePlugin(action: DispatchAction<PluginRenamePayload>): SagaIterator {
  try {
    const {pluginId, pageId, newPluginId, namespace} = action.payload;
    if (pluginId === newPluginId || !validateNaming(newPluginId)) return;

    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    if (pageId) {
      let renamedId = newPluginId;
      const pluginConfig: PluginConfigType<any> = appConfig.getPagePlugins(pageId)?.get(pluginId);
      if (!pluginConfig) {
        // TODO(gaurav) DONE move to eventbus
        // yield put(makeToast({content: `Can't find plugin ${pluginId}`, appearances: 'error'}));
        triggerCustomEventListener('makeToast', {content: `Can't find plugin ${pluginId}`, appearances: 'error'});
        return;
      }
      if (pluginConfig.namespace) {
        let newPluginConfig = pluginConfig.set(
          'namespace',
          new PluginNamespaceImpl(pluginConfig.namespace.getNamespace(), newPluginId),
        );
        newAppConfig = newAppConfig.setIn(['pages', pageId, 'plugins', pluginId], newPluginConfig);
        renamedId = addNamespace(pluginConfig.namespace, renamedId);
      }
      const isPluginAlreadyExist = !!newAppConfig.getIn(['pages', pageId, 'plugins', renamedId]);
      if (isPluginAlreadyExist) {
        // TODO(gaurav) DONE move to eventbus
        // yield put(makeToast({content: "Can't rename plugin to already existing plugin name", appearances: 'info'}));
        triggerCustomEventListener('makeToast', {
          content: "Can't rename plugin to already existing plugin name",
          appearances: 'info',
        });
        return;
      }

      newAppConfig = newAppConfig.setIn(
        ['pages', pageId, 'plugins'],
        newAppConfig
          .getIn(['pages', pageId, 'plugins'])
          .mapKeys(k => (k === pluginId ? renamedId : k))
          .update((plugins: any) =>
            plugins.map((plugin: PluginConfig) => {
              if (plugin?.layout?.container === pluginId)
                return plugin.set('layout', plugin.layout.set('container', renamedId));
              return plugin;
            }),
          )
          .setIn([renamedId, 'id'], renamedId),
      );
      yield put({
        type: DispatchActions.UPDATE_APP_CONFIG,
        payload: newAppConfig,
      });
      // let newPluginConfig = newAppConfig.getIn(['pages', pageId, 'plugins', renamedId]);
      // yield call(recalculatePluginConfig, pageId, renamedId, newPluginConfig, newPluginConfig?.config);
      // yield call(addPluginDone, {
      //   type: DispatchActions.ADD_PLUGIN_DONE,
      //   payload: {
      //     pageId,
      //     pluginId,
      //     pluginConfig: newPluginConfig,
      //   },
      // });
    } else {
      const isPluginAlreadyExist = !!newAppConfig.getIn(['plugins', newPluginId]);
      if (isPluginAlreadyExist) {
        // TODO(gaurav) DONE eventbus
        // yield put(makeToast({content: "Can't rename plugin to already existing plugin name", appearances: 'info'}));
        triggerCustomEventListener('makeToast', {
          content: "Can't rename plugin to already existing plugin name",
          appearances: 'info',
        });
        return;
      }

      newAppConfig = newAppConfig.setIn(
        ['plugins'],
        newAppConfig
          .getIn(['plugins'])
          .mapKeys(k => (k === pluginId ? newPluginId : k))
          .update((plugin: PluginConfig) => {
            if (plugin?.layout?.container === pluginId)
              return plugin.set('layout', plugin.layout.set('container', newPluginId));
            return plugin;
          })
          .setIn([newPluginId, 'id'], newPluginId),
      );
      yield put({
        type: DispatchActions.UPDATE_APP_CONFIG,
        payload: newAppConfig,
      });
    }

    // Delay until rename has happened to follow through with update references.
    yield call(updatePluginIdReferences, pageId, pluginId, newPluginId, namespace);
    let renamedId = newPluginId;
    if (namespace) {
      renamedId = addNamespace(namespace, newPluginId);
    }
    // TODO(gaurav) DONE Figure out how to get this import
    // const selectedPluginSel = yield select(selectSelectedPluginSelector);
    // selectedPluginSel[selectedPluginSel.length - 1] = renamedId;
    // yield put(selectPlugin(selectedPluginSel));
    triggerCustomEventListener('renameSelectedPlugin', renamedId);
  } catch (e) {}
}

function* handleDeletePlugin(action: DispatchAction<PluginDeletePayload>): SagaIterator {
  const {pluginIds, pageId} = action.payload;

  const appConfig: AppConfig = yield select(selectAppConfig);
  let newAppConfig = appConfig;

  if (pageId) {
    const pageConfig = appConfig.getPage(pageId);
    const idsToDelete = pluginIds.slice();
    pageConfig.plugins.forEach(plugin => {
      if (plugin.layout && idsToDelete.includes(plugin.layout.container)) idsToDelete.push(plugin.id);
    });
    const newPlugins = idsToDelete.reduce((map, key) => {
      return map.delete(key);
    }, pageConfig.plugins);

    newAppConfig = newAppConfig.setIn(['pages', pageId, 'plugins'], newPlugins);
  } else {
    const newPlugins = pluginIds.reduce((map, key) => {
      return map.delete(key);
    }, newAppConfig.plugins);
    newAppConfig = newAppConfig.set('plugins', newPlugins);
  }

  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
}

export function* pluginDelete(pluginId: string, pageId: string) {
  const appConfig: AppConfig = yield select(selectAppConfig);
  const deleteIds = [pluginId];
  if (pageId) {
    const pageConfig = appConfig.getPage(pageId);
    const pagePlugins = pageConfig.plugins;
    const pluginConfig = pagePlugins.get(pluginId);
    if (pluginConfig) {
      if (pluginConfig.subtype === 'ModuleInstance') {
        let newAppConfig = appConfig;
        const appModel: AppModelType = yield select(selectAppModel);
        let newAppModel = appModel;
        ({newAppConfig, newAppModel} = deleteModulePlugins(newAppConfig, newAppModel, pageId, pluginConfig));
        yield put({
          type: DispatchActions.UPDATE_STAGE_MODEL,
          payload: {
            appModel: newAppModel,
            desc: 'Delete Plugin',
            meta: {
              pageId,
              pluginId,
            },
          },
        });
        yield put(commitStageModel());
        yield put({
          type: DispatchActions.UPDATE_APP_CONFIG,
          payload: newAppConfig,
        });
      } else {
        const childrenToDelete = getPluginsContained(pluginId, pageConfig);
        if (childrenToDelete && childrenToDelete.size > 0) {
          childrenToDelete.forEach(config => {
            deleteIds.push(config.id);
          });
        }
      }
    }
  }

  yield put({
    type: DispatchActions.DELETE_PLUGIN,
    payload: {
      pageId,
      pluginIds: deleteIds,
    },
  });
}

export function* updatePluginIdReferences(
  pageId: string,
  pluginId: string,
  newId: string,
  namespace: PluginNamespace | undefined,
) {
  //TODO: Update references to changed pluginId
  logger.info(`updatePluginIdReferences: ${pluginId}, ${pageId}, ${newId}`);
  try {
    const isDelete = newId === '';
    // Ids that are namespace independant to use for replace.
    let renamedId = newId;
    let originalPluginId = pluginId;
    if (namespace) {
      renamedId = addNamespace(namespace, newId);
      originalPluginId = removeNamespace(pluginId, namespace);
    }
    const appModel: AppModelType = yield select(selectAppModel);
    const pageKey = appModel.getPageKeysForId(pageId).get(0);

    const depGraph = appModel.dependencyGraph;
    const validSelectors = depGraph
      .topologicalSort()
      .filter(
        (selector: Selector) =>
          depGraph.lookupDynamicString(selector) && typeof depGraph.lookupDynamicString(selector) === 'string',
      );
    // logger.info(pageKey, originalPluginId, renamedId, namespace);
    const currentPluginSelector = getPluginSelector(pageKey, renamedId).map(e => (e == renamedId ? pluginId : e));

    // logger.info(pageId, originalPluginId, namespace, currentPluginSelector);

    const namespaces = namespace?.getNamespace();
    // logger.info(namespaces);

    const dependentPlugins = validSelectors
      .map(selector => {
        return {
          bindingVars: getJSBindingVariables(depGraph.lookupDynamicString(selector)),
          bindingString: depGraph.lookupDynamicString(selector),
          selector,
        };
      })
      .filter(selectorBindings => {
        if (selectorBindings.bindingVars.length) {
          const currentPluginPageKey = selectorBindings.selector[0];
          if (currentPluginPageKey != pageKey) return false;
          for (let i = 0; i < selectorBindings.bindingVars.length; i++) {
            const varPathSel = selectorBindings.bindingVars[i][0];
            const pluginIdIndex = getPluginIdIndexInSelector(selectorBindings.selector);
            const currentPluginId = selectorBindings.selector[pluginIdIndex];
            const currentNamespace = new PluginNamespaceImpl(getNamespaces(currentPluginId), currentPluginId);
            const pageVarPathSel = getPluginSelector(pageKey, varPathSel, currentNamespace);
            let check = true;
            check = _.isEqual(pageVarPathSel, currentPluginSelector);
            return check;
          }
        } else {
          return false;
        }
      })
      .map(selectorBindings => {
        const pluginIdIndex = getPluginIdIndexInSelector(selectorBindings.selector);
        selectorBindings.pluginId = selectorBindings.selector[pluginIdIndex];
        selectorBindings.update = selectorBindings.selector.slice(pluginIdIndex + 1);
        if (selectorBindings.update.length == 1 && typeof selectorBindings.update[0] === 'object')
          selectorBindings.update = selectorBindings.update[0];
        if (selectorBindings.update[0] != 'analytics') selectorBindings.update.unshift('config');
        return selectorBindings;
      });
    // logger.info(dependentPlugins);
    for (let i in dependentPlugins) {
      const {newValue, hasNewValue} = updateProperty(dependentPlugins[i].bindingString, originalPluginId, newId);
      if (hasNewValue) {
        // logger.info(`UPDATING - `, pageId, dependentPlugins[i].pluginId, dependentPlugins[i].update, newValue);
        yield put(pluginConfigSetPathValue(dependentPlugins[i].pluginId, pageId, dependentPlugins[i].update, newValue));
      }
    }
  } catch (e) {
    logger.error(e);
  }
}

export function generateUniquePluginId(pluginsMap: Immutable.OrderedMap<string, PluginConfig>, pluginPrefix: string) {
  let pluginIds = pluginsMap.keySeq().toArray();
  if (pluginIds)
    pluginIds = pluginIds.map(id => (id.indexOf(NAMESPACE_SPERATOR) == -1 ? id : id.split(NAMESPACE_SPERATOR)[1]));
  let i = 1;
  let uniquePluginId = `${pluginPrefix}${i}`;
  while (true) {
    if (!pluginIds.includes(uniquePluginId)) break;
    i++;
    uniquePluginId = `${pluginPrefix}${i}`;
  }
  return uniquePluginId;
}

export function generateUniqueDatasourcePluginId(
  pluginsMap: Immutable.OrderedMap<string, PluginConfig>,
  pluginPrefix: string,
) {
  const pluginIds = pluginsMap.keySeq().toArray();
  let i = 1;
  let uniquePluginId = `${pluginPrefix}${i}`;

  if (!pluginIds.includes(pluginPrefix)) {
    return pluginPrefix;
  }

  while (true) {
    if (!pluginIds.includes(uniquePluginId)) break;
    i++;
    uniquePluginId = `${pluginPrefix}${i}`;
  }
  return uniquePluginId;
}

export function* addPluginConfig(
  config: PluginConfig,
  pageId: string | undefined,
  refWidget: string | undefined,
  afterRefWidget: boolean | undefined,
) {
  const appConfig: AppConfig = yield select(selectAppConfig);
  const pluginId = config.id;
  const isGlobalPlugin = pageId === undefined;
  const pageConfig = appConfig.getPage(pageId);
  const pluginsMap: Immutable.OrderedMap<string, PluginConfig> = isGlobalPlugin
    ? appConfig.plugins
    : pageConfig.plugins;

  let newAppConfig = appConfig;
  if (isGlobalPlugin) {
    newAppConfig = newAppConfig.set('plugins', pluginsMap.set(pluginId, config));
  } else {
    if (refWidget) {
      let pagePlugins = pageConfig.plugins.takeUntil((c, pluginId) => pluginId == refWidget);
      if (afterRefWidget) {
        pagePlugins = pagePlugins.set(refWidget, pageConfig.plugins.get(refWidget));
        pagePlugins = pagePlugins.set(pluginId, config);
      } else {
        pagePlugins = pagePlugins.set(pluginId, config);
        pagePlugins = pagePlugins.set(refWidget, pageConfig.plugins.get(refWidget));
      }
      pagePlugins = pagePlugins.concat(pageConfig.plugins.skipUntil((c, pluginId) => pluginId == refWidget).skip(1));

      newAppConfig = newAppConfig.setIn(['pages', pageId, 'plugins'], pagePlugins);
    } else {
      newAppConfig = newAppConfig.setIn(['pages', pageId, 'plugins'], pageConfig.plugins.set(pluginId, config));
    }
  }
  yield putResolve({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
  // yield put({
  //   type: DispatchActions.ADD_PLUGIN_DONE,
  //   payload: {
  //     pageId,
  //     pluginId,
  //     pluginConfig: config,
  //   },
  // });
  yield call(addPluginDone, {
    type: DispatchActions.ADD_PLUGIN_DONE,
    payload: {
      pageId,
      pluginId,
      pluginConfig: config,
    },
  });
  if (config.subtype === 'ModuleInstance') {
    yield call(createModulePlugin, pluginId, pageId, config.config.get('moduleUUID'));
  }
  yield call(generatePurePageModelFromConfig, appConfig, pageId);
  yield call(hydratePluginModelCachesForPage, pageId);
}

export function* movePluginSaga(action: DispatchAction<MoveWidgetPayload>): SagaIterator {
  try {
    const {pluginType, pluginId, pageId, container, refWidget, afterRefWidget} = action.payload;

    const config = yield select(selectPluginConfig, pageId, pluginId);
    let newConfig = config.mergeDeepIn(['layout'], Immutable.fromJS({container}));

    const appConfig: AppConfig = yield select(selectAppConfig);
    const pageConfig = appConfig.getPage(pageId);
    let newAppConfig = appConfig;
    if (refWidget) {
      const preMovePlugins = pageConfig.plugins.filter(pluginConfig => pluginConfig.id !== pluginId);
      let pagePlugins = preMovePlugins.takeUntil((c, pluginId) => pluginId == refWidget);
      if (afterRefWidget) {
        pagePlugins = pagePlugins.set(refWidget, preMovePlugins.get(refWidget));
        pagePlugins = pagePlugins.set(pluginId, newConfig);
      } else {
        pagePlugins = pagePlugins.set(pluginId, newConfig);
        pagePlugins = pagePlugins.set(refWidget, preMovePlugins.get(refWidget));
      }
      pagePlugins = pagePlugins.concat(preMovePlugins.skipUntil((c, pluginId) => pluginId == refWidget).skip(1));

      newAppConfig = newAppConfig.setIn(['pages', pageId, 'plugins'], pagePlugins);
    } else {
      newAppConfig = newAppConfig.setIn(['pages', pageId, 'plugins'], pageConfig.plugins.set(pluginId, newConfig));
    }
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield put({
      type: DispatchActions.PLUGIN_MOVE_DONE,
      payload: {
        pageId,
        pluginId,
        pluginConfig: newConfig,
      },
    });
  } catch (e) {}
}

export function* deletePluginConfigEvent(action: DispatchAction<PluginConfigEventDelete>): SagaIterator {
  try {
    const {pluginId, pageId, selector} = action.payload;
    yield put({
      type: DispatchActions.PLUGIN_DELETE_EVENT_PROPERTY,
      payload: {
        pluginId,
        pageId,
        selector,
      },
    });
    yield call(eventDeleteDependencyHandler, pageId, pluginId, selector);
  } catch (e) {}
}

export function* updatePluginEventConfig(action: DispatchAction<PluginConfigUpdate>): SagaIterator {
  try {
    const {pluginId, pageId, update} = action.payload;
    const pluginConfig = yield select(selectPluginConfig, pageId, pluginId);
    yield put({
      type: DispatchActions.PLUGIN_UPDATE_EVENT,
      payload: {
        pluginId,
        pageId,
        update,
      },
    });

    yield call(recalculatePluginConfig, pageId, pluginId, pluginConfig, update.toJS());
  } catch (e) {}
}

function* generateNewPageId(pages: Immutable.Map<string, PageConfig>) {
  let i = 1;
  const pageIds = pages.keySeq().toArray();
  let newPageId = `Page${i}`;
  while (true) {
    if (!pageIds.includes(newPageId)) break;
    i++;
    newPageId = `Page${i}`;
  }
  return newPageId;
}

export function* handleAddPage(action: DispatchAction<AddPage>): SagaIterator {
  try {
    const pageId = action.payload?.pageId;
    const appConfig: AppConfig = yield select(selectAppConfig);
    const pages = appConfig.pages;
    const newPageId = pageId ? pageId : yield call(generateNewPageId, pages);
    if (pages.get(newPageId)) {
      // TODO(gaurav) DONE eventbus
      // yield put(makeToast({content: 'Page With Same Title Exists', appearances: 'error'}));
      triggerCustomEventListener('makeToast', {content: 'Page With Same Title Exists', appearances: 'error'});
    } else {
      yield put({
        type: DispatchActions.ADD_NEW_PAGE_DONE,
        payload: new PageConfig({pageId: newPageId}),
      });
    }
  } catch (e) {}
}

export function updatePageIdReferencesInNav(appConfig: AppConfig, oldId: string, newId: string): AppConfig {
  const updateNav = (navConfig: NavigatorConfig, oldPageId: string, newPageId: string) => {
    return navConfig.update('screens', screens =>
      screens.map(screenOrNav => {
        if (screenOrNav.screen === oldPageId) return screenOrNav.set('screen', newPageId);
        if (screenOrNav.header === oldPageId) return screenOrNav.set('header', newPageId);
        if (screenOrNav.type === 'navigator') return updateNav(screenOrNav, oldPageId, newPageId);
        return screenOrNav;
      }),
    );
  };
  return appConfig.setIn(['navigation', 'rootNavigator'], updateNav(appConfig.navigation.rootNavigator, oldId, newId));
}

export function* handleUpdatePageId(action: DispatchAction<UpdatePageId>): SagaIterator {
  try {
    const {pageId, newPageId} = action.payload;
    if (!validateNaming(newPageId)) return;

    const appConfig: AppConfig = yield select(selectAppConfig);
    const pageConfig = appConfig.pages.get(pageId);
    const pages = appConfig.get('pages');
    const pageIDs = pages.map(page => page.pageId);
    const isSameNameScreenExists = pageIDs.some(page => page === newPageId);
    if (isSameNameScreenExists) {
      // TODO(gaurav) eventbus
      // yield put(makeToast({content: "Can't rename page to already existing page name", appearances: 'error'}));
      triggerCustomEventListener('makeToast', {
        content: "Can't rename page to already existing page name",
        appearances: 'error',
      });
      return;
    }
    let newAppConfig = appConfig;
    newAppConfig = newAppConfig.set(
      'pages',
      newAppConfig.pages.mapKeys(key => {
        if (key === pageId) return newPageId;
        return key;
      }),
    );
    newAppConfig = newAppConfig.setIn(['pages', newPageId], pageConfig?.set('pageId', newPageId));
    newAppConfig = updatePageIdReferencesInNav(newAppConfig, pageId, newPageId);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield put({
      type: DispatchActions.UPDATE_PAGE_ID_DONE,
      payload: action.payload,
    });
  } catch (e) {
    logger.error(e);
  }
}

export function* handlePageDelete(action: DispatchAction<DeletePageConfig>): SagaIterator {
  try {
    const {pageId} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    const pageConfig = appConfig.pages.get(pageId);

    // Reducer removes pageConfig from pages. remove all Nav Components that refer to it.
    let newAppConfig = cloneDeep(appConfig);
    newAppConfig = updatePageIdReferencesInNav(newAppConfig, pageId, '');

    if (pageConfig?.get('type') !== 'Screen') {
      const navigation = newAppConfig.get('navigation');
      const appModel: AppModelType = yield select(selectAppModel);
      let newModel = cloneDeep(appModel);

      newModel.dependencyGraph.addNavigation(navigation);
      newModel.dependencyGraph.updateNavigation(navigation);
      let updatedModel = newModel.updateApptileNavigation(navigation.rootNavigator);

      const renderOrder = updatedModel.dependencyGraph
        .topologicalSort()
        .filter((selector: Selector) => selector[0] === 'ApptileNavigation')
        .filter((selector: Selector) => updatedModel.dependencyGraph.lookupDynamicString(selector));
      const scratchMemory: JSModel = {
        $global: {},
        unresolved: true,
        $context: {},
        hasCurrentPage: false,
        currentPage: {},
        hasIndex: false,
        i: 0,
      };

      const contextId = newModel.evalContextStack.createEvaluationContext();
      let evalContext = newModel.evalContextStack.getEvaluationContext(contextId);

      for (const selector of renderOrder) {
        updatedModel = yield call(
          evaluateJSBindingString,
          selector,
          updatedModel.dependencyGraph.lookupDynamicString(selector),
          updatedModel.dependencyGraph,
          updatedModel,
          scratchMemory,
          contextId,
          updatedModel.dependencyGraph.lookupNamespace(selector),
        );
      }

      const evalResults = evalContext.getResults();
      updatedModel = updatedModel.setAllValues(evalResults);

      yield put({
        type: DispatchActions.UPDATE_STAGE_MODEL,
        payload: {
          appModel: updatedModel,
          desc: 'Delete attachment',
        },
      });
    }
    yield put(commitStageModel());

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });

    yield put({
      type: DispatchActions.DELETE_PAGE_CONFIG_DONE,
      payload: {
        pageId,
      },
    });
  } catch (e) {
    logger.error(e);
  }
}

export default function* editorAppConfigSagas(): SagaIterator {
  yield all([
    takeLatest(DispatchActions.FETCH_APPCONFIG, fetchAppConfigSagaWeb),
    takeEvery(DispatchActions.ADD_PLUGIN, addPluginSaga),
    takeEvery(DispatchActions.PLUGIN_MOVE, movePluginSaga),
    takeEvery(DispatchActions.PLUGIN_UPDATE_CONFIG_PATH, updatePluginConfigPath),
    takeEvery(DispatchActions.PLUGIN_SET_CONFIG_PATH_VALUE, updatePluginConfigPathValue),
    takeEvery(DispatchActions.PLUGIN_CONFIG_UPDATE, updatePluginConfig),
    takeEvery(DispatchActions.PLUGIN_CONFIG_SET_PROP_VALUE, updatePluginConfigPropertyValue),
    takeEvery(DispatchActions.PLUGIN_LAYOUT_UPDATE, updatePluginLayout),
    takeEvery(DispatchActions.PLUGIN_STYLE_UPDATE, updatePluginStyle),
    takeEvery(DispatchActions.BULK_PLUGIN_UPDATE, bulkUpdatePlugins),
    takeEvery(DispatchActions.PLUGIN_RENAME, handleRenamePlugin),
    takeEvery(DispatchActions.DELETE_PLUGIN, handleDeletePlugin),

    takeEvery(DispatchActions.PLUGIN_CONFIG_EVENT_DELETE, deletePluginConfigEvent),
    takeEvery(DispatchActions.PLUGIN_CONFIG_EVENT_UPDATE, updatePluginEventConfig),

    takeEvery(DispatchActions.ADD_NEW_PAGE, handleAddPage),
    takeEvery(DispatchActions.UPDATE_PAGE_ID, handleUpdatePageId),
    takeEvery(DispatchActions.DELETE_PAGE_CONFIG, handlePageDelete),
  ]);
  yield all([takeEvery(DispatchActions.ADD_DATASOURCE_PLUGIN, addDatasourcePluginSaga)]);
}
