import {addDatasourcePlugin, DispatchAction, DispatchActions, NavigatorConfig, RecordSerializer} from 'apptile-core';
import {SagaIterator} from '@redux-saga/types';
import {normalize, schema} from 'normalizr';
import {all, call, put, takeLatest, select, putResolve, take} from 'redux-saga/effects';
import {
  closeIntegrationEditorModal,
  CREATE_APP_INTEGRATION_CREDENTIAL,
  CREATE_APP_INTEGRATION_CREDENTIAL_FAILED,
  CREATE_APP_INTEGRATION_CREDENTIAL_SUCCESS,
  fetchAppIntegrations,
  FETCH_APP_INTEGRATION,
  FETCH_APP_INTEGRATIONS,
  FETCH_APP_INTEGRATIONS_FAILED,
  FETCH_APP_INTEGRATIONS_SUCCESS,
  FETCH_APP_INTEGRATION_FAILED,
  FETCH_APP_INTEGRATION_SUCCESS,
  FETCH_INTEGRATION,
  FETCH_INTEGRATION_FAILED,
  FETCH_INTEGRATION_LIST,
  FETCH_INTEGRATION_LIST_FAILED,
  FETCH_INTEGRATION_LIST_SUCCESS,
  FETCH_INTEGRATION_SUCCESS,
  ICreateAppIntegrationCredentials,
  IFetchAppIntegrationPayload,
  IFetchIntegration,
  ISaveAppIntegrationCredentials,
  SAVE_APP_INTEGRATION_CREDENTIAL,
  SAVE_APP_INTEGRATION_CREDENTIAL_FAILED,
  SAVE_APP_INTEGRATION_CREDENTIAL_SUCCESS,
  REMOVE_APP_INTEGRATION,
  TOGGLE_DELETE_INTEGRATION_MODAL,
  fetchPage,
  softRestartConfig,
  pluginDelete,
  FetchIntegrationsPayload,
  FETCH_INTEGRATION_CATEGORIES,
  FETCH_INTEGRATION_CATEGORIES_SUCCESS,
  POPULATE_ALL_INTEGRATIONS,
  POPULATE_ALL_INTEGRATIONS_FAILED,
  APP_INTEGRATION_PAGE_SUCCESS,
} from '../actions/editorActions';
import {makeToast} from '../actions/toastActions';
import {FetchNormalizedResponse, IFetchIntegrationResponse} from '../api/ApiTypes';
import IntegrationsApi from '../api/IntegrationsApi';

import {AppConfig, PageConfig, ScreenConfig} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {pluginConfigUpdatePath} from 'apptile-core';
import _, {cloneDeep, mergeWith} from 'lodash';
import {IntegrationCodesMappingWithDataSource} from '@/root/app/plugins/datasource/constants';
import {GetRegisteredPlugin} from 'apptile-core';
import {datasourceTypeModelSel} from 'apptile-core';
import {store} from 'apptile-core';
import {updatePageIdReferencesInNav} from './editorAppConfigSaga';

const itemSchema = new schema.Entity('items');
const itemListSchema = new schema.Array(itemSchema);

function _normalizeFetchPlans(response: any): FetchNormalizedResponse<IFetchIntegrationResponse> {
  return normalize(response, itemListSchema);
}

export function* fetchAppIntegrationsHandler(action: DispatchAction<IFetchAppIntegrationPayload>): SagaIterator {
  try {
    const {appId} = action.payload;
    const result = yield call(IntegrationsApi.fetchAppIntegrations, appId);
    yield put({
      type: FETCH_APP_INTEGRATIONS_SUCCESS,
      payload: _normalizeFetchPlans(result.data),
    });
  } catch (e) {
    yield put({
      type: FETCH_APP_INTEGRATIONS_FAILED,
      payload: {
        e,
      },
    });
  }
}

export function* fetchIntegrations(action: DispatchAction<FetchIntegrationsPayload>): SagaIterator {
  try {
    const {category} = action.payload;
    const result = yield call(IntegrationsApi.fetchIntegrationList, category);

    const filteredResult = _.filter(result.data, function (o) {
      return o.category !== 'Themes';
    });

    if (_.isEmpty(category)) {
      yield put({
        type: POPULATE_ALL_INTEGRATIONS,
        payload: _normalizeFetchPlans(result.data),
      });
    }

    yield put({
      type: FETCH_INTEGRATION_LIST_SUCCESS,
      payload: _normalizeFetchPlans(filteredResult),
    });
  } catch (e) {
    yield put({
      type: FETCH_INTEGRATION_LIST_FAILED,
      payload: {
        e,
      },
    });
    yield put({
      type: POPULATE_ALL_INTEGRATIONS_FAILED,
      payload: {
        e,
      },
    });
  }
}

export function* fetchIntegrationCategories(): SagaIterator {
  try {
    const categories = yield call(IntegrationsApi.fetchIntegrationCategories);

    yield put({
      type: FETCH_INTEGRATION_CATEGORIES_SUCCESS,
      payload: {categories},
    });
  } catch (e) {
    yield put({
      type: FETCH_INTEGRATION_LIST_FAILED,
      payload: {
        e,
      },
    });
  }
}

export function* fetchAppIntegration(action: DispatchAction<IFetchAppIntegrationPayload>): SagaIterator {
  try {
    const {appId, appIntegrationId} = action.payload;
    const result = yield call(IntegrationsApi.fetchAppIntegration, appId, appIntegrationId);
    yield put({
      type: FETCH_APP_INTEGRATION_SUCCESS,
      payload: result.data,
    });
  } catch (e) {
    yield put({
      type: FETCH_APP_INTEGRATION_FAILED,
      payload: {
        e,
      },
    });
  }
}

function* updateConfig(integrationCode: string, item: any) {
  const registeredPlugin = GetRegisteredPlugin(IntegrationCodesMappingWithDataSource[integrationCode]);
  if (!registeredPlugin) {
    return;
  }
  const pluginModelSel = state => datasourceTypeModelSel(state, IntegrationCodesMappingWithDataSource[integrationCode]);
  const pluginModel = pluginModelSel(store.getState());

  yield put(
    pluginConfigUpdatePath(
      pluginModel?.get('id') ||
        registeredPlugin.options.pluginListing.labelPrefix ||
        IntegrationCodesMappingWithDataSource[integrationCode],
      null,
      ['config'],
      item,
    ),
  );
}

function* updateIntegrationConfig(credentials: any[], integrationCode: string) {
  yield all(
    Object.keys(credentials).map((item: string) => call(updateConfig, integrationCode, {[item]: credentials[item]})),
  );
}

export function* UpdateIntegrationCredentials(action: DispatchAction<ISaveAppIntegrationCredentials>): SagaIterator {
  try {
    const {appId, id, credentials, platformType} = action.payload;
    const result = yield call(IntegrationsApi.saveAppIntegrationCredentials, appId, id, credentials);
    yield put({
      type: SAVE_APP_INTEGRATION_CREDENTIAL_SUCCESS,
      payload: result.data,
    });
    yield put(
      makeToast({
        content: 'Integration updated successfully',
        appearances: 'success',
      }),
    );
    yield put(closeIntegrationEditorModal());
    yield call(updateIntegrationConfig, credentials, platformType);
  } catch (e) {
    yield put({
      type: SAVE_APP_INTEGRATION_CREDENTIAL_FAILED,
      payload: {
        e,
      },
    });
    yield put(
      makeToast({
        content: 'An error occured please try again later',
        appearances: 'error',
      }),
    );
  }
}

export function* handleApplyIntegrationPages(tempPages: any, isSoftRestart: boolean = false): SagaIterator {
  const appConfig: AppConfig = yield select(selectAppConfig);
  let newAppConfig = appConfig;
  tempPages.forEach((page: any) => {
    const pageData: any = page.currentSavedVersion.data;
    if (!_.isEmpty(pageData.modules)) {
      pageData.modules.forEach(module => {
        const {moduleUUID} = module;
        if (!newAppConfig.modules.get(moduleUUID)) {
          newAppConfig = newAppConfig.setIn(['modules', moduleUUID], module);
        }
      });
    }
    if (pageData.page) {
      const pageConfig = pageData.page as PageConfig;
      newAppConfig = newAppConfig.setIn(['pages', page.navigationInfo.screen], pageConfig);
    }

    if (!page.mappedScreen) {
      if (page.navigationInfo.isTab) {
        if (page.navigationInfo.isCenter) {
          const mainScreens = newAppConfig.getIn(['navigation', 'rootNavigator', 'screens', 'Main', 'screens']);
          const bottomTabSize = mainScreens.size;
          const middleTabIndex = Math.floor(bottomTabSize / 2);
          let leftPartTab = mainScreens.slice(0, middleTabIndex);
          const rightPartTab = mainScreens.slice(middleTabIndex, bottomTabSize);
          leftPartTab = leftPartTab.set(page.navigationInfo.name, new ScreenConfig({...page.navigationInfo}));
          const newBottomTab = leftPartTab.merge(rightPartTab);
          newAppConfig = newAppConfig.setIn(
            ['navigation', 'rootNavigator', 'screens', 'Main', 'screens'],
            newBottomTab,
          );
        } else {
          newAppConfig = newAppConfig.setIn(
            ['navigation', 'rootNavigator', 'screens', 'Main', 'screens', page.navigationInfo.name],
            new ScreenConfig({...page.navigationInfo}),
          );
        }
      } else {
        newAppConfig = newAppConfig.setIn(
          ['navigation', 'rootNavigator', 'screens', page.navigationInfo.name],
          new ScreenConfig({...page.navigationInfo}),
        );
      }
    } else {
      newAppConfig = updatePageIdReferencesInNav(newAppConfig, page.mappedScreen, page.navigationInfo.screen);
    }
  });

  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
  if (isSoftRestart) {
    yield put(softRestartConfig());
  }
}

export function* createIntegration(action: DispatchAction<ICreateAppIntegrationCredentials>): SagaIterator {
  try {
    const {appId, credentials, platformType, tempPages} = action.payload;
    const result = yield call(IntegrationsApi.createIntegration, appId, {credentials, platformType});
    yield put({
      type: CREATE_APP_INTEGRATION_CREDENTIAL_SUCCESS,
      payload: result.data,
    });
    yield put(
      makeToast({
        content: 'Integration saved successfully',
        appearances: 'success',
      }),
    );
    yield put(fetchAppIntegrations(appId));

    //add integtation pages and update dependancy graph
    yield call(handleApplyIntegrationPages, tempPages);

    const payload = {
      configType: 'datasource',
      layout: undefined,
      pluginType: IntegrationCodesMappingWithDataSource[platformType],
    };
    if (payload.pluginType) yield put(addDatasourcePlugin(payload));
    yield call(updateIntegrationConfig, credentials, platformType);
    yield put({
      type: APP_INTEGRATION_PAGE_SUCCESS,
    });
  } catch (e) {
    yield put({
      type: CREATE_APP_INTEGRATION_CREDENTIAL_FAILED,
      payload: {
        e,
      },
    });
    yield put(
      makeToast({
        content: 'An error occured please try again later',
        appearances: 'error',
      }),
    );
  } finally {
    yield put(softRestartConfig());
  }
}

export function* fetchIntegration(action: DispatchAction<IFetchIntegration>): SagaIterator {
  try {
    const {appId, integrationId} = action.payload;
    const result = yield call(IntegrationsApi.fetchIntegration, appId, integrationId);
    yield put({
      type: FETCH_INTEGRATION_SUCCESS,
      payload: result.data,
    });
  } catch (e) {
    yield put({
      type: FETCH_INTEGRATION_FAILED,
      payload: {
        e,
      },
    });
  }
}

const deleteNavigationItem = (navSelector: any[], navConfig: any, update: boolean, pageId: string) => {
  if (navSelector.length == 1 && navSelector[0] == '/') {
    return this; //Cannot delete Root Nav
  } else {
    const selPath = navSelector[0] === '/' ? navSelector.slice(1) : navSelector;
    const updatePath = ['rootNavigator'];
    selPath.forEach((val: string) => updatePath.push('screens', val));
    const screenCollectionPath = updatePath.slice(0, -1);
    const deleteNavName = updatePath[updatePath.length - 1];
    if (update) {
      return navConfig.setIn([...screenCollectionPath, pageId, 'screen'], pageId);
    } else {
      return navConfig.setIn(screenCollectionPath, navConfig.getIn(screenCollectionPath).remove(deleteNavName));
    }
  }
};

export function updatePageReferencesInNav(appConfig: AppConfig, pageId: string, path: string[]): AppConfig {
  let config: any;
  const updateNav = (navConfig: NavigatorConfig, pageId: string, path: string[], navigation: any) => {
    navConfig.screens.valueSeq().forEach(data => {
      if (data.get('name') === pageId) {
        path.push(pageId);
        if (appConfig.pages.get(pageId)) {
          config = deleteNavigationItem(path, navigation, true, pageId);
        } else {
          config = deleteNavigationItem(path, navigation, false, pageId);
        }
        return;
      }
      if (data.get('type') === 'navigator') {
        const newPath = cloneDeep(path);
        newPath.push(data.get('name'));
        return updateNav(data, pageId, newPath, navigation);
      }
    });
    return config ? config : appConfig;
  };

  return updateNav(appConfig.navigation.rootNavigator, pageId, ['/'], appConfig.navigation);
}

function* handleRemoveIntegrationPages(tempPages: any): SagaIterator {
  const appConfig: AppConfig = yield select(selectAppConfig);
  let newAppConfig = appConfig;

  tempPages.forEach((tempPage: any) => {
    const pageId = newAppConfig.pages.filter(page => page.get('pageUUID') === tempPage.id);
    const [key] = pageId;
    newAppConfig = newAppConfig.set('pages', newAppConfig.pages.delete(key[0]));
    const rootNavigator = updatePageReferencesInNav(newAppConfig, tempPage.navigationInfo.name, []);
    newAppConfig = newAppConfig.setIn(['navigation'], rootNavigator);
  });
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
}

export function* toggleAppIntegration(action: DispatchAction<any>): SagaIterator {
  try {
    const {appId, appIntegrationId, isActive, intergrationType, tempPages, credentials} = action.payload;
    const activeIntegration = yield call(IntegrationsApi.toggleAppIntegration, appId, appIntegrationId, isActive);
    const result = yield call(IntegrationsApi.fetchAppIntegrations, appId);
    yield put({
      type: FETCH_APP_INTEGRATIONS_SUCCESS,
      payload: _normalizeFetchPlans(result.data),
    });
    if (isActive) {
      yield put(
        makeToast({
          content: 'Integration re activated successfully',
          appearances: 'success',
        }),
      );
      const payload = {
        configType: 'datasource',
        layout: undefined,
        pluginType: IntegrationCodesMappingWithDataSource[intergrationType],
      };
      //add integtation pages and update dependancy graph
      yield put(fetchPage([intergrationType], true, false, false));
      if (payload.pluginType) yield put(addDatasourcePlugin(payload));
      yield call(updateIntegrationConfig, activeIntegration.data.credentials, intergrationType);
    } else {
      yield call(handleRemoveIntegrationPages, tempPages);
      yield put(pluginDelete(IntegrationCodesMappingWithDataSource[intergrationType], ''));
      yield put(softRestartConfig());
      yield put(
        makeToast({
          content: 'Integration removed successfully',
          appearances: 'success',
        }),
      );
      yield put({
        type: TOGGLE_DELETE_INTEGRATION_MODAL,
        payload: {deleteIntegrationModalVisibility: false},
      });
    }
  } catch (e) {
    yield put({
      type: FETCH_APP_INTEGRATION_FAILED,
      payload: {
        e,
      },
    });
  }
}

export default function* integrationSagas(): SagaIterator {
  yield all([
    takeLatest(FETCH_APP_INTEGRATIONS, fetchAppIntegrationsHandler),
    takeLatest(FETCH_APP_INTEGRATION, fetchAppIntegration),
    takeLatest(SAVE_APP_INTEGRATION_CREDENTIAL, UpdateIntegrationCredentials),
    takeLatest(CREATE_APP_INTEGRATION_CREDENTIAL, createIntegration),
    takeLatest(FETCH_INTEGRATION_LIST, fetchIntegrations),
    takeLatest(FETCH_INTEGRATION_CATEGORIES, fetchIntegrationCategories),
    takeLatest(FETCH_INTEGRATION, fetchIntegration),
    takeLatest(REMOVE_APP_INTEGRATION, toggleAppIntegration),
  ]);
}
