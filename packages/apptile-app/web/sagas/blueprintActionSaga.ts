import {SagaIterator} from '@redux-saga/types';
import {all, call, put, select, takeLeading} from 'redux-saga/effects';
import {DispatchAction, DispatchActions} from 'apptile-core';
import {
  EXPORT_BLUEPRINT,
  ExportBlueprintPayload,
  UPDATE_BLUEPRINT_INFO,
  UPDATE_BLUEPRINT_DEFINITION,
  GET_BLUEPRINTS,
  SET_BLUEPRINT_DATA,
} from '../actions/editorActions';
import {AppConfig, PageConfig, RecordSerializer} from 'apptile-core';
import _ from 'lodash';
import BlueprintsApi from '../api/BlueprintsApi';
import {makeToast} from '../actions/toastActions';

function* handleBlueprintExport(action: DispatchAction<ExportBlueprintPayload>): SagaIterator {
  const {blueprintDetails} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  if (!appConfig) throw 'Could not get Blueprint';
  const blueprintData = RecordSerializer.stringify(appConfig);
  try {
    const response = yield call(BlueprintsApi.createBlueprint, {...blueprintDetails, data: blueprintData});
    if (response.data.id) {
      let newAppConfig = appConfig.set('blueprintUUID', response.data.id);
      yield put({
        type: DispatchActions.UPDATE_APP_CONFIG,
        payload: newAppConfig,
      });
    }
    yield put(makeToast({content: `Blueprint "${blueprintDetails.name}" saved.`, appearances: 'success'}));
  } catch (e) {
    yield put(makeToast({content: `Error exporting blueprint "${blueprintDetails.name}" !`, appearances: 'error'}));
  }
}

function* handleBlueprintInfoSave(action: DispatchAction<ExportBlueprintPayload>): SagaIterator {
  const {blueprintDetails} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  if (!appConfig) throw 'Could not get Blueprint';
  try {
    const moduleResponse = yield call(BlueprintsApi.updateBlueprintMeta, blueprintDetails);
    yield put(makeToast({content: `Blueprint "${blueprintDetails.name}" info saved.`, appearances: 'success'}));
  } catch (e) {
    yield put(makeToast({content: `Error exporting blueprint "${blueprintDetails.name}" !`, appearances: 'error'}));
  }
}

function* handlePageUpdate(action: DispatchAction<ExportBlueprintPayload>): SagaIterator {
  const {blueprintDetails} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  if (!appConfig) throw 'Could not get Blueprint';
  let newAppConfig = appConfig.set('blueprintUUID', blueprintDetails.id);
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
  const blueprintData = RecordSerializer.stringify(appConfig);
  try {
    const moduleResponse = yield call(BlueprintsApi.updateBlueprint, {...blueprintDetails, data: blueprintData});
    yield put(makeToast({content: `Blueprint "${blueprintDetails.name}" saved.`, appearances: 'success'}));
  } catch (e) {
    yield put(makeToast({content: `Error exporting blueprint "${blueprintDetails.name}" !`, appearances: 'error'}));
  }
}

function* handleGetBlueprints(action: DispatchAction<any>): SagaIterator {
  try {
    const moduleResponse = yield call(BlueprintsApi.getAllBlueprints, [], 0, 20);
    const items = _.get(moduleResponse, ['data', 'items'], []);
    yield put({
      type: SET_BLUEPRINT_DATA,
      payload: items,
    });
  } catch (e) {}
}

export default function* BlueprintsActionsSaga(): SagaIterator {
  yield all([
    takeLeading(EXPORT_BLUEPRINT, handleBlueprintExport),
    takeLeading(UPDATE_BLUEPRINT_INFO, handleBlueprintInfoSave),
    takeLeading(UPDATE_BLUEPRINT_DEFINITION, handlePageUpdate),
    takeLeading(GET_BLUEPRINTS, handleGetBlueprints),
  ]);
}
