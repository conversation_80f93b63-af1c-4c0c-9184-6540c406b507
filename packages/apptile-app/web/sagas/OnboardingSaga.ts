import {<PERSON>Iterator} from '@redux-saga/types';
import {all, put, takeLatest, call, takeLeading, select, delay} from 'redux-saga/effects';
import {FETCH_ORGS, saveAppState, softRestartConfig} from '../actions/editorActions';

import {
  CHANGE_ONBOARDING_METADATA,
  CURRENCY_CONVERT_SALES_AMOUNT,
  END_FETCH_BRAND,
  FETCH_BRAND,
  FETCH_ONBOARDING_METADATA,
  FILL_MANDATORY_FIELDS,
  SET_BRAND_DATA,
  SET_ONBOARDING_METADATA,
  SET_STORE_DETAILS,
  START_FETCH_BRAND,
  UPDATE_BASIC_APP_INFO,
  UPDATE_ONBOARDING_STATUS_DONE,
  UPDATE_STORE_DETAILS,
  WEBFLOW_API_CALLS_ERROR,
  WEBFLOW_API_CALLS_FINISHED,
  WEBFLOW_API_CALLS_LOADING,
  WEBFLOW_API_CALLS_START,
  fillingMandtoryFinished,
} from '../actions/onboardingActions';
import AppApi from '../api/AppApi';
import <PERSON><PERSON><PERSON> from '../webflowApi/CollectionsApi';
import _ from 'lodash';
import {getShopifyObjectCache} from '../integrations/shopify/ShopifyObjectCache';
import {selectAppConfig} from 'apptile-core';
import {selectMandatoryCheck} from '../selectors/EditorModuleSelectors';
import {AppConfig, ModuleEditorConfig} from 'apptile-core';
import Immutable from 'immutable';
import {NAMESPACE_SPERATOR} from 'apptile-core';
import {DispatchActions} from 'apptile-core';
import OnboardingApi from '../api/onboardingApi';
import BrandApi from '../api/BrandApi';
import {selectAppModel} from 'apptile-core';
import {EditorRootState} from '../store/EditorRootState';

export function* setStoreName(action: any): SagaIterator {
  const payload = action.payload;

  yield put({
    type: SET_STORE_DETAILS,
    payload,
  });
}

export function* updateBasicAppInfo(action: any): SagaIterator {
  const {appId, infoObject} = action.payload;

  try {
    yield call(AppApi.updateBasicAppInfo, appId, infoObject);

    //fetch the updated app name from the db
    if (infoObject.name) {
      yield put({
        type: FETCH_ORGS,
      });
    }
    //Now update the status
    yield put({
      type: UPDATE_ONBOARDING_STATUS_DONE,
      payload: {...infoObject, nameChanged: !!infoObject.name},
    });
  } catch (error) {
    logger.error(error);
  }
}

export function* callWebflowApis(): SagaIterator {
  const collections = yield call(CollectionsApi.getCollections);
  const Themes = collections.data?.find((e: any) => e.slug == 'theme')?._id;
  const Tags = collections.data?.find((e: any) => e.slug == 'theme-tags')?._id;
  const Stats = collections.data?.find((e: any) => e.slug == 'theme-stats')?._id;
  const Integrations = collections.data?.find((e: any) => e.slug == 'integrations')?._id;
  const Benifits = collections.data?.find((e: any) => e.slug == 'theme-benefits')?._id;
  const Preview = collections.data?.find((e: any) => e.slug == 'theme-preview')?._id;

  try {
    //Start Webflow Apis Calls
    yield put({
      type: WEBFLOW_API_CALLS_LOADING,
    });

    const tagsResponse = yield call(CollectionsApi.getCollectionItems, Tags);
    const themesResponse = yield call(CollectionsApi.getCollectionItems, Themes);
    //filtering themes based on the 'theme-selectable' field.
    themesResponse.data.items = _.filter(themesResponse.data.items, t => t['theme-selectable'] === true);
    themesResponse.data.items = _.sortBy(themesResponse.data.items, ['custom-order']);

    const statsResponse = yield call(CollectionsApi.getCollectionItems, Stats);
    const integrationsResponse = yield call(CollectionsApi.getCollectionItems, Integrations);
    // Create a copy of the data property of the integrationsResponse object
    const filteredIntegrationsResponse = { ...integrationsResponse.data }

    // Only include items that have a truthy 'new-visual-merchandize' property and a truthy 'integration-code' property
    filteredIntegrationsResponse.items = filteredIntegrationsResponse.items.filter((i:any) => Boolean(i['new-visual-merchandize']) && (i['integration-code']))
    const benifitsResponse = yield call(CollectionsApi.getCollectionItems, Benifits);
    const previewsResponse = yield call(CollectionsApi.getCollectionItems, Preview);
    //Webflow Api Calls Finished
    yield put({
      type: WEBFLOW_API_CALLS_FINISHED,
      payload: {
        themes: themesResponse.data,
        tags: tagsResponse.data,
        stats: statsResponse.data,
        integrations: filteredIntegrationsResponse,
        benifits: benifitsResponse.data,
        preview: previewsResponse.data,
        loading: false,
      },
    });
  } catch (error) {
    yield put({
      type: WEBFLOW_API_CALLS_ERROR,
    });

    logger.error(error);
  }
}

export function* fillMandtoryFields(action): SagaIterator {
  const {onboarding, appId, saveApp} = action.payload;
  let countReload = 0;
  try {
    const appConfig = yield select(selectAppConfig);
    let newAppConfig = yield select(selectAppConfig);
    let newAppModel = yield select(selectAppModel);
    const mandatoryFields = yield select(selectMandatoryCheck());
    const getItems = function* () {
      countReload += 1;
      const productListRaw = yield (yield getShopifyObjectCache()).getProductsList();
      const collectionsListRaw = yield (yield getShopifyObjectCache()).getCollectionsList();
      const blogsListRaw = yield (yield getShopifyObjectCache()).getBlogsList();
      const imagesListRaw = yield (yield getShopifyObjectCache()).getImagesList();
      const productsLoaded = yield (yield getShopifyObjectCache()).getConfigValue('productsLoaded');
      const collectionsLoaded = yield (yield getShopifyObjectCache()).getConfigValue('collectionsLoaded');
      const blogsLoaded = yield (yield getShopifyObjectCache()).getConfigValue('blogsLoaded');
      const imagesLoaded = yield (yield getShopifyObjectCache()).getConfigValue('imagesLoaded');
      if (!productsLoaded || !collectionsLoaded || !blogsLoaded || !imagesLoaded) {
        if (countReload < 5) {
          yield delay(3000);
          yield getItems();
        } else {
          yield put(fillingMandtoryFinished(true));
          if (onboarding) {
            yield put({
              type: UPDATE_BASIC_APP_INFO,
              payload: {appId, infoObject: {isOnboarded: true}},
            });
          }
          return 'null';
        }
      } else {
        const productList = productListRaw.filter((e: any) => e?.featuredImage);
        const collectionsList = collectionsListRaw.filter(
          (e: any) => e?.image?.url && (!_.isNil(e?.productsCount) ? e?.productsCount > 0 : true),
        );
        const blogsList = blogsListRaw.filter((e: any) => e?.post_count);
        let productListIndex = 0;
        let collectionsListIndex = 0;
        let blogsListIndex = 0;
        const getLatestIndex = (type: string) => {
          if (type === 'product') {
            productListIndex += 1;
            if (productListIndex >= productList.length) {
              productListIndex = 0;
            }
            return productListIndex;
          } else if (type === 'blog') {
            blogsListIndex += 1;
            if (blogsListIndex >= blogsList.length) {
              blogsListIndex = 0;
            }
            return blogsListIndex;
          } else {
            collectionsListIndex += 1;
            if (collectionsListIndex >= collectionsList.length) {
              collectionsListIndex = 0;
            }
            return collectionsListIndex;
          }
        };
        const autoFetchedUrl =
          imagesListRaw.length >= 2
            ? imagesListRaw[3]?.url ||
              imagesListRaw[4]?.url ||
              'https://cdn.apptile.io/23a145ba-da98-4d6d-8c06-1a597b7a4a2f/19c387fc-19f0-486e-a4c8-e5352b3bf46e/original.png'
            : imagesListRaw[1]?.url ||
              imagesListRaw[0]?.url ||
              'https://cdn.apptile.io/23a145ba-da98-4d6d-8c06-1a597b7a4a2f/19c387fc-19f0-486e-a4c8-e5352b3bf46e/original.png';
        for (let i in mandatoryFields?.pages) {
          const pageConfig = appConfig.get('pages').get(i);
          let newPageConfig = newAppConfig.get('pages').get(i);
          mandatoryFields?.pages[i]?.plugins?.map((pluginId: string) => {
            const pluginConfig = pageConfig.get('plugins').get(pluginId);
            const moduleUUID = pluginConfig?.config?.get('moduleUUID');
            const moduleRecord = appConfig?.modules?.get(moduleUUID);
            const editors = moduleRecord?.editors;
            const styleEditors = moduleRecord?.styleEditors;
            const basicEditors = moduleRecord?.basicEditors;
            let mandatoryEditors: Immutable.OrderedMap<string, ModuleEditorConfig> = Immutable.OrderedMap();
            styleEditors?.map((editorRecord: ModuleEditorConfig, key: string) => {
              if (editorRecord.mandatory) {
                mandatoryEditors = mandatoryEditors.set(key, editorRecord);
              }
            });
            basicEditors?.map((editorRecord: ModuleEditorConfig, key: string) => {
              if (editorRecord.mandatory) {
                mandatoryEditors = mandatoryEditors.set(key, editorRecord);
              }
            });
            editors?.map((editorRecord: ModuleEditorConfig, key: string) => {
              if (editorRecord.mandatory) {
                mandatoryEditors = mandatoryEditors.set(key, editorRecord);
              }
            });
            const childNamespace = pluginConfig?.config?.get('childNamespace');
            const pagePlugins = pageConfig?.get('plugins');
            let newPagePlugins = newPageConfig?.get('plugins');
            mandatoryEditors?.map((editorRecord: ModuleEditorConfig) => {
              const selector = _.clone(editorRecord.selector);
              selector[0] = childNamespace + NAMESPACE_SPERATOR + selector[0];
              const currentPlugin = pagePlugins?.get(selector[0]);
              const editorType = editorRecord?.editorType;
              const storedValue = pagePlugins?.getIn(selector);
              if (editorType?.type == 'assetEditor') {
                const baseSelector = selector.slice(0, -1);
                const sourceTypeProperty = pagePlugins?.getIn([...baseSelector, editorType?.props?.sourceTypeProperty]);
                if (sourceTypeProperty?.toLowerCase() == 'url') {
                  selector[selector.length - 1] = editorType?.props?.urlProperty;
                } else {
                  selector[selector.length - 1] = editorType?.props?.assetProperty;
                }
                const storedValue = pagePlugins?.getIn(selector);
                if (!storedValue) {
                  newPagePlugins = newPagePlugins?.setIn(
                    [...baseSelector, editorType?.props?.sourceTypeProperty],
                    'url',
                  );
                  newPagePlugins = newPagePlugins?.setIn(
                    [...baseSelector, editorType?.props?.urlProperty],
                    autoFetchedUrl,
                  );
                }
              } else if (editorType?.type == 'listEditor') {
                const shopifyType = editorType?.props?.shopifyType;
                const newStoredValue = storedValue?.map((e: any, i: number) => {
                  const item =
                    shopifyType === 'Product'
                      ? productList[getLatestIndex('product')]
                      : collectionsList[getLatestIndex('collection')];
                  if (!item) return e;
                  e = {
                    ...e,
                    url: shopifyType === 'Product' ? item?.featuredImage : item['image']?.url,
                    navEntityId: item?.handle,
                    navEntityType: shopifyType === 'Product' ? 'Product' : 'Collection',
                    resizeMode: 'cover',
                    title: item?.title,
                  };
                  return e;
                });
                newPagePlugins = newPagePlugins?.setIn(selector, newStoredValue);
              } else if (editorType?.type == 'shopifyCollectionHandleControl' && collectionsList.length > 0) {
                newPagePlugins = newPagePlugins?.setIn(selector, collectionsList[getLatestIndex('collection')].handle);
              } else if (editorType?.type == 'shopifyProductHandleControl' && productList.length > 0) {
                newPagePlugins = newPagePlugins?.setIn(selector, productList[getLatestIndex('product')].handle);
              } else if (editorType?.type == 'shopifyBlogHandleControl' && blogsList.length > 0) {
                newPagePlugins = newPagePlugins?.setIn(selector, blogsList[getLatestIndex('blog')].handle);
              } else if (editorType?.type == 'shopifyBlogControl' && blogsList.length > 0) {
                newPagePlugins = newPagePlugins?.setIn(
                  selector,
                  `{{${JSON.stringify(blogsList[getLatestIndex('blog')])}}}`,
                );
              } else if (currentPlugin?.type == 'widget') {
                const widgetType = currentPlugin?.subtype;
                if (widgetType == 'VideoPlayerWidget') {
                  const baseSelector = selector.slice(0, -1);
                  newPagePlugins = newPagePlugins?.setIn(
                    [...baseSelector, 'value'],
                    'https://res.cloudinary.com/dxtmp1uf3/video/upload/v1697202245/VideoDemo_eb167r.mov',
                  );
                } else if (widgetType == 'WebViewWidget') {
                  const baseSelector = selector.slice(0, -1);
                  if (pageConfig.pageId == 'PrivacyPolicy') {
                    newPagePlugins = newPagePlugins?.setIn(
                      [...baseSelector, 'value'],
                      newAppModel?.jsModel?.shopify?.shop?.privacyPolicy?.url ?? '',
                    );
                  } else if (pageConfig.pageId == 'RefundPolicy') {
                    newPagePlugins = newPagePlugins?.setIn(
                      [...baseSelector, 'value'],
                      newAppModel?.jsModel?.shopify?.shop?.refundPolicy?.url ?? '',
                    );
                  } else if (pageConfig.pageId == 'TermsOfService') {
                    newPagePlugins = newPagePlugins?.setIn(
                      [...baseSelector, 'value'],
                      newAppModel?.jsModel?.shopify?.shop?.termsOfService?.url ?? '',
                    );
                  }
                }
              }
            });
            newPageConfig = newPageConfig?.set('plugins', newPagePlugins);
          });
          newAppConfig = newAppConfig.setIn(['pages', i], newPageConfig);
        }
        yield put({
          type: DispatchActions.UPDATE_APP_CONFIG,
          payload: newAppConfig,
        });
        yield put(softRestartConfig());
        yield put(fillingMandtoryFinished(false));
        if (saveApp) {
          yield put(saveAppState());
        }
        if (onboarding) {
          yield put({
            type: UPDATE_BASIC_APP_INFO,
            payload: {appId, infoObject: {isOnboarded: true}},
          });
        }
        return '';
      }
    };
    yield getItems();
  } catch (error) {
    logger.error(error);
    yield put(fillingMandtoryFinished(true));
    if (onboarding) {
      yield put({
        type: UPDATE_BASIC_APP_INFO,
        payload: {appId, infoObject: {isOnboarded: true}},
      });
    }
  }
}

export function* changeOnboardingMetadata(action: any): SagaIterator {
  const {metadata} = action.payload;
  const appId: string = yield select((state: EditorRootState) => state.apptile.appId);

  try {
    if (appId) {
      const onboardingData = yield call(OnboardingApi.saveOnboardingData, appId, metadata);
      yield put({
        type: SET_ONBOARDING_METADATA,
        payload: onboardingData.data.metadata,
      });
    } else {
      throw new Error('App id not found');
    }
  } catch (error) {
    logger.error(error);
  }
}

export function* fetchOnboardingMetadata(action: any): SagaIterator {
  const appId = action.payload;

  try {
    const onboardingData = yield call(OnboardingApi.fetchOnboardingData, appId);
    yield put({
      type: SET_ONBOARDING_METADATA,
      payload: onboardingData.data.metadata,
    });
  } catch (error) {
    logger.error(error);
  }
}

export function* fetchBrand(action: any): SagaIterator {
  const domain = action.payload;

  try {
    yield put({
      type: START_FETCH_BRAND,
    });
    const brandData = yield call(BrandApi.fetchBrandData, domain);

    yield put({
      type: SET_BRAND_DATA,
      payload: brandData.data,
    });

    yield put({
      type: CURRENCY_CONVERT_SALES_AMOUNT,
      payload: {domain},
    });
  } catch (error) {
    logger.error('onboarding error: ', error);
    yield put({
      type: END_FETCH_BRAND,
    });
  }
}

export function* currencyConvertSalesAmount(action: any): SagaIterator {
  try {
    const convertedBrandData = (yield call(BrandApi.currencyConvertSalesData, action.payload?.domain))?.data;
    yield put({
      type: SET_BRAND_DATA,
      payload: convertedBrandData,
    });
  } catch (error) {
    logger.error(error);
  }
}

export default function* onboardingSagas(): SagaIterator {
  yield all([
    takeLatest(UPDATE_STORE_DETAILS, setStoreName),
    takeLatest(UPDATE_BASIC_APP_INFO, updateBasicAppInfo),
    takeLatest(FETCH_BRAND, fetchBrand),
    takeLatest(WEBFLOW_API_CALLS_START, callWebflowApis),
    takeLeading(FILL_MANDATORY_FIELDS, fillMandtoryFields),
    takeLatest(CHANGE_ONBOARDING_METADATA, changeOnboardingMetadata),
    takeLatest(FETCH_ONBOARDING_METADATA, fetchOnboardingMetadata),
    takeLatest(CURRENCY_CONVERT_SALES_AMOUNT, currencyConvertSalesAmount),
  ]);
}
