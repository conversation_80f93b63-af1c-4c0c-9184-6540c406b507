import {SagaIterator} from '@redux-saga/types';
import {all, call, put, select, takeEvery, takeLatest, takeLeading} from 'redux-saga/effects';
import {DispatchAction, DispatchActions, selectAppConfig} from 'apptile-core';
import {
  ExportTilePayload,
  EXPORT_TILE,
  fetchedTileRecord,
  fetchTile,
  FetchTilePayload,
  FETCH_TILE,
  GET_INTEGRATION_TILES,
  SET_TEMP_TILES_DATA,
  UPDATE_TILE_DEFINITION,
  UPDATE_TILE_INFO,
  EXPORT_TILE_VARIANT,
  UPDATE_TILE_VARIANT_INFO,
  UPDATE_TILE_VARIANT_DEFINITION,
  ExportTileVariantPayload,
} from '../actions/editorActions';
import {AppConfig, ModuleRecord, RecordSerializer} from 'apptile-core';
import _, {get} from 'lodash';
import TilesApi from '../api/TilesApi';
import {makeToast} from '../actions/toastActions';
import {selectTilesCache} from '../selectors/TileSelector';
import {TilesCacheType} from '../common/webDatatypes';
import {selectModuleByUUID} from 'apptile-core';

function* handleTileExport(action: DispatchAction<ExportTilePayload>): SagaIterator {
  const {moduleUUID, tileDetails} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  let moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);
  if (!moduleRecord) throw 'Cannot Find Parent Module';
  moduleRecord = moduleRecord.set('moduleName', tileDetails?.name).set('tags', tileDetails?.tags);
  let newAppConfig = appConfig.setIn(['modules', moduleUUID], moduleRecord);
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
  const moduleData = RecordSerializer.stringify(moduleRecord);
  try {
    const moduleResponse = yield call(TilesApi.createTileTemplate, {...tileDetails, data: moduleData});
    yield put(makeToast({content: `Tile "${moduleRecord.moduleName}" saved.`, appearances: 'success'}));
  } catch (e) {
    yield put(makeToast({content: `Error exporting tile "${moduleRecord.moduleName}" !`, appearances: 'error'}));
  }
}

function* handleTileInfoSave(action: DispatchAction<ExportTilePayload>): SagaIterator {
  const {moduleUUID, tileDetails} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  let moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);
  if (!moduleRecord) throw 'Cannot Find Parent Module';
  moduleRecord = moduleRecord.set('moduleName', tileDetails?.name).set('tags', tileDetails?.tags);
  let newAppConfig = appConfig.setIn(['modules', moduleUUID], moduleRecord);
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
  try {
    const moduleResponse = yield call(TilesApi.updateTileMeta, tileDetails);
    yield put(makeToast({content: `Tile "${moduleRecord.moduleName}" info saved.`, appearances: 'success'}));
  } catch (e) {
    yield put(makeToast({content: `Error exporting tile "${moduleRecord.moduleName}" !`, appearances: 'error'}));
  }
}

function* handleTileUpdate(action: DispatchAction<ExportTilePayload>): SagaIterator {
  const {moduleUUID, tileDetails} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  let moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);
  if (!moduleRecord) throw 'Cannot Find Parent Module';
  moduleRecord = moduleRecord
    .set('moduleName', tileDetails?.name)
    .set('tags', tileDetails?.tags)
    .set('moduleUUID', tileDetails?.id);
  let newAppConfig = appConfig.setIn(['modules', tileDetails?.id], moduleRecord);
  const moduleData = RecordSerializer.stringify(moduleRecord);
  try {
    const moduleResponse = yield call(TilesApi.updateTileTemplate, {
      ...tileDetails,
      data: moduleData,
      oldId: moduleUUID,
    });
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield put(makeToast({content: `Tile "${moduleRecord.moduleName}" saved.`, appearances: 'success'}));
  } catch (e) {
    yield put(makeToast({content: `Error exporting tile "${moduleRecord.moduleName}" !`, appearances: 'error'}));
  }
}

function* handleTileVariantExport(action: DispatchAction<ExportTileVariantPayload>): SagaIterator {
  const {moduleUUID, variantDetails} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  let moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);
  if (!moduleRecord) throw 'Cannot Find Parent Module';
  const moduleData = RecordSerializer.stringify(moduleRecord);
  try {
    const moduleResponse = yield call(TilesApi.createTileVariantTemplate, {...variantDetails, data: moduleData});
    yield put(
      makeToast({
        content: `Variant "${variantDetails.name}" for "${moduleRecord.moduleName}" saved.`,
        appearances: 'success',
      }),
    );
  } catch (e) {
    yield put(
      makeToast({
        content: `Error exporting Variant "${variantDetails.name}" for "${moduleRecord.moduleName}" !`,
        appearances: 'error',
      }),
    );
  }
}

function* handleTileVariantInfoSave(action: DispatchAction<ExportTileVariantPayload>): SagaIterator {
  const {moduleUUID, variantDetails} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  let moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);
  if (!moduleRecord) throw 'Cannot Find Parent Module';
  try {
    const moduleResponse = yield call(TilesApi.updateTileVariantMeta, {...variantDetails});
    yield put(
      makeToast({
        content: `Variant "${variantDetails.name}" for "${moduleRecord.moduleName}" info saved.`,
        appearances: 'success',
      }),
    );
  } catch (e) {
    yield put(
      makeToast({
        content: `Error saving info for Variant "${variantDetails.name}" for "${moduleRecord.moduleName}" !`,
        appearances: 'error',
      }),
    );
  }
}

function* handleTileVariantUpdate(action: DispatchAction<ExportTileVariantPayload>): SagaIterator {
  const {moduleUUID, variantDetails} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  let moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);
  if (!moduleRecord) throw 'Cannot Find Parent Module';
  const moduleData = RecordSerializer.stringify(moduleRecord);
  try {
    const moduleResponse = yield call(TilesApi.updateTileVariantTemplate, {
      ...variantDetails,
      data: moduleData,
    });
    yield put(
      makeToast({
        content: `Variant "${variantDetails.name}" for "${moduleRecord.moduleName}" saved.`,
        appearances: 'success',
      }),
    );
  } catch (e) {
    yield put(
      makeToast({
        content: `Error saving Variant "${variantDetails.name}" for "${moduleRecord.moduleName}" !`,
        appearances: 'error',
      }),
    );
  }
}

function* fetchTileDetails(action: DispatchAction<FetchTilePayload>): SagaIterator {
  const {moduleUUID, localDefinition, setLocalDefinition = false} = action.payload;
  const tilesCacheState: TilesCacheType = yield select(selectTilesCache);
  try {
    if (!tilesCacheState.isTileCached(moduleUUID)) {
      let moduleConfig: ModuleRecord;
      if (localDefinition) {
        moduleConfig = yield select(state => selectModuleByUUID(state, moduleUUID));
      } else {
        const moduleResponse = yield call(TilesApi.getTemplate, moduleUUID);
        const tileData = moduleResponse.data?.currentSavedVersion?.data;
        moduleConfig = RecordSerializer.parse(tileData);
      }
      if (moduleConfig) {
        if (setLocalDefinition) {
          const appConfig = yield select(selectAppConfig);
          let newAppConfig = appConfig.setIn(['modules', moduleUUID], moduleConfig);
          yield put({
            type: DispatchActions.UPDATE_APP_CONFIG,
            payload: newAppConfig,
          });
        }
        yield put(fetchedTileRecord(moduleUUID, moduleConfig));
        // yield put(makeToast({content: `Updated tile details for ${moduleConfig?.moduleName}`, appearances: 'info'}));
        if (moduleConfig.moduleConfig) {
          for (const [pid, pluginConfig] of moduleConfig.moduleConfig) {
            if (pluginConfig.subtype === 'ModuleInstance') {
              yield put(fetchTile(pluginConfig.config.get('moduleUUID'), localDefinition));
            }
          }
        }
      }
    }
  } catch (e) {
    yield put(makeToast({content: `Error fetching tile details !`, appearances: 'error'}));
  }
}

function* fetchIntegrationTiles(action: DispatchAction<any>): SagaIterator {
  const {tags} = action.payload;
  try {
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    const pages = appConfig.get('pages');

    const moduleResponse = yield call(TilesApi.getTiles, tags, 0, 50);
    const tiles = get(moduleResponse, ['data', 'items'], []);
    let tempTiles: any = {data: {}, totalTiles: 0};
    tiles.forEach((tile: any) => {
      pages.forEach((value, pageKey) => {
        const plugins = value.get('plugins');
        if (plugins) {
          const modulePlugins = plugins.filter((value, key) => value.subtype === 'ModuleInstance');
          if (modulePlugins) {
            modulePlugins.forEach((value, key) => {
              if (value.getIn(['config', 'moduleUUID']) === tile.id) {
                tempTiles.data[pageKey] = tempTiles.data[pageKey]
                  ? [tempTiles.data[pageKey].push(tile.name)]
                  : [tile.name];
                tempTiles.totalTiles = tempTiles.totalTiles + 1;
                return tempTiles;
              }
            });
          }
        }
      });
    });
    yield put({
      type: SET_TEMP_TILES_DATA,
      payload: tempTiles,
    });
  } catch (e) {
    yield put(makeToast({content: `Error fetching tile details !`, appearances: 'error'}));
  }
}

export default function* TilesActionsSaga(): SagaIterator {
  yield all([
    takeLeading(EXPORT_TILE, handleTileExport),
    takeLeading(UPDATE_TILE_INFO, handleTileInfoSave),
    takeLeading(UPDATE_TILE_DEFINITION, handleTileUpdate),
    takeLeading(EXPORT_TILE_VARIANT, handleTileVariantExport),
    takeLeading(UPDATE_TILE_VARIANT_INFO, handleTileVariantInfoSave),
    takeLeading(UPDATE_TILE_VARIANT_DEFINITION, handleTileVariantUpdate),
    takeEvery(FETCH_TILE, fetchTileDetails),
    takeEvery(GET_INTEGRATION_TILES, fetchIntegrationTiles),
  ]);
}
