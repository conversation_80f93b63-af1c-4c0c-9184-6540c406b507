import {addNamespace, EventHandlerConfig, LayoutRecord, pluginConfigSetPathValue} from 'apptile-core';
import {GetRegisteredPlugin, DependencyGraph} from 'apptile-core';
import {configProcessor} from '@/root/web/common/configProcessor';
import {SagaIterator} from '@redux-saga/types';
import Immutable from 'immutable';
import _ from 'lodash';
import {all, call, delay, put, select, take, takeEvery, takeLatest} from 'redux-saga/effects';
import {
  bulkPluginUpdates,
  NavComponentDelete,
  NavUpdateName,
  pluginConfigEventUpdate,
  pluginConfigUpdate,
  pluginConfigUpdatePath,
  UpdatePageId,
} from 'apptile-core';
import {DispatchAction, DispatchActions, DispatchEmptyAction, selectPlugin} from 'apptile-core';
import {
  AppConfig,
  AppModelType,
  IDatasourceCredentialTypes,
  ImageConfigParams,
  ImageRecord,
  ImmutableMapType,
  ModuleRecord,
  PageConfig,
  PluginConfig,
  PluginNamespaceImpl,
  RecordSerializer,
} from 'apptile-core';
import {SerializedBindings} from 'apptile-core';
import {isContainerTypeWidget} from 'apptile-core';
import {getModuleDependencies, getPluginsContained} from 'apptile-core';
import {sortPluginsByContainerDepth} from 'apptile-core';
import {selectAppState, selectEditorState} from 'apptile-core';
import {pageConfigsSelector, selectAppConfig, selectPluginConfig} from 'apptile-core';
import {selectAppModel, selectPageModelInitState, selectPreInitModel} from 'apptile-core';
import {apptileState} from 'apptile-core';
import AppConfigApi from '../../web/api/AppConfigApi';
import * as EditorActions from '../actions/editorActions';
import {makeToast, removeToast} from '../actions/toastActions';
import AppApi from '../api/AppApi';
import IntegrationsApi from '../api/IntegrationsApi';
import {copyToClipboard} from '../common/utils';
import {AssetReduxState} from '../store/AssetReducer';
import {EditorRootState} from '../store/EditorRootState';
import {validateNaming} from 'apptile-core';
import {getNavigationContext} from 'apptile-core';
import BlueprintsApi from '../api/BlueprintsApi';
import {
  FILLING_MANDATORY_FINISHED,
  UPDATE_ONBOARDING_STATUS_DONE,
  fillMandtoryFields,
} from '../actions/onboardingActions';
import {modelUpdateAction} from 'apptile-core';
import {v4 as uuid} from 'uuid';
import {selectScreensInNav, selectSelectedPluginConfig} from '../selectors/EditorSelectors';
import Analytics from '@/root/web/lib/segment';
import {selectModuleByUUID} from 'apptile-core';
import {allAvailablePlans, allAvailablePlansOrdered} from 'apptile-core';
import AnalyticsApi from '../api/AnalyticsApi';
import {currentPlanFeaturesSelector} from '../selectors/FeatureGatingSelector';
import {getPluginModelStylesFromConfig} from 'apptile-core';
import {selectApptileThemeModel, themeEvaluator} from 'apptile-core';
import {addPluginConfig, generateUniquePluginId, pluginDelete} from './editorAppConfigSaga';
import SnapshotsApi from '../api/SnapshotsApi';
import {selectOnboardingStatus} from '../selectors/OnboardingSelector';
import {initPlugins} from '@/root/app/plugins/initPlugins';
import {handleApplyIntegrationPages} from './IntegrationSaga';
import {selectModulePluginsInPage} from '../selectors/EditorModuleSelectors';
import TilesApi from '../api/TilesApi';

let progressIds = [] as string[];

export function* appSaveSaga(action: DispatchAction<EditorActions.SaveAppPayload>): SagaIterator {
  const beforeunloadListener = (event: BeforeUnloadEvent) => {
    event.preventDefault(); // Cancel the navigation
    event.returnValue = ''; // Required for older browsers
    return 'You have unsaved changes. Are you sure you want to leave this page?';
  };
  window.addEventListener('beforeunload', beforeunloadListener);

  const saveProgressToastId = uuid();
  progressIds.push(saveProgressToastId); // persisting the toastIds incase this function execution halts in the middle and a new function call takes over again[happens with takeLatest(appEditor Saga)]
  const publishProgressToastId = uuid();
  try {
    const {newSave, remark} = action.payload;
    let {backup = false} = action.payload;
    const apptile: apptileState = yield select((state: EditorRootState) => state.apptile);
    const appConfig: AppConfig = yield select(state => state.appConfig.current);

    const processedAppConfig = configProcessor(appConfig);

    // if(!force){
    //   const appSaveMetaData = yield call(AppConfigApi.fetchAppSaveMeta, apptile.appId, apptile.appSaveId);
    //   if(appSaveMetaData.data){
    //     if(new Date(apptile.appSaveUpdatedAt).getTime() < new Date(appSaveMetaData.data.updatedAt).getTime()){
    //       processedAppConfig.errors.push({heading: 'Newer App Save Found',description: `This app was updated on ${new Date(appSaveMetaData.data.updatedAt)}. Please refresh before saving. If this happens again, please contact us.`})
    //     }
    //   }else{
    //     throw new Error('App save meta data not available');
    //   }
    // }

    if (processedAppConfig.errors && processedAppConfig.errors.length > 0) {
      yield put(
        makeToast({
          content: processedAppConfig.errors.map((e: any) => `${e.heading}\n${e.description}`).join('\n\n'),
          appearances: 'error',
          duration: 5000,
        }),
      );
    } else {
      let appConfigData = processedAppConfig.value;
      yield put({type: DispatchActions.IS_APP_SAVING});

      if (action.payload.showToast) {
        yield put(
          makeToast({id: saveProgressToastId, content: 'Saving your work...', appearances: 'info', cancellable: false}),
        );
      }
      if (!window.CompressionStream) {
        yield put(makeToast({content: 'Browser Not Supported', appearances: 'error'}));
        throw new Error('Browser Not Supported');
      }
      appConfigData = yield call(clearPageModelCache, appConfigData);

      const appSaveCommitResponse = yield call(
        AppConfigApi.saveAppConfig,
        apptile.appId,
        apptile.forkId,
        apptile.appBranch,
        apptile.appSaveId,
        RecordSerializer.stringify(appConfigData),
        remark,
        backup,
      );
      const appSaveCommit = appSaveCommitResponse?.data;
      put({
        type: EditorActions.APP_SAVE_SUCCESS,
        payload: {
          appSaveId: appSaveCommit?.id,
        },
      });
      if (action.payload.showToast) {
        yield put(removeToast(progressIds));
        yield put(makeToast({content: 'App design saved!', appearances: 'success'}));
      }
      if (newSave) {
        yield put(
          makeToast({
            id: publishProgressToastId,
            content: 'Pushing to end users...',
            appearances: 'info',
            cancellable: false,
          }),
        );
        // NOTE: The pureModel is inited during startup and does not include changes done during the current session.
        // the obvious way to rebuild depgraph and binding caches is to restart the App so the initial boot sequence runs,
        // generating the fresh and updated depgraph and compiled bindings cache.
        yield call(softRestartConfig);
        yield take(DispatchActions.INITIALIZE_STAGE_MODEL);
        appConfigData = yield call(generatePageModelCache, appConfigData);
        // const previousPublishes = yield call(AppConfigApi.getAllPublishedIds, apptile.appId);
        const appSavePublishResponse = yield call(
          AppConfigApi.publishAppConfig,
          apptile.appId,
          apptile.forkId,
          apptile.appBranch,
          RecordSerializer.stringify(appConfigData),
          appSaveCommit?.id,
          remark,
        );
        const appPublishCommit = appSavePublishResponse?.data;
        let publishResult;
        if (appPublishCommit?.id) {
          publishResult = yield call(
            AppConfigApi.publishCommit,
            apptile.appId,
            apptile.forkId,
            apptile.appBranch,
            appPublishCommit?.id,
          );
        }
        // const verificationResult = yield call(AppConfigApi.verifyPublish, apptile.appId, previousPublishes.data);
        appConfigData = yield call(clearPageModelCache, appConfigData);
        yield put(removeToast(publishProgressToastId));
        if (publishResult.data?.publishedCommitId == appPublishCommit?.id) {
          yield put(makeToast({content: 'App design published!', appearances: 'success'}));
        } else {
          yield put(makeToast({content: 'Publish verification failed', appearances: 'error'}));
          logger.error('Verification of publish failed.', publishResult.data);
        }
      }
      yield all([
        put({
          type: EditorActions.APP_SAVE_SUCCESS,
          payload: {
            appSaveId: appSaveCommit?.id,
          },
        }),
        put({type: EditorActions.FETCH_ORGS}),
        put({type: DispatchActions.IS_APP_SAVED}),
      ]);
    }
    progressIds = [];
    //Check if the user is onboarded if not then mark isOnboarded true
    const isOnboarded = yield select(selectOnboardingStatus);
    if (!isOnboarded) {
      yield call(AppApi.updateBasicAppInfo, apptile.appId as number, {isOnboarded: true});
      yield put({
        type: UPDATE_ONBOARDING_STATUS_DONE,
        payload: {isOnboarded: true},
      });
    }
  } catch (e) {
    logger.error(e);
    yield put({
      type: EditorActions.APP_SAVE_FAILED,
      payload: {e},
    });
    yield put({type: DispatchActions.IS_APP_SAVE_FAILED});
    if (action.payload.showToast) {
      yield put(removeToast(progressIds));
      yield put(removeToast(publishProgressToastId));
      yield put(makeToast({content: 'App design save failed!', appearances: 'error'}));
      progressIds = [];
    }
  } finally {
    window.removeEventListener('beforeunload', beforeunloadListener);
  }
}

export function* handleCreateAppBranch(action: DispatchAction<EditorActions.CreateAppBranchPayload>): SagaIterator {
  yield put({type: EditorActions.CREATE_BRANCH_META, payload: {isCreating: true, error: ''}});
  const apptile: apptileState = yield select((state: EditorRootState) => state.apptile);
  const appConfig: AppConfig = yield select(state => state.appConfig.current);

  const processedAppConfig = configProcessor(appConfig);

  if (processedAppConfig.errors && processedAppConfig.errors.length > 0) {
    yield put(
      makeToast({
        content: processedAppConfig.errors.map((e: any) => `${e.heading}\n${e.description}`).join('\n\n'),
        appearances: 'error',
        duration: 5000,
      }),
    );
  } else {
    let appConfigData = processedAppConfig.value;
    appConfigData = yield call(clearPageModelCache, appConfigData);
    try {
      yield call(
        AppConfigApi.createAppBranch,
        apptile.appId,
        apptile.forkId,
        action.payload.title,
        RecordSerializer.stringify(appConfigData),
      );
      yield put({
        type: EditorActions.FETCH_BRANCHES_WITH_OTA,
        payload: {
          appId: apptile.appId,
          forkId: apptile.forkId,
        },
      });
      yield put({type: EditorActions.CREATE_BRANCH_META, payload: {isCreating: false, error: ''}});
    } catch (error) {
      yield put({type: EditorActions.CREATE_BRANCH_META, payload: {isCreating: false, error: error}});
      yield put(makeToast({content: 'Branch creation failed', appearances: 'error'}));
      logger.error('Error creating app branch', error);
    }
  }
}

export function* handleDeleteAppBranch(action: DispatchAction<EditorActions.DeleteBranchPayload>): SagaIterator {
  const apptile: apptileState = yield select((state: EditorRootState) => state.apptile);
  try {
    yield call(AppConfigApi.deleteAppBranch, apptile.appId, apptile.forkId, action.payload.branchName);
    yield put({
      type: EditorActions.FETCH_BRANCHES_WITH_OTA,
      payload: {
        appId: apptile.appId,
        forkId: apptile.forkId,
      },
    });
  } catch (error) {
    logger.error('Error deleting app branch', error);
  }
}

//Saga for scheduling OTA
export function* handleScheduleOta(action: DispatchAction<EditorActions.IScheduleOtaPayload>): SagaIterator {
  const apptile: apptileState = yield select((state: EditorRootState) => state.apptile);
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  const getCurrentBranch = (yield select((state: EditorRootState) => state.branches.branchesByNameWithOta))[
    apptile.appBranch
  ];
  const processedAppConfig = configProcessor(appConfig);

  if (processedAppConfig.errors && processedAppConfig.errors.length > 0) {
    yield put(
      makeToast({
        content: processedAppConfig.errors.map((e: any) => `${e.heading}\n${e.description}`).join('\n\n'),
        appearances: 'error',
        duration: 5000,
      }),
    );
  } else {
    let appConfigData = processedAppConfig.value;
    appConfigData = yield call(clearPageModelCache, appConfigData);
    try {
      //Creating an app save commit
      const appSaveCommitResponse = yield call(
        AppConfigApi.saveAppConfig,
        apptile.appId,
        apptile.forkId,
        apptile.appBranch,
        apptile.appSaveId,
        RecordSerializer.stringify(appConfigData),
        'Scheduled ota save',
        false,
      );
      const appSaveCommit = appSaveCommitResponse?.data;

      yield call(softRestartConfig);
      yield take(DispatchActions.INITIALIZE_STAGE_MODEL);
      appConfigData = yield call(generatePageModelCache, appConfigData);

      //Publishing the app save commit
      const appSavePublishResponse = yield call(
        AppConfigApi.publishAppConfig,
        apptile.appId,
        apptile.forkId,
        apptile.appBranch,
        RecordSerializer.stringify(appConfigData),
        appSaveCommit?.id,
        'Scheduled ota publish',
      );
      const appPublishCommit = appSavePublishResponse?.data;

      //Creating a schedule for the published commit
      const createScheduleParams = {
        forkId: apptile.forkId,
        branchId: getCurrentBranch.id,
        startDate: action.payload.startDate,
        endDate: action.payload.endDate,
        targetCommitId: appPublishCommit.id,
      };

      yield call(SnapshotsApi.scheduleOta, createScheduleParams);
      yield put({
        type: EditorActions.FETCH_BRANCHES_WITH_OTA,
        payload: {
          appId: apptile.appId,
          forkId: apptile.forkId,
        },
      });
      yield put(makeToast({content: 'OTA Scheduled Successfully', appearances: 'success'}));
      yield put({type: EditorActions.SCHEDULE_OTA_SUCCESS});
    } catch (error) {
      logger.error('Error creating app branch', error);
      yield put({type: EditorActions.SCHEDULE_OTA_FAILED, payload: {error}});
      yield put(makeToast({content: 'OTA Schedule Failed', appearances: 'error'}));
    }
  }
}

//Saga for fetching and setting overlapping otas
export function* handleFetchOverlappingOtas(action: DispatchAction<EditorActions.ICheckOverlappingOtas>): SagaIterator {
  const apptile: apptileState = yield select((state: EditorRootState) => state.apptile);
  try {
    const overlappingSnapshots = yield call(SnapshotsApi.checkOtaOverlap, {
      forkId: apptile.forkId,
      startDate: action.payload.startDate,
    });
    yield put({
      type: EditorActions.CHECK_OVERLAPPING_OTAS_SUCCESS,
      payload: overlappingSnapshots?.data,
    });
  } catch (error) {
    logger.error('Error fetching overlapping otas', error);
  }
}

export function* appSaveSuccessSaga(action: DispatchAction<EditorActions.AppSaveSuccessPayload>): SagaIterator {
  try {
    const appSaveId = action.payload?.appSaveId;
    Analytics.track('editor:design_appSaved');
    const apptile: apptileState = yield select((state: EditorRootState) => state.apptile);
    // const appSaveMetaData = yield call(AppConfigApi.fetchAppSaveMeta, apptile.appId, apptile.appSaveId);
    const appConfig: apptileState = yield select((state: EditorRootState) => state.appConfig);
    // Will Update apptileCore with a new action.
    // yield put({type: DispatchActions.FETCH_APPCONFIG_FINISHED, payload: {appConfig: appConfig.current, ...apptile, appSaveUpdatedAt: appSaveMetaData?.data?.updatedAt ?? apptile.appSaveUpdatedAt}});
    yield put({
      type: DispatchActions.FETCH_APPCONFIG_FINISHED,
      payload: {appConfig: appConfig.current, ...apptile, appSaveId},
    });
  } catch (e) {}
}

export function* appUpdateSaga(action: DispatchAction<EditorActions.UpdateAppPayload>): SagaIterator {
  try {
    const {publishedAppSaveId} = action.payload;
    const apptile: apptileState = yield select((state: EditorRootState) => state.apptile);
    yield call(AppApi.updateCurrentApp, apptile.appId, {publishedAppSaveId});
    yield all([put({type: EditorActions.APP_UPDATE_SUCCESS}), put({type: EditorActions.FETCH_ORGS})]);
    yield put(makeToast({content: 'Publish succeeded!', appearances: 'info'}));
  } catch (e) {
    yield put({type: EditorActions.APP_UPDATE_FAILED, payload: {e}});
    yield put(makeToast({content: 'Publish failed!', appearances: 'error'}));
  }
}

export function* pluginSelectSaga(action: DispatchAction<string[]>): SagaIterator {
  yield put(EditorActions.openPropertyInspector());
}
export function* handleNavComponentSelect(action: DispatchAction<string[]>): SagaIterator {
  yield put(EditorActions.openPropertyInspector());
}
export function* pageSelectSaga(action: DispatchAction<string>): SagaIterator {
  yield put(EditorActions.openPropertyInspector());
}

// This function will take screens array and index on which swap operation needs to done
const swapScreens = (screenArray: any, index: number, operation: string) => {
  let idx;
  if (operation === 'UP') {
    idx = index - 1;
  } else {
    idx = index + 1;
  }
  const temp = screenArray[index];
  screenArray[index] = screenArray[idx];
  screenArray[idx] = temp;
  return screenArray;
};

// This function returns a path on which updated array will be set
const createPath = (path: string[], config: any) => {
  let updatedPath;
  if (path.length == 1 && path[0] == '/') {
    return config.navigation.rootNavigator;
  } else {
    const selPath = path[0] === '/' ? path.slice(1) : path;
    updatedPath = ['rootNavigator'];
    selPath.forEach((val: string) => updatedPath.push('screens', val));
    updatedPath.pop();
    return updatedPath;
  }
};

// It will find the index of the screen which needs to be re ordered and will return updated navigator config
const findScreen = (path: string[], navigator: any, originalPath: string[], appConfig: any, operation: string): any => {
  for (let i = 1; i < path.length; i++) {
    const selectedScreen = navigator[path[i]];
    if (selectedScreen.type === 'navigator') {
      const newPath = path.splice(i);
      return findScreen(newPath, selectedScreen.screens, originalPath, appConfig, operation);
    } else {
      const screenNames = Object.keys(navigator);
      const index = screenNames.findIndex((name: string) => name === selectedScreen.name);
      // return if screen is first in the deck and user tries to move up or screen is last in the deck and user tries to move down
      if ((index === 0 && operation === 'UP') || (index === screenNames.length - 1 && operation === 'DOWN')) {
        return;
      }
      let updatedConfig = _.cloneDeep(appConfig);
      const updatedPath = createPath(originalPath, updatedConfig);
      const editPath = updatedConfig.navigation.getIn(updatedPath).toArray();

      const updatedScreenArray = swapScreens(editPath, index, operation);
      const orderedScreens = updatedScreenArray.reduce((acc: any, item: any) => {
        if (item[1].type === 'navigator') {
          return acc.set(item[0], item[1]);
        } else {
          return acc.set(item[0], item[1]);
        }
      }, new Immutable.OrderedMap());
      updatedConfig = updatedConfig.navigation.setIn(updatedPath, orderedScreens);
      return updatedConfig;
    }
  }
};

const moveScreen = (arr: [string, any][], from: number, to: number): [string, any][] => {
  const item = arr.splice(from, 1)[0];
  arr.splice(to, 0, item);
  return arr;
};

const findScreenV2 = (
  path: string[],
  navigator: any,
  originalPath: string[],
  appConfig: any,
  targetIndex: number,
): any => {
  for (let i = 1; i < path.length; i++) {
    const selectedScreen = navigator[path[i]];
    const isTopTabReordered = selectedScreen.navigatorType === 'topTab' && originalPath.length === 4;
    console.log(selectedScreen.type && isTopTabReordered);
    if ((selectedScreen.type === 'navigator' && selectedScreen.navigatorType === 'tab') || isTopTabReordered) {
      const newPath = path.splice(i);
      return findScreenV2(newPath, selectedScreen.screens, originalPath, appConfig, targetIndex);
    } else {
      const screenNames = Object.keys(navigator);
      const currentIndex = screenNames.findIndex((name: string) => name === selectedScreen.name);

      if (currentIndex === -1 || targetIndex < 0 || targetIndex >= screenNames.length || currentIndex === targetIndex) {
        console.log('faioled');
        return;
      }

      let updatedConfig = _.cloneDeep(appConfig);
      const updatedPath = createPath(originalPath, updatedConfig);
      const editPath = updatedConfig.navigation.getIn(updatedPath).toArray();

      const updatedScreenArray = moveScreen(editPath, currentIndex, targetIndex);

      const orderedScreens = updatedScreenArray.reduce((acc: any, item: any) => {
        return acc.set(item[0], item[1]);
      }, new Immutable.OrderedMap());

      updatedConfig = updatedConfig.navigation.setIn(updatedPath, orderedScreens);
      return updatedConfig;
    }
  }
};

export function* handleUpdateNavigationReordering(
  action: DispatchAction<EditorActions.NavigationReoderingPayload>,
): SagaIterator {
  const {operation, path} = action.payload;
  const appConfig: AppConfig = yield select(selectAppConfig);
  let newAppConfig = _.cloneDeep(appConfig);
  const navigationConfig = appConfig.get('navigation');
  const rootNavigator = navigationConfig?.get('rootNavigator');
  const originalPath = _.cloneDeep(path);
  const config = findScreen(path, rootNavigator.toJS().screens, originalPath, appConfig, operation);
  /* if config is undefined it means screen which needs to be reodered is either at first place and can't move up 
     or it is a last screen on the deck and can't moved down further */
  if (!config) {
    if (operation === 'UP') {
      yield put(makeToast({content: 'Cannot move further up!', appearances: 'info'}));
    } else {
      yield put(makeToast({content: 'Cannot move further down!', appearances: 'info'}));
    }
  }
  newAppConfig = config ? newAppConfig.setIn(['navigation'], config) : newAppConfig;
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
}

export function* handleUpdateNavigationReorderingV2(
  action: DispatchAction<EditorActions.NavigationReoderingPayloadV2>,
): SagaIterator {
  const {operation, path, toIndex} = action.payload;
  const appConfig: AppConfig = yield select(selectAppConfig);
  let newAppConfig = _.cloneDeep(appConfig);
  const navigationConfig = appConfig.get('navigation');
  const rootNavigator = navigationConfig?.get('rootNavigator');
  const originalPath = _.cloneDeep(path);
  const config = findScreenV2(path, rootNavigator.toJS().screens, originalPath, appConfig, toIndex);
  /* if config is undefined it means screen which needs to be reodered is either at first place and can't move up 
     or it is a last screen on the deck and can't moved down further */
  if (!config) {
    yield put(makeToast({content: 'Please drag to other position', appearances: 'info'}));
  }
  newAppConfig = config ? newAppConfig.setIn(['navigation'], config) : newAppConfig;
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
}
export function* pluginUpdateId(action: DispatchAction<EditorActions.PluginUpdateIdPayload>): SagaIterator {
  const {pluginId, pageId, newPluginId, namespace} = action.payload;
  let renamedId = newPluginId;
  if (namespace) {
    renamedId = addNamespace(namespace, newPluginId);
  }
  if (pluginId == newPluginId.trim() || pluginId == renamedId) {
    yield put(makeToast({content: `Rename ignored as new and old Id's are same`, appearances: 'info'}));
  } else if (!new RegExp('^[a-z0-9]{3,}$', 'gim').test(newPluginId)) {
    yield put(makeToast({content: `Invalid New Plugin Id only A-z and 0-9 is allowed`, appearances: 'info'}));
  } else {
    yield put({
      type: DispatchActions.PLUGIN_RENAME,
      payload: {
        pluginId,
        pageId,
        newPluginId,
        namespace,
      },
    });
  }
}

export function* handleNavUpdateName(action: DispatchAction<NavUpdateName>): SagaIterator {
  const {navSelector, newNavName} = action.payload;
  if (!validateNaming(newNavName)) return;

  const newSelector = navSelector.length > 1 ? navSelector.slice(0, -1).concat(newNavName) : navSelector;
  yield put({
    type: EditorActions.EDITOR_SELECT_NAV_COMPONENT,
    payload: newSelector,
  });
}
export function* handleNavDeleteComponent(action: DispatchAction<NavComponentDelete>): SagaIterator {
  const {navSelector} = action.payload;
  yield put({
    type: EditorActions.EDITOR_SELECT_NAV_COMPONENT,
    payload: null,
  });
}

export function* handlePluginDelete(action: DispatchAction<EditorActions.PluginDeletePayload>): SagaIterator {
  const {pluginId, pageId} = action.payload;

  yield call(pluginDelete, pluginId, pageId);

  // Removed Update PluginIdReferences to not have breaking binding.
  // yield call(updatePluginIdReferences, pageId, pluginId, '');

  yield put(selectPlugin(null));
  yield put(EditorActions.openPluginListing());
}
export function* moduleVariantUpdate(action: DispatchAction<EditorActions.ModuleVariantPayload>): SagaIterator {
  const {pluginId, pageId, variant} = action.payload;
  const selectedPlugin = yield select(selectSelectedPluginConfig);
  yield put(
    pluginConfigUpdatePath(pluginId, pageId, ['config'], {
      variantSelected: variant?.id ?? 'custom',
      changesDone: false,
      variantGating: variant?.gating ?? allAvailablePlans.CORE,
    }),
  );
  const childNamespace = selectedPlugin?.config?.get('childNamespace');
  const updatableConfigProps: any = {
    TextWidget: [
      'adjustsFontSizeToFit',
      'minFontScale',
      'numLines',
      'horizontalAlign',
      'verticalAlign',
      'overflowType',
    ],
    ListViewWidget: [
      'isSliderMode',
      'numPlaceholderItems',
      'itemWidth',
      'isRepeaterMode',
      'numColumns',
      'itemHeight',
      'horizontal',
    ],
  };
  if (childNamespace && variant) {
    const moduleConfig: ModuleRecord = RecordSerializer.parse(variant?.data);
    if (moduleConfig && moduleConfig.moduleConfig) {
      // moduleConfig.moduleConfig.keys[];
      // console.log(moduleConfig.moduleConfig);
      const plugins = Array.from(moduleConfig.moduleConfig.keys());
      const updates: any = [];
      for (let i in plugins) {
        const pluginConfig: PluginConfig | undefined = moduleConfig.moduleConfig.get(plugins[i]);
        if (pluginConfig) {
          const namespace = new PluginNamespaceImpl([childNamespace]);
          const namespacePluginId = addNamespace(namespace, pluginConfig.id);
          const layout = pluginConfig.layout.setIn(
            ['container'],
            pluginConfig.layout.container ? addNamespace(namespace, pluginConfig.layout.container) : pluginId,
          );
          updates.push({
            pluginId: namespacePluginId,
            pageId,
            layout: layout ?? null,
            style: pluginConfig?.config?.get('style') ?? null,
          });
          if (updatableConfigProps[pluginConfig?.subtype]) {
            const configValues = updatableConfigProps[pluginConfig?.subtype]
              .map((e: string) => [e, pluginConfig?.config?.get(e)])
              .filter((e: any) => !_.isNil(e[1]));
            updates[updates.length - 1].config = Immutable.Map(configValues);
          }
        }
      }
      if (updates.length > 0) {
        yield put(bulkPluginUpdates(updates));
      }
    }
  }
}

export function* handleUpdatePageId(action: DispatchAction<UpdatePageId>): SagaIterator {
  const {pageId, newPageId} = action.payload;
  yield put(EditorActions.selectPageConfig(newPageId));
}

export function* handleEditorCopy(action: DispatchEmptyAction): SagaIterator {
  const editorState = yield select(selectEditorState);
  const {selectedPageId, selectedPluginConfigSel} = editorState;
  const isPlugin: Boolean = !!selectedPluginConfigSel;
  const isPage: Boolean = !!selectedPageId;
  const appConfig: AppConfig = yield select(selectAppConfig);
  if (isPage) {
    const pageConfig = appConfig.getPage(selectedPageId);
    const modulesList = getModuleDependencies(pageConfig.plugins.valueSeq().toArray(), appConfig).toArray();
    const copyData = {
      modules: modulesList,
      page: pageConfig,
    };
    copyToClipboard(RecordSerializer.stringify(copyData));
    const toastText = modulesList.length
      ? `Copied page with id "${selectedPageId}", along with ${modulesList.length} module definitions.`
      : `Copied page with id "${selectedPageId}".`;
    yield put(makeToast({content: toastText, appearances: 'info'}));
  } else if (isPlugin) {
    const appModel: AppModelType = yield select(selectAppModel);
    const pluginModel = appModel.getModelValue(selectedPluginConfigSel);
    let pluginDetails = pluginModel?.toJS();
    pluginDetails = pluginDetails[0] ? pluginDetails[0] : pluginDetails;
    const {pageKey, id: pluginId} = pluginDetails;
    const pageId: string = appModel.getPageId(pageKey);
    const pageConfig = appConfig.getPage(pageId);
    const pluginConfig: PluginConfig = pageConfig.getPluginId(pluginId) as PluginConfig;
    const pluginsContained = [pluginConfig.setIn(['layout', 'container'], '')].concat(
      getPluginsContained(pluginId, pageConfig).toArray(),
    );
    const modulesList = getModuleDependencies(pluginsContained, appConfig).toArray();

    var pluginsToCopyMap = Immutable.OrderedMap<string, PluginConfig>(pluginsContained.map(p => [p.get('id'), p]));
    // logger.info(pluginsToCopyMap.keySeq().toArray());
    pluginsToCopyMap = sortPluginsByContainerDepth(pluginsToCopyMap);
    // logger.info(pluginsToCopyMap.keySeq().toArray());

    var pluginsToCopy = pluginsToCopyMap.valueSeq().toArray();
    var pluginsToCopy = pluginsToCopy.filter(p => !p.namespace);
    const copyData = {
      modules: modulesList,
      plugins: pluginsToCopy,
    };
    copyToClipboard(RecordSerializer.stringify(copyData));
    const toastText = modulesList.length
      ? `Copied ${pluginsToCopy.length} tiles, along with ${modulesList.length} module definitions.`
      : `Copied ${pluginsToCopy.length} tiles.`;
    yield put(makeToast({content: toastText, appearances: 'info'}));
  }
}

export function* handleConfigInjection(action: DispatchAction<string>): SagaIterator {
  const pasteString = action.payload;
  try {
    const pasteData: any = RecordSerializer.parse(pasteString);
    if (pasteData.modules !== null && (!!pasteData.page || !!pasteData.plugins)) {
      const appConfig: AppConfig = yield select(selectAppConfig);
      let newAppConfig = appConfig;
      if (!_.isEmpty(pasteData.modules)) {
        pasteData.modules.forEach(module => {
          const {moduleUUID} = module;
          if (!newAppConfig.modules.get(moduleUUID)) {
            newAppConfig = newAppConfig.setIn(['modules', moduleUUID], module);
          }
        });
        yield put({
          type: DispatchActions.UPDATE_APP_CONFIG,
          payload: newAppConfig,
        });
      }

      if (pasteData.page) {
        const pageConfig = pasteData.page as PageConfig;
        if (!newAppConfig.pages.get(pageConfig.pageId)) {
          // Add Page to Appconfig
          newAppConfig = newAppConfig.setIn(['pages', pageConfig.pageId], pageConfig);
          yield put({
            type: DispatchActions.UPDATE_APP_CONFIG,
            payload: newAppConfig,
          });

          yield put({
            type: DispatchActions.INIT_APP_MODEL,
          });

          // yield put(makeToast({content: `Page added with pageId "${pageConfig.pageId}".`, appearances: 'info'}));
        } else {
          // yield put(
          //   makeToast({content: `A page with pageId "${pageConfig.pageId}" already exists.`, appearances: 'error'}),
          // );
        }
      } else if (pasteData.plugins) {
        const appState: EditorRootState = yield select(selectAppState);
        const editorState = yield select(selectEditorState);
        const appModel = yield select(selectAppModel);
        const {selectedPluginConfigSel, selectedPageType, activeAttachmentKey, activeAttachmentId} = editorState;
        const activeNavigation = appState.activeNavigation;
        const {activePageKey, activePageId} = activeNavigation;
        const activePage = selectedPageType === 'Header' ? activeAttachmentId : activePageId;
        const activeKey = selectedPageType === 'Header' ? activeAttachmentKey : activePageKey;
        const pageConfig: PageConfig = newAppConfig.getPage(activePage);

        const modulesToRename: Set<String> = new Set<String>();
        pasteData.plugins.forEach(plugin => {
          const {id} = plugin;
          if (pageConfig.plugins.get(id)) {
            modulesToRename.add(id);
          }
        });

        // Figure out where to paste.
        let containerToPaste: string | null = null;
        let refWidget = '';
        if (selectedPluginConfigSel) {
          if (selectedPluginConfigSel[0] === activeKey) {
            // A widget on page is selected.
            let selectedModel = appModel.getModelValue(selectedPluginConfigSel);
            selectedModel = selectedModel.get('id') ? selectedModel : selectedModel.get(0);
            const selectedWidgetType = selectedModel.get('pluginType');
            refWidget = selectedModel.get('id');
            if (isContainerTypeWidget(selectedWidgetType)) {
              containerToPaste = selectedModel.get('id');
            } else {
              containerToPaste = selectedModel.getIn(['layout', 'container']);
            }
          }
        }
        if (!containerToPaste) containerToPaste = '';

        // Rename plugins with conflicting ids and update their containers.
        let renamedPlugins: PluginConfig[] = pasteData.plugins.slice();
        pasteData.plugins.forEach(plugin => {
          const {id} = plugin;
          if (modulesToRename.has(id)) {
            const uniquePluginId = generateUniquePluginId(pageConfig.plugins, id);
            renamedPlugins = renamedPlugins.map(changePlugin => {
              if (changePlugin.id === id) {
                if (changePlugin.subtype === 'ModuleInstance')
                  changePlugin = changePlugin.setIn(['config', 'childNamespace'], uniquePluginId);
                return changePlugin.set('id', uniquePluginId);
              }
              if (changePlugin.layout.get('container') === id)
                return changePlugin.setIn(['layout', 'container'], uniquePluginId);
              return changePlugin;
            });
          }
        });

        // Create map to sort by container depth
        let pluginsToCopyMap: Immutable.OrderedMap<string, PluginConfig> = new Immutable.OrderedMap();
        renamedPlugins.forEach(pluginConfig => {
          pluginsToCopyMap = pluginsToCopyMap.set(pluginConfig.id, pluginConfig);
        });
        pluginsToCopyMap = sortPluginsByContainerDepth(pluginsToCopyMap);
        // Update root element to be parented under containerToPaste
        pluginsToCopyMap = pluginsToCopyMap.map(pluginConfig => {
          return pluginConfig.setIn(
            ['layout', 'container'],
            pluginConfig.layout.container ? pluginConfig.layout.container : containerToPaste,
          );
        });
        yield put(makeToast({content: `Pasting into "${containerToPaste}".`, appearances: 'info'}));
        yield call(async function () {
          return new Promise(resolve => setTimeout(resolve, 10));
        });
        for (const [_, pluginConfig] of pluginsToCopyMap) {
          // logger.info(`Pasting widget ${pluginConfig.id} after ${refWidget}`);
          yield call(addPluginConfig, pluginConfig, activePage, refWidget, true);
          refWidget = pluginConfig.id;
        }
        // // Too many add plugin calls cause appmodel to go out of sync due to too many model updates
        // for (const [_, pluginConfig] of pluginsToCopyMap) {
        //   yield call(recalculatePluginConfig, activePageId, pluginConfig.id, pluginConfig, {});
        // }

        yield put(makeToast({content: `Renaming "${modulesToRename.size}" modules.`, appearances: 'warning'}));
      }
    }
    yield put({type: 'CONFIG_INJECTION_DONE'});
  } catch (e) {
    logger.error(e);
    yield put({type: 'CONFIG_INJECTION_ERROR'});
    return;
  }
}

export function* handleEditorPaste(action: DispatchAction<string>): SagaIterator {
  const pasteString = action.payload;
  try {
    const pasteData: any = RecordSerializer.parse(pasteString);
    if (pasteData.modules !== null && (!!pasteData.page || !!pasteData.plugins)) {
      const appConfig: AppConfig = yield select(selectAppConfig);
      let newAppConfig = appConfig;
      if (!_.isEmpty(pasteData.modules)) {
        pasteData.modules.forEach(module => {
          const {moduleUUID} = module;
          if (!newAppConfig.modules.get(moduleUUID)) {
            newAppConfig = newAppConfig.setIn(['modules', moduleUUID], module);
          }
        });
        yield put({
          type: DispatchActions.UPDATE_APP_CONFIG,
          payload: newAppConfig,
        });
      }

      if (pasteData.page) {
        const pageConfig = pasteData.page as PageConfig;
        if (!newAppConfig.pages.get(pageConfig.pageId)) {
          // Add Page to Appconfig
          newAppConfig = newAppConfig.setIn(['pages', pageConfig.pageId], pageConfig);
          yield put({
            type: DispatchActions.UPDATE_APP_CONFIG,
            payload: newAppConfig,
          });
          yield put(makeToast({content: `Page added with pageId "${pageConfig.pageId}".`, appearances: 'info'}));
        } else {
          yield put(
            makeToast({content: `A page with pageId "${pageConfig.pageId}" already exists.`, appearances: 'error'}),
          );
        }
      } else if (pasteData.plugins) {
        const appState: EditorRootState = yield select(selectAppState);
        const editorState = yield select(selectEditorState);
        const appModel = yield select(selectAppModel);
        const {selectedPluginConfigSel, selectedPageType, activeAttachmentKey, activeAttachmentId} = editorState;
        const activeNavigation = appState.activeNavigation;
        const {activePageKey, activePageId} = activeNavigation;
        const activePage = selectedPageType === 'Header' ? activeAttachmentId : activePageId;
        const activeKey = selectedPageType === 'Header' ? activeAttachmentKey : activePageKey;
        const pageConfig: PageConfig = newAppConfig.getPage(activePage);

        const modulesToRename: Set<String> = new Set<String>();
        pasteData.plugins.forEach(plugin => {
          const {id} = plugin;
          if (pageConfig.plugins.get(id)) {
            modulesToRename.add(id);
          }
        });

        // Figure out where to paste.
        let containerToPaste: string | null = null;
        let refWidget = '';
        if (selectedPluginConfigSel) {
          if (selectedPluginConfigSel[0] === activeKey) {
            // A widget on page is selected.
            let selectedModel = appModel.getModelValue(selectedPluginConfigSel);
            selectedModel = selectedModel.get('id') ? selectedModel : selectedModel.get(0);
            const selectedWidgetType = selectedModel.get('pluginType');
            refWidget = selectedModel.get('id');
            if (isContainerTypeWidget(selectedWidgetType)) {
              containerToPaste = selectedModel.get('id');
            } else {
              containerToPaste = selectedModel.getIn(['layout', 'container']);
            }
          }
        }
        if (!containerToPaste) containerToPaste = '';

        // Rename plugins with conflicting ids and update their containers.
        let renamedPlugins: PluginConfig[] = pasteData.plugins.slice();
        pasteData.plugins.forEach(plugin => {
          const {id} = plugin;
          if (modulesToRename.has(id)) {
            const uniquePluginId = generateUniquePluginId(pageConfig.plugins, id);
            renamedPlugins = renamedPlugins.map(changePlugin => {
              if (changePlugin.id === id) {
                if (changePlugin.subtype === 'ModuleInstance')
                  changePlugin = changePlugin.setIn(['config', 'childNamespace'], uniquePluginId);
                return changePlugin.set('id', uniquePluginId);
              }
              if (changePlugin.layout.get('container') === id)
                return changePlugin.setIn(['layout', 'container'], uniquePluginId);
              return changePlugin;
            });
          }
        });

        // Create map to sort by container depth
        let pluginsToCopyMap: Immutable.OrderedMap<string, PluginConfig> = new Immutable.OrderedMap();
        renamedPlugins.forEach(pluginConfig => {
          pluginsToCopyMap = pluginsToCopyMap.set(pluginConfig.id, pluginConfig);
        });
        pluginsToCopyMap = sortPluginsByContainerDepth(pluginsToCopyMap);
        // Update root element to be parented under containerToPaste
        pluginsToCopyMap = pluginsToCopyMap.map(pluginConfig => {
          return pluginConfig.setIn(
            ['layout', 'container'],
            pluginConfig.layout.container ? pluginConfig.layout.container : containerToPaste,
          );
        });
        yield put(makeToast({content: `Pasting into "${containerToPaste}".`, appearances: 'info'}));
        yield call(async function () {
          return new Promise(resolve => setTimeout(resolve, 10));
        });
        for (const [_, pluginConfig] of pluginsToCopyMap) {
          // logger.info(`Pasting widget ${pluginConfig.id} after ${refWidget}`);
          yield call(addPluginConfig, pluginConfig, activePage, refWidget, true);
          refWidget = pluginConfig.id;
        }
        // // Too many add plugin calls cause appmodel to go out of sync due to too many model updates
        // for (const [_, pluginConfig] of pluginsToCopyMap) {
        //   yield call(recalculatePluginConfig, activePageId, pluginConfig.id, pluginConfig, {});
        // }

        yield put(makeToast({content: `Renaming "${modulesToRename.size}" modules.`, appearances: 'warning'}));
      }
    }
  } catch (e) {
    logger.error(e);
    return;
  }
}

export function* handleSaveAppImageRecord(action: DispatchAction<EditorActions.SaveImageRecordPayload>): SagaIterator {
  try {
    const {assetId} = action.payload;
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    const assetState: AssetReduxState = yield select((state: EditorRootState) => state.asset);
    const {assetsById, assetVariantsById} = assetState;
    const currentAsset = assetsById[assetId] ? assetsById[assetId] : null;

    if (!currentAsset) {
      yield put(makeToast({content: 'An error while updating image!', appearances: 'error'}));
      return;
    }

    const variants = {};
    currentAsset?.variants?.forEach(variantId => {
      const variant = assetVariantsById[variantId] ?? null;
      if (variantId && variant) {
        variants[variantId] = _.pick(variant, ['fileName', 'width', 'height', 'fileUrl']) as ImageConfigParams;
      }
    });

    const defaultImage = _.pick(currentAsset, ['fileName', 'width', 'height', 'fileUrl']) as ImageConfigParams;

    const curreImageRecord = new ImageRecord({
      variants: Immutable.OrderedMap(variants),
      ...defaultImage,
    });

    const newAppConfig = appConfig.setIn(['images', assetId], curreImageRecord);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
  } catch (e) {
    yield put({
      type: EditorActions.SAVE_IMAGE_RECORD_FAILED,
      payload: {
        error: e,
      },
    });
  }
}

export function* handleBulkImportAppImageRecord(action: DispatchAction<any>): SagaIterator {
  try {
    const {pasteString} = action.payload;
    const appConfig: AppConfig = yield select(state => state.appConfig.current);

    let appImages = appConfig?.get('images') as Immutable.Map<string, ImageRecord>;
    const imageRecords = JSON.parse(pasteString);
    Object.entries(imageRecords).map(([assetId, variantsObj]) => {
      const {variants, ...defaultImage} = variantsObj as any;
      const curreImageRecord = new ImageRecord({
        variants: Immutable.OrderedMap(variants),
        ...defaultImage,
      });
      appImages = appImages.set(assetId, curreImageRecord);
    });

    const newAppConfig = appConfig?.setIn(['images'], appImages);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield put(makeToast({content: 'App Images copied successfully.', appearances: 'success'}));
  } catch (e) {
    console.log(e);
  }
}

export function* handleSaveAppImageRecordFailed(): SagaIterator {
  yield put(makeToast({content: 'An error while updating image!', appearances: 'error'}));
}

function* editorEventReordering(pluginId: string, pageId: string, eventIndex: number, moveUp: boolean): SagaIterator {
  if (eventIndex === 0 && moveUp) throw new Error('Can not move event up, already at first position!');

  const pluginConfig = yield select(selectPluginConfig, pageId, pluginId);
  if (!pluginConfig) throw new Error('Please select a valid plugin');

  const pluginConfigEvents = pluginConfig.getIn(['config', 'events']);
  if (!pluginConfigEvents) throw new Error(`Events does not exists for ${pluginId}`);

  if (eventIndex + 1 === pluginConfigEvents.size && !moveUp) {
    throw new Error('Can not move event down!');
  }

  const oldEventItem = pluginConfigEvents.get(eventIndex);
  if (!oldEventItem) throw new Error(`Could'nt fetch a valid event`);

  let newPluginConfigEvents = pluginConfigEvents.delete(eventIndex);
  const toIndex = moveUp ? eventIndex - 1 : eventIndex + 1;

  newPluginConfigEvents = newPluginConfigEvents.insert(toIndex, oldEventItem);
  const pluginEventUpdate = Immutable.Map({events: newPluginConfigEvents});

  yield put(pluginConfigEventUpdate(pluginId, pageId, pluginEventUpdate));
}

function* handleEditorEventReorderUpward(action: DispatchAction<EditorActions.EventReorderPayload>): SagaIterator {
  try {
    const {eventIndex, pageId, pluginId} = action.payload;
    yield call(editorEventReordering, pluginId, pageId, eventIndex, true);
  } catch (error) {
    yield put(makeToast({content: error?.message || error, appearances: 'error'}));
  }
}

function* handleEditorEventReorderDownward(action: DispatchAction<EditorActions.EventReorderPayload>): SagaIterator {
  try {
    const {eventIndex, pageId, pluginId} = action.payload;
    yield call(editorEventReordering, pluginId, pageId, eventIndex, false);
  } catch (error) {
    yield put(makeToast({content: error?.message || error, appearances: 'error'}));
  }
}

export function* deleteAppImageRecord(action: DispatchAction<EditorActions.deleteAppImagePayload>): SagaIterator {
  try {
    const {imageSelector} = action.payload;
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    const newAppConfig = appConfig.deleteIn(imageSelector);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield put({
      type: EditorActions.DELETE_IMAGE_RECORD_SUCCESS,
    });
  } catch (e) {
    yield put({
      type: EditorActions.DELETE_IMAGE_RECORD_FAILED,
      payload: {
        error: e,
      },
    });
  }
}

function* generatePageModelCache(appConfig: any) {
  const appModel = yield select(selectAppModel);
  const themeModel = yield select(selectApptileThemeModel);
  const themeEval = themeEvaluator(themeModel?.themeValue);
  const pageConfigs: Immutable.Map<string, PageConfig> = yield select(pageConfigsSelector);
  const codeCaches: SerializedBindings = {raw: [], transpiled: []};

  let pureModel = yield select(selectPreInitModel);
  const pureCodeCache = pureModel.dependencyGraph.getTranspiledBindings();
  codeCaches.raw = codeCaches.raw.concat(pureCodeCache.raw);
  codeCaches.transpiled = codeCaches.transpiled.concat(pureCodeCache.transpiled);

  for (let pageConfig of pageConfigs?.valueSeq()?.toArray()) {
    // if (!pageConfig.disablePageCache) {
    // let pureModel = yield select(selectPreInitModel);
    const pageId = pageConfig.pageId;
    // pureModel = yield call(generatePurePageModelFromConfig, appConfig, pageId);
    // const codeCache = pureModel.dependencyGraph.getTranspiledBindings();
    // codeCaches.raw = codeCaches.raw.concat(codeCache.raw);
    // codeCaches.transpiled = codeCaches.transpiled.concat(codeCache.transpiled);
    // pageConfig = pageConfig.set('_cachedPluginModels', pureModel.getModelValue([pageId, 'plugins']));
    // const {depGraph, parentContainers} = pureModel.dependencyGraph;
    // const incomingEdges = {};
    // const nodes = {};
    // const outgoingEdges = {};
    // const pc = {};

    // for (let k of depGraph.incomingEdges.keys()) {
    //   if (!k.endsWith('_transpiledBindings') && (k === pageId || k.startsWith(pageId + '.plugins'))) {
    //     incomingEdges[k] = depGraph.incomingEdges.get(k).filter((it: string) => !it.includes('_transpiledBindings'));
    //   }
    // }

    // for (let k of depGraph.nodes.keys()) {
    //   if (!k.endsWith('_transpiledBindings') && (k === pageId || k.startsWith(pageId + '.'))) {
    //     nodes[k] = depGraph.nodes.get(k);
    //   }
    // }

    // for (let k of depGraph.outgoingEdges.keys()) {
    //   if (!k.endsWith('_transpiledBindings') && (k === pageId || k.startsWith(pageId + '.plugins'))) {
    //     outgoingEdges[k] = depGraph.outgoingEdges.get(k).filter((it: string) => !it.includes('_transpiledBindings'));
    //   }
    // }

    // for (let k of parentContainers.keys()) {
    //   if (!k.endsWith('_transpiledBindings') && k.startsWith(pageId + '.plugins')) {
    //     pc[k] = parentContainers.get(k);
    //   }
    // }

    // let cachedGraph = {
    //   incomingEdges,
    //   nodes,
    //   outgoingEdges,
    //   parentContainers: pc,
    // };

    pageConfig = pageConfig.set(
      'plugins',
      pageConfig.plugins.map(plConfig => {
        const nsId = addNamespace(plConfig.namespace, plConfig.id);
        let modelValue: ImmutableMapType = pureModel.getModelValue([pageId, 'plugins', plConfig.id]);
        // if (modelValue?.has(0)) modelValue = modelValue.get(0);
        return plConfig
          .set('_modelStyles', getPluginModelStylesFromConfig(themeEval, plConfig))
          .set('_cachedModel', modelValue)
          .set('layout', plConfig.layout._generateFlexPropertiesCache());
      }),
    );

    /*
      let cachedGraph: CachedDependencyGraph = {
        incomingEdges: _.pickBy(depGraph.incomingEdges, (v, k) => k === pageId || _.startsWith(k, pageId + '.plugins')),
        nodes: _.pickBy(depGraph.nodes, (v, k) => k === pageId || _.startsWith(k, pageId + '.')),
        outgoingEdges: _.pickBy(depGraph.outgoingEdges, (v, k) => k === pageId || _.startsWith(k, pageId + '.plugins')),
        parentContainers: _.pickBy(parentContainers, (v, k) => _.startsWith(k, pageId + '.plugins')),
      };
      */
    // pageConfig = pageConfig.set('_cachedDependencyGraph', cachedGraph);
    appConfig = appConfig.setIn(['pages', pageId], pageConfig);
    // }
  }

  // dedupe code in caches
  const rawCodeSet = new Set();
  const dedupedCodeCaches: SerializedBindings = {raw: [], transpiled: []};
  if (codeCaches.raw.length != codeCaches.transpiled.length) {
    throw new Error(
      'Error in code cache generation! Number of transpiled functions is different from number of raw bindings',
    );
  }

  for (let index = 0; index < codeCaches.raw.length; ++index) {
    if (!rawCodeSet.has(codeCaches.raw[index])) {
      rawCodeSet.add(codeCaches.raw[index]);
      dedupedCodeCaches.raw.push(codeCaches.raw[index]);
      dedupedCodeCaches.transpiled.push(codeCaches.transpiled[index]);
    }
  }

  if (dedupedCodeCaches.raw.length != dedupedCodeCaches.transpiled.length) {
    throw new Error(
      'Error in code cache generation! Number of transpiled functions is different from number of raw bindings',
    );
  }

  // appConfig = appConfig.set('_transpiledBindings', dedupedCodeCaches);
  const _cachedDependencyGraph = appModel.dependencyGraph.serialize(dedupedCodeCaches);
  const newDependencyGraph = new DependencyGraph(appModel.dependencyGraph.globalScopeIdentifiers);
  newDependencyGraph.deserialize(_cachedDependencyGraph);
  const equalityCheckResults = newDependencyGraph.equal(appModel.dependencyGraph);
  if (equalityCheckResults.length > 0) {
    yield put({
      type: 'EDITOR_RECORD_BINDING_ERROR',
      payload: {
        binding: 'DEPGRAPH ERRORS',
        error: {
          transpiledFn: JSON.stringify(equalityCheckResults),
          error:
            'Check console for formatted output. The following errors were found while serializing the dependencygraph.',
        },
      },
    });
    logger.error('Depgraph errors: ', equalityCheckResults);
  }
  appConfig = appConfig.set('_cachedDependencyGraph', _cachedDependencyGraph);
  return appConfig;
}

export function* handleGeneratePageModelCaches(action: DispatchEmptyAction): SagaIterator {
  try {
    let appConfig: AppConfig = yield select(selectAppConfig);
    appConfig = yield call(generatePageModelCache, appConfig);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: appConfig,
    });
  } catch (e) {
    logger.error(e);
  }
}

function* clearPageModelCache(appConfig: any) {
  // appConfig = appConfig.delete('_transpiledBindings');
  const pageConfigs: Immutable.Map<string, PageConfig> = yield select(pageConfigsSelector);
  for (let pageConfig of pageConfigs.valueSeq().toArray()) {
    const pageId = pageConfig.pageId;
    pageConfig = pageConfig.delete('_cachedPluginModels');
    pageConfig = pageConfig.delete('_cachedDependencyGraph');
    pageConfig = pageConfig.delete('_transpiledBindings');
    pageConfig = pageConfig.set(
      'plugins',
      pageConfig.plugins.map(plConfig => {
        return plConfig
          .set('_modelStyles', undefined)
          .set('_cachedModel', undefined)
          .set('layout', plConfig.layout._clearFlexPropertiesCache());
      }),
    );
    if (appConfig.getIn(['pages', pageId])) appConfig = appConfig.setIn(['pages', pageId], pageConfig);
  }
  return appConfig.delete('_cachedDependencyGraph');
}

export function* handleClearPageModelCaches(action: DispatchEmptyAction): SagaIterator {
  try {
    let appConfig: AppConfig = yield select(selectAppConfig);
    appConfig = yield call(clearPageModelCache, appConfig);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: appConfig,
    });
  } catch (e) {
    logger.error(e);
  }
}

export function* updateDatasourceCredentials(pluginId: string, appId: string): SagaIterator {
  const dsConfig = yield select(selectPluginConfig, '', pluginId);
  if (!dsConfig) throw new Error(`Could not find a valid datasource for ${pluginId}`);

  const dsModel = GetRegisteredPlugin(dsConfig.subtype);
  if (!dsModel) throw new Error(`Could not find dsModel for ${dsConfig.subtype}`);

  const platformType = dsModel.getPlatformIdentifier();
  if (!platformType) return;

  const response = yield call(IntegrationsApi.fetchAppIntegrationSecrets, appId, platformType);
  const dsCredential: IDatasourceCredentialTypes = response?.data ?? {};

  if (!dsCredential) throw new Error(`Could not find dsModel`);

  const configUpdates = dsModel.resolveCredentialConfigs(dsCredential);

  if (configUpdates) {
    configUpdates.secretsConfigured = true;
    return yield put(pluginConfigUpdate(pluginId, '', configUpdates));
  }
  return false;
}

export function* clearDatasourceCredentials(pluginId: string): SagaIterator {
  const dsConfig = yield select(selectPluginConfig, '', pluginId);
  if (!dsConfig) throw new Error(`Could not find a valid datasource for ${pluginId}`);

  const dsModel = GetRegisteredPlugin(dsConfig.subtype);
  if (!dsModel) throw new Error(`Could not find dsModel for ${dsConfig.subtype}`);

  const credentialKeys = dsModel.resolveClearCredentialConfigs();
  let configUpdates: any = {
    secretsConfigured: false,
  };
  if (credentialKeys) {
    credentialKeys.forEach((k: string) => (configUpdates[k] = null));
    return yield put(pluginConfigUpdate(pluginId, '', configUpdates));
  }
  return false;
}

export function* replaceAppConfigSaga(action: DispatchAction<EditorActions.FetchBlueprintPayload>): SagaIterator {
  const {blueprintId, appId, appSaveId, onboarding, themeShift = false} = action.payload;

  try {
    const blueprintResponse = yield call(BlueprintsApi.getBlueprint, blueprintId);
    let appcfg = RecordSerializer.parse(blueprintResponse?.data?.currentSavedVersion?.data);
    if (themeShift) {
      const oldAppConfig = yield select(state => selectAppConfig(state));
      if (oldAppConfig) {
        const brandSettings = oldAppConfig.getIn(['settings', 'Brand']);
        if (brandSettings) {
          appcfg = appcfg.setIn(['settings', 'Brand'], brandSettings);
          const assetId = brandSettings.getSettingValue('logoAssetId');
          if (assetId) {
            appcfg = appcfg.setIn(['images', assetId], oldAppConfig.getIn(['images', assetId]));
          }
        }
      }
    }
    yield put({
      type: DispatchActions.FETCH_APPCONFIG_FINISHED,
      payload: {
        appId: appId,
        appSaveId: appSaveId,
        appConfig: appcfg,
        appSaveUpdatedAt: new Date().toISOString(),
      },
    });

    //We init plugins here because we need to have the plugins in the plugins registry for the model to be constructed.
    initPlugins();
    yield put({
      type: EditorActions.CONFIGURE_DATASOURCES_WILE_ONBOARDING,
      payload: {appId, onboarding, themeShift},
    });
  } catch (e) {
    yield put({
      type: DispatchActions.INIT_APP_MODEL_ERROR,
      payload: {
        e,
      },
    });
  }
}

export function* configureDataSourcesWhileOnboarding(
  action: DispatchAction<EditorActions.IConfigureDataSourcesWhileOnboardingPayload>,
): SagaIterator {
  const {appId, onboarding, themeShift} = action.payload;
  try {
    let updateAvailable = false;
    const appConfig: AppConfig = yield select(selectAppConfig);
    if (!appConfig) return;

    const globalPlugins = appConfig.get('plugins');
    const datasourceConfigs = globalPlugins.filter(pluginConfig => {
      return pluginConfig.type === 'datasource';
    });

    for (const [pluginId] of datasourceConfigs) {
      try {
        const result = yield call(updateDatasourceCredentials, pluginId, appId);
        if (result) updateAvailable = true;
      } catch (error) {
        logger.error(error);
      }
    }

    //Useful to fetch the shop data and products and collections. This will be used while filling mandatory fields
    yield all([
      put({
        type: DispatchActions.DESTROY_APP_MODEL,
      }),
      put({
        type: DispatchActions.INIT_APP_MODEL,
      }),
    ]);
    if (onboarding) {
      logger.info('Saving Because Coming From OnBoarding');
      yield put({
        type: EditorActions.APP_SAVE,
        payload: {
          newSave: false,
          showToast: false,
          remark: 'Onboarding save',
        },
      });
      yield put(fillMandtoryFields(true, appId, true));
    } else if (themeShift) {
      logger.info('Saving Because Shifting Theme');
      yield put(fillMandtoryFields(false, appId, false));
      yield take(FILLING_MANDATORY_FINISHED);
      yield put({
        type: EditorActions.APP_SAVE,
        payload: {
          newSave: false,
          showToast: false,
          remark: 'Theme Transfer',
          backup: true,
        },
      });
    }
  } catch (error) {
    logger.error(error);
  }
}

export function* configureDatasources(
  action: DispatchAction<EditorActions.IConfigureDatasourcesPayload>,
): SagaIterator {
  try {
    let updateAvailable = false;
    const {appId, forceUpdateSecrets} = action.payload;

    const appConfig: AppConfig = yield select(selectAppConfig);
    if (!appConfig) return;

    const globalPlugins = appConfig.get('plugins');
    const datasourceConfigs = globalPlugins.filter(pluginConfig => {
      return pluginConfig.type === 'datasource';
    });

    for (const [pluginId, dsConfig] of datasourceConfigs) {
      const secretUpdateRequired = forceUpdateSecrets ? true : !dsConfig.config.get('secretsConfigured', false);
      if (secretUpdateRequired) {
        try {
          const result = yield call(updateDatasourceCredentials, pluginId, appId);
          if (result) updateAvailable = true;
        } catch (error) {
          logger.error(error);
        }
      }
    }

    if (updateAvailable) {
      yield all([
        put({
          type: DispatchActions.DESTROY_APP_MODEL,
        }),
        put({
          type: DispatchActions.INIT_APP_MODEL,
        }),
        put({
          type: EditorActions.APP_SAVE,
          payload: {
            newSave: false,
            showToast: false,
          },
        }),
      ]);
    } else {
      logger.error('Nothing to configure in datasources');
    }
  } catch (e) {
    logger.error(e);
  }
}

export function* clearDatasources(): SagaIterator {
  try {
    const appConfig: AppConfig = yield select(selectAppConfig);
    if (!appConfig) return;

    const globalPlugins = appConfig.get('plugins');
    const datasourceConfigs = globalPlugins.filter(pluginConfig => {
      return pluginConfig.type === 'datasource';
    });

    for (const [pluginId] of datasourceConfigs) {
      try {
        yield call(clearDatasourceCredentials, pluginId);
      } catch (error) {
        logger.error(error);
      }
    }
    yield call(softRestartConfig);
  } catch (e) {
    logger.error(e);
  }
}

export function* softRestartConfig(): SagaIterator {
  try {
    yield put({
      type: DispatchActions.RESET_APP_MODEL,
    });
    yield put({
      type: DispatchActions.INIT_APP_MODEL,
    });
  } catch (e) {}
}

export function* handleSetEditorPageParams(
  action: DispatchAction<EditorActions.SetEditorPageParamsPayload>,
): SagaIterator {
  try {
    const {pageKey, paramName, value} = action?.payload;
    yield put(
      modelUpdateAction([
        {
          selector: [pageKey, 'params', paramName],
          newValue: value,
        },
      ]),
    );
  } catch (e) {
    logger.error(e);
  }
}

export function* addNewPageInNavigation(action: any): SagaIterator {
  const {screenId} = action.payload;
  try {
    yield call(softRestartConfig);
    yield take([DispatchActions.APPTILE_ACTIVE_PAGE]);
    yield put({type: EditorActions.EDITOR_SELECT_NAV_COMPONENT, payload: screenId});
    yield put({type: EditorActions.EDITOR_SELECTED_PAGE_TYPE, payload: 'screen'});
    const context = getNavigationContext();
    context.navigate(screenId);
  } catch (e) {}
}

export function* sendTileAnalyticsSaga(action: any): SagaIterator {
  const {moduleUUID, eventName, eventData = {}} = action.payload;
  const moduleRecord: ModuleRecord = yield select((state: any) => selectModuleByUUID(state, moduleUUID));
  const currentPlanFeatures = yield select((state: any) => currentPlanFeaturesSelector(state));
  const userPlan = allAvailablePlansOrdered.find((e: string) => currentPlanFeatures.includes(e));
  // console.log('sendTileAnalytics', {
  //   ...eventData,
  //   tileId: moduleUUID,
  //   tileName: moduleRecord.get('moduleName'),
  //   userPlan,
  // });
  try {
    if (moduleRecord)
      Analytics.track(eventName, {
        ...eventData,
        tileId: moduleUUID,
        tileName: moduleRecord.get('moduleName'),
        userPlan,
      });
  } catch (e) {}
}

export function* fetchAppConfigAndInitPageModelSaga(
  action: DispatchAction<EditorActions.FetchAppAndInitConfigPayload>,
): SagaIterator {
  const pageConfig = JSON.stringify(require('@/root/web/assets/extras/themeOnboardingPage.json'));
  const pageId = action.payload.pageId;
  const pageKey = action.payload.pageKey;

  yield put({
    type: DispatchActions.FETCH_APPCONFIG,
    payload: {
      appId: action.payload.appId,
    },
  });

  yield take(DispatchActions.FETCH_APPCONFIG_FINISHED);

  yield put({
    type: EditorActions.CONFIG_INJECTION,
    payload: pageConfig,
  });

  yield take('CONFIG_INJECTION_DONE');

  const pageModel = yield select(selectPageModelInitState);

  if (!pageModel?.inited) {
    const globals = yield select(state => state?.appModel?.getModelValue([]));

    if (!globals.get(pageKey)) {
      logger.info('[LIFECYCLE] PAGE initPageModel');
      yield put({
        type: DispatchActions.INIT_PAGE_MODEL,
        payload: {
          pageId,
          pageKey,
          context: {},
        },
      });
    }
  }
}
export function* setHasOneSignalSaga(action: any): SagaIterator {
  try {
    const integrationData = yield call(IntegrationsApi.fetchAppIntegrationSecrets, action.payload.appId, 'oneSignal');

    if (integrationData) {
      yield put({
        type: EditorActions.SET_HAS_ONE_SIGNAL,
        payload: {
          hasOneSignal: true,
          oneSignalAppId: integrationData?.data?.oneSignalAppId,
        },
      });
    }
  } catch (err) {
    logger.error(err);
  }
}
export function* fetchAnalyticsUrl(action: any): SagaIterator {
  try {
    const appIdSelector = (state: EditorRootState) => state.apptile.appId;
    const appId = yield select(appIdSelector);
    const response = yield call(AnalyticsApi.getDashboardUrl, appId);
    const {dashboardEmbedUrl} = response?.data ?? {};
    if (dashboardEmbedUrl) {
      yield put(EditorActions.setAnalyticsUrl(dashboardEmbedUrl, false));
    } else {
      yield put(EditorActions.setAnalyticsUrl('', true));
    }
  } catch (err) {
    yield put(EditorActions.setAnalyticsUrl('', true));
    logger.error(err);
  }
}

export function* addForceUpdateSaga(action: any): SagaIterator {
  const saveProgressToastId = uuid();
  yield put(
    makeToast({
      id: saveProgressToastId,
      content: 'Adding Force Update Banner',
      appearances: 'info',
      cancellable: false,
    }),
  );
  try {
    const {iosVersion, androidVersion, androidUrl, iosUrl} = action?.payload;
    const FORCE_UPDATE_EXP = 'forceUpdateBannerExp';
    const screens = yield select(selectScreensInNav);
    let appConfig = yield select((state: EditorRootState) => state.appConfig.current);
    const pageId = screens?.[0]?.name;
    const forceUpdateLogic = `{{!((Apptile?.platform === "android" && Apptile?.versionNumber >= ${androidVersion}) || (Apptile?.platform === "ios" && Apptile?.versionNumber >= ${iosVersion}) || Apptile?.platform === "web")}}`;
    const forceUpdateExp = appConfig?.getIn(['pages', pageId, 'plugins', FORCE_UPDATE_EXP]);
    const forceUpdatePage = appConfig?.getIn(['pages', 'ForceUpdate']);
    if (forceUpdateExp) {
      yield put(pluginConfigUpdatePath(FORCE_UPDATE_EXP, pageId, ['config'], {value: forceUpdateLogic}));
    } else {
      let newAppConfig = appConfig;
      const pageConfig: PluginConfig = appConfig?.getPage(pageId);
      const config = new PluginConfig({
        id: FORCE_UPDATE_EXP,
        type: 'state',
        subtype: 'LogicalExpressionPlugin',
        layout: new LayoutRecord(),
        config: Immutable.Map({
          value: forceUpdateLogic,
          previousValue: '',
          runWhenModelUpdates: true,
          runWhenPageLoads: false,
          runOnPageFocus: true,
          events: Immutable.List([
            new EventHandlerConfig({
              label: 'onReturnTrue',
              type: 'page',
              method: 'navigate',
              pluginId: null,
              isGlobalPlugin: false,
              screenName: 'ForceUpdate',
              prop: '',
              value: null,
              params: Immutable.Map({}),
              hasCondition: false,
              condition: '',
            }),
          ]),
        }),
      });
      newAppConfig = newAppConfig?.setIn(
        ['pages', pageId, 'plugins'],
        pageConfig?.plugins?.set(FORCE_UPDATE_EXP, config),
      );
      yield put({
        type: DispatchActions.UPDATE_APP_CONFIG,
        payload: newAppConfig,
      });
    }
    if (!forceUpdatePage) {
      const tempPages = yield select((state: EditorRootState) => state.pages.tempPages);
      yield call(handleApplyIntegrationPages, tempPages, true);
      yield delay(1000);
    }
    appConfig = yield select((state: EditorRootState) => state.appConfig.current);
    const forceUpdateTile = appConfig
      ?.getIn(['pages', 'ForceUpdate', 'plugins'])
      ?.find((e: any) => e.subtype == 'ModuleInstance');
    yield put(
      pluginConfigSetPathValue(
        forceUpdateTile.id,
        'ForceUpdate',
        ['config', 'events'],
        Immutable.List([
          new EventHandlerConfig({
            label: 'IOS',
            type: 'action',
            method: 'triggerAction',
            pluginId: 'Apptile',
            isGlobalPlugin: true,
            screenName: '',
            prop: '',
            value: 'openLink',
            params: Immutable.Map({
              link: iosUrl,
            }),
            hasCondition: false,
            condition: '?event.condition',
          }),
          new EventHandlerConfig({
            label: 'Android',
            type: 'action',
            method: 'triggerAction',
            pluginId: 'Apptile',
            isGlobalPlugin: true,
            screenName: '',
            prop: '',
            value: 'openLink',
            params: Immutable.Map({
              link: androidUrl,
            }),
            hasCondition: false,
            condition: '?event.condition',
          }),
        ]),
      ),
    );
    yield delay(2000);
    yield put(removeToast(saveProgressToastId));
    yield put(EditorActions.softRestartConfig());
    yield put(makeToast({content: 'Force Update Banner Added', appearances: 'success', cancellable: true}));
  } catch (err) {
    yield put(removeToast(saveProgressToastId));
    yield put(EditorActions.softRestartConfig());
    yield put(makeToast({content: 'Force Update Banner Failed', appearances: 'error', cancellable: true}));
  }
}

export function* addMultiLanguageSwitchPluginSaga(action: any): SagaIterator {
  const {shouldDropPlugin} = action.payload;
  const languageSwitchTileTag = 'language-fork-switch';

  let latestModuleUUID;
  try {
    const tiles = yield call(TilesApi.getTiles, [languageSwitchTileTag]);
    latestModuleUUID = tiles?.data?.items?.[0]?.id;
  } catch (err) {
    logger.error('Error fetching tiles with tag', languageSwitchTileTag, err);
  }

  const appConfig: AppConfig = yield select(selectAppConfig);
  const pagePluginsMap = yield select(selectModulePluginsInPage);

  const isModulePresentInAppConfig = pagePluginsMap?.['Home']?.some((plugin: any) => {
    const module = appConfig?.getIn(['modules', plugin?.config?.moduleUUID]);
    return module?.tags?.includes(languageSwitchTileTag);
  });

  if (!isModulePresentInAppConfig && shouldDropPlugin && latestModuleUUID) {
    try {
      const homePageId = 'Home';
      const pageConfig = appConfig?.getPage(homePageId);

      if (pageConfig) {
        yield put({
          type: EditorActions.FETCH_TILE,
          payload: {
            moduleUUID: latestModuleUUID,
            localDefinition: false,
          },
        });

        yield put({
          type: DispatchActions.ADD_PLUGIN,
          payload: {
            layout: {},
            pluginType: 'ModuleInstance',
            configType: 'widget',
            moduleUUID: latestModuleUUID,
            bFetchTileFromServer: true,
            container: '',
            pageId: homePageId,
          },
        });

        yield take(DispatchActions.UPDATE_APP_CONFIG);

        // Add 1.5 second delay
        yield delay(1500);
      }
    } catch (err) {
      logger.error('Error adding multi language switch tile', err);
    }
  }

  yield put({
    type: EditorActions.APP_SAVE,
    payload: {
      newSave: false,
      showToast: true,
    },
  });
}

export default function* editorActionSagas(): SagaIterator {
  yield all([
    takeLatest(EditorActions.FETCH_APPCONFIG_AND_INIT, fetchAppConfigAndInitPageModelSaga),
    takeLatest(EditorActions.APP_SAVE, appSaveSaga),
    takeLatest(EditorActions.APP_SAVE_SUCCESS, appSaveSuccessSaga),
    takeLatest(EditorActions.APP_UPDATE, appUpdateSaga),
    takeLatest(DispatchActions.SELECT_PLUGIN, pluginSelectSaga),
    takeLatest(EditorActions.EDITOR_SELECT_NAV_COMPONENT, handleNavComponentSelect),
    takeLatest(EditorActions.PLUGIN_DELETE, handlePluginDelete),
    takeEvery(EditorActions.PLUGIN_UPDATE_ID, pluginUpdateId),
    takeLatest(EditorActions.MODULE_VARIANT_UPDATE, moduleVariantUpdate),
    takeEvery(DispatchActions.UPDATE_NAVIGATION_NAME, handleNavUpdateName),
    takeEvery(DispatchActions.DELETE_NAVIGATION_COMPONENT, handleNavDeleteComponent),
    takeLatest(EditorActions.EDITOR_SELECT_PAGE, pageSelectSaga),
    takeLatest(DispatchActions.UPDATE_PAGE_ID_DONE, handleUpdatePageId),
    takeLatest(EditorActions.EDITOR_COPY, handleEditorCopy),
    takeLatest(EditorActions.EDITOR_PASTE, handleEditorPaste),
    takeLatest(EditorActions.CONFIG_INJECTION, handleConfigInjection),
    takeLatest(EditorActions.SAVE_IMAGE_RECORD, handleSaveAppImageRecord),
    takeLatest(EditorActions.SAVE_IMAGE_RECORD_FAILED, handleSaveAppImageRecordFailed),
    takeLatest(EditorActions.SAVE_IMAGE_RECORD_FAILED, handleSaveAppImageRecordFailed),
    takeLatest(EditorActions.EDITOR_NAVIGATION_REORDERING, handleUpdateNavigationReordering),
    takeLatest(EditorActions.EDITOR_EVENT_REORDER_UP, handleEditorEventReorderUpward),
    takeLatest(EditorActions.EDITOR_EVENT_REORDER_DOWN, handleEditorEventReorderDownward),
    takeLatest(EditorActions.DELETE_IMAGE_RECORD, deleteAppImageRecord),
    takeLatest(EditorActions.IMPORT_IMAGE_RECORDS, handleBulkImportAppImageRecord),
    takeLatest(EditorActions.EDITOR_GENERATE_PAGE_MODEL_CACHES, handleGeneratePageModelCaches),
    takeLatest(EditorActions.EDITOR_CLEAR_PAGE_MODEL_CACHES, handleClearPageModelCaches),
    takeEvery(EditorActions.CONFIGURE_DATASOURCES, configureDatasources),
    takeEvery(EditorActions.CONFIGURE_DATASOURCES_WILE_ONBOARDING, configureDataSourcesWhileOnboarding),
    takeEvery(EditorActions.CLEAR_DATASOURCES_CREDENTIALS, clearDatasources),
    takeEvery(EditorActions.SOFT_RESTART_EDITOR, softRestartConfig),
    takeEvery(EditorActions.SET_EDITOR_PAGE_PARAMS, handleSetEditorPageParams),
    takeEvery(EditorActions.ADD_NEW_PAGE_IN_NAVIGATION, addNewPageInNavigation),
    takeEvery(EditorActions.REPLACE_APP_CONFIG, replaceAppConfigSaga),
    takeEvery(EditorActions.SEND_TILE_ANALYTICS, sendTileAnalyticsSaga),
    takeEvery(EditorActions.CHECK_HAS_ONE_SIGNAL, setHasOneSignalSaga),
    takeEvery(EditorActions.FETCH_ANALYTICS_URL, fetchAnalyticsUrl),
    takeLatest(EditorActions.CREATE_APP_BRANCH, handleCreateAppBranch),
    takeLatest(EditorActions.DELETE_BRANCH, handleDeleteAppBranch),
    takeLatest(EditorActions.SCHEDULE_OTA, handleScheduleOta),
    takeLatest(EditorActions.CHECK_OVERLAPPING_OTAS, handleFetchOverlappingOtas),
    takeLatest(EditorActions.ADD_FORCE_UPDATE_BANNER, addForceUpdateSaga),
    takeLatest(EditorActions.ADD_MULTI_LANGUAGE_SWITCH_PLUGIN, addMultiLanguageSwitchPluginSaga),
    takeLatest(EditorActions.EDITOR_NAVIGATION_REORDERINGV2, handleUpdateNavigationReorderingV2),
  ]);
}
