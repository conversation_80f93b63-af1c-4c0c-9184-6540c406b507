import {SagaIterator} from '@redux-saga/types';
import {all, put, takeLatest, call, select, take, delay} from 'redux-saga/effects';
import _ from 'lodash';
import {LivelyA<PERSON>} from '../api/LivelyApi';
import {
  CREATE_STREAM,
  CREATE_STREAM_SUCCESS,
  FETCH_FACEBOOK_PAGES,
  FETCH_FACEBOOK_PAGES_FAILED,
  FETCH_FACEBOOK_PAGES_SUCCESS,
  LIVELY_REGISTER,
  LIVELY_LOGIN,
  LIVELY_LOGIN_FAILED,
  LIVELY_LOGIN_TRIGGERED,
  LIVELY_LOGIN_SUCCESS,
  livelyLoginProps,
  streamCreationProps,
  livelyRegisterProps,
  loginToLively as loginToLivelyAction,
  LIVELY_REGISTER_FAILED,
  LIVELY_INTEGRATION_CODE,
  LIVELY_REGISTER_STATUS,
  LIVELY_REGISTER_SUCCESS,
  <PERSON>ET<PERSON>_FACEBOOK_TOKEN_SUCCESS,
  FET<PERSON>_FACEBOOK_TOKEN_FAILED,
  FET<PERSON>_FACEBOOK_TOKEN,
  LIVE_SELLING_INIT,
  IDisplayStreamDeleteConfirmPayload,
  DELETE_STREAM,
  DELETE_STREAM_SUCCESS,
  DELETE_STREAM_FAILED,
  LIVELY_FETCH_PAST_STREAMS,
  LIVELY_FETCH_PAST_STREAMS_SUCCESS,
  LIVELY_FETCH_PAST_STREAMS_FAILED,
  LIVELY_FETCH_UPCOMING_STREAMS,
  LIVELY_FETCH_IN_PROGRESS_STREAMS,
  LIVELY_FETCH_ACTIVE_STREAMS,
  LIVELY_FETCH_UPCOMING_STREAMS_SUCCESS,
  LIVELY_FETCH_UPCOMING_STREAMS_FAILED,
  LIVELY_FETCH_IN_PROGRESS_STREAMS_SUCCESS,
  LIVELY_FETCH_IN_PROGRESS_STREAMS_FAILED,
  CREATE_STREAM_FAILED,
  CLOSE_STREAM_DELETE_CONFIRM,
  DELETE_UPCOMING_STREAM,
  LIVELY_LOGOUT,
  LIVELY_LOGOUT_SUCCESS,
  FETCH_INSTAGRAM_TOKEN_SUCCESS,
  FETCH_INSTAGRAM_TOKEN_FAILED,
  FETCH_INSTAGRAM_PAGES_FAILED,
  FETCH_INSTAGRAM_PAGES_SUCCESS,
  FETCH_INSTAGRAM_PAGES,
  FETCH_INSTAGRAM_TOKEN,
  ILivelyWebsite,
} from '../actions/liveSellingActions';
import {
  APP_INTEGRATION_PAGE_SUCCESS,
  SAVE_APP_INTEGRATION_CREDENTIAL_SUCCESS,
  createAppIntegrationCredentials,
  saveAppIntegrationCredentials,
  saveAppState,
} from '../actions/editorActions';
import {DefaultRootState, useDispatch} from 'react-redux';
import localStorage from '../../../apptile-core/common/LocalStorage';
import {DispatchAction, LocalStorage} from 'apptile-core';
import moment from 'moment';
import {makeToast, removeToast} from '../actions/toastActions';
import {v4 as uuid} from 'uuid';
import {DASHBOARD_MESSAGES, LOGIN_MESSAGES} from '../../app/common/utils/apiErrorMessages/specificFeature';
import {COMMON_ERROR_MESSAGE} from '../../app/common/utils/apiErrorMessages/generalMessages';
import {handleApiError} from '../views/live/shared/CommonError';
import {dispatch} from '@segment/analytics-core';

export function handleErrorCondition(error: any, place: string) {
  let errorMessage = COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR;
  if (error.code === 'ERR_NETWORK') {
    errorMessage = COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR;
  } else if (!error.response && !error.code) {
    errorMessage = COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR;
  } else if (error.response?.status === 404) {
    console.error(`${error.code} - ${error.response?.data?.message}: `, error);
  } else if (error.response?.status === 401) {
    errorMessage = COMMON_ERROR_MESSAGE.ERROR.AUTH_ERROR;
  }
  console.error(`At ${place} - ${errorMessage}`, error);
  return errorMessage;
}

function redirectToScionBranch() {
  const url = new URL(window.location.href);
  url.searchParams.set('build', 'v3-roadmap-3159');
  window.location.href = url.toString();
}

export function* registerToLively(action: {payload: livelyRegisterProps}): SagaIterator {
  try {
    const {email, password, appId} = action.payload;
    const appName = (yield select(state => state?.orgs?.appsById?.[appId]?.name))?.replace(/[^\da-z]/gim, '_');
    const authResponse = yield call(LivelyApi.Register, email, password, appName, appName.slice(0, 20));
    const companyId = authResponse.data?.user?.company_id;
    const livelyIntegration = yield select((state: DefaultRootState) => {
      const allIntegrations = state.integration.appIntegrationsById;
      return allIntegrations[
        Object.keys(allIntegrations).find(e => allIntegrations[e].integrationCode == LIVELY_INTEGRATION_CODE)
      ];
    });
    yield put({
      type: LIVELY_REGISTER_STATUS,
      payload: {
        registrationState: 'Configuring live streaming for your app',
      },
    });
    yield put(loginToLivelyAction({email, password, appId, directAuth: true, buildRedirection: false}));
    yield take(LIVELY_LOGIN_SUCCESS);
    const authToken = yield select(state => state?.liveSelling?.auth?.authToken);
    const apptileAppId = yield select(state => state?.apptile?.appId);
    const websiteId = yield select(state => state?.liveSelling?.auth?.livelyUser?.website_id);
    //Add metadata to company
    yield call(LivelyApi.addMetadataToCompany, authToken, {app_id: apptileAppId});
    const storefrontApiUrl = yield select(state => state?.appModel.getModelValue(['shopify', 'storefrontApiUrl']));
    const storefrontAccessToken = yield select(state =>
      state?.appModel.getModelValue(['shopify', 'storefrontAccessToken']),
    );
    const storeUrl = storefrontApiUrl.slice(storefrontApiUrl.indexOf('//') + 2, storefrontApiUrl.indexOf('/api'));
    const saveShopifyCreds = yield call(LivelyApi.saveShopifyCreds, authToken, storefrontAccessToken, storeUrl);
    yield put({
      type: LIVELY_REGISTER_STATUS,
      payload: {
        registrationState: 'Adding live streaming page to your app',
      },
    });
    const widgetsResponse = yield call(LivelyApi.getWidgets, authToken);
    const pastStreamsWidgetId = widgetsResponse?.data?.data?.[0]?.widget_uuid;

    if (livelyIntegration) {
      yield put(
        saveAppIntegrationCredentials(
          appId,
          livelyIntegration.appIntegrations[0].id,
          {brandId: companyId, livelyLoginEmail: email, pastStreamsWidgetId},
          LIVELY_INTEGRATION_CODE,
        ),
      );
    } else {
      const tempPages = yield select(state => state.pages?.tempPages);
      yield put(
        createAppIntegrationCredentials(
          appId,
          {brandId: companyId, livelyLoginEmail: email, pastStreamsWidgetId},
          LIVELY_INTEGRATION_CODE,
          tempPages,
        ),
      );
    }
    if (livelyIntegration) {
      yield take(SAVE_APP_INTEGRATION_CREDENTIAL_SUCCESS);
    } else {
      yield take(APP_INTEGRATION_PAGE_SUCCESS);
    }
    yield put({
      type: LIVELY_REGISTER_STATUS,
      payload: {
        registrationState: 'Saving your app',
      },
    });
    yield call(
      () =>
        new Promise(resolve => {
          setTimeout(resolve, 600);
        }),
    );
    yield put(saveAppState(false, true, 'Live selling enabled'));
    yield put({
      type: LIVELY_REGISTER_SUCCESS,
    });

    if (websiteId != ILivelyWebsite.SHOPIFY) {
      redirectToScionBranch();
    }

  } catch (er) {
    console.log('LIVELY_REGISTER_FAILED', er);
    yield put({
      type: LIVELY_REGISTER_FAILED,
      payload: {
        registerError: 'Something is wrong please try again',
      },
    });
  }
}

export function* loginToLively(action: {payload: livelyLoginProps}): SagaIterator {
  try {
    yield put({
      type: LIVELY_LOGIN_TRIGGERED,
    });
    const {email, appId, password, directAuth, buildRedirection} = action.payload;
    const authResponse = yield call(directAuth ? LivelyApi.LivelyLogin : LivelyApi.Login, email, password);
    // yield call(LivelyApi.addMetadataToCompany, authResponse.data?.user?.token, appId);
    const toastAction = makeToast({
      content: LOGIN_MESSAGES.SUCCESS.LOGIN_SUCCESS,
      appearances: 'success',
      duration: 3000,
    });
    yield put(toastAction);
    yield put({
      type: LIVELY_LOGIN_SUCCESS,
      payload: {
        authToken: authResponse.data?.user?.token,
        livelyUser: authResponse.data?.user,
        livelyAppId: appId,
      },
    });
    localStorage.setValue('authToken', authResponse.data?.user?.token);
    localStorage.setValue('livelyAppId', appId);
    localStorage.setValue('liveLoginEmail', email);
    localStorage.setValue('livelyUser', authResponse.data?.user);
    
    // If the partner is not shopify, redirect to v3 roadmap build
    if (buildRedirection && authResponse?.data?.user?.website_id != ILivelyWebsite.SHOPIFY) {
      redirectToScionBranch();
    }

  } catch (error) {
    let errorMessage = COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR;
    if (error.code === 'ERR_NETWORK') {
      errorMessage = COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR;
    } else if (!error.response && !error.code) {
      errorMessage = COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR;
    } else if (error?.response?.data?.data?.message == 'Authentication Failed') {
      errorMessage = LOGIN_MESSAGES.ERROR.LOGIN_FAILED;
    }
    const toastAction = makeToast({
      content: errorMessage,
      appearances: 'error',
      duration: 3000,
    });
    console.error(errorMessage, error);
    if (errorMessage !== LOGIN_MESSAGES.ERROR.LOGIN_FAILED) {
      yield put(toastAction);
    }

    yield put({
      type: LIVELY_LOGIN_FAILED,
      payload: {
        loginError: errorMessage,
      },
    });
  }
}

export function* fetchFacebookPages(): SagaIterator {
  try {
    const authToken = yield select(state => state.liveSelling.auth.authToken);
    const facebookPages = yield call(LivelyApi.getFacebookPages, authToken);
    console.log(`facebookPages`, facebookPages);
    yield put({
      type: FETCH_FACEBOOK_PAGES_SUCCESS,
      payload: facebookPages.data.data,
    });
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'fetchFacebookPages');

    const isNetworkError = errorMessage === COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR;
    if (!isNetworkError) {
      yield put(makeToast({content: errorMessage, appearances: 'error', duration: 2000}));
    }

    yield put({
      type: FETCH_FACEBOOK_PAGES_FAILED,
      payload: {
        noFacebookLogin: error?.response?.status == 403,
        isNetworkError: isNetworkError,
      },
    });
  }
}

export function* fetchFacebookToken(action: {payload: {streamingId: string}}): SagaIterator {
  try {
    const authToken = yield select(state => state.liveSelling.auth.authToken);
    const facebookToken = yield call(LivelyApi.getFacebookToken, authToken, action.payload.streamingId);
    yield put({
      type: FETCH_FACEBOOK_TOKEN_SUCCESS,
      payload: {
        streamingId: action.payload.streamingId,
        ...facebookToken?.data?.data,
      },
    });
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'fetchFacebookToken');
    yield put({
      type: FETCH_FACEBOOK_TOKEN_FAILED,
      payload: {
        streamingId: action.payload.streamingId,
        facebookTokenError: errorMessage,
      },
    });
  }
}

export function* fetchInstagramPages(): SagaIterator {
  try {
    const authToken = yield select(state => state.liveSelling.auth.authToken);
    const instagramPages = yield call(LivelyApi.getInstagramPages, authToken);
    console.log(`instagramPages`, instagramPages);
    const verifiedPages = [];
    for (let i in instagramPages.data.data) {
      const page = instagramPages.data.data[i];
      try {
        const sessionCheck = yield call(LivelyApi.checkInstagramSession, authToken, page?.account_handle);
        console.log('sessionCheck', sessionCheck);
        verifiedPages.push(page);
      } catch (err) {
        console.log('session check failed', err);
      }
    }
    yield put({
      type: FETCH_INSTAGRAM_PAGES_SUCCESS,
      payload: verifiedPages,
    });
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'fetchInstagramPages');

    const isNetworkError = errorMessage === COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR;
    if (!isNetworkError) {
      yield put(makeToast({content: errorMessage, appearances: 'error', duration: 2000}));
    }
    yield delay(2000);
    yield put({
      type: FETCH_INSTAGRAM_PAGES_FAILED,
      payload: {
        noInstagramLogin: error?.response?.status == 403,
        isNetworkError: isNetworkError,
      },
    });
  }
}

export function* fetchInstagramToken(action: {payload: {streamingId: string; instaAuthId: string}}): SagaIterator {
  try {
    const authToken = yield select(state => state.liveSelling.auth.authToken);
    const instagramToken = yield call(
      LivelyApi.getInstagramToken,
      authToken,
      action.payload.streamingId,
      action.payload.instaAuthId,
    );
    yield put({
      type: FETCH_INSTAGRAM_TOKEN_SUCCESS,
      payload: {
        streamingId: action.payload.streamingId,
        ...instagramToken?.data?.data,
      },
    });
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'fetchInstagramToken');
    yield put({
      type: FETCH_INSTAGRAM_TOKEN_FAILED,
      payload: {
        streamingId: action.payload.streamingId,
        instagramTokenError: errorMessage,
      },
    });
  }
}

export function* createStream(action: {payload: streamCreationProps}): SagaIterator {
  try {
    const authToken = yield select(state => state.liveSelling.auth.authToken);
    const {
      topic,
      description,
      thumbnailUrl,
      platforms,
      streamProducts,
      fbAuthId,
      instaAuthId,
      scheduleDate,
      hasNotification,
      notificationInfo,
      hasStoreCredit,
      storeCreditInfo,
    } = action.payload;
    const currentTime = Math.floor(scheduleDate.getTime() / 1000);
    const endTime = currentTime + 18000;
    const createResponse = yield call(
      LivelyApi.apptileCreateLiveStream,
      authToken,
      topic,
      description,
      thumbnailUrl,
      fbAuthId,
      instaAuthId,
      platforms,
      streamProducts,
      currentTime,
      endTime,
      {},
      hasNotification,
      notificationInfo,
      hasStoreCredit,
      storeCreditInfo,
    );

    yield put({
      type: CREATE_STREAM_SUCCESS,
      payload: createResponse?.data,
    });
    yield put(makeToast({content: DASHBOARD_MESSAGES.SUCCESS.STREAM_CREATED, appearances: 'success', duration: 2000}));
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'createStream');
    yield put(makeToast({content: errorMessage, appearances: 'error', duration: 2000}));
    yield put({
      type: CREATE_STREAM_FAILED,
      streamCreationError: errorMessage,
    });
  }
}

export function* initLiveSellingDashboard(action: any): SagaIterator {
  const authToken = yield call(localStorage.getValue, 'authToken');
  const livelyUser = yield call(localStorage.getValue, 'livelyUser');
  const livelyAppId = yield call(localStorage.getValue, 'livelyAppId');
  const companyInfo = yield call(localStorage.getValue, 'companyInfo');

  yield put({
    type: LIVELY_LOGIN_SUCCESS,
    payload: {
      authToken: authToken,
      livelyAppId: livelyAppId,
      livelyUser: livelyUser,
      companyInfo: companyInfo,
    },
  });
}

async function getFeedIdFromFileId(authToken: string, fileId: string) {
  try {
    const {data} = (await LivelyApi.getFeeds(authToken)) as any;
    let feed = data.data.feeds.find((feed: any) => feed.meta?.file_id === fileId);
    if (feed) return feed._id;
    else {
      let currentPage = 2;
      const totalPages = data.data.pagination.totalPages;
      while (currentPage <= totalPages) {
        const {data} = (await LivelyApi.getFeeds(authToken, currentPage)) as any;
        let feed = data.data.feeds.find((feed: any) => feed.meta?.file_id == fileId);
        if (feed) {
          return feed._id;
        }
        currentPage++;
      }
    }
  } catch (error) {
    handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.GENERIC_ERROR, dispatch, 'getFeedIdFromFileId');
  }
}

export function* deleteStream(action: DispatchAction<IDisplayStreamDeleteConfirmPayload>): SagaIterator {
  const deleteProgressId = uuid();

  try {
    const {streamId, feedFileId} = action.payload;
    const authToken = yield select(state => state?.liveSelling?.auth?.authToken);

    let deleteFeedResponse;

    if (!_.isEmpty(feedFileId)) {
      console.log('xxx feedId', feedFileId);
      const feedId = yield call(getFeedIdFromFileId, authToken, feedFileId);

      if (!_.isEmpty(feedId)) {
        deleteFeedResponse = yield call(LivelyApi.deleteFeed, authToken, feedId);
        const archiveStream = yield call(LivelyApi.apptileArchiveStream, authToken, streamId);

        yield put({
          type: LIVELY_FETCH_PAST_STREAMS,
        });
        yield put({
          type: LIVELY_FETCH_ACTIVE_STREAMS,
        });
        yield put({
          type: CLOSE_STREAM_DELETE_CONFIRM,
        });

        yield put({
          type: DELETE_STREAM_SUCCESS,
          payload: {
            deleteFeedResponse,
            archiveStream,
          },
        });

        yield put(removeToast(deleteProgressId));
        yield put(
          makeToast({content: DASHBOARD_MESSAGES.SUCCESS.STREAM_DELETION, appearances: 'success', duration: 2000}),
        );
      } else {
        console.error('empty feedId');
        yield put(
          makeToast({content: COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR, appearances: 'error', duration: 2000}),
        );
      }
    }
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'deleteStream');

    yield put(makeToast({content: errorMessage, appearances: 'error', duration: 2000}));
    yield put({
      type: DELETE_STREAM_FAILED,
      payload: errorMessage,
    });
  }
}

export function* deleteUpcomingStream(action: DispatchAction<IDisplayStreamDeleteConfirmPayload>): SagaIterator {
  const deleteProgressId = uuid();

  try {
    const {streamId, onCreateClose} = action.payload;
    const authToken = yield select(state => state?.liveSelling?.auth?.authToken);

    let deleteFeedResponse;

    const archiveStream = yield call(LivelyApi.apptileArchiveStream, authToken, streamId);

    yield put({
      type: LIVELY_FETCH_PAST_STREAMS,
    });
    yield put({
      type: LIVELY_FETCH_ACTIVE_STREAMS,
    });
    yield put({
      type: CLOSE_STREAM_DELETE_CONFIRM,
    });

    yield put({
      type: DELETE_STREAM_SUCCESS,
      payload: {
        deleteFeedResponse,
        archiveStream,
      },
    });

    if (onCreateClose && typeof onCreateClose === 'function') {
      onCreateClose();
    }

    yield put(removeToast(deleteProgressId));
    yield put(makeToast({content: DASHBOARD_MESSAGES.SUCCESS.STREAM_DELETION, appearances: 'success', duration: 2000}));
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'deleteUpcomingStream');

    yield put(makeToast({content: errorMessage, appearances: 'error', duration: 2000}));
    yield put({
      type: DELETE_STREAM_FAILED,
      payload: errorMessage,
    });
  }
}

export function* livelyFetchPastStreams(): SagaIterator {
  try {
    const startDate = moment().subtract(7, 'days').format('YYYY-MM-DD');
    const endDate = moment().format('YYYY-MM-DD');
    const authToken = yield select(state => state?.liveSelling?.auth?.authToken);
    const response = yield call(LivelyApi.getPastStreams, authToken, startDate, endDate);
    const pastLiveStreams = response?.data?.data?.live_streams;
    const pastRecondings = pastLiveStreams?.data?.data?.live_streams?.filter(stream => stream?.recording) || [];
    yield put({
      type: LIVELY_FETCH_PAST_STREAMS_SUCCESS,
      payload: {
        data: pastLiveStreams,
        recordings: pastRecondings,
      },
    });
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'livelyFetchPastStreams');
    const isNetworkError = errorMessage === COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR;
    if (!isNetworkError) {
      yield put(makeToast({content: errorMessage, appearances: 'error', duration: 2000}));
    }
    yield put({
      type: LIVELY_FETCH_PAST_STREAMS_FAILED,
      payload: {errorMessage, isNetworkError: isNetworkError},
    });
  }
}

export function* livelyFetchInProgressStreams(): SagaIterator {
  try {
    const startDate = moment().format('YYYY-MM-DD');
    const authToken = yield select(state => state?.liveSelling?.auth?.authToken);
    const inProgressLiveStreams = yield call(LivelyApi.getInProgressStreams, authToken, startDate, '');
    const InProgressStreams = inProgressLiveStreams?.data?.data?.live_streams;
    if (_.isArray(InProgressStreams)) {
      yield put({
        type: LIVELY_FETCH_IN_PROGRESS_STREAMS_SUCCESS,
        payload: {
          data: InProgressStreams,
        },
      });
    } else {
      console.error('livelyFetchInProgressStreams', InProgressStreams);
      yield put({
        type: LIVELY_FETCH_IN_PROGRESS_STREAMS_FAILED,
        payload: {errorMessage: COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR, isNetworkError: false},
      });
    }
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'livelyFetchInProgressStreams');

    const isNetworkError = errorMessage === COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR;
    if (!isNetworkError) {
      yield put(makeToast({content: errorMessage, appearances: 'error', duration: 2000}));
    }
    yield put({
      type: LIVELY_FETCH_IN_PROGRESS_STREAMS_FAILED,
      payload: {errorMessage, isNetworkError: isNetworkError},
    });
  }
}

export function* livelyFetchUpcomingStreams(): SagaIterator {
  try {
    const startDate = moment().format('YYYY-MM-DD');
    const authToken = yield select(state => state?.liveSelling?.auth?.authToken);
    const response = yield call(LivelyApi.getUpcomingStreams, authToken, startDate, '');
    const upcomingLiveStreams = response?.data?.data?.live_streams;
    if (_.isArray(upcomingLiveStreams)) {
      yield put({
        type: LIVELY_FETCH_UPCOMING_STREAMS_SUCCESS,
        payload: {
          data: upcomingLiveStreams,
        },
      });
    } else {
      console.error('livelyFetchUpcomingStreams', upcomingLiveStreams);
      yield put({
        type: LIVELY_FETCH_UPCOMING_STREAMS_FAILED,
        payload: {errorMessage: COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR, isNetworkError: false},
      });
    }
  } catch (error) {
    let errorMessage = handleErrorCondition(error, 'livelyFetchUpcomingStreams');
    const isNetworkError = errorMessage === COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR;
    if (!isNetworkError) {
      yield put(makeToast({content: errorMessage, appearances: 'error', duration: 2000}));
    }
    yield put({
      type: LIVELY_FETCH_UPCOMING_STREAMS_FAILED,
      payload: {errorMessage, isNetworkError: isNetworkError},
    });
  }
}

export function* livelyFetchActiveStreams(): SagaIterator {
  yield put({
    type: LIVELY_FETCH_UPCOMING_STREAMS,
  });
  yield put({
    type: LIVELY_FETCH_IN_PROGRESS_STREAMS,
  });
}

export function* livelyLogout(): SagaIterator {
  const liveLoginEmail = yield call(LocalStorage.getValue, 'liveLoginEmail');
  const appId = yield call(LocalStorage.getValue, 'livelyAppId');
  localStorage.removeItem('authToken');
  localStorage.removeItem('livelyUser');
  window.location.href = `${window.location.origin}/live-selling/login?liveLoginEmail=${liveLoginEmail}&app-id=${appId}`;
  yield put({
    type: LIVELY_LOGOUT_SUCCESS,
  });
}

export default function* liveSellingSagas(): SagaIterator {
  yield all([
    takeLatest(LIVE_SELLING_INIT, initLiveSellingDashboard),
    takeLatest(LIVELY_REGISTER, registerToLively),
    takeLatest(LIVELY_LOGIN, loginToLively),
    takeLatest(FETCH_FACEBOOK_PAGES, fetchFacebookPages),
    takeLatest(FETCH_FACEBOOK_TOKEN, fetchFacebookToken),
    takeLatest(FETCH_INSTAGRAM_PAGES, fetchInstagramPages),
    takeLatest(FETCH_INSTAGRAM_TOKEN, fetchInstagramToken),
    takeLatest(CREATE_STREAM, createStream),
    takeLatest(DELETE_STREAM, deleteStream),
    takeLatest(DELETE_UPCOMING_STREAM, deleteUpcomingStream),
    takeLatest(LIVELY_FETCH_PAST_STREAMS, livelyFetchPastStreams),
    takeLatest(LIVELY_FETCH_UPCOMING_STREAMS, livelyFetchUpcomingStreams),
    takeLatest(LIVELY_FETCH_IN_PROGRESS_STREAMS, livelyFetchInProgressStreams),
    takeLatest(LIVELY_FETCH_ACTIVE_STREAMS, livelyFetchActiveStreams),
    takeLatest(LIVELY_LOGOUT, livelyLogout),
  ]);
}
