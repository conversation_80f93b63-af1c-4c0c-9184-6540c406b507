import {DispatchActions, DispatchEmptyAction} from 'apptile-core';
import {SagaIterator} from '@redux-saga/types';
import {all, call, fork, put, takeLatest} from 'redux-saga/effects';
import {
  CreateOrgAppPayload,
  CREATE_ORG_APP,
  DeleteOrgAppPayload,
  DELETE_APP,
  DELETE_APP_FAILED,
  DELETE_APP_SUCCESS,
  DispatchAction,
  FETCH_CURRENT_APP,
  FETCH_CURRENT_APP_FAILED,
  FETCH_CURRENT_APP_SUCCESS,
  FETCH_ORGS,
  DESTROY_APP_ASSETS,
  FETCH_ORGS_FAILED,
  CHANGE_APP_CONTEXT_DATA,
  ChangeAppContextPayload as ChangeAppContextDataPayload,
  FETCH_BRANCHES,
} from '../actions/editorActions';
import {ICurrentApp} from '../api/ApiTypes';
import AppApi from '../api/AppApi';
import Org<PERSON><PERSON> from '../api/OrgApi';
import {getEntityBulkData} from '../integrations/shopify/ShopifyObjectsLoader';

export function* deleteAppSaga(action: DispatchAction<DeleteOrgAppPayload>): SagaIterator {
  try {
    if (!action.payload) {
      yield put({
        type: DELETE_APP_FAILED,
      });
      return;
    }
    const appId = action.payload.appId;
    const orgId = action.payload.orgId;
    yield call(OrgApi.deleteApp, orgId, appId);
    yield put({
      type: DELETE_APP_SUCCESS,
      payload: action.payload,
    });
  } catch (e) {
    yield put({
      type: DELETE_APP_FAILED,
    });
  }
}

export function* createOrgsSaga(action: DispatchAction<CreateOrgAppPayload>): SagaIterator {
  try {
    const {orgId, name, baseBlueprintId} = action.payload;
    yield call(OrgApi.createOrgsApps, orgId, name, baseBlueprintId);
    yield put({
      type: FETCH_ORGS,
    });
  } catch (e) {
    yield put({
      type: FETCH_ORGS_FAILED,
    });
  }
}

export function* fetchCurrentApp(): SagaIterator {
  try {
    const response = yield call(AppApi.fetchCurrentApp);
    const {organizationId, uuid} = response.data as ICurrentApp;
    yield put({
      type: DispatchActions.CHANGE_APPCONFIG,
      payload: {
        orgId: organizationId,
        appId: uuid,
      },
    });
  } catch (e) {
    yield put({
      type: FETCH_CURRENT_APP_FAILED,
    });
  }
}

export function* changeAppContextData(action: DispatchAction<ChangeAppContextDataPayload>): SagaIterator {
  const {appId} = action.payload;
  yield call(getEntityBulkData, 'PRODUCT', appId);
  yield call(getEntityBulkData, 'COLLECTION', appId);
  yield call(getEntityBulkData, 'BLOG', appId);
  yield call(getEntityBulkData, 'IMAGES', appId);
  // yield call(getEntityBulkData, 'APPTILE_ORDERS', appId);
}

export function* cleanUpApp(): SagaIterator {
  yield all([
    put({
      type: DispatchActions.DESTROY_APP_CONFIG,
    }),
    put({
      type: DispatchActions.DESTROY_APP_MODEL,
    }),
    put({
      type: DispatchActions.DESTROY_APPTILE_CONFIG,
    }),
    put({
      type: DispatchActions.DESTROY_APPTILE_THEME,
    }),
    put({
      type: DESTROY_APP_ASSETS,
    }),
  ]);
}

export default function* appSagas(): SagaIterator {
  yield all([
    takeLatest(DELETE_APP, deleteAppSaga),
    takeLatest(CREATE_ORG_APP, createOrgsSaga),
    takeLatest(FETCH_CURRENT_APP, fetchCurrentApp),
    takeLatest(CHANGE_APP_CONTEXT_DATA, changeAppContextData),
    takeLatest(DispatchActions.CLEAN_UP_APP, cleanUpApp),
  ]);
}
