/**
 * ApptileWebIcons icon set component.
 * Usage: <ApptileWebIcons name="icon-name" size={20} color="#4F8EF7" />
 */

import createIconSet from 'react-native-vector-icons/lib/create-icon-set';
const glyphMap = {
  "TRBL-button-top": 59905,
  "TRBL-button": 59906,
  "accordion": 59907,
  "add-image": 59908,
  "alignment-bottom": 59909,
  "alignment-center": 59910,
  "alignment-left": 59911,
  "alignment-middle": 59912,
  "alignment-right": 59913,
  "alignment-top": 59914,
  "analytics-outline": 59915,
  "apptile-live": 59916,
  "back-arrow": 59917,
  "badge": 59918,
  "bell-outline": 59919,
  "book-outline": 59920,
  "border-radius-left": 59921,
  "border-radius": 59922,
  "brands-outline": 59923,
  "button": 59924,
  "calender": 59925,
  "checkbox": 59926,
  "clock-refresh": 59927,
  "clock": 59928,
  "container": 59929,
  "datasource": 59930,
  "delete": 59931,
  "document": 59932,
  "down-arrow": 59933,
  "download-outline": 59934,
  "edit-icon": 59935,
  "expression": 59936,
  "gear-outline": 59937,
  "go-live": 59938,
  "help": 59939,
  "house-outline": 59940,
  "icon": 59941,
  "image-carousel": 59942,
  "image-list": 59943,
  "image": 59944,
  "integration-disconnect": 59945,
  "integrations": 59946,
  "list-view": 59947,
  "local-storage": 59948,
  "magicwand": 59949,
  "magnify": 59950,
  "menu-vertical": 59951,
  "modal": 59952,
  "notifications": 59953,
  "pages-outline": 59954,
  "pages": 59955,
  "percentage-outline": 59956,
  "pills": 59957,
  "pin-fill": 59958,
  "pin-outline": 59959,
  "query": 59960,
  "radio-button": 59961,
  "rich-text": 59962,
  "select": 59963,
  "send-icon": 59964,
  "settings": 59965,
  "slider-range": 59966,
  "small-shop": 59967,
  "snapshots-outline": 59968,
  "star-rating": 59969,
  "state": 59970,
  "switch": 59971,
  "text-input": 59972,
  "text": 59973,
  "themes": 59974,
  "tiles-old": 59975,
  "tiles": 59976,
  "timer": 59977,
  "video-player": 59978,
  "web-view": 59979,
  "your-design": 59980
};

const iconSet = createIconSet(glyphMap, 'ApptileWebIcons', 'ApptileWebIcons.ttf');

export default iconSet;
export const {
  Button,
  getImageSource,
  getImageSourceSync,
} = iconSet;

