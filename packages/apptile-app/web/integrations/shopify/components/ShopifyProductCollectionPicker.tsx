import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Text, StyleSheet, Image, View, Pressable} from 'react-native';
import {ShopifyObjectCacheCollection, ShopifyObjectCacheProduct} from '../ShopifyObjectCacheTypes';
import Fuse from 'fuse.js';
import _, {debounce} from 'lodash';
import {replaceNullCollectionImage, replaceProductImage} from '../ShopifyObjectCache';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';
import theme from '@/root/web/styles-v2/theme';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import PopoverComponent from '../../../components-v2/base/Popover';
import {
  TransformGetProductsPaginatedQuery,
  TransformSearchCollections,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {datasourceTypeModelSel} from 'apptile-core';
import CodeInputControlV2 from '@/root/web/components/controls-v2/CodeInputControl';

const FUSE_OPTIONS = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  // includeMatches: true,
  findAllMatches: true,
  minMatchCharLength: 1,
  // location: 0,
  // threshold: 0.6,
  // distance: 100,
  // useExtendedSearch: false,
  ignoreLocation: true,
  // ignoreFieldNorm: false,
  // fieldNormWeight: 1,
  keys: ['title', 'handle'],
};

export enum ItemTypeEnum {
  PRODUCT = 'product',
  COLLECTION = 'collection',
}

export interface ShopifyItemPickerProps {
  itemType: ItemTypeEnum;
  currentItem: null | {
    value: ShopifyObjectCacheProduct | ShopifyObjectCacheCollection;
    type: ItemTypeEnum;
  };
  onProductChange: (item: null | ShopifyObjectCacheProduct) => void;
  onCollectionChange: (item: null | ShopifyObjectCacheCollection) => void;
}

export interface ShopifyProductCollectionPickerProps {
  name?: string;
  productHandle: string;
  collectionHandle: string;
  label: string;
  onProductChange: (value: any) => void;
  onCollectionChange: (value: any) => void;
  queryRunner: any;
  config?: Immutable.Map<string, any>;
  defaultOpen?: boolean;
  setOpened?: any;
  nonPopoverMode?: boolean;
}

export const ShopifyProductCollectionPicker: React.FC<ShopifyProductCollectionPickerProps> = (
  props: ShopifyProductCollectionPickerProps,
) => {
  const {
    name,
    productHandle,
    collectionHandle,
    label,
    onProductChange,
    onCollectionChange,
    config,
    defaultOpen = false,
    setOpened,
    nonPopoverMode = false,
  } = props;
  let {queryRunner} = props;
  const [currentItem, setCurrentItem] = useState<{
    value: ShopifyObjectCacheProduct | ShopifyObjectCacheCollection;
    type: ItemTypeEnum;
  } | null>(null);
  const [isEditing, setEditing] = useState(defaultOpen);
  const [shopifyItems, setShopifyItems] = useState<{
    products: ShopifyObjectCacheProduct[] | null;
    collections: ShopifyObjectCacheCollection[] | null;
  } | null>(null);
  const [fuseProductsSearch, setFuseProductSearch] = useState(new Fuse(shopifyItems?.products ?? [], FUSE_OPTIONS));
  const [fuseCollectionsSearch, setFuseCollectionsSearch] = useState(
    new Fuse(shopifyItems?.collections ?? [], FUSE_OPTIONS),
  );
  const [query, setQuery] = useState('');
  const [filteredProductItems, setFilteredProductItems] = useState(
    shopifyItems?.products.sort((a: any, b: any) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()),
  );
  const [filteredCollectionItems, setFilteredCollectionItems] = useState(
    shopifyItems?.collections.sort(
      (a: any, b: any) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
    ),
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isCollectionsLoading, setIsCollectionsLoading] = useState(true);

  const onItemChanged = useCallback(
    item => {
      if (setOpened) setOpened(false);
      if (item.type === ItemTypeEnum.PRODUCT) {
        onProductChange(item.value);
        onCollectionChange('');
      }
      if (item.type === ItemTypeEnum.COLLECTION) {
        onCollectionChange(item.value);
        onProductChange('');
      }
      debouncedSetQuery('');
      setEditing(false);
    },
    [onProductChange, onCollectionChange, setOpened],
  );
  const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const ShopifyDSModel = shopifyModelSel ? shopifyModelSel(store.getState()) : null;
  if (!queryRunner) {
    queryRunner = ShopifyDSModel?.get('queryRunner');
  }
  const getProducts = async () => {
    if (queryRunner && queryRunner.runQuery) {
      const countryCode = ShopifyDSModel?.getIn(['shop', 'paymentSettings', 'countryCode']) ?? 'US';
      setIsLoading(true);
      const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.SEARCH_PRODUCTS_FOR_DROPDOWN, {
        first: 50,
        query: query,
        countryCode,
      });
      const {transformedData: queryTransformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformGetProductsPaginatedQuery},
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      setFilteredProductItems(replaceProductImage(queryTransformedData));
      setIsLoading(false);
      return queryTransformedData;
    } else {
      return [];
    }
  };
  const getCollections = async () => {
    if (queryRunner && queryRunner.runQuery) {
      const countryCode = ShopifyDSModel?.getIn(['shop', 'paymentSettings', 'countryCode']) ?? 'US';
      setIsCollectionsLoading(true);
      const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.SEARCH_COLLECTIONS_FOR_DROPDOWN, {
        first: 3,
        query: query,
        countryCode,
      });
      const {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformSearchCollections},
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      setFilteredCollectionItems(replaceNullCollectionImage(transformedData));
      setIsCollectionsLoading(false);
      return transformedData;
    } else {
      return [];
    }
  };
  const debouncedSetQuery = debounce(setQuery, 150);
  useEffect(() => {
    (async function () {
      if (productHandle) {
        const products = await getProducts();
        setCurrentItem({type: ItemTypeEnum.PRODUCT, value: _.find(products, item => item?.handle === productHandle)});
        setEditing(false);
      } else if (collectionHandle) {
        const collections = await getCollections();
        setCurrentItem({
          type: ItemTypeEnum.COLLECTION,
          value: _.find(collections, item => item?.handle === collectionHandle),
        });
        setEditing(false);
      } else {
        setCurrentItem(null);
      }
    })();
  }, [productHandle, collectionHandle]);

  useEffect(() => {
    if (query === productHandle) setQuery('');
    if (query === collectionHandle) setQuery('');
    if (setOpened) setOpened(isEditing);
    (async function () {
      const getItems = async () => {
        setIsLoading(true);
        setIsCollectionsLoading(true);
        const collections = await getCollections();
        const products = await getProducts();
        const items = {
          products,
          collections,
        };
        setShopifyItems(items);
      };
      getItems();
    })();
  }, [isEditing]);
  useEffect(() => {
    setFilteredProductItems(
      shopifyItems?.products
        ?.sort((a: any, b: any) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        ?.slice(0, 50),
    );
    setIsLoading(false);
  }, [shopifyItems?.products]);
  useEffect(() => {
    setFilteredCollectionItems(
      shopifyItems?.collections
        ?.sort((a: any, b: any) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        ?.slice(0, 50),
    );
    setIsCollectionsLoading(false);
  }, [shopifyItems?.collections]);
  useEffect(() => {
    fuseCollectionsSearch.setCollection(shopifyItems?.collections ?? []);
  }, [fuseCollectionsSearch, shopifyItems?.collections]);
  useEffect(() => {
    fuseProductsSearch.setCollection(shopifyItems?.products ?? []);
  }, [fuseProductsSearch, shopifyItems?.products]);
  useEffect(() => {
    setIsLoading(true);
    getProducts();
  }, [fuseProductsSearch, query]);
  useEffect(() => {
    setIsCollectionsLoading(true);
    getCollections();
  }, [fuseCollectionsSearch, query]);
  useMountEffect(() => {
    setIsLoading(true);
    setIsCollectionsLoading(true);
    (async function () {
      const getItems = async () => {
        const products = await getProducts();
        const collections = await getCollections();
        const items = {
          products,
          collections,
        };
        setShopifyItems(items);
        // if (!products.length) window.setTimeout(getItems, 1000);
      };
      getItems();
    })();
  });
  const buttonRef = useRef(null);

  const pickerBody = (
    <View
      style={{
        width: buttonRef?.current?.getBoundingClientRect().width,
        marginTop: 4,
        backgroundColor: '#ffffff',
        overflow: 'hidden',
        borderRadius: 6,
        borderColor: '#DADADA',
        borderWidth: 1,
      }}>
      <View>
        <ApptileWebIcon
          style={{position: 'absolute', marginTop: 12, marginLeft: 6, zIndex: 1}}
          name={'magnify'}
          size={19}
          color={theme.CONTROL_INPUT_COLOR}
        />
        <CodeInputControlV2
          placeholder={'Search '}
          singleLine={true}
          defaultValue={query}
          autoFocus={true}
          inputStyles={{paddingLeft: 26}}
          onChange={function (value: string): void {
            debouncedSetQuery(value);
          }}
        />
      </View>
      <View
        style={{
          height: 250,
          overflow: 'scroll',
          paddingHorizontal: 14,
          paddingTop: 9,
        }}>
        <View>
          <Text style={[commonStyles.baseText, styles.itemHeader]}>Collections</Text>
          <View style={{flex: 1, marginLeft: 10}}>
            <ShopifyProductCollectionBody
              itemType={ItemTypeEnum.COLLECTION}
              query={query}
              filteredItems={filteredCollectionItems}
              isLoading={isCollectionsLoading}
              debouncedSetQuery={debouncedSetQuery}
              onItemChanged={onItemChanged}
            />
          </View>
        </View>
        <View>
          <Text style={[commonStyles.baseText, styles.itemHeader]}>Products</Text>
          <View style={{flex: 1, marginLeft: 10}}>
            <ShopifyProductCollectionBody
              itemType={ItemTypeEnum.PRODUCT}
              query={query}
              filteredItems={filteredProductItems}
              isLoading={isLoading}
              debouncedSetQuery={debouncedSetQuery}
              onItemChanged={onItemChanged}
            />
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.editorInputItem}>
      {!!label && (
        <Text style={[commonStyles.labelText, commonStyles.labelContainer, {alignSelf: 'flex-start', paddingTop: 8}]}>
          {label}
        </Text>
      )}
      <View style={[{flex: 1}, !!label && commonStyles.inputContainer]}>
        {nonPopoverMode ? (
          <>
            <View ref={buttonRef}>
              <ShopifyProductCollectionTrigger
                currentItem={currentItem}
                isEditing={isEditing}
                setEditing={setEditing}
              />
            </View>
            {isEditing && {pickerBody}}
          </>
        ) : (
          <PopoverComponent
            visible={isEditing}
            onVisibleChange={setEditing}
            positions={['bottom', 'left']}
            trigger={
              <View ref={buttonRef}>
                <ShopifyProductCollectionTrigger
                  currentItem={currentItem}
                  isEditing={isEditing}
                  setEditing={setEditing}
                />
              </View>
            }>
            {pickerBody}
          </PopoverComponent>
        )}
      </View>
    </View>
  );
};

export const ShopifyProductCollectionBody: React.FC<{
  itemType: ItemTypeEnum;
  filteredItems: (ShopifyObjectCacheProduct | ShopifyObjectCacheCollection)[];
  isLoading: boolean;
  onItemChanged: (item: {value: ShopifyObjectCacheProduct | ShopifyObjectCacheCollection; type: ItemTypeEnum}) => void;
}> = ({itemType, filteredItems, isLoading, onItemChanged}) => {
  return (
    <View style={styles.itemContainer}>
      <View style={{height: '100%', overflow: 'scroll'}}>
        {isLoading && (
          <Image style={{width: 25, height: 25}} source={require('@/root/web/assets/images/preloader.svg')} />
        )}
        {!isLoading &&
          !!filteredItems?.length &&
          filteredItems?.slice(0, 50)?.map((item, index) => (
            <Pressable
              style={[styles.item]}
              key={index}
              onPress={() => {
                onItemChanged({
                  type: itemType,
                  value: item,
                });
              }}>
              <View style={styles.itemImageContainer}>
                <Image
                  resizeMode="cover"
                  source={
                    item?.image?.url || item?.featuredImage?.url
                      ? {uri: item?.image?.url || item?.featuredImage?.url}
                      : require('../../../assets/images/placeholder-image.png')
                  }
                  style={styles.itemImage}
                />
              </View>
              <View style={[styles.itemTextContainer]}>
                <Text style={commonStyles.baseText}>{item.title}</Text>
              </View>
            </Pressable>
          ))}
        {!isLoading && !filteredItems?.length && <Text style={commonStyles.baseText}>No Results Found</Text>}
      </View>
    </View>
  );
};

export const ShopifyProductCollectionTrigger: React.FC<{
  currentItem: {value: ShopifyObjectCacheProduct | ShopifyObjectCacheCollection; type: ItemTypeEnum} | null;
  setEditing: React.Dispatch<React.SetStateAction<boolean>>;
  isEditing: boolean;
}> = ({currentItem, setEditing, isEditing}) => {
  const currentItemImageUrl = currentItem?.value?.image?.url || currentItem?.value?.featuredImage?.url;
  return (
    <Pressable
      onPress={() => {
        setEditing(!isEditing);
      }}
      style={[commonStyles.input, styles.inputContainer]}>
      <View style={[styles.item, {paddingVertical: 4}]}>
        <View style={styles.itemImageContainer}>
          <Image
            resizeMode="cover"
            source={
              currentItemImageUrl ? {uri: currentItemImageUrl} : require('../../../assets/images/placeholder-image.png')
            }
            style={styles.itemImage}
          />
        </View>
        <View style={[styles.itemTextContainer, {width: '90%', maxWidth: '90%'}]}>
          {currentItem ? (
            <>
              <Text style={[commonStyles.baseText, styles.currentItemText]}>{currentItem?.value?.title}</Text>
              <View style={styles.editIconContainer}>
                <ApptileWebIcon
                  name={'edit-icon'}
                  size={19}
                  onPress={() => {
                    setEditing(!isEditing);
                  }}
                  color={theme.CONTROL_PLACEHOLDER_COLOR}
                />
              </View>
            </>
          ) : (
            <Text style={[commonStyles.baseText, styles.currentItemText]}>Select an item</Text>
          )}
        </View>
      </View>
    </Pressable>
  );
};

// export interface ShopifyItemHandlePickerProps {
//   name: string;
//   value: string;
//   label: string;
//   itemType: ItemTypeEnum;
//   onChange: (value: any) => void;
//   config?: Immutable.Map<string, any>;
// }

// export const ShopifyItemHandlePicker: React.FC<ShopifyItemHandlePickerProps> = (
//   props: ShopifyItemHandlePickerProps,
// ) => {
//   const {onChange} = props;
//   const onItemChanged = useCallback(
//     item => {
//       if (item) onChange && onChange(item.handle);
//       else onChange('');
//     },
//     [onChange],
//   );

//   const objPickerProps = {
//     ...props,
//     onChange: onItemChanged,
//   };
//   return <ShopifyProductCollectionPicker {...objPickerProps} />;
// };

const styles = StyleSheet.create({
  editorInputItem: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  itemImage: {
    width: 24,
    height: 24,
    overflow: 'hidden',
    borderRadius: 2,
  },
  container: {
    flex: 1,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    paddingHorizontal: 10,
    paddingVertical: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemContainer: {
    marginTop: 4,
    borderRadius: 6,
    padding: 2,
    overflow: 'hidden',
  },
  item: {
    width: '100%',
    flexDirection: 'row',
    paddingVertical: 5,
  },
  itemImageContainer: {
    width: 24,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    borderRadius: 2,
  },
  itemTextContainer: {
    paddingLeft: 8,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  editIconContainer: {
    width: 24,
    overflow: 'hidden',
    justifyContent: 'center',
  },
  currentItemText: {
    paddingRight: 8,
    whiteSpace: 'nowrap',
    wordBreak: 'break-all',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    justifyContent: 'center',
  },
  itemHeader: {
    color: '#3C3C3C',
    fontSize: 12,
    fontWeight: '600',
  },
});
