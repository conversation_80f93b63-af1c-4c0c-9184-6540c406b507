import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Text, StyleSheet, Image, View, Pressable} from 'react-native';
import {ShopifyObjectCacheCollection, ShopifyObjectCacheProduct} from '../ShopifyObjectCacheTypes';
import Fuse from 'fuse.js';
import _, {debounce} from 'lodash';
import {replaceNullCollectionImage, replaceProductImage} from '../ShopifyObjectCache';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';
import theme from '@/root/web/styles-v2/theme';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import CodeInput from '@/root/web/components/codeEditor/codeInput';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import PopoverComponent from '../../../components-v2/base/Popover';
import {
  TransformGetProductsPaginatedQuery,
  TransformSearchCollections,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import * as BlogGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/blog';
import {datasourceTypeModelSel} from 'apptile-core';
import {TransformGetBlogs} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/blogTransformer';
import CodeInputControlV2 from '@/root/web/components/controls-v2/CodeInputControl';

const FUSE_OPTIONS = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  // includeMatches: true,
  findAllMatches: true,
  minMatchCharLength: 1,
  // location: 0,
  // threshold: 0.6,
  // distance: 100,
  // useExtendedSearch: false,
  ignoreLocation: true,
  // ignoreFieldNorm: false,
  // fieldNormWeight: 1,
  keys: ['title', 'handle'],
};

export interface ShopifyItemPickerProps {
  itemType: 'product' | 'collection' | 'blog';
  currentItem: null | ShopifyObjectCacheProduct | ShopifyObjectCacheCollection;
  onChange: (item: null | ShopifyObjectCacheProduct | ShopifyObjectCacheCollection) => void;
}

export interface ShopifyItemObjectPickerProps {
  name?: string;
  value: string;
  label: string;
  itemType: ShopifyItemPickerProps['itemType'];
  onChange: (value: any) => void;
  config?: Immutable.Map<string, any>;
  defaultOpen?: boolean;
  setOpened?: any;
  nonPopoverMode?: boolean;
  queryRunner?: any;
}

export const ShopifyItemObjectPicker: React.FC<ShopifyItemObjectPickerProps> = (
  props: ShopifyItemObjectPickerProps,
) => {
  const {
    name,
    value,
    label,
    onChange,
    config,
    itemType,
    defaultOpen = false,
    setOpened,
    nonPopoverMode = false,
  } = props;
  let {queryRunner} = props;
  const [currentItem, setCurrentItem] = useState<ShopifyObjectCacheProduct | ShopifyObjectCacheCollection | null>(null);
  const [isEditing, setEditing] = useState(defaultOpen);
  const [shopifyItems, setShopifyItems] = useState<ShopifyObjectCacheProduct[] | ShopifyObjectCacheCollection[] | null>(
    null,
  );
  const [fuseSearch, setFuseSearch] = useState(new Fuse(shopifyItems ?? [], FUSE_OPTIONS));
  const [query, setQuery] = useState('');
  const [filteredItems, setFilteredItems] = useState(
    shopifyItems?.sort((a: any, b: any) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()),
  );
  const [isLoading, setIsLoading] = useState(!shopifyItems?.length);

  const onItemChanged = useCallback(
    item => {
      if (setOpened) setOpened(false);
      onChange && onChange(item);
      setEditing(false);
    },
    [onChange],
  );
  const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const ShopifyDSModel = shopifyModelSel ? shopifyModelSel(store.getState()) : null;
  if (!queryRunner) {
    queryRunner = ShopifyDSModel?.get('queryRunner');
  }
  const getProducts = async (value?: string) => {
    if (queryRunner && queryRunner.runQuery) {
      const countryCode = ShopifyDSModel?.getIn(['shop', 'paymentSettings', 'countryCode']) ?? 'US';
      const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.SEARCH_PRODUCTS_FOR_DROPDOWN, {
        first: 50,
        query: value ? value : query,
        countryCode,
      });
      const {transformedData: queryTransformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformGetProductsPaginatedQuery},
        ShopifyDSModel?.get('shop') ?? {},
        ShopifyDSModel,
      );
      if (value && queryTransformedData.length == 0) return getProducts();
      setFilteredItems(replaceProductImage(queryTransformedData));
      return queryTransformedData;
    } else {
      return [];
    }
  };
  const getCollections = async (value?: string) => {
    if (queryRunner && queryRunner.runQuery) {
      const countryCode = ShopifyDSModel?.getIn(['shop', 'paymentSettings', 'countryCode']) ?? 'US';
      const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.SEARCH_COLLECTIONS_FOR_DROPDOWN, {
        first: 50,
        query: value ? value : query,
        countryCode,
      });
      const {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformSearchCollections},
        ShopifyDSModel?.get('shop') ?? {},
        ShopifyDSModel,
      );
      if (value && transformedData.length == 0) return getCollections();
      setFilteredItems(replaceNullCollectionImage(transformedData));
      return transformedData;
    } else {
      return [];
    }
  };
  const getBlogs = async () => {
    if (queryRunner && queryRunner.runQuery) {
      const queryResponse = await queryRunner.runQuery('query', BlogGqls.GET_BLOGS, {first: 100});
      const {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformGetBlogs},
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      setFilteredItems(transformedData);
      return transformedData;
    } else {
      return [];
    }
  };
  const debouncedSetQuery = debounce(setQuery, 150);
  useEffect(() => {
    (async function () {
      if (value) {
        const items =
          itemType === 'product'
            ? await getProducts(value)
            : itemType === 'blog'
            ? await getBlogs()
            : await getCollections(value);
        setCurrentItem(_.find(items, item => item?.handle === value));
        setEditing(false);
      } else {
        setCurrentItem(null);
      }
    })();
  }, [itemType, value, label]);

  useEffect(() => {
    if (query === value) setQuery('');
    if (setOpened) setOpened(isEditing);
    (async function () {
      const getItems = async () => {
        const items =
          itemType === 'product'
            ? await getProducts()
            : itemType === 'blog'
            ? await getBlogs()
            : await getCollections();
        setShopifyItems(items);
      };
      getItems();
    })();
  }, [itemType, isEditing]);
  useEffect(() => {
    setFilteredItems(
      shopifyItems
        ?.sort((a: any, b: any) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        ?.slice(0, 50),
    );
    setIsLoading(false);
  }, [shopifyItems]);
  useEffect(() => {
    fuseSearch.setCollection(shopifyItems ?? []);
  }, [fuseSearch, shopifyItems]);
  useEffect(() => {
    itemType === 'product' ? getProducts() : getCollections();
  }, [fuseSearch, query]);
  useMountEffect(() => {
    (async function () {
      const getItems = async () => {
        const items =
          itemType === 'product'
            ? await getProducts()
            : itemType === 'blog'
            ? await getBlogs()
            : await getCollections();
        console.log(items);
        setShopifyItems(items);
        // if (!items.length) window.setTimeout(getItems, 1000);
      };
      getItems();
    })();
  });
  const buttonRef = useRef(null);
  return (
    <View style={styles.editorInputItem}>
      {!!label && (
        <Text style={[commonStyles.labelText, commonStyles.labelContainer, {alignSelf: 'flex-start', paddingTop: 8}]}>
          {label}
        </Text>
      )}
      <View style={[{flex: 1}, !!label && commonStyles.inputContainer]}>
        {nonPopoverMode ? (
          <>
            <View ref={buttonRef}>
              {currentItem && (
                <Pressable
                  onPress={() => {
                    setEditing(!isEditing);
                  }}
                  style={[commonStyles.input, styles.inputContainer]}>
                  <View style={[styles.item, {paddingVertical: 4}]}>
                    <View style={styles.itemImageContainer}>
                      <Image
                        resizeMode="cover"
                        source={
                          currentItem?.image?.url || currentItem?.featuredImage?.url
                            ? {uri: currentItem?.image?.url || currentItem?.featuredImage?.url}
                            : require('../../../assets/images/placeholder-image.png')
                        }
                        style={styles.itemImage}
                      />
                    </View>
                    <View style={[styles.itemTextContainer, {width: '90%', maxWidth: '90%'}]}>
                      <Text style={[commonStyles.baseText, styles.currentItemText]}>{currentItem.title}</Text>
                      <View style={styles.editIconContainer}>
                        <ApptileWebIcon
                          name={'edit-icon'}
                          size={19}
                          onPress={() => {
                            setEditing(!isEditing);
                          }}
                          color={theme.CONTROL_PLACEHOLDER_COLOR}
                        />
                      </View>
                    </View>
                  </View>
                </Pressable>
              )}
              {!currentItem && (
                <Pressable
                  onPress={() => {
                    setEditing(!isEditing);
                  }}
                  style={[commonStyles.input, styles.inputContainer]}>
                  <View style={[styles.item, {paddingVertical: 4}]}>
                    <View style={styles.itemImageContainer}>
                      <Image
                        resizeMode="cover"
                        source={
                          currentItem?.image?.url || currentItem?.featuredImage?.url
                            ? {uri: currentItem?.image?.url || currentItem?.featuredImage?.url}
                            : require('../../../assets/images/placeholder-image.png')
                        }
                        style={styles.itemImage}
                      />
                    </View>
                    <View style={[styles.itemTextContainer, {width: '100%'}]}>
                      <Text style={[commonStyles.baseText, styles.currentItemText]}>Select a {itemType}</Text>
                    </View>
                  </View>
                </Pressable>
              )}
            </View>
            {isEditing && (
              <View
                style={{
                  width: buttonRef?.current?.getBoundingClientRect().width,
                  marginTop: 4,
                  backgroundColor: '#ffffff',
                  overflow: 'hidden',
                  borderRadius: 6,
                }}>
                <View>
                  <ApptileWebIcon
                    style={{position: 'absolute', marginTop: 12, marginLeft: 6, zIndex: 1}}
                    name={'magnify'}
                    size={19}
                    color={theme.CONTROL_INPUT_COLOR}
                  />
                  <CodeInputControlV2
                    placeholder={'Search ' + itemType}
                    singleLine={true}
                    autoFocus={true}
                    defaultValue={query}
                    inputStyles={{paddingLeft: 26}}
                    onChange={function (value: string): void {
                      debouncedSetQuery(value.replace(/[\n]/gim, ''));
                    }}
                  />
                </View>
                <View style={styles.itemContainer}>
                  <View style={{height: '100%', overflow: 'scroll'}}>
                    {isLoading && <Text style={styles.item}>Loading {itemType}'s</Text>}
                    {!isLoading &&
                      !!filteredItems?.length &&
                      filteredItems?.slice(0, 50)?.map((item, index) => (
                        <Pressable
                          style={[
                            styles.item,
                            {
                              borderBottomWidth: 1,
                              borderBottomColor: '#44444444',
                            },
                          ]}
                          key={index}
                          onPress={() => {
                            onItemChanged(item);
                          }}>
                          <View style={styles.itemImageContainer}>
                            <Image
                              resizeMode="cover"
                              source={
                                item?.image?.url || item?.featuredImage?.url
                                  ? {uri: item?.image?.url || item?.featuredImage?.url}
                                  : require('../../../assets/images/placeholder-image.png')
                              }
                              style={styles.itemImage}
                            />
                          </View>
                          <View style={[styles.itemTextContainer]}>
                            <Text style={commonStyles.baseText}>{item.title}</Text>
                          </View>
                        </Pressable>
                      ))}
                    {!isLoading && !filteredItems?.length && (
                      <Text style={commonStyles.baseText}>No Results Found</Text>
                    )}
                  </View>
                </View>
              </View>
            )}
          </>
        ) : (
          <PopoverComponent
            visible={isEditing}
            onVisibleChange={setEditing}
            positions={['bottom', 'left']}
            trigger={
              <View ref={buttonRef}>
                {currentItem && (
                  <Pressable
                    onPress={() => {
                      setEditing(!isEditing);
                    }}
                    style={[commonStyles.input, styles.inputContainer]}>
                    <View style={[styles.item, {paddingVertical: 4}]}>
                      <View style={styles.itemImageContainer}>
                        <Image
                          resizeMode="cover"
                          source={
                            currentItem?.image?.url || currentItem?.featuredImage?.url
                              ? {uri: currentItem?.image?.url || currentItem?.featuredImage?.url}
                              : require('../../../assets/images/placeholder-image.png')
                          }
                          style={styles.itemImage}
                        />
                      </View>
                      <View style={[styles.itemTextContainer, {width: '90%', maxWidth: '90%'}]}>
                        <Text style={[commonStyles.baseText, styles.currentItemText]}>{currentItem.title}</Text>
                        <View style={styles.editIconContainer}>
                          <ApptileWebIcon
                            name={'edit-icon'}
                            size={19}
                            onPress={() => {
                              setEditing(!isEditing);
                            }}
                            color={theme.CONTROL_PLACEHOLDER_COLOR}
                          />
                        </View>
                      </View>
                    </View>
                  </Pressable>
                )}
                {!currentItem && (
                  <Pressable
                    onPress={() => {
                      setEditing(!isEditing);
                    }}
                    style={[commonStyles.input, styles.inputContainer]}>
                    <View style={[styles.item, {paddingVertical: 4}]}>
                      <View style={styles.itemImageContainer}>
                        <Image
                          resizeMode="cover"
                          source={
                            currentItem?.image?.url || currentItem?.featuredImage?.url
                              ? {uri: currentItem?.image?.url || currentItem?.featuredImage?.url}
                              : require('../../../assets/images/placeholder-image.png')
                          }
                          style={styles.itemImage}
                        />
                      </View>
                      <View style={[styles.itemTextContainer, {width: '100%'}]}>
                        <Text style={[commonStyles.baseText, styles.currentItemText]}>Select a {itemType}</Text>
                      </View>
                    </View>
                  </Pressable>
                )}
              </View>
            }>
            <View
              style={{
                width: buttonRef?.current?.getBoundingClientRect().width,
                marginTop: 4,
                backgroundColor: '#ffffff',
                overflow: 'hidden',
                borderRadius: 6,
              }}>
              <View>
                <ApptileWebIcon
                  style={{position: 'absolute', marginTop: 12, marginLeft: 6, zIndex: 1}}
                  name={'magnify'}
                  size={19}
                  color={theme.CONTROL_INPUT_COLOR}
                />
                <CodeInputControlV2
                  placeholder={'Search ' + itemType}
                  singleLine={true}
                  autoFocus={true}
                  defaultValue={query}
                  inputStyles={{paddingLeft: 26}}
                  onChange={function (value: string): void {
                    debouncedSetQuery(value);
                  }}
                />
              </View>
              <View style={styles.itemContainer}>
                <View style={{height: '100%', overflow: 'scroll'}}>
                  {isLoading && <Text style={styles.item}>Loading {itemType}'s</Text>}
                  {!isLoading &&
                    !!filteredItems?.length &&
                    filteredItems?.slice(0, 50)?.map((item, index) => (
                      <Pressable
                        style={[
                          styles.item,
                          {
                            borderBottomWidth: 1,
                            borderBottomColor: '#44444444',
                          },
                        ]}
                        key={index}
                        onPress={() => {
                          onItemChanged(item);
                        }}>
                        <View style={styles.itemImageContainer}>
                          <Image
                            resizeMode="cover"
                            source={
                              item?.image?.url || item?.featuredImage?.url
                                ? {uri: item?.image?.url || item?.featuredImage?.url}
                                : require('../../../assets/images/placeholder-image.png')
                            }
                            style={styles.itemImage}
                          />
                        </View>
                        <View style={[styles.itemTextContainer]}>
                          <Text style={commonStyles.baseText}>{item.title}</Text>
                        </View>
                      </Pressable>
                    ))}
                  {!isLoading && !filteredItems?.length && <Text style={commonStyles.baseText}>No Results Found</Text>}
                </View>
              </View>
            </View>
          </PopoverComponent>
        )}
      </View>
    </View>
  );
};
export interface ShopifyItemHandlePickerProps {
  name: string;
  value: string;
  label: string;
  itemType: ShopifyItemPickerProps['itemType'];
  onChange: (value: any) => void;
  config?: Immutable.Map<string, any>;
}

export const ShopifyItemHandlePicker: React.FC<ShopifyItemHandlePickerProps> = (
  props: ShopifyItemHandlePickerProps,
) => {
  const {onChange} = props;
  const onItemChanged = useCallback(
    item => {
      if (item) onChange && onChange(item.handle);
      else onChange('');
    },
    [onChange],
  );

  const objPickerProps = {
    ...props,
    onChange: onItemChanged,
  };
  return <ShopifyItemObjectPicker {...objPickerProps} />;
};

const styles = StyleSheet.create({
  editorInputItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  itemImage: {
    width: 24,
    height: 24,
    overflow: 'hidden',
    borderRadius: 2,
  },
  container: {
    flex: 1,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    paddingHorizontal: 10,
    paddingVertical: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContainer: {
    marginTop: 4,
    borderWidth: 1,
    borderRadius: 6,
    borderColor: theme.INPUT_BORDER,
    padding: 2,
    height: 150,
    overflow: 'hidden',
  },
  item: {
    width: '100%',
    flexDirection: 'row',
    paddingVertical: 5,
  },
  itemImageContainer: {
    width: 24,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    borderRadius: 2,
  },
  itemTextContainer: {
    paddingLeft: 8,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  editIconContainer: {
    width: 24,
    overflow: 'hidden',
    justifyContent: 'center',
  },
  currentItemText: {
    paddingRight: 8,
    whiteSpace: 'nowrap',
    wordBreak: 'break-all',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    justifyContent: 'center',
  },
});
