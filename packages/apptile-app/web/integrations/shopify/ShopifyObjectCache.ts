import localForage from 'localforage';
import * as memoryDriver from 'localforage-driver-memory';
import {
  ShopifyObjectCacheCollection,
  ShopifyObjectCacheConfigKeys,
  ShopifyObjectCacheProduct,
  ShopifyObjectCacheApptileOrders,
  ShopifyObjectCacheImages,
} from './ShopifyObjectCacheTypes';
import {store, datasourceTypeModelSel} from 'apptile-core';
import {GET_COLLECTION_HANDLE_PRODUCTS_ONLY_IMAGES} from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import {TransformGetCollectionProductsQuery} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import _ from 'lodash';

const isIndexedDBValid = async () => {
  try {
    return (
      'indexedDB' in window &&
      indexedDB
        .databases()
        .then(() => {
          return true;
        })
        .catch(() => {
          return false;
        })
    );
  } catch (e) {
    logger.warn('IndexedDB is not available for usage for cache!');
    return false;
  }
};

const SHOPIFY_CACHEDB_NAME = 'apptlie_shopify_cache';
var LOCALFORAGE_DRIVERS = [localForage.INDEXEDDB, memoryDriver._driver, localForage.LOCALSTORAGE, localForage.WEBSQL];
var __initedShopifyObjectCache: boolean = false;
localForage.defineDriver(memoryDriver);
localForage.config({
  driver: LOCALFORAGE_DRIVERS,
});

let collectionFetching = false;

const fetchNullCollectionImage = async (collections: any) => {
  if (!collectionFetching) {
    if (collections) {
      const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
      const newCollections = [...collections];
      const ShopifyDSModel = shopifyModelSel(store.getState());
      if (!ShopifyDSModel) {
        collectionFetching = false;
        return null;
      }
      collectionFetching = true;
      const queryRunner = ShopifyDSModel?.get('queryRunner');
      if (queryRunner && queryRunner.runQuery) {
        for (let i in newCollections) {
          if (!newCollections[i]?.image?.url && !newCollections[i]?.ranReplacement) {
            const queryResponse = await queryRunner.runQuery('query', GET_COLLECTION_HANDLE_PRODUCTS_ONLY_IMAGES, {
              first: 20,
              collectionHandle: newCollections[i].handle,
            });
            const {transformedData} = processShopifyGraphqlQueryResponse(
              queryResponse,
              {transformer: TransformGetCollectionProductsQuery},
              ShopifyDSModel?.get('shop'),
              ShopifyDSModel,
            );
            const productWithImage = transformedData?.find(e => e?.images?.[0]?.src);
            const imageURL = productWithImage?.images?.[0]?.src;
            newCollections[i].image = {url: imageURL};
            newCollections[i].ranReplacement = true;
            await (await getShopifyObjectCache()).setCollectionsItem(newCollections[i]);
          }
        }
      }
      collectionFetching = false;
    } else {
      collectionFetching = false;
    }
  } else {
    logger.info('Already fetching null collection images');
  }
};

export const replaceNullCollectionImage = (collections: any) => {
  const imageReplacement: any = {};
  const productsCount: any = {};

  // // Create a lookup object for the featured images
  const newCollections = collections?.map((item: any) => {
    if (!item?.featuredImage) {
      let image = '';
      item.products?.map((e: any) => (image = _.get(e, 'images[0].src', image)));
      item.image = {url: image};
    } else {
      item.image = {url: item?.featuredImage};
    }
    return item;
  });
  // Replace the image property with the featured image URL for matching parent IDs
  // collections?.forEach(item => {
  //   if (item?.image === null && item?.id in imageReplacement) {
  //     item.image = {url: imageReplacement[item?.id]};
  //   }
  // });
  // const newCollections = collections
  //   ?.filter(d => !d.__parentId)
  //   ?.map((item: any) => ({...item, productsCount: (productsCount[item.id] ?? 1) - 1}));
  // fetchNullCollectionImage(newCollections);
  return newCollections;
};
export const replaceProductImage = (products: any) => {
  const newProducts = products?.map((item: any) => {
    if (!item?.featuredImage) {
      let image = '';
      item.images?.map((e: any) => (image = e.src ?? image));
      item.image = {url: image};
    } else {
      item.image = {url: item?.featuredImage};
    }
    return item;
  });
  return newProducts;
};

export async function initShopifyObjectCache() {
  if (await isIndexedDBValid()) {
    LOCALFORAGE_DRIVERS = [localForage.INDEXEDDB, memoryDriver._driver, localForage.LOCALSTORAGE, localForage.WEBSQL];
  } else {
    LOCALFORAGE_DRIVERS = [memoryDriver._driver, localForage.LOCALSTORAGE, localForage.WEBSQL];
  }
  // logger.info(`Memory Driver:`, memoryDriver);
  // logger.info(`DRIVERS`, LOCALFORAGE_DRIVERS);
  localForage.setDriver(
    LOCALFORAGE_DRIVERS,
    () => {
      // logger.info(`Set Driver Done!`);
    },
    error => {
      logger.error(`Set Driver Failed!`, error);
    },
  );

  const appId = store.getState().apptile.appId;
  localForage.config({
    driver: LOCALFORAGE_DRIVERS,
    name: `${SHOPIFY_CACHEDB_NAME}_${appId}`,
  });
  return await localForage.ready().then(() => {
    __initedShopifyObjectCache = true;
  });
}

const _initCache = () => {
  const ShopifyObjectCache = (function () {
    const appId = store.getState().apptile.appId;
    // logger.info('Initing Indexed DBs !!!');
    var configdb = localForage.createInstance({
      driver: LOCALFORAGE_DRIVERS,
      name: `${SHOPIFY_CACHEDB_NAME}_${appId}`,
      storeName: 'shopify_cache_config',
    });
    var productsdb = localForage.createInstance({
      driver: LOCALFORAGE_DRIVERS,
      name: `${SHOPIFY_CACHEDB_NAME}_${appId}`,
      storeName: 'shopify_products',
    });
    var collectionsdb = localForage.createInstance({
      driver: LOCALFORAGE_DRIVERS,
      name: `${SHOPIFY_CACHEDB_NAME}_${appId}`,
      storeName: 'shopify_collections',
    });
    var blogsdb = localForage.createInstance({
      driver: LOCALFORAGE_DRIVERS,
      name: `${SHOPIFY_CACHEDB_NAME}_${appId}`,
      storeName: 'shopify_blogs',
    });
    var apptileOrdersdb = localForage.createInstance({
      driver: LOCALFORAGE_DRIVERS,
      name: `${SHOPIFY_CACHEDB_NAME}_${appId}`,
      storeName: 'shopify_apptile_orders',
    });
    var imagesdb = localForage.createInstance({
      driver: LOCALFORAGE_DRIVERS,
      name: `${SHOPIFY_CACHEDB_NAME}_${appId}`,
      storeName: 'shopify_apptile_images',
    });

    // Call dummy get calls and ensure DBs are created.
    configdb.getItem('');
    productsdb.getItem('');
    collectionsdb.getItem('');
    blogsdb.getItem('');
    apptileOrdersdb.getItem('');
    imagesdb.getItem('');

    return {
      setConfigValue: async (key: ShopifyObjectCacheConfigKeys, value: any) => {
        return configdb.setItem(key, value);
      },
      getConfigValue: async (key: ShopifyObjectCacheConfigKeys) => {
        return configdb.getItem(key);
      },
      setProductsList: async (products: Array<ShopifyObjectCacheProduct>) => {
        const newProducts = replaceProductImage(products);
        newProducts.forEach((product: ShopifyObjectCacheProduct) => productsdb.setItem(product?.id, product));
      },
      getProductsList: async (): Promise<Array<ShopifyObjectCacheProduct>> => {
        var products: Array<ShopifyObjectCacheProduct> = [];
        return productsdb
          .iterate(product => {
            products.push(product as ShopifyObjectCacheProduct);
          })
          .then(() => {
            return products;
          });
      },
      setCollectionsList: async (collections: Array<ShopifyObjectCacheCollection>) => {
        const newData = replaceNullCollectionImage(collections);
        newData?.forEach(collection => {
          collectionsdb.setItem(collection?.id, collection);
        });
      },
      setCollectionsItem: async (collection: ShopifyObjectCacheCollection) => {
        collectionsdb.setItem(collection?.id, collection);
      },
      getCollectionsList: async (): Promise<Array<ShopifyObjectCacheCollection>> => {
        var collections: Array<ShopifyObjectCacheProduct> = [];
        return collectionsdb
          .iterate(collection => {
            collections.push(collection as ShopifyObjectCacheCollection);
          })
          .then(() => {
            if (collections.find(e => !e?.image?.url)) fetchNullCollectionImage(collections);
            return collections;
          });
      },
      setBlogsList: async (blogs: any) => {
        blogs.forEach((blog: any) => blogsdb.setItem(blog?.id, blog));
      },
      getBlogsList: async (): Promise<Array<ShopifyObjectCacheCollection>> => {
        var blogs: Array<ShopifyObjectCacheProduct> = [];
        return blogsdb
          .iterate(blog => {
            blogs.push(blog as ShopifyObjectCacheCollection);
          })
          .then(() => {
            return blogs;
          });
      },
      setApptileOrdersList: async (orders: Array<ShopifyObjectCacheApptileOrders>) => {
        orders.forEach(order => apptileOrdersdb.setItem(order?.id, order));
      },
      getApptileOrdersList: async (): Promise<Array<ShopifyObjectCacheApptileOrders>> => {
        var orders: Array<ShopifyObjectCacheApptileOrders> = [];
        return apptileOrdersdb
          .iterate(order => {
            orders.push(order as ShopifyObjectCacheApptileOrders);
          })
          .then(() => {
            return orders;
          });
      },
      setImagesList: async (images: Array<ShopifyObjectCacheImages>) => {
        images.forEach((image: ShopifyObjectCacheImages) => {
          imagesdb.setItem(image?.url, image);
        });
      },
      getImagesList: async (): Promise<Array<ShopifyObjectCacheImages>> => {
        var images: Array<ShopifyObjectCacheImages> = [];
        return imagesdb
          .iterate(image => {
            images.push(image as ShopifyObjectCacheImages);
          })
          .then(() => {
            return images;
          });
      },
      clearProductsData: async (): Promise<void> => {
        await configdb.setItem('productsLoaded', false);
        await productsdb.clear();
      },
      clearCollectionsData: async (): Promise<void> => {
        await configdb.setItem('collectionsLoaded', false);
        await collectionsdb.clear();
      },
      clearBlogsData: async (): Promise<void> => {
        await configdb.setItem('blogsLoaded', false);
        await blogsdb.clear();
      },
      clearApptileOrdersData: async (): Promise<void> => {
        await configdb.setItem('apptileOrdersLoaded', false);
        await apptileOrdersdb.clear();
      },
      clearApptileImages: async (): Promise<void> => {
        await configdb.setItem('imagesLoaded', false);
        await imagesdb.clear();
      },
    };
  })();
  return ShopifyObjectCache;
};

var __ShopifyObjectCache: ReturnType<typeof _initCache> | null = null;

export async function getShopifyObjectCache() {
  return localForage.ready().then(async () => {
    if (!__initedShopifyObjectCache) await initShopifyObjectCache();
    if (!__ShopifyObjectCache) {
      __ShopifyObjectCache = _initCache();
    }
    return __ShopifyObjectCache;
  });
}
