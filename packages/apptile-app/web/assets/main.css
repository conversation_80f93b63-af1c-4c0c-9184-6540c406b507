/* These styles make the body full-height */
html,
body {
  height: 100%;
}

/* These styles disable body scrolling if you are using <ScrollView> */
body {
  overflow: hidden;
  font-family: 'Work Sans', Arial, 'Helvetica Neue', Helvetica, sans-serif;
}

/* These styles make the root element full-height */
#app-root {
  height: 100%;
}

body {
  margin: 0px;
}

dialog {
  outline: none;
  border: none;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
}

dialog::backdrop {
 background: rgba(0, 0, 0, 0.5);
}

dialog .root {
  max-width: 600px;
}

dialog .root .banner {
  width: 100%;
}

dialog .root .primary-text {
  margin: 24px 0;
  font-weight: 600;
  font-size: 35px;
  font-family: "Work Sans", sans-serif;
}

dialog .root .secondary-text {
  margin-bottom: 30px;
  font-size: 14px;
  font-family: "Work Sans", sans-serif;
}

dialog .root .buttonrow {
  display: flex;
  grid-gap: 15px;
}

dialog .root button {
  color: white;
}

dialog .root #fullscreener {
  border-radius: 38.5px;
  padding: 11px 16.5px;
  width: 200px;
  height: 50px;
  background-color: rgb(16, 96, 224);
}

dialog .root #close {
  background: transparent;
  color: black;
}

dialog .root #close,
dialog .root #fullscreener {
  cursor: pointer;
  border: none;
  font-size: 16px;
  font-family: "Work Sans", sans-serif;
  touch-action: manipulation;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preloader {
  display: flex;
  justify-content: center;
  background: #ece9e1;
  height: 100vh;
}

@media only screen and (max-width: 1024px) {
  .preloader .main-container {
    flex-direction: column;
  }

  .preloader h1 {
    text-align: center;
  }

  .preloader h6 {
    text-align: center;
  }
}

@media only screen and (min-width: 1025px) {
  .preloader .main-container {
    flex-direction: row;
  }
}

.preloader h1 {
  font-family: Red Hat Display, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 50px;
  line-height: 60px;
  color: #2588cf;
  letter-spacing: 1px;
  margin: 0px;
}

.preloader h6 {
  font-family: Red Hat Display, sans-serif;
  font-size: 35px;
  font-weight: 400;
  line-height: 60px;
  text-align: left;
  margin: 0px;
}

.preloader .main-container {
  display: flex;
  height: fit-content;
  align-self: center;
}

.preloader .preview-container {
  display: flex;
  height: fit-content;
  align-self: center;
}

.preloader .heading-cont {
  align-items: center;
  display: flex;
  flex-shrink: 0;
  flex: auto;
}

.preloader .heading {
  width: 100%;
  margin: 40px;
}

.preloader .loading-cont {
  flex: auto;
  flex-shrink: 0;
  min-width: 600px;
}

.widget {
  box-sizing: border-box;
  outline: 2px solid transparent;
  transition: margin 340ms, opacity 340ms;
}
.widget.widget__selected {
  outline: 1px dashed rgba(0, 0, 255, 0.3);
  outline-offset: -1px;
}
.widget.widget__hover {
  outline: 1px dashed rgba(0, 0, 255, 0.5);
  outline-offset: -1px;
}
.widget.widget__hover_source {
  outline: 2px solid rgba(0, 0, 255, 0.786);
  outline-offset: -2px;
  cursor: pointer;
}

.widget.widget__drag_source {
  opacity: 0.35;
}
.widget.widget__drag_source > * {
  pointer-events: none;
}

.widget.widget__over_droppable {
  outline: 1px dashed rgba(128, 0, 128, 0.786);
  outline-offset: -1px;
}
.widget.widget__over_drop_target {
  outline: 2px solid rgba(128, 0, 128, 0.786);
  outline-offset: -2px;
}

.widget.widget__hover_source,
.widget.widget__selected,
.widget.widget__hover,
.widget.widget__over_droppable,
.widget.widget__over_drop_target {
  z-index: 999;
}
.widget.widget__hover_source *,
.widget.widget__selected *,
.widget.widget__hover *,
.widget.widget__over_droppable *,
.widget.widget__over_drop_target * {
  z-index: -1;
}

.widget::before {
  content: attr(data-handler-text);
  position: absolute;
  height: 22px;
  color: white;
  padding: 0px 8px;
  font-size: 10px;
  font-weight: normal;
  font-family: ApptileWebIcons, Arial, 'Helvetica Neue', Helvetica, sans-serif;
  line-height: 22px;
  pointer-events: none;
  white-space: nowrap;
  opacity: 0;
  background: transparent;
  transition: opacity 180ms, background 220ms;
  display: none; /* temporarily hide */
}

.widget.widget__hover::before,
.widget.widget__hover_source::before {
  opacity: 1;
  cursor: grab;
  pointer-events: auto;
  background: rgba(0, 0, 255, 0.786);
}

.widget.widget__over_droppable::before,
.widget.widget__over_drop_target::before {
  opacity: 1;
  pointer-events: auto;
  background: rgba(128, 0, 128, 0.786);
}

.widget::after {
  content: '';
  position: absolute;
  opacity: 0;
  transition: opacity 180ms;
}

.sortable {
}
.sortable .sortable-item {
  position: relative;
}
.sortable .sortable-item.sortable-item__dragging {
  visibility: hidden;
}
.sortable .sortable-item .drag-handle-container {
  position: absolute;
  cursor: grab;
  left: 0px;
  top: 0px;
  width: 27px;
  height: 95%;
  padding-top: 2px;
  padding-left: 2px;
  overflow: hidden;
  /* z-index: 2; */
}
.sortable .sortable-item .drag-handle {
  width: 0px;    
    height: 88%;
    overflow: hidden;
    transition: all 200ms linear;
    border-radius: 8px;
    justify-content: center;
    align-items: center;
    display: flex;
}
.sortable .sortable-item .drag-handle-container:hover .drag-handle {
  width: 27px;
  padding-left: 5px;
}

/* Webkit based browsers (Chrome,Opera,Safari) */
.slider {
  -webkit-appearance: none;
  border-radius: 12px;
  background-color: rgb(200, 200, 200);
}

.slider::-webkit-slider-runnable-track {
  height: 2px;
  border: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  border: 0.88px solid #c2c2c2;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ffffff;
  margin-top: -7px;
  box-shadow: 0px 3.50168px 3.50168px rgba(0, 0, 0, 0.25);
}

/* Mozilla Firefox */
.slider::-moz-range-track {
  height: 2px;
  border: none;
}

.slider::-moz-range-thumb {
  -webkit-appearance: none;
  border: 0.88px solid #c2c2c2;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ffffff;
  margin-top: -7px;
  box-shadow: 0px 3.50168px 3.50168px rgba(0, 0, 0, 0.25);
}

.select-control {
  background: url('icons/down-arrow.svg') no-repeat right;
  -webkit-appearance: none;
  background-position-x: 95%;
}

.alpha-picker div:first-child {
  background-image: unset !important;
}
.circle-picker span div span div {
  border: solid 2px #00000077;
}

/* Custom Scrollbar */

/* total width */
*::-webkit-scrollbar {
  background-color:#0000;
  width: 8px;
  height: 8px;
}

/* background of the scrollbar except button or resizer */
*::-webkit-scrollbar-track {
  background-color:#0000;
}

*::-webkit-scrollbar-track:hover {
  background-color:#00000007;
}

/* scrollbar itself */
*::-webkit-scrollbar-thumb {
  background-color:#babac0;
  border-radius: 16px;
}

*::-webkit-scrollbar-thumb:hover {
  background-color:#a0a0a5;
}

/* set button(top and bottom of the scrollbar) */
*::-webkit-scrollbar-button {display:none;height: 0; width: 0;}

#appPreviewContainer *::-webkit-scrollbar {
  background-color:#0000;
  width: 0px;
  height: 0px;
}
.react-tiny-popover-container{
  z-index: 100000;
}

/* 
 * This is copied from the response of  https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,300;0,400;0,500;1,400;1,500&display=swap
 * This server stalls sometimes and gives us a bad LCP rating. Hence we have copied this to our own cdn.
 * */
/* vietnamese */
@font-face {
  font-family: 'Work Sans';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYqz_wNahGAdqQ43Rh_eZDkv_1i4_D2E4A.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Work Sans';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYqz_wNahGAdqQ43Rh_eZDlv_1i4_D2E4A.woff2) format('woff2');
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Work Sans';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYqz_wNahGAdqQ43Rh_eZDrv_1i4_D2.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Work Sans';
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYqz_wNahGAdqQ43Rh_eZDkv_1i4_D2E4A.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Work Sans';
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYqz_wNahGAdqQ43Rh_eZDlv_1i4_D2E4A.woff2) format('woff2');
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Work Sans';
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYqz_wNahGAdqQ43Rh_eZDrv_1i4_D2.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYsz_wNahGAdqQ43Rh_c6DptfpA4cD3.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYsz_wNahGAdqQ43Rh_cqDptfpA4cD3.woff2) format('woff2');
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYsz_wNahGAdqQ43Rh_fKDptfpA4Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYsz_wNahGAdqQ43Rh_c6DptfpA4cD3.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYsz_wNahGAdqQ43Rh_cqDptfpA4cD3.woff2) format('woff2');
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYsz_wNahGAdqQ43Rh_fKDptfpA4Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYsz_wNahGAdqQ43Rh_c6DptfpA4cD3.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYsz_wNahGAdqQ43Rh_cqDptfpA4cD3.woff2) format('woff2');
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGYsz_wNahGAdqQ43Rh_fKDptfpA4Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* vietnamese */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K5fQBiAJpo3ZKyHaQfhf.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K5fQBiEJpo3ZKyHaQfhf.woff2) format('woff2');
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/worksans/v18/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K5fQBi8Jpo3ZKyHaQQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

* {
  font-family: 'Work Sans', sans-serif;
}

#local-video video{
  object-fit: cover !important;
}
/* End of font stylesheet */

/* publish flow css */
.publishFlow-scrollbar::-webkit-scrollbar {
  display: none; /* Hides scrollbar in Chrome, Safari, and Opera */
}
