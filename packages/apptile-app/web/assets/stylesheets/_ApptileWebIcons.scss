$__iconfont__data: map-merge(if(global_variable_exists('__iconfont__data'), $__iconfont__data, ()), (
	"ApptileWebIcons": (
		"TRBL-button-top": "\ea01",
		"TRBL-button": "\ea02",
		"accordion": "\ea03",
		"add-image": "\ea04",
		"alignment-bottom": "\ea05",
		"alignment-center": "\ea06",
		"alignment-left": "\ea07",
		"alignment-middle": "\ea08",
		"alignment-right": "\ea09",
		"alignment-top": "\ea0a",
		"analytics-outline": "\ea0b",
		"apptile-live": "\ea0c",
		"back-arrow": "\ea0d",
		"badge": "\ea0e",
		"bell-outline": "\ea0f",
		"book-outline": "\ea10",
		"border-radius-left": "\ea11",
		"border-radius": "\ea12",
		"brands-outline": "\ea13",
		"button": "\ea14",
		"calender": "\ea15",
		"checkbox": "\ea16",
		"clock-refresh": "\ea17",
		"clock": "\ea18",
		"container": "\ea19",
		"datasource": "\ea1a",
		"delete": "\ea1b",
		"document": "\ea1c",
		"down-arrow": "\ea1d",
		"download-outline": "\ea1e",
		"edit-icon": "\ea1f",
		"expression": "\ea20",
		"gear-outline": "\ea21",
		"go-live": "\ea22",
		"help": "\ea23",
		"house-outline": "\ea24",
		"icon": "\ea25",
		"image-carousel": "\ea26",
		"image-list": "\ea27",
		"image": "\ea28",
		"integration-disconnect": "\ea29",
		"integrations": "\ea2a",
		"list-view": "\ea2b",
		"local-storage": "\ea2c",
		"magicwand": "\ea2d",
		"magnify": "\ea2e",
		"menu-vertical": "\ea2f",
		"modal": "\ea30",
		"notifications": "\ea31",
		"pages-outline": "\ea32",
		"pages": "\ea33",
		"percentage-outline": "\ea34",
		"pills": "\ea35",
		"pin-fill": "\ea36",
		"pin-outline": "\ea37",
		"query": "\ea38",
		"radio-button": "\ea39",
		"rich-text": "\ea3a",
		"select": "\ea3b",
		"send-icon": "\ea3c",
		"settings": "\ea3d",
		"slider-range": "\ea3e",
		"small-shop": "\ea3f",
		"snapshots-outline": "\ea40",
		"star-rating": "\ea41",
		"state": "\ea42",
		"switch": "\ea43",
		"text-input": "\ea44",
		"text": "\ea45",
		"themes": "\ea46",
		"tiles-old": "\ea47",
		"tiles": "\ea48",
		"timer": "\ea49",
		"video-player": "\ea4a",
		"web-view": "\ea4b",
		"your-design": "\ea4c"
	)
));


$create-font-face: true !default; // should the @font-face tag get created?

// should there be a custom class for each icon? will be .filename
$create-icon-classes: true !default; 

// what is the common class name that icons share? in this case icons need to have .icon.filename in their classes
// this requires you to have 2 classes on each icon html element, but reduced redeclaration of the font family
// for each icon
$icon-common-class: 'icon' !default;

// if you whish to prefix your filenames, here you can do so.
// if this string stays empty, your classes will use the filename, for example
// an icon called star.svg will result in a class called .star
// if you use the prefix to be 'icon-' it would result in .icon-star
$icon-prefix: '' !default; 

// helper function to get the correct font group
@function iconfont-group($group: null) {
  @if (null == $group) {
    $group: nth(map-keys($__iconfont__data), 1);
  }
  @if (false == map-has-key($__iconfont__data, $group)) {
    @warn 'Undefined Iconfont Family!';
    @return ();
  }
  @return map-get($__iconfont__data, $group);
}

// helper function to get the correct icon of a group
@function iconfont-item($name) {
  $slash: str-index($name, '/');
  $group: null;
  @if ($slash) {
    $group: str-slice($name, 0, $slash - 1);
    $name: str-slice($name, $slash + 1);
  } @else {
    $group: nth(map-keys($__iconfont__data), 1);
  }
  $group: iconfont-group($group);
  @if (false == map-has-key($group, $name)) {
    @warn 'Undefined Iconfont Glyph!';
    @return '';
  }
  @return map-get($group, $name);
}

// complete mixing to include the icon
// usage:
// .my_icon{ @include iconfont('star') }
@mixin iconfont($icon) {
  $slash: str-index($icon, '/');
  $group: null;
  @if ($slash) {
    $group: str-slice($icon, 0, $slash - 1);
  } @else {
    $group: nth(map-keys($__iconfont__data), 1);
  }
  &:before {
    font-family: $group;
    font-style: normal;
    font-weight: 400;
    content: iconfont-item($icon);
  }
}

// creates the font face tag if the variable is set to true (default)
@if $create-font-face == true {
  @font-face {
   font-family: "ApptileWebIcons";
   src: url('../webfonts/ApptileWebIcons.eot'); /* IE9 Compat Modes */
   src: url('../webfonts/ApptileWebIcons.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
      url('../webfonts/ApptileWebIcons.woff') format('woff'), /* Pretty Modern Browsers */
      url('../webfonts/ApptileWebIcons.ttf')  format('truetype'), /* Safari, Android, iOS */
      url('../webfonts/ApptileWebIcons.svg') format('svg'); /* Legacy iOS */
  }
}

// creates icon classes for each individual loaded svg (default)
@if $create-icon-classes == true {
  .#{$icon-common-class} {
    font-style: normal;
    font-weight: 400;

    @each $icon, $content in map-get($__iconfont__data, "ApptileWebIcons") {
      &.#{$icon-prefix}#{$icon}:before {
        font-family: "ApptileWebIcons";
        content: iconfont-item("ApptileWebIcons/#{$icon}");
      }
    }
  }
}
