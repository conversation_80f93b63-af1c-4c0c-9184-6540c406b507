import { WEB_SENTRY_TRACE_SAMPLE_RATE, WEB_SENTRY_DSN } from '../.env.json';
// This file exists because we want to run code that initializes
// the values on globalThis before any other code runs. 
// This is imported in the entrypoint of webpack before any other imports.
// We cannot add this code directly in the entrypoint file because 
// the code in the entrypoint file will run after it has run the code in 
// all of the imported modules
globalThis.ENABLE_LOGROCKET = process.env.ENABLE_LOGROCKET;
// globalThis.ENABLE_REDUX_LOGGER = true;
// globalThis.ENABLE_WEB_LOGS = true;
// globalThis.ENABLE_CUSTOM_HINTING = true;
// globalThis.ENABLE_BINDING_ERROR_LOGGING = true;


globalThis.WEB_SENTRY_TRACE_SAMPLE_RATE = WEB_SENTRY_TRACE_SAMPLE_RATE;
globalThis.WEB_SENTRY_DSN = WEB_SENTRY_DSN;
export default {
  shouldInitWebLogrocket: globalThis.ENABLE_LOGROCKET
}
