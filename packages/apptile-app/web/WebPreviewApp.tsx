import { injectReducer, logger } from 'apptile-core';
logger.info("Logger initialized for preview app");
import React, {useEffect, useState} from 'react';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {Provider} from 'react-redux';
import {rootSaga} from 'apptile-core';
import {store, sagaMiddleware} from 'apptile-core';
import {WebFontLoader} from '@/root/web/views/settings/brand/fonts';
import { CustomIconProvider } from 'apptile-core';
import { ThemeContainer } from 'apptile-core';
import {ApptileAnimationsContextProvider} from 'apptile-core';
import AppPreview from './views/preview/AppPreview';
import {WEB_API_SERVER_ENDPOINT} from '../.env.json';
import {getLinkingPrefixesInDevAndWeb} from '../app/common/utils/getLinkingPrefixes';
import {GetRegisteredNativePage} from '../app/views/prebuilt';
import {getLinkingPrefixesInDistributedApp} from '../app/common/utils/mobile-only';
import appBranchesSaga from './sagas/AppBranchSaga';
import appForksSaga from './sagas/AppForkSaga';
import editorAppConfigSagas from './sagas/editorAppConfigSaga';

global.GetRegisteredNativePage = GetRegisteredNativePage;
global.WEB_API_SERVER_ENDPOINT = WEB_API_SERVER_ENDPOINT;
global.getLinkingPrefixesInDevAndWeb = getLinkingPrefixesInDevAndWeb;
global.getLinkingPrefixesInDistributedApp = getLinkingPrefixesInDistributedApp;

const WebPreviewApp = () => {
  const [booted, setBooted] = useState(false);
  useEffect(() => {
    sagaMiddleware.run(rootSaga, [
      editorAppConfigSagas,
      appBranchesSaga,
      appForksSaga,
    ]);
    setBooted(true);
  }, []);

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      {booted ? (
        <Provider store={store}>
          <WebFontLoader />
          <CustomIconProvider>
            <ApptileAnimationsContextProvider>
              <ThemeContainer>
                <AppPreview />
              </ThemeContainer>
            </ApptileAnimationsContextProvider>
          </CustomIconProvider>
        </Provider>
      ) : (
        <></>
      )}
    </GestureHandlerRootView>
  );
};

export default WebPreviewApp;
