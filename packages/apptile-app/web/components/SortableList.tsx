import React, {FC, useState, useRef, useCallback, useEffect, useContext, useMemo} from 'react';
import {findNodeHandle} from 'react-native';
import {useDrag, useDrop} from 'react-dnd';
import {SafeAny} from '@/root/app/common/types';
import clsx from 'clsx';
import {throttle} from 'lodash';
import _ from 'lodash';
import {MaterialCommunityIcons} from 'apptile-core';
import theme from '../styles-v2/theme';

const SortableListItemTypeContext = React.createContext('');

export interface SortableListItemProps {
  itemKey: SafeAny;
  item: any;
  onEnd: (dropped: boolean) => void;
}

export const SortableListItem: FC<SortableListItemProps> = props => {
  const {itemKey, item, onEnd, children} = props;
  const dragContext = useContext(SortableListItemTypeContext);
  const handleRef = useRef<any>(null);
  const elementRef = useRef<any>(null);

  const hoverCallback = useCallback(
    (item, monitor): any => {
      if (monitor.isOver({shallow: true}) /*|| item?.payload?.container === containerId */) {
        const currentEle = elementRef?.current;
        const {prevDrag, payload} = item;

        const lastHoveredItem = {
          itemKey,
          lastHoverCoordinates: monitor.getClientOffset(),
          boundingRect: currentEle.getBoundingClientRect(),
          bAfterItem: false,
        };

        const offset = monitor.getClientOffset();
        const {x: dragX, y: dragY} = offset;
        const viewHandle = findNodeHandle(currentEle);
        const bounds = viewHandle?.getBoundingClientRect();
        const {x, y, width, height} = bounds;
        const offsetX = dragX - x;
        const offsetY = dragY - y;
        let positionName;
        positionName = offsetY > height / 2 ? 'bottom' : 'top';
        const beforeRefWidget = ['top', 'left'].includes(positionName);
        const afterRefWidget = ['right', 'bottom'].includes(positionName);

        if (afterRefWidget) lastHoveredItem.bAfterItem = true;

        item.prevDrag = lastHoveredItem;
        item.payload = payload;
      }
    },
    [itemKey],
  );
  const throttledHoverCallback = useMemo(() => throttle(hoverCallback, 66), [hoverCallback]);
  const throttledHover = useCallback(
    (item, monitor) => {
      throttledHoverCallback(item, monitor);
    },
    [throttledHoverCallback],
  );
  const [collected, drag, dragPreview] = useDrag({
    type: dragContext,
    item: () => {
      const dragItem = {
        type: dragContext,
        payload: {
          itemKey,
          item,
        },
      };
      return dragItem;
    },
    collect(monitor) {
      return {
        item: monitor.getItem(),
        isDragging: monitor.isDragging(),
        initialOffset: monitor.getInitialSourceClientOffset(),
      };
    },
    canDrag(monitor) {
      const currentEle = handleRef?.current;
      if (!currentEle) return false;
      return true;
    },
    end: (item, monitor) => {
      onEnd(monitor.didDrop());
    },
  });
  const [{isOver, isOverCurrentItem, canDrop}, drop] = useDrop({
    accept: dragContext,
    collect(monitor) {
      return {
        canDrop: monitor.canDrop(),
        isOver: monitor.isOver(),
        isOverCurrentItem: monitor.isOver({shallow: true}),
      };
    },
    canDrop: () => true,
    hover: throttledHover,
  });

  const setDragRef = useCallback(
    ref => {
      if (ref) {
        const refHandle = ref && findNodeHandle(ref);
        if (refHandle) {
          drag(refHandle);
          handleRef.current = refHandle;
        }
      }
    },
    [drag],
  );
  const setItemRef = useCallback(
    ref => {
      if (ref) {
        const refHandle = ref && findNodeHandle(ref);
        if (refHandle) {
          dragPreview(drop(refHandle));
          elementRef.current = refHandle;
        }
      }
    },
    [drop],
  );
  const getClasses = useCallback(
    () => clsx('sortable-item', collected.isDragging && 'sortable-item__dragging'),
    [collected.isDragging],
  );

  return (
    <div className={getClasses()} ref={setItemRef}>
      {children}
      <div className="drag-handle-container">
        <div className="drag-handle" ref={setDragRef} style={{background: theme.INPUT_BACKGROUND}}>
          <MaterialCommunityIcons name="menu" size={20} color={theme.CONTROL_PLACEHOLDER_COLOR} />
        </div>
      </div>
    </div>
  );
};

export interface SortableListProps {
  dragKey: string;
  data: [SafeAny, any][];
  itemComponent?: React.FC<{itemVal: any; itemKey: SafeAny}>;
  componentProps?: Record<string, any>;
  onChange: (data: [SafeAny, any][], item: any) => void;
}

const SortableList: FC<SortableListProps> = props => {
  const {data, dragKey, itemComponent: ChildComponent, componentProps, onChange, children} = props;
  const [dataEntries, setDataEntries] = useState(data);
  const [draggedItem, setDraggedItem] = useState(null);
  useEffect(() => {
    setDataEntries(data?.slice());
  }, [data]);
  const onDragEnd = useCallback(
    dropped => {
      if (dropped) {
        if (onChange) onChange(dataEntries, draggedItem);
      } else {
        setDataEntries(data);
      }
    },
    [data, dataEntries, onChange],
  );

  const hoverCallback = useCallback(
    (item, monitor): any => {
      const {prevDrag, payload} = item;
      setDraggedItem(item.payload);
      const {itemKey: refItemKey, bAfterItem} = prevDrag ?? {};
      const {itemKey} = payload;
      if (itemKey && refItemKey) {
        let refIndex = _.findIndex(dataEntries, dataItem => {
          return dataItem[0] === refItemKey;
        });
        const itemIndex = _.findIndex(dataEntries, dataItem => {
          return dataItem[0] === itemKey;
        });
        // logger.info(refItemKey, refIndex, itemIndex, bAfterItem);
        if (bAfterItem) {
          if (itemIndex === refIndex + 1) return;
          const itemData = dataEntries.splice(itemIndex, 1)[0];
          refIndex = _.findIndex(dataEntries, dataItem => {
            return dataItem[0] === refItemKey;
          });
          dataEntries.splice(refIndex + 1, 0, itemData);
          setDataEntries(dataEntries.slice(0));
          // logger.info('Set Entries AFTER: ', dataEntries);
        } else {
          if (itemIndex === refIndex - 1) return;
          const itemData = dataEntries.splice(itemIndex, 1)[0];
          refIndex = _.findIndex(dataEntries, dataItem => {
            return dataItem[0] === refItemKey;
          });
          dataEntries.splice(refIndex, 0, itemData);
          setDataEntries(dataEntries.slice(0));
          // logger.info('Set Entries BEFORE: ', dataEntries);
        }
      }
    },
    [dataEntries],
  );
  const throttledHoverCallback = useMemo(() => throttle(hoverCallback, 66), [hoverCallback]);
  const throttledHover = useCallback(
    (item, monitor) => {
      throttledHoverCallback(item, monitor);
    },
    [throttledHoverCallback],
  );
  const [{isOver, isOverCurrentItem, canDrop}, drop] = useDrop({
    accept: dragKey,
    collect(monitor) {
      return {
        canDrop: monitor.canDrop(),
        isOver: monitor.isOver(),
        isOverCurrentItem: monitor.isOver({shallow: true}),
      };
    },
    canDrop: () => true,
    hover: throttledHover,
  });
  const elementRef = useRef<any>(null);
  const setElementRef = useCallback(
    ref => {
      if (ref) {
        const refHandle = ref && findNodeHandle(ref);
        if (refHandle) {
          drop(refHandle);
          elementRef.current = refHandle;
        }
      }
    },
    [drop],
  );
  return (
    <SortableListItemTypeContext.Provider value={dragKey}>
      <div className="sortable" ref={setElementRef}>
        {dataEntries?.map(([key, value]) => {
          return (
            <SortableListItem key={key} itemKey={key} item={value} onEnd={onDragEnd}>
              {ChildComponent && <ChildComponent key={key} itemVal={value} itemKey={key} {...componentProps} />}
            </SortableListItem>
          );
        })}
      </div>
    </SortableListItemTypeContext.Provider>
  );
};

export default SortableList;
