import React from 'react';
import {PropertyEditorCustomProps} from 'apptile-core';
import {PropertyEditorProps} from '../../../apptile-core/common/EditorControlTypes.tsx';
import CheckboxControl from './controls/CheckboxControl';
import CodeInputControl from './controls/CodeInputControl';
import FormatInputControl from './controls/FormatInputControl';
import ColorInputControl from './controls/ColorInputControl';
import ImageListEditorControl from './controls/listEditor';
import PageParamsEditor from './controls/PageParamsEditor';
import QuadInputControl from './controls/QuadInputControl';
import TypographyInputControl from './controls/typographyControl';
import RadioGroupControl from './controls/RadioGroupControl';
import RadioGroupControlV2 from './controls-v2/RadioGroupControl';
import DropDownControl from './controls/DropDownControl';
import PageIdSelectorControl from './controls/PageIdSelectorControl';
import ScreenSelectorControl from './controls/ScreenSelectorControl';
import CurrencySelectorControl from './controls/CurrencySelectorControl.tsx';
import RegexInputControl from './controls/RegexInputControl/RegexInputControl';
import HeaderIdSelectorControl from './controls/HeaderIdSelectorControl';
import EventsInspector from './controls/EventsInspector';
import AnalyticsEditor from './controls/AnalyticsEditor';
import StylesEditor from './controls/StylesEditor';
import LayoutEditor from '../views/propertyInspector/components/LayoutEditor';
import QueryInspector from './controls/QueryInspector';
import AssetEditor from './controls/assetEditor/assetEditor';
import BorderRadiusControl from './controls/BorderRadiusControl';
import RangeSliderControl from './controls/RangeSliderControl';
import NumericInputControl from './controls/NumericInputControl';
import TRBLControl from './controls/TRBLControl';
import IconChooserControl from './controls/IconChooserControl';
import EditorSectionHeader from './controls/EditorSectionHeader';
import ValueMinMaxControl from './controls/ValueMinMaxControl';
import JsonInputControl from './controls/jsonEditor/JsonInputControl';
import JsonBuilderControl from './controls/JsonBuilderControl.tsx';
import ShopifyEntityKeyControl from './controls/ShopifyEntityKeyControl';
import MFAuthQueryInspector from './controls/MFAuthQueryInspector';
import AlignmentControl from './controls/AlignmentControl';
import ExportPageDialog from './controls/ExportPageDialog';
import DateAndTimeControl from './controls/DateAndTimeControl';
import {isEmpty} from 'lodash';
import AnimationsEditor from './controls/AnimationsEditor';
import TextDecorationControl from './controls/TextDecorationControl';
import TextTransformControl from './controls/TextTransformControl';
import MFAuthenticationQueryInspector from './controls/MFAuthenticationQueryInspector';
import AspectRatioControl from './controls/AspectRatioControl';
import RichTextControl from './controls/RichTextControl';
import {ShopifyItemObjectPicker} from '../integrations/shopify/components/ShopifyItemPicker';
import TypographyInputControlV2 from './controls-v2/typographyControl';
import CodeInputControlV2 from './controls-v2/CodeInputControl';
import ImageListEditorControlV2 from './controls-v2/listEditor';
import CloudinaryEditor from './controls/cloudinaryEditor';
import CustomDataControl from './controls/CustomDataControl/index.tsx';
import CodeMirrorInput from './controls-v2/codeEditor/CodeMirrorInput.tsx';
import NavigationInput from './controls-v2/NavigationInput.tsx';

export type CheckboxProps = PropertyEditorProps<PropertyEditorCustomProps['checkbox']>;
export type CodeInputProps = PropertyEditorProps<PropertyEditorCustomProps['codeInput']>;
export type FormatInputProps = PropertyEditorProps<PropertyEditorCustomProps['formatInput']>;
export type AspectRatioProps = PropertyEditorProps<PropertyEditorCustomProps['aspectRatio']>;
export type RegexInputProps = PropertyEditorProps<PropertyEditorCustomProps['regexInput']>;

export type IconChooserInputProps = PropertyEditorProps<PropertyEditorCustomProps['iconChooserInput']>;
export type NumericInputProps = PropertyEditorProps<PropertyEditorCustomProps['numericInput']>;
export type RadioGroupProps = PropertyEditorProps<PropertyEditorCustomProps['radioGroup']>;
export type RangeSliderProps = PropertyEditorProps<PropertyEditorCustomProps['rangeSliderInput']>;
export type DropDownProps = PropertyEditorProps<PropertyEditorCustomProps['dropDown']>;
export type PageIdSelectorProps = PropertyEditorProps<PropertyEditorCustomProps['pageIdSelector']>;
export type LoaderIdSelectorProps = PropertyEditorProps<PropertyEditorCustomProps['loaderIdSelector']>;
export type ScreenSelectorProps = PropertyEditorProps<PropertyEditorCustomProps['screenSelector']>;
export type CurrencySelectorProps = PropertyEditorProps<PropertyEditorCustomProps['currencySelector']>;

export type QuadInputProps = PropertyEditorProps<PropertyEditorCustomProps['quadInput']>;
export type BorderRadiusEditorProps = PropertyEditorProps<PropertyEditorCustomProps['borderRadiusEditor']>;
export type TRBLValuesEditorProps = PropertyEditorProps<PropertyEditorCustomProps['trblValuesEditor']>;
export type AlignmentEditorProps = PropertyEditorProps<PropertyEditorCustomProps['alignmentEditor']>;
export type ValueMinMaxEditorProps = PropertyEditorProps<PropertyEditorCustomProps['valueMinMaxEditor']>;
export type TypographyInputProps = PropertyEditorProps<PropertyEditorCustomProps['typographyInput']>;
export type ColorInputProps = PropertyEditorProps<PropertyEditorCustomProps['colorInput']>;
export type LayoutEditorProps = PropertyEditorProps<PropertyEditorCustomProps['layoutEditor']>;
export type StylesEditorProps = PropertyEditorProps<PropertyEditorCustomProps['stylesEditor']>;
export type JSONInputProps = PropertyEditorProps<PropertyEditorCustomProps['jsonInput']>;
export type CustomListProps = PropertyEditorProps<PropertyEditorCustomProps['customList']>;
export type TextDecorationControlProps = PropertyEditorProps<PropertyEditorCustomProps['textDecorationControl']>;
export type TextTransformControlProps = PropertyEditorProps<PropertyEditorCustomProps['textTransformControl']>;

export type EventsEditorProps = PropertyEditorProps<PropertyEditorCustomProps['eventsEditor']>;
export type QueryEditorProps = PropertyEditorProps<PropertyEditorCustomProps['queryEditor']>;

export type MFAuthQueryEditorProps = PropertyEditorProps<PropertyEditorCustomProps['mfAuthQueryEditor']>;

export type ModuleEditorProps = PropertyEditorProps<PropertyEditorCustomProps['moduleEditor']>;

export type assetEditorProps = PropertyEditorProps<PropertyEditorCustomProps['assetEditor']>;
export type cloudinaryEditorProps = PropertyEditorProps<PropertyEditorCustomProps['cloudinaryEditor']>;
export type imageListEditorProps = PropertyEditorProps<PropertyEditorCustomProps['listEditor']>;
export type AnalyticsEditorProps = PropertyEditorProps<PropertyEditorCustomProps['analyticsEditor']>;
export type AnimationsEditorProps = PropertyEditorProps<PropertyEditorCustomProps['animationsEditor']>;
export type EditorSectionHeaderProps = PropertyEditorProps<PropertyEditorCustomProps['editorSectionHeader']>;
export type ShopifyCollectionHandleProps = PropertyEditorProps<
  PropertyEditorCustomProps['shopifyCollectionHandleControl']
>;
export type ShopifyProductHandleProps = PropertyEditorProps<
  PropertyEditorCustomProps['shopifyCollectionHandleControl']
>;

export type ShopifyProductProps = PropertyEditorProps<PropertyEditorCustomProps['shopifyCollectionHandleControl']>;
export type ShopifyBlogHandleProps = PropertyEditorProps<PropertyEditorCustomProps['shopifyBlogHandleControl']>;
export type DateAndTimInputProps = PropertyEditorProps<PropertyEditorCustomProps['dateAndTimeInput']>;
export type RichTextControlProps = PropertyEditorProps<PropertyEditorCustomProps['richTextControl']>;

const checkbox: React.FC<CheckboxProps> = ({configProps, value, onChange}) => {
  return (
    <CheckboxControl
      value={value}
      label={configProps.label}
      fullSizeLabel={configProps.fullSizeLabel ?? false}
      reverse={configProps.reverse ?? false}
      onChange={onChange}
    />
  );
};

const radioGroupV2: React.FC<RadioGroupProps> = ({configProps, value, defaultValue, onChange}) => {
  return (
    <RadioGroupControlV2
      value={value}
      defaultValue={defaultValue}
      options={configProps.options}
      disableBinding={configProps.disableBinding}
      allowDeselect={configProps.allowDeselect}
      label={configProps.label}
      onChange={onChange}
    />
  );
};

const radioGroup: React.FC<RadioGroupProps> = ({configProps, value, defaultValue, onChange}) => {
  return (
    <RadioGroupControl
      value={value}
      defaultValue={defaultValue}
      options={configProps.options}
      disableBinding={configProps.disableBinding}
      allowDeselect={configProps.allowDeselect}
      label={configProps.label}
      onChange={onChange}
    />
  );
};

const codemirrorInput: React.FC<any> = ({configProps, value, defaultValue, onChange}) => {
  return (
    <CodeMirrorInput
      value={value}
      defaultValue={defaultValue}
      placeholder={configProps.placeholder}
      onChange={onChange}
      label={configProps.label}
      noOfLines={configProps.noOfLines}
      language={configProps.language}
      ignoreBinding={configProps.ignoreBinding}
    />
  );
};

const dropDown: React.FC<DropDownProps> = ({configProps, value, defaultValue, onChange}) => {
  return <DropDownControl defaultValue={defaultValue} onChange={onChange} value={value} {...configProps} />;
};

const jsonInput: React.FC<JSONInputProps> = ({configProps, value, defaultValue, onChange}) => {
  return <JsonInputControl onChange={onChange} value={value} {...configProps} />;
};

const customList: React.FC<CustomListProps> = ({
  configProps,
  value,
  defaultValue,
  onChange,
  onCustomPropChange,
  config,
}) => {
  return (
    <JsonBuilderControl
      onChange={onChange}
      onCustomPropChange={onCustomPropChange}
      value={value}
      schema={config.get('schema')}
      {...configProps}
    />
  );
};

const pageIdSelector: React.FC<PageIdSelectorProps> = ({configProps, value, defaultValue, onChange}) => {
  return <PageIdSelectorControl defaultValue={defaultValue} onChange={onChange} value={value} {...configProps} />;
};

const loaderIdSelector: React.FC<LoaderIdSelectorProps> = ({configProps, value, defaultValue, onChange}) => {
  return <PageIdSelectorControl defaultValue={defaultValue} onChange={onChange} value={value} {...configProps} />;
};

const screenSelector: React.FC<ScreenSelectorProps> = ({configProps, value, defaultValue, onChange}) => {
  return <ScreenSelectorControl defaultValue={defaultValue} onChange={onChange} value={value} {...configProps} />;
};

const currencySelector: React.FC<CurrencySelectorProps> = ({configProps, value, defaultValue, onChange}) => {
  return <CurrencySelectorControl defaultValue={defaultValue} onChange={onChange} value={value} {...configProps} />;
};

const headerIdSelector: React.FC<PageIdSelectorProps> = ({configProps, value, defaultValue, onCustomPropChange}) => {
  return (
    <HeaderIdSelectorControl
      defaultValue={defaultValue}
      onCustomPropChange={(k, v) => onCustomPropChange(k, v)}
      value={value}
      {...configProps}
    />
  );
};

// TODO: ColorInput PluginEditorProps<any> ->  PropertyEditorCustomProps['radioGroup']
const codeInput: React.FC<CodeInputProps> = ({configProps, value, name, defaultValue, onChange}) => {
  return (
    <CodeInputControl
      value={value}
      defaultValue={defaultValue}
      label={configProps.label}
      singleLine={configProps.singleLine}
      name={name}
      placeholder={configProps.placeholder}
      validationPattern={configProps.validationPattern}
      validationError={configProps.validationError}
      noOfLines={configProps.noOfLines}
      onChange={text => onChange(text, true, isEmpty(text))}
    />
  );
};
const codeInputV2: React.FC<CodeInputProps> = ({configProps, value, name, defaultValue, onChange}) => {
  return (
    <CodeInputControlV2
      value={value}
      defaultValue={defaultValue}
      label={configProps.label}
      singleLine={configProps.singleLine}
      name={name}
      placeholder={configProps.placeholder}
      validationPattern={configProps.validationPattern}
      validationError={configProps.validationError}
      noOfLines={configProps.noOfLines}
      onChange={text => onChange(text, true, isEmpty(text))}
    />
  );
};

// TODO: ColorInput PluginEditorProps<any> ->  PropertyEditorCustomProps['radioGroup']
const formatInput: React.FC<FormatInputProps> = ({configProps, value, name, defaultValue, onChange}) => {
  return (
    <FormatInputControl
      value={value}
      defaultValue={defaultValue}
      label={configProps.label}
      singleLine={configProps.singleLine}
      name={name}
      placeholder={configProps.placeholder}
      validationPattern={configProps.validationPattern}
      validationError={configProps.validationError}
      prefix={configProps.prefix}
      suffix={configProps.suffix}
      onChange={text => onChange(text, true, isEmpty(text))}
    />
  );
};

const aspectRatio: React.FC<AspectRatioProps> = ({configProps, value, onChange}) => {
  return (
    <AspectRatioControl
      value={value}
      label={configProps.label}
      onChange={text => onChange(text, true, isEmpty(text))}
    />
  );
};

const iconChooserInput: React.FC<IconChooserInputProps> = ({
  configProps,
  value,
  name,
  defaultValue,
  onChange,
  onCustomPropChange,
  config,
}) => {
  return (
    <IconChooserControl
      value={value}
      defaultValue={defaultValue}
      label={configProps.label}
      name={name}
      config={config}
      placeholder={configProps.placeholder}
      onChange={text => onChange(text, true)}
      onCustomPropChange={onCustomPropChange}
    />
  );
};

const regexInput: React.FC<RegexInputProps> = ({pageId, pluginId, configProps, value, name, onChange}) => {
  return (
    <RegexInputControl
      pageId={pageId}
      pluginId={pluginId}
      value={value}
      label={configProps.label}
      name={name}
      showRecommendation={configProps.showRecommendation}
      regexLabel={configProps.regexLabel || 'label'}
      placeholder={configProps.placeholder}
      onChange={text => onChange(text, true)}
    />
  );
};

const quadInput: React.FC<QuadInputProps> = ({
  configProps,
  defaultValue,
  value,
  name,
  config,
  onChange,
  onCustomPropChange,
}) => {
  return (
    <QuadInputControl
      value={value}
      name={name}
      label={configProps.label}
      defaultValue={defaultValue}
      placeholder={configProps.placeholder}
      onChange={text => onChange(text, true)}
      config={config}
      onCustomPropChange={(k, v) => onCustomPropChange(k, v, true)}
      layout={configProps.layout}
      options={configProps.options}
    />
  );
};

const borderRadiusEditor: React.FC<BorderRadiusEditorProps> = ({
  configProps,
  defaultValue,
  value,
  name,
  config,
  onChange,
  onCustomPropChange,
  ...props
}) => {
  return (
    <BorderRadiusControl
      value={value}
      name={name}
      label={configProps.label}
      defaultValue={defaultValue}
      onChange={onChange}
      config={config}
      onCustomPropChange={onCustomPropChange}
      options={configProps.options}
      {...props}
    />
  );
};

const trblValuesEditor: React.FC<TRBLValuesEditorProps> = ({
  configProps,
  defaultValue,
  value,
  name,
  config,
  onChange,
  onCustomPropChange,
  ...props
}) => {
  return (
    <TRBLControl
      value={value}
      name={name}
      label={configProps.label}
      defaultValue={defaultValue}
      onChange={onChange}
      config={config}
      onCustomPropChange={onCustomPropChange}
      options={configProps.options}
      {...props}
    />
  );
};

const alignmentEditor: React.FC<AlignmentEditorProps> = ({
  configProps,
  defaultValue,
  value,
  name,
  config,
  onChange,
  onCustomPropChange,
}) => {
  return (
    <AlignmentControl
      value={value}
      name={name}
      label={configProps.label}
      defaultValue={defaultValue}
      onChange={onChange}
      config={config}
      onCustomPropChange={onCustomPropChange}
      options={configProps.options}
    />
  );
};

const valueMinMaxEditor: React.FC<ValueMinMaxEditorProps> = ({
  configProps,
  defaultValue,
  value,
  name,
  config,
  onChange,
  onCustomPropChange,
}) => {
  return (
    <ValueMinMaxControl
      value={value}
      name={name}
      label={configProps.label}
      defaultValue={defaultValue}
      onChange={onChange}
      config={config}
      onCustomPropChange={onCustomPropChange}
      options={configProps.options}
    />
  );
};

const typographyInput: React.FC<TypographyInputProps> = ({configProps, onChange, ...props}) => {
  return (
    <TypographyInputControl
      label={configProps.label}
      disableExport={configProps.disableExport}
      onChange={text => onChange(text, true)}
      {...props}
    />
  );
};

const typographyInputV2: React.FC<TypographyInputProps> = ({configProps, onChange, ...props}) => {
  return (
    <TypographyInputControlV2
      label={configProps.label}
      disableExport={configProps.disableExport}
      onChange={text => onChange(text, true)}
      {...props}
    />
  );
};

const colorInput: React.FC<ColorInputProps> = ({configProps, name, value, defaultValue, onChange, ...props}) => {
  return (
    <ColorInputControl
      value={value}
      name={name}
      defaultValue={defaultValue}
      label={configProps.label}
      placeholder={configProps.placeholder}
      onChange={text => onChange(text, true, isEmpty(text))}
      {...props}
    />
  );
};

const rangeSliderInput: React.FC<RangeSliderProps> = ({configProps, value, defaultValue, onChange}) => {
  return (
    <RangeSliderControl
      value={value}
      defaultValue={defaultValue}
      label={configProps.label}
      placeholder={configProps.placeholder}
      maxRange={configProps.maxRange}
      minRange={configProps.minRange}
      steps={configProps.steps}
      onChange={text => onChange(text, true)}
    />
  );
};

const numericInput: React.FC<NumericInputProps> = ({configProps, value, defaultValue, onChange}) => {
  return (
    <NumericInputControl
      value={value}
      defaultValue={defaultValue}
      label={configProps.label}
      placeholder={configProps.placeholder}
      unit={configProps.unit}
      noUnit={configProps.noUnit}
      onChange={text => onChange(text, true)}
    />
  );
};

const listEditor: React.FC<imageListEditorProps> = props => {
  return <ImageListEditorControl {...props} />;
};

const listEditorV2: React.FC<imageListEditorProps> = props => {
  return <ImageListEditorControlV2 {...props} />;
};

const pageParamsEditor: React.FC<EventsEditorProps> = ({configProps, value, onChange}) => {
  return <PageParamsEditor value={value} label={configProps.label} onChange={value => onChange(value, false)} />;
};

const exportPageDialog: React.FC<EventsEditorProps> = ({pageId, config, configProps, value, onChange}) => {
  return (
    <ExportPageDialog
      label={configProps.label}
      onChange={value => onChange(value, false)}
      pageUUID={config.get('pageUUID')}
      value={value}
      pageId={pageId}
    />
  );
};

const stylesEditor: React.FC<StylesEditorProps> = props => {
  return <StylesEditor {...props} />;
};

const layoutEditor: React.FC<LayoutEditorProps> = props => {
  return <LayoutEditor {...props} />;
};

const eventsEditor: React.FC<EventsEditorProps> = props => {
  return <EventsInspector {...props} />;
};

const queryBuilder: React.FC<QueryEditorProps> = props => {
  return <QueryInspector {...props} />;
};
const moduleEditor: React.FC<ModuleEditorProps> = () => {
  return <></>;
};

const assetEditor: React.FC<assetEditorProps> = props => {
  return <AssetEditor {...props} />;
};

const cloudinaryEditor: React.FC<cloudinaryEditorProps> = props => {
  return <CloudinaryEditor {...props} />;
};

const analyticsEditor: React.FC<AnalyticsEditorProps> = props => {
  return <AnalyticsEditor {...props} />;
};
const animationsEditor: React.FC<AnimationsEditorProps> = props => {
  return <AnimationsEditor {...props} />;
};

const editorSectionHeader: React.FC<EditorSectionHeaderProps> = props => {
  const {configProps, name} = props;
  return <EditorSectionHeader name={name} label={configProps.label} />;
};

const shopifyCollectionHandleControl: React.FC<ShopifyCollectionHandleProps> = props => {
  return <ShopifyEntityKeyControl label={props?.configProps?.label} type="handle" itemType="collection" {...props} />;
};

const shopifyProductHandleControl: React.FC<ShopifyCollectionHandleProps> = props => {
  return <ShopifyEntityKeyControl label={props?.configProps?.label} type="handle" itemType="product" {...props} />;
};

const shopifyCollectionControl: React.FC<ShopifyProductProps> = props => {
  return <ShopifyEntityKeyControl label={props?.configProps?.label} type="all" itemType="collection" {...props} />;
};

const shopifyProductControl: React.FC<ShopifyProductProps> = props => {
  return <ShopifyEntityKeyControl label={props?.configProps?.label} type="all" itemType="product" {...props} />;
};

const shopifyBlogHandleControl: React.FC<ShopifyBlogHandleProps> = props => {
  return <ShopifyEntityKeyControl label={props?.configProps?.label} type="handle" itemType="blog" {...props} />;
};

const shopifyBlogControl: React.FC<ShopifyBlogHandleProps> = props => {
  return <ShopifyEntityKeyControl label={props?.configProps?.label} type="all" itemType="blog" {...props} />;
};

const dateAndTimeInput: React.FC<DateAndTimInputProps> = ({
  configProps,
  name,
  value,
  defaultValue,
  onChange,
  ...props
}) => {
  return (
    <DateAndTimeControl
      value={value}
      name={name}
      defaultValue={defaultValue}
      label={configProps.label}
      disableBinding={configProps.disableBinding}
      onChange={text => onChange(text, true)}
      {...props}
    />
  );
};

const mfAuthQueryEditor: React.FC<MFAuthQueryEditorProps> = props => {
  return <MFAuthQueryInspector {...props} />;
};

const mfAuthenticationQueryEditor: React.FC<MFAuthQueryEditorProps> = props => {
  return <MFAuthenticationQueryInspector {...props} />;
};

const textDecorationInput: React.FC<TextDecorationControlProps> = props => {
  const {value, defaultValue, onChange, configProps} = props;
  return (
    <TextDecorationControl
      value={value}
      defaultValue={defaultValue}
      onChange={text => onChange(text, true)}
      label={configProps.label}
    />
  );
};

const textTransformInput: React.FC<TextTransformControlProps> = props => {
  const {value, defaultValue, onChange, configProps} = props;
  return (
    <TextTransformControl
      value={value}
      defaultValue={defaultValue}
      onChange={text => onChange(text, true)}
      label={configProps.label}
    />
  );
};

const richTextStyleControl: React.FC<TextTransformControlProps> = props => {
  return <RichTextControl {...props} />;
};

const customData = props => {
  return <CustomDataControl {...props} />;
};

const navigationInput: React.FC<any> = props => {
  return <NavigationInput {...props} />;
};

export const pluginEditorComponents = {
  codeInput,
  formatInput,
  aspectRatio,
  quadInput,
  borderRadiusEditor,
  trblValuesEditor,
  valueMinMaxEditor,
  checkbox,
  rangeSliderInput,
  typographyInput,
  radioGroup,
  colorInput,
  stylesEditor,
  layoutEditor,
  eventsEditor,
  queryBuilder,
  pageParamsEditor,
  moduleEditor,
  assetEditor,
  cloudinaryEditor,
  listEditor,
  dropDown,
  pageIdSelector,
  loaderIdSelector,
  screenSelector,
  currencySelector,
  regexInput,
  numericInput,
  iconChooserInput,
  headerIdSelector,
  analyticsEditor,
  animationsEditor,
  editorSectionHeader,
  jsonInput,
  customList,
  shopifyCollectionHandleControl,
  shopifyProductHandleControl,
  shopifyCollectionControl,
  shopifyProductControl,
  shopifyBlogHandleControl,
  shopifyBlogControl,
  mfAuthQueryEditor,
  alignmentEditor,
  exportPageDialog,
  dateAndTimeInput,
  textDecorationInput,
  textTransformInput,
  mfAuthenticationQueryEditor,
  richTextStyleControl,
  customData,
  codemirrorInput,
  navigationInput,
};

export const pluginEditorComponentsV2 = {
  codeInput: codeInputV2,
  formatInput,
  aspectRatio,
  quadInput,
  borderRadiusEditor,
  trblValuesEditor,
  valueMinMaxEditor,
  checkbox,
  rangeSliderInput,
  typographyInput: typographyInputV2,
  radioGroup: radioGroupV2,
  colorInput,
  stylesEditor,
  layoutEditor,
  eventsEditor,
  queryBuilder,
  pageParamsEditor,
  moduleEditor,
  assetEditor,
  cloudinaryEditor,
  listEditor: listEditorV2,
  dropDown,
  pageIdSelector,
  loaderIdSelector,
  screenSelector,
  currencySelector,
  regexInput,
  numericInput,
  iconChooserInput,
  headerIdSelector,
  analyticsEditor,
  animationsEditor,
  editorSectionHeader,
  jsonInput,
  customList,
  shopifyCollectionHandleControl,
  shopifyProductHandleControl,
  shopifyCollectionControl,
  shopifyProductControl,
  shopifyBlogHandleControl,
  shopifyBlogControl,
  mfAuthQueryEditor,
  alignmentEditor,
  exportPageDialog,
  dateAndTimeInput,
  textDecorationInput,
  textTransformInput,
  mfAuthenticationQueryEditor,
  richTextStyleControl,
  customData,
  codemirrorInput,
  navigationInput,
};
