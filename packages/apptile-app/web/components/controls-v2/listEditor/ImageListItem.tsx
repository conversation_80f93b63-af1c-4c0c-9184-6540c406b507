import {debounce} from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, Text, TextInput, TouchableOpacity, Image, View, Pressable} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import {IImageListItem} from '../../../../app/plugins/state/DisplayImageList/ImageListTypes';
import CodeInput from '../../codeEditor/codeInput';
import RadioGroupControl from '../../controls/RadioGroupControl';
import AssetEditor from '../../controls/assetEditor/assetEditor';
import Immutable from 'immutable';
import ScreenSelectorControl from '../../controls/ScreenSelectorControl';
import {useSelector} from 'react-redux';
import {ShopifyEntityTypes, SHOPIFY_PAGES_MAP} from '@/root/web/integrations/shopify/constants/ShopifyConstants';
import _ from 'lodash';
import {ShopifyItemObjectPicker} from '@/root/web/integrations/shopify/components/ShopifyItemPicker';
import CollapsiblePanel from '../../CollapsiblePanel';
import theme from '@/root/web/styles-v2/theme';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon';
import CodeInputControl from '../CodeInputControl';
import Tooltip from '@/root/web/components-v2/base/Tooltip/Index';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import CodeInputControlV2 from '../CodeInputControl';
import {EditorRootState} from '@/root/web/store/EditorRootState';
export interface ImageListItemProps {
  imageItem: IImageListItem;
  shopifyType?: ShopifyEntityTypes;
  onUpdateImageItem: (imageItem: IImageListItem) => void;
  onDeleteImageItem: () => void;
  disableRemove?: boolean;
  totalItems: number;
  minLength: number;
}
const ImageListItem: React.FC<ImageListItemProps> = props => {
  const {onUpdateImageItem, onDeleteImageItem, shopifyType, imageItem, disableRemove, totalItems, minLength} = props;
  const [listItem, setListItem] = useState<IImageListItem>(imageItem);
  useEffect(() => {
    setListItem(imageItem);
  }, [imageItem]);
  const [isExpanded, setExpanded] = useState(false);
  const [isOverlayPopoverOpen, setOverLayPopverOpen] = useState(false);

  // GET ASSET URL FROM ID
  const assetState = useSelector((state: EditorRootState) => state.asset);
  const currentAsset = assetState.assetsById[listItem.assetId];
  useEffect(() => {
    if (
      currentAsset &&
      listItem.navEntityType === 'External Link' &&
      listItem.assetId === currentAsset.id &&
      listItem.url !== currentAsset.thumbUrl
    ) {
      setListItem({
        ...listItem,
        url: currentAsset.thumbUrl,
      });
      onUpdateImageItem({...listItem, url: currentAsset.thumbUrl});
    }
  }, [currentAsset, listItem]);

  /// FIXME: SHOPIFY related customizations
  const platform = useSelector(state => state.platform);
  const [isEmbeddedInShopify] = useState(platform?.isEmbeddedInShopify);
  const [pageType, setPageType] = useState(
    shopifyType ?? _.findKey(SHOPIFY_PAGES_MAP, p => p === listItem.navEntityType),
  );
  const [navigatePage, setNavigatePage] = useState(pageType);
  useEffect(() => {
    setPageType(shopifyType ?? _.findKey(SHOPIFY_PAGES_MAP, p => p === listItem.navEntityType));
  }, [listItem, shopifyType]);
  useEffect(() => {
    if (
      pageType &&
      SHOPIFY_PAGES_MAP[pageType] &&
      (!listItem.navEntityType || listItem.navEntityType != SHOPIFY_PAGES_MAP[pageType])
    ) {
      onValueChange('navEntityType', pageType);
      setNavigatePage(pageType);
    } else if (!SHOPIFY_PAGES_MAP[pageType]) {
      setNavigatePage('Others');
    }
  }, [pageType]);

  const onShopifyItemUpdate = useCallback(
    (item: any) => {
      console.log(item);
      const {image, featuredImage} = item;
      const finalImage = image || featuredImage;
      var update = {
        ...{
          navEntityId: item?.handle,
          title: item?.title,
        },
      };
      // Update image url if not uploaded image.
      if (listItem.sourceType === 'url') {
        update = {
          ...update,
          ...{
            sourceType: 'url',
            url:
              finalImage?.url ??
              'https://cdn.apptile.io/e9ccc9c6-97b2-4e6e-9011-e41ea93efd3c/6a27a5a5-9c4a-413a-9fbd-a70fff2ba1a5/original.png',
          },
        };
      }
      setListItem({
        ...listItem,
        ...update,
      });
      onUpdateImageItem({
        ...listItem,
        ...update,
      });
      setExpanded(false);
    },
    [onUpdateImageItem, listItem],
  );
  //// FIXME: End SHOPIFY customizations

  const onValueChange = useCallback(
    (key, value) => {
      if (listItem.navEntityType === 'ExternalLink' && key === 'navEntityId') {
        setListItem({...listItem, ...{[key]: value, title: value}});
        onUpdateImageItem({...listItem, ...{[key]: value, title: value}});
      } else if (listItem.navEntityType === 'ExternalLink' && key === 'navEntityType') {
        setListItem({...listItem, ...{[key]: value, sourceType: 'url', title: ''}});
        onUpdateImageItem({...listItem, ...{[key]: value, sourceType: 'url', title: ''}});
      } else {
        setListItem({...listItem, ...{[key]: value}});
        onUpdateImageItem({...listItem, ...{[key]: value}});
      }
    },
    [onUpdateImageItem, listItem],
  );
  const onBulkValueChange = useCallback(
    update => {
      setListItem({...listItem, ...update});
      onUpdateImageItem({...listItem, ...update});
    },
    [onUpdateImageItem, listItem],
  );
  const debouncedOnValueChange = debounce(onValueChange, 450);
  const onDelete = useCallback(() => {
    onDeleteImageItem();
    setExpanded(false);
  }, [onDeleteImageItem]);

  const [imageConfigMap, setImageConfigMap] = useState(Immutable.Map(imageItem));
  useEffect(() => {
    setImageConfigMap(Immutable.Map(imageItem));
  }, [imageItem]);

  return (
    <View style={styles.itemContainer}>
      <CollapsiblePanel
        isOpen={isExpanded}
        setOpen={(open: boolean) => {
          setExpanded(!isExpanded);
        }}
        backgroundStyle={{borderWidth: 0}}
        title="section"
        customHeader={
          <Pressable
            style={[styles.rowContainer, commonStyles.input, styles.selectionContainer]}
            onPress={() => {
              setExpanded(!isExpanded);
            }}>
            <View style={[styles.rowContainer, {flex: 1}]}>
              <Image
                style={[styles.inputImage]}
                resizeMode="cover"
                source={
                  listItem.url && listItem.url.trim()
                    ? {uri: listItem.url}
                    : require('../../../assets/images/placeholder-image.png')
                }
              />
              <View style={[styles.rowContainer, {flex: 1, justifyContent: 'space-between', wrap: 'nowrap'}]}>
                <Text
                  style={{
                    color: listItem.title ? theme.CONTROL_INPUT_COLOR : theme.CONTROL_PLACEHOLDER_COLOR,
                    textWrap: 'nowrap',
                    textOverflow: 'ellipsis',
                    overflow: 'hidden',
                    fontSize: theme.FONT_SIZE,
                  }}>
                  {listItem.title || `Select ${pageType || 'Product/Collection'}`}
                </Text>
                {isExpanded && (
                  <Text style={{color: theme.CONTROL_PLACEHOLDER_COLOR, paddingVertical: 5}}>Editing...</Text>
                )}
                {!isExpanded && totalItems > minLength && (
                  <Pressable
                    style={[
                      styles.rowContainer,
                      styles.editCollectionContainer,
                      totalItems <= minLength ? {cursor: 'not-allowed'} : {},
                    ]}
                    disabled={totalItems <= minLength}
                    onPress={onDelete}>
                    <MaterialCommunityIcons
                      name={'close'}
                      size={16}
                      color={totalItems <= minLength ? theme.CONTROL_PLACEHOLDER_COLOR : theme.SECONDARY_COLOR}
                    />
                  </Pressable>
                )}
              </View>
            </View>
          </Pressable>
        }>
        <View style={styles.editContainer}>
          <View style={[styles.rowContainer, {justifyContent: 'space-between'}]}>
            <Text style={commonStyles.heading}>CARD CONTENT</Text>
            <MaterialCommunityIcons
              onPress={() => {
                setExpanded(!isExpanded);
              }}
              name="close"
              size={14}
            />
          </View>
          <AssetEditor
            configProps={{
              label: 'Image',
              urlProperty: 'url',
              assetProperty: 'assetId',
              sourceTypeProperty: 'sourceType',
              disableBinding: true,
            }}
            config={imageConfigMap}
            onBulkValueChange={onBulkValueChange}
            setOpened={setOverLayPopverOpen}
          />
          <CodeInputControlV2
            label={listItem.navEntityType == 'ExternalLink' ? 'URL' : 'Title'}
            value={listItem.title}
            singleLine={true}
            onChange={(value: string) => debouncedOnValueChange('title', value)}
          />

          {pageType === 'Product' ? (
            // FIXME: Ugly hack for Embedded editor in shopify to only show 2 Options.
            <ShopifyItemObjectPicker
              label={'Navigate To'}
              value={listItem.navEntityId}
              itemType="product"
              onChange={onShopifyItemUpdate}
              defaultOpen={false}
              setOpened={setOverLayPopverOpen}
            />
          ) : pageType === 'Collection' ? (
            // FIXME: Ugly hack for Embedded editor in shopify to only show 2 Options.
            <ShopifyItemObjectPicker
              label={'Navigate To'}
              value={listItem.navEntityId}
              itemType="collection"
              onChange={onShopifyItemUpdate}
              defaultOpen={false}
              setOpened={setOverLayPopverOpen}
            />
          ) : (
            <ScreenSelectorControl
              value={listItem.navEntityType}
              label="Navigate To"
              disableBinding={true}
              onChange={(value: string) => onValueChange('navEntityType', value)}
            />
          )}
        </View>
      </CollapsiblePanel>
    </View>
  );
};

const styles = StyleSheet.create({
  rowContainer: {
    alignContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    flexBasis: 'auto',
  },
  editCollectionContainer: {
    padding: 5,
  },
  itemContainer: {
    alignContent: 'flex-start',
    flexDirection: 'column',
    flexBasis: 'auto',
    marginBottom: 8,
  },
  selectionContainer: {
    paddingVertical: 6,
    paddingHorizontal: 8,
  },
  codeInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#b9b9b9',
    fontSize: 11,
    padding: 2,
  },
  valueText: {
    flex: 1,
    flexGrow: 1,
  },
  titleText: {
    flex: 1,
    flexBasis: 'auto',
    margin: 10,
    fontSize: 18,
    flexGrow: 0,
  },
  nameInput: {
    flex: 1,
    fontSize: 12,
  },
  actionIconsWrapper: {
    justifyContent: 'space-between',
    alignItems: 'center',
    alignSelf: 'stretch',
    flexGrow: 0,
    flexBasis: 'auto',
    marginLeft: 'auto',
  },
  actionIcon: {
    flexGrow: 0,
    flexBasis: 'auto',
  },
  inputImage: {
    height: 20,
    width: 20,
    borderRadius: 4,
    marginRight: 8,
  },
  editContainer: {
    width: '100%',
    padding: 12,
    borderWidth: 1,
    borderColor: theme.CONTROL_BORDER,
    borderRadius: 8,
    gap: 14,
    marginTop: 8,
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: '#FFFFFF',
  },
});

export default ImageListItem;
