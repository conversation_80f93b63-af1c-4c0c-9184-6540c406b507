import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {View, Text, StyleSheet, Pressable, TextInput as TextInputReact} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import _, {debounce, isEmpty} from 'lodash';

import {useTheme} from 'apptile-core';
import PopoverComponent from '@/root/web/components-v2/base/Popover';

import {TypographyInputProps} from '../../pluginEditorComponents';
import {strsel} from 'apptile-core';
import {pluginConfigUpdatePath} from 'apptile-core';
import {defaultTypographyEditors} from 'apptile-core';
import {setThemeConfig} from '../../../actions/themeActions';

import {selectActiveThemeConfig} from 'apptile-core';


import commonStyles from '@/root/web/styles-v2/commonStyles';
import theme from '@/root/web/styles-v2/theme';
import {bindActionCreators} from 'redux';
import {MaterialCommunityIcons} from 'apptile-core';

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#DADADA',
  },
  iconContainerStyle: {
    backgroundColor: 'transparent',
    marginRight: 5,
  },
  searchContainer: {
    borderColor: '#DADADA',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    paddingTop: 13,
    paddingBottom: 0,
    marginTop: 8,
  },
  bodyFont: {fontSize: 12},
  fontSearchItem: {
    marginBottom: 13,
  },
  fontSearchContainer: {
    borderColor: '#DADADA',
  },
  fontSizeContainer: {
    width: 50,
  },
  fontChooser: {
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    flexGrow: 1,
    marginRight: 5,
  },
  rowSpaceLayout: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  fontChooserHeading: {
    fontSize: 12,
    marginBottom: 5,
  },
  editorInputItem: {
    flexShrink: 1,
    flexGrow: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  inputContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  sizeContainer: {
    flexDirection: 'row',
    backgroundColor: theme.INPUT_BACKGROUND,
    overflow: 'hidden',
  },
  sizeButtons: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: theme.INPUT_BACKGROUND,
    userSelect: false,
  },
  sizeInput: {
    width: 28,
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
  },

  editorInputItem: {
    flexShrink: 1,
    flexGrow: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  dropdownOptions: {
    width: 170,
    maxHeight: 170,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.25)',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    marginTop: 5,
  },
  popoverText: {
    flexDirection: 'row',
    padding: 8,
    textAlign: 'left',
    width: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    alignItems: 'center',
    borderRadius: 8,
  },
});

const textInputStyles = {
  textInputStyles: {outline: 'none', paddingVertical: 7},
  inputWrapperStyles: {alignItems: 'center'},
};

interface TypographyInputControlProps extends Omit<TypographyInputProps, 'configProps'> {
  label: string;
  inTheme?: boolean;
  disableExport?: boolean;
}

const TypographyInputControlV2: React.FC<TypographyInputControlProps> = props => {
  const {
    pluginId,
    pageId,
    configPathSelector,
    config,
    value,
    name,
    inTheme,
    disableExport = false,
    onUpdateRawValue,
    label,
    entityConfig,
  } = props;

  const {themeEvaluator} = useTheme();
  const dispatch = useDispatch();
  const themeConfig = useSelector(selectActiveThemeConfig);

  const themeProfile = ['typography'];
  const themePresets = themeProfile ? themeEvaluator(strsel(themeProfile)) : {};
  const presets = Object.keys(themePresets).map(preset => ({
    name: _.startCase(preset),
    value: `typography.${preset}`,
    theme: preset,
  }));

  const updatePreset = useCallback(
    (val: string) => {
      if (!inTheme) dispatch(pluginConfigUpdatePath(pluginId, pageId, configPathSelector.slice(1), {_inherit: val}));
      else
        dispatch(
          setThemeConfig({
            selector: configPathSelector.concat('dependencies'),
            value: [val],
          }),
        );
    },
    [configPathSelector, dispatch, inTheme, pageId, pluginId],
  );

  const [presetValue, setPresetValue] = useState<string>();
  useEffect(() => {
    let preset = inTheme
      ? _.first(_.get(themeConfig, configPathSelector.concat('dependencies'), []))
      : config?.getIn([name, '_inherit']);
    setPresetValue((preset as string) ?? '');
  }, [config, configPathSelector, inTheme, name, themeConfig]);

  const editorConfig = defaultTypographyEditors;
  const platform = useSelector(state => state.platform);
  const {pluginConfigUpdatePath: updateConfig} = useMemo(
    () => bindActionCreators({pluginConfigUpdatePath}, dispatch),
    [dispatch],
  );

  const debouncedUpdateConfig = debounce(
    (widgetId, pageId, selector, update, remove) => updateConfig(widgetId, pageId, selector, update, remove),
    300,
  );
  const onChange = useCallback(
    (selector: string[], name, v: unknown, debounced = false, remove = false) => {
      if (debounced) {
        debouncedUpdateConfig(pluginId, pageId, selector, remove ? {} : {[name]: v}, remove ? [name] : undefined);
      } else {
        updateConfig(pluginId, pageId, selector, remove ? {} : {[name]: v}, remove ? [name] : undefined);
      }
    },
    [debouncedUpdateConfig, pluginId, pageId, updateConfig],
  );

  const debouncedOnChange = debounce((selector: string[], v: any) => {
    let fontSize = Number(v);
    fontSize = isNaN(fontSize) ? 10 : fontSize;
    onChange(selector, 'fontSize', fontSize);
    onChange(selector, 'lineHeight', fontSize * 1.5);
  }, 400);

  const currentTheme = presets.find(e => e.value == presetValue)?.theme;

  const [size, setSize] = useState(value?.get('fontSize') ?? themePresets[currentTheme]?.fontSize);

  useEffect(() => {
    const fontSize = Number(value?.get('fontSize') ?? themePresets[currentTheme]?.fontSize);
    const newFontSize = isNaN(fontSize) ? `${themePresets?.body?.fontSize}` : `${fontSize}`;
    setSize(newFontSize);
  }, [currentTheme, value]);
  const [selValue, setSelValue] = useState<string>(presetValue);
  const [showPopover, setShowPopover] = useState(false);
  let currentItemName = value || presetValue;
  let currentItem = null;
  const filteredOption = presets.filter(v => v.value === presetValue)[0];
  currentItemName = filteredOption ? filteredOption.name : '';
  currentItem = filteredOption;

  const [currentOption, setCurrentOption] = useState<string>(currentItemName);

  useEffect(() => {
    if (!isEmpty(presetValue)) {
      setSelValue(presetValue);
    }

    let currentItemName = presetValue;
    if (Array.isArray(presets) && presets.find(v => v.value === presetValue)) {
      const filteredOption = presets.filter(v => v.value === presetValue)[0];
      currentItemName = filteredOption ? filteredOption.name : '';
      currentItem = filteredOption;
    }
    setCurrentOption(currentItemName);
  }, [presetValue, presets]);
  const buttonRef = useRef(null);
  const containerRef = useRef(null);
  return (
    <View style={[!disableExport && {margin: 4, padding: 4, borderWidth: 1.5, borderColor: '#0005', borderRadius: 4}]}>
      <View style={styles.editorInputItem}>
        <View ref={containerRef} style={styles.editorInputItem}>
          {label && (
            <View style={commonStyles.labelContainer}>
              <Text style={[commonStyles.labelText]}>{label}</Text>
            </View>
          )}
          <View style={[label ? commonStyles.inputContainer : {width: '100%'}, styles.inputContainer]}>
            <PopoverComponent
              positions={['bottom', 'left', 'right', 'top']}
              visible={showPopover}
              onVisibleChange={setShowPopover}
              trigger={
                <View
                  ref={buttonRef}
                  style={[
                    {
                      flexDirection: 'row',
                      alignItems: 'center',
                      padding: 8,
                    },
                    commonStyles.input,
                  ]}>
                  <Text style={[commonStyles.inputText, {flex: 1, overflow: 'hidden'}]}>
                    {currentOption || selValue || 'Select Value'}
                  </Text>
                  <Text style={{width: 15}}>
                    <MaterialCommunityIcons
                      name={showPopover ? 'chevron-up' : 'chevron-down'}
                      size={16}
                      color={theme.SECONDARY_COLOR}
                    />
                  </Text>
                </View>
              }>
              <View
                style={[
                  styles.dropdownOptions,
                  buttonRef?.current && {width: buttonRef?.current?.getBoundingClientRect()?.width},
                  containerRef?.current && {
                    maxHeight: 170,
                  },
                ]}>
                <View style={[{flex: 1, height: '100%', padding: 4, overflowY: 'auto'}]}>
                  {presets.map((item: any, index: number) => {
                    const itemName = item.name;
                    return (
                      <View key={item.value + '-' + index}>
                        <Pressable
                          style={[styles.popoverText, presetValue == item.value && {backgroundColor: theme.PRIMARY_OPAQUE_BACKGROUND}]}
                          onPress={() => {
                            setShowPopover(false);
                            setCurrentOption(itemName);
                            updatePreset(item.value);
                          }}>
                          <Text style={[themePresets[item.theme], {fontSize: 14, lineHeight: 18},presetValue == item.value &&{color: theme.PRIMARY_COLOR}]}>{itemName}</Text>
                        </Pressable>
                      </View>
                    );
                  })}
                </View>
              </View>
            </PopoverComponent>

            <View style={[styles.sizeContainer, {borderRadius: theme.INPUT_BORDER_RADIUS}]}>
              <Pressable
                style={[styles.sizeButtons]}
                onPress={() => {
                  let fontSize = Number(size);
                  fontSize = isNaN(fontSize) ? themePresets?.body?.fontSize : fontSize;
                  fontSize = fontSize - 1;
                  onChange(configPathSelector.slice(1), 'fontSize', fontSize);
                  onChange(configPathSelector.slice(1), 'lineHeight', fontSize * 1.5);
                }}>
                <MaterialCommunityIcons name="minus" size={14} />
              </Pressable>
              <TextInputReact
                value={size}
                style={[commonStyles.inputText, styles.sizeInput]}
                onChange={change => {
                  setSize(change?.target?.value);
                  debouncedOnChange(configPathSelector.slice(1), change?.target?.value);
                }}
              />
              <Pressable
                style={[styles.sizeButtons]}
                onPress={() => {
                  let fontSize = Number(size);
                  fontSize = isNaN(fontSize) ? themePresets?.body?.fontSize : fontSize;
                  fontSize = fontSize + 1;
                  onChange(configPathSelector.slice(1), 'fontSize', fontSize);
                  onChange(configPathSelector.slice(1), 'lineHeight', fontSize * 1.5);
                }}>
                <MaterialCommunityIcons name="plus" size={14} />
              </Pressable>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default TypographyInputControlV2;
