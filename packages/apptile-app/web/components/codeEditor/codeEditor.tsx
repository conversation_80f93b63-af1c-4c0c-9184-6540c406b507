/* eslint-disable no-lone-blocks */
import * as codemirror from 'codemirror';
import 'codemirror/addon/display/autorefresh';
import 'codemirror/addon/display/placeholder';
import 'codemirror/addon/edit/closebrackets';
import 'codemirror/addon/edit/matchbrackets';
import 'codemirror/addon/hint/anyword-hint';
import 'codemirror/addon/hint/javascript-hint';
import 'codemirror/addon/hint/show-hint';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/sql-hint';
import 'codemirror/addon/mode/multiplex';
// import tern from 'tern';
// import 'codemirror/addon/tern/tern';
import 'codemirror/addon/tern/tern.css';
import 'codemirror/lib/codemirror.css';
import 'codemirror/mode/javascript/javascript';
import 'codemirror/mode/sql/sql';
import isEmpty from 'lodash-es/isEmpty';
import isEqual from 'lodash-es/isEqual';
import {forwardRef, default as React, SyntheticEvent} from 'react';
import {StyleSheet, View} from 'react-native';
import 'imports-loader?type=commonjs&imports=tern&wrapper=window&!codemirror/addon/tern/tern';

import InitModes from './config/EditorModes';
import _ from 'lodash';
import commonStyles from '../../styles-v2/commonStyles';
import theme from '../../styles-v2/theme';

// window.tern = tern;
export const CodeMirrorEmpty = `.CodeMirror-empty {
  color: #a0a0a0;
}
.CodeMirror-placeholder {
  color: ${theme.CONTROL_PLACEHOLDER_COLOR} !important;
}
.CodeMirror {
  height: auto;
  min-height: 28px;
  padding: 0px;
  background: transparent !important;
  font-family: ${theme.FONT_FAMILY};
  font-size: ${theme.FONT_SIZE}px;
  line-height: ${theme.LINE_HEIGHT + 2}px;
  color: ${theme.CONTROL_INPUT_COLOR};
}
.CodeMirror *::-webkit-scrollbar {
  background-color:#0000;
  width: 4px;
  height: 4px;
}
/* scrollbar itself */
.CodeMirror *::-webkit-scrollbar-thumb {
  border-radius: 6px;
}
/* set button(top and bottom of the scrollbar) */
.CodeMirror *::-webkit-scrollbar-button {display:none;height: 0; width: 0;}
.CodeMirror-lines { padding: 8px 4px;}
.CodeMirror-line { padding-right: 4px;}
.CodeMirror pre.CodeMirror-line, .CodeMirror pre.CodeMirror-line-like{
  padding: 0px 6px 0px 4px;
}
`;

// Create stylesheet
const style = document.createElement('style');
style.type = 'text/css';
if (style.styleSheet) {
  style.styleSheet.cssText = CodeMirrorEmpty;
} else {
  style.appendChild(document.createTextNode(CodeMirrorEmpty));
}

// Inject stylesheet
document.head.appendChild(style);

export type CodemirrorEditor = codemirror.Editor;
export type CodemirrorEditorChange = codemirror.EditorChange;
export type CodemirrorScrollInfo = codemirror.ScrollInfo;
export type CodemirrorPosition = codemirror.Position;
export type CodemirrorLineHandle = codemirror.LineHandle;

export interface EditorEvent {
  (editor: CodemirrorEditor, event?: SyntheticEvent): void;
}

export interface EditorChangeEvent {
  (editor: CodemirrorEditor, changeObj: CodemirrorEditorChange): void;
}

export interface KeyHandledEvent {
  (editor: CodemirrorEditor, name: string, event: SyntheticEvent): void;
}

export interface ICodeMirrorActions {
  onChange?(editor: CodemirrorEditor, data: CodemirrorEditorChange, value: unknown): void;
  onCursorActivity?(editor: CodemirrorEditor): void;
  onFocusChange?(e: boolean): void;
  onScroll?(editor: CodemirrorEditor, data: CodemirrorScrollInfo): void;

  onBlur?: EditorEvent;
  onContextMenu?: EditorEvent;
  onCopy?: EditorEvent;
  onCursor?: (editor: CodemirrorEditor, data: CodemirrorPosition) => void;
  onCut?: EditorEvent;
  onDblClick?: EditorEvent;
  onDragEnter?: EditorEvent;
  onDragLeave?: EditorEvent;
  onDragOver?: EditorEvent;
  onDragStart?: EditorEvent;
  onDrop?: EditorEvent;
  onFocus?: EditorEvent;
  onGutterClick?: (editor: CodemirrorEditor, lineNumber: number, gutter: string, event: Event) => void;
  onInputRead?: EditorChangeEvent;
  onKeyDown?: EditorEvent;
  onKeyHandled?: KeyHandledEvent;
  onKeyPress?: EditorEvent;
  onKeyUp?: EditorEvent;
  onMouseDown?: EditorEvent;
  onPaste?: EditorEvent;
  onRenderLine?: (editor: CodemirrorEditor, line: CodemirrorLineHandle, element: HTMLElement) => void;
  onSelection?: (editor: CodemirrorEditor, data: Record<string, unknown>) => void;
  onTouchStart?: EditorEvent;
  onUpdate?: (editor: CodemirrorEditor) => void;
  onViewportChange?: (editor: CodemirrorEditor, start: number, end: number) => void;
}

export interface ICustomHints {
  tables: Record<string, Array<string>> | Array<string>;
}

export interface IcodeEditorProps extends ICodeMirrorActions {
  autoFocus?: boolean;
  className?: string;
  editorInstance?: CodemirrorEditor;
  defaultValue?: string;
  name?: string;
  options: Record<string, unknown>;
  path?: string;
  value?: string;
  preserveScrollPosition?: boolean;
  customHints?: ICustomHints | null;
  editorSize?: Array<string>;
  placeholder?: string;
  ternDefs?: Array<Record<string, unknown>>;
  forwardedRef?: React.MutableRefObject<{isFocused: () => boolean}>;
}

const normalizeLineEndings = str => {
  if (typeof str !== 'string') return str;
  else {
    return str.replace(/\r\n|\r/g, '\n');
  }
};

interface codeEditorState {
  isFocused: boolean;
}
class CodeEditor extends React.Component<IcodeEditorProps, codeEditorState> {
  editor: codemirror.Editor;
  textareaNode = React.createRef<HTMLTextAreaElement>();
  server: codemirror.defaults.TernServer;
  defaultEditorSize = [600, 100];

  constructor(props: IcodeEditorProps) {
    super(props);

    this.state = {
      isFocused: false,
    };
  }

  getCodeMirrorInstance(): codemirror.Editor {
    return this.props.editorInstance || codemirror;
  }

  componentDidMount(): void {
    InitModes(codemirror);
    const value = this.props.defaultValue || this.props.value;
    const codeMirrorInstance = this.getCodeMirrorInstance();
    this.editor = codeMirrorInstance.fromTextArea(this.textareaNode, this.props.options);
    if (value) this.editor.setValue(value);
    const editorSize = this.props.editorSize || this.defaultEditorSize || [];
    this.editor.setSize(...editorSize);
    this.enableAutoSuggestionUsingTern();

    if (this.props.forwardedRef) {
      this.props.forwardedRef.current = {
        isFocused: () => {
          return this.state.isFocused;
        },
      };
    }

    this.editor.on('focus', (cm, event) => {
      this.editor.setCursor({line: this.editor.lineCount() || 1, ch: 0});
      this.editor.refresh();
    });

    this.editor.on('keyup', (cm, event) => {
      if (!cm.state.completionActive) {
        const {defaultHint, dynamicHints} = this.getEditorHint(cm);
        codemirror.commands.autocomplete(cm, defaultHint, dynamicHints);
      }
    });

    this.editor.on('change', (doc, change) => {
      if (this.props.onChange && change.origin !== 'setValue') {
        this.props.onChange(this.getCodeMirrorInstance(), change, doc.getValue());
      }
    });

    this.connectEvents(this.props);
  }

  showTernHint(codeEditor: codemirror.Editor): void {
    this.editor.setOption('extraKeys', {
      'Ctrl-Space': cm => {
        this.server.complete(cm);
      },
      'Ctrl-I': cm => {
        this.server.showType(cm);
      },
    });
    codemirror.default.showHint(codeEditor, this.server.getHint, {
      completeSingle: false,
    });
  }

  getEditorHint(cm: codemirror.Editor): {
    defaultHint: unknown;
    dynamicHints: unknown;
  } {
    const doc = cm.getDoc();
    const POS = doc.getCursor();
    const mode = codemirror.innerMode(cm.getMode(), cm.getTokenAt(POS).state).mode.name;
    this.editor.doc.modeOption = mode;
    let dynamicHints = {
      completeSingle: false,
      alignWithWord: true,
    };
    if (!isEmpty(this.props.customHints)) {
      let customHints = this.props.customHints;
      //Make customHints compatible to the codemirror format if array supplied
      if (_.isArray(this.props.customHints?.tables)) {
        const customHintTable = this.props.customHints?.tables.reduce(function (map, key) {
          map[key] = [];
          return map;
        }, {});
        customHints = {tables: customHintTable as ICustomHints['tables']};
      }
      dynamicHints = {...dynamicHints, ...customHints};
    }

    let defaultHint = null;

    if (mode == 'sql') {
      defaultHint = codemirror.default.hint.sql;
    } else if (mode == 'javascript') {
      if (window.ENABLE_CUSTOM_HINTING) {
        if (this.props.hintFcn && this.props.hintFcn.call) {
          defaultHint = this.props.hintFcn
        } else {
          defaultHint = codemirror.default.hint.javascript;
        }
      } else {
        defaultHint = codemirror.default.hint.javascript;
      }
    }
    return {defaultHint, dynamicHints};
  }

  enableAutoSuggestionUsingTern: () => Promise<void> = async () => {
    // const ternPromise = await tern;
    // window['tern'] = ternPromise;

    const fileFilter = (val, name, doc) => {
      val = val.replaceAll('{{', '[[');
      val = val.replaceAll('}}', ']]');
      return val;
    };

    const server = (this.server = new codemirror.default.TernServer({
      // defs: [lodash, moment],
      defs: this.props.ternDefs || [],
      fileFilter: fileFilter,
    }));

    this.editor.on('cursorActivity', cm => {
      server.updateArgHints(cm);
    });

    codemirror.commands.autocomplete = (cm, defaultHint, dynamicHints) => {
      const doc = cm.getDoc();
      const POS = doc.getCursor();
      const mode = codemirror.innerMode(cm.getMode(), cm.getTokenAt(POS).state).mode.name;

      if (window.ENABLE_CUSTOM_HINTING) {
        codemirror.default.showHint(cm, defaultHint, dynamicHints);
      } else {
        const tillCursorPosition = doc.getRange({line: 0, ch: 0}, POS);
        const isInnerModeActive = tillCursorPosition.match(/\{\{[^}]*$/);

        if (isInnerModeActive) {
          if (mode == 'javascript') {
            this.showTernHint(cm);
          }
        } else {
          codemirror.default.showHint(cm, defaultHint, dynamicHints);
        }
      }
    };
  };

  componentWillUnmount(): void {
    // FIXME: on Unmount release refernces
    if (this.editor) {
      this.editor.toTextArea();
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps: IcodeEditorProps): void {
    if (
      this.editor &&
      nextProps.value !== undefined &&
      nextProps.value !== this.props.value &&
      normalizeLineEndings(this.editor.getValue()) !== normalizeLineEndings(nextProps.value)
    ) {
      if (this.props.preserveScrollPosition) {
        const prevScrollPosition = this.editor.getScrollInfo();
        this.editor.setValue(nextProps.value);
        this.editor.scrollTo(prevScrollPosition.left, prevScrollPosition.top);
      } else {
        this.editor.setValue(nextProps.value);
      }
    }
    if (typeof nextProps.options === 'object') {
      for (const optionName in nextProps.options) {
        if (nextProps.options.hasOwnProperty(optionName)) {
          this.setOptionIfChanged(optionName, nextProps.options[optionName]);
        }
      }
    }
  }

  setOptionIfChanged(optionName: string, newValue: unknown): void {
    const oldValue = this.editor.getOption(optionName);
    if (!isEqual(oldValue, newValue)) {
      this.editor.setOption(optionName, newValue);
    }
  }

  connectEvents(props: ICodeMirrorActions): void {
    Object.keys(props || {})
      .filter(p => /^on/.test(p))
      .forEach(prop => {
        switch (prop) {
          case 'onBlur':
            {
              (this.editor as codemirror.Editor).on('blur', (cm, event) => {
                this.setState({
                  isFocused: false,
                });
                this.props.onBlur(this.editor, event);
              });
            }
            break;
          case 'onContextMenu': {
            this.editor.on('contextmenu', (cm, event) => {
              this.props.onContextMenu(this.editor, event);
            });
            break;
          }
          case 'onCopy': {
            this.editor.on('copy', (cm, event?) => {
              this.props.onCopy(this.editor, event);
            });
            break;
          }
          case 'onCursor':
            {
              this.editor.on('cursorActivity', cm => {
                this.props.onCursor(this.editor, this.editor.getDoc().getCursor());
              });
            }
            break;
          case 'onCursorActivity':
            {
              this.editor.on('cursorActivity', cm => {
                this.props.onCursorActivity(this.editor);
              });
            }
            break;
          case 'onCut': {
            this.editor.on('cut', (cm, event?) => {
              this.props.onCut(this.editor, event);
            });
            break;
          }
          case 'onDblClick': {
            this.editor.on('dblclick', (cm, event) => {
              this.props.onDblClick(this.editor, event);
            });
            break;
          }
          case 'onDragEnter':
            {
              this.editor.on('dragenter', (cm, event) => {
                this.props.onDragEnter(this.editor, event);
              });
            }
            break;
          case 'onDragLeave': {
            this.editor.on('dragleave', (cm, event) => {
              this.props.onDragLeave(this.editor, event);
            });
            break;
          }
          case 'onDragOver':
            {
              this.editor.on('dragover', (cm, event) => {
                this.props.onDragOver(this.editor, event);
              });
            }
            break;
          case 'onDragStart': {
            this.editor.on('dragstart', (cm, event) => {
              this.props.onDragStart(this.editor, event);
            });
            break;
          }
          case 'onDrop':
            {
              this.editor.on('drop', (cm, event) => {
                this.props.onDrop(this.editor, event);
              });
            }
            break;
          case 'onFocus':
            {
              (this.editor as codemirror.Editor).on('focus', (cm, event) => {
                this.setState({
                  isFocused: true,
                });
                this.props.onFocus(this.editor, event);
              });
            }
            break;
          case 'onGutterClick':
            {
              this.editor.on('gutterClick', (cm, lineNumber, gutter, event) => {
                this.props.onGutterClick(this.editor, lineNumber, gutter, event);
              });
            }
            break;
          case 'onInputRead':
            {
              this.editor.on('inputRead', (cm, EditorChangeEvent) => {
                this.props.onInputRead(this.editor, EditorChangeEvent);
              });
            }
            break;
          case 'onKeyDown':
            {
              this.editor.on('keydown', (cm, event) => {
                this.props.onKeyDown(this.editor, event);
              });
            }
            break;
          case 'onKeyHandled':
            {
              this.editor.on('keyHandled', (cm, key, event) => {
                this.props.onKeyHandled(this.editor, key, event);
              });
            }
            break;
          case 'onKeyPress':
            {
              this.editor.on('keypress', (cm, event) => {
                this.props.onKeyPress(this.editor, event);
              });
            }
            break;
          case 'onKeyUp':
            {
              this.editor.on('keyup', (cm, event) => {
                this.props.onKeyUp(this.editor, event);
              });
            }
            break;
          case 'onMouseDown': {
            this.editor.on('mousedown', (cm, event) => {
              this.props.onMouseDown(this.editor, event);
            });
            break;
          }
          case 'onPaste': {
            this.editor.on('paste', (cm, event?) => {
              this.props.onPaste(this.editor, event);
            });
            break;
          }
          case 'onRenderLine': {
            this.editor.on('renderLine', (cm, line, element) => {
              this.props.onRenderLine(this.editor, line, element);
            });
            break;
          }
          case 'onScroll':
            {
              this.editor.on('scroll', cm => {
                this.props.onScroll(this.editor, this.editor.getScrollInfo());
              });
            }
            break;
          case 'onSelection':
            {
              this.editor.on('beforeSelectionChange', (cm, data) => {
                this.props.onSelection(this.editor, data);
              });
            }
            break;
          case 'onTouchStart': {
            this.editor.on('touchstart', (cm, event) => {
              this.props.onTouchStart(this.editor, event);
            });
            break;
          }
          case 'onUpdate':
            {
              this.editor.on('update', cm => {
                this.props.onUpdate(this.editor);
              });
            }
            break;
          case 'onViewportChange':
            {
              this.editor.on('viewportChange', (cm, from, to) => {
                this.props.onViewportChange(this.editor, from, to);
              });
            }
            break;
        }
      });
  }

  render(): React.ReactNode {
    return (
      <View style={this.state.isFocused ? styles.codeEditorFocused : styles.codeEditor}>
        <textarea
          ref={ref => (this.textareaNode = ref)}
          name={this.props.name || this.props.path}
          defaultValue={this.props.value}
          autoComplete="off"
          autoFocus={this.props.autoFocus}
          placeholder={this.props.placeholder ? this.props.placeholder : ''}
        />
      </View>
    );
  }
}

export default CodeEditor;

const styles = StyleSheet.create({
  codeEditorFocused: {
    flex: 1,
    flexDirection: 'row',
    // padding: 5,
  },
  codeEditor: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
