import {isJSBinding, selectAppConfig} from 'apptile-core';
import React, {useCallback, forwardRef, useState} from 'react';
import {StyleSheet, Text, View, Button} from 'react-native';
import {connect, useSelector} from 'react-redux';
import {getModelJSForPlugin, JSModel} from 'apptile-core';
import {resolvePluginDocs} from 'apptile-core';
import {selectEditorState} from 'apptile-core';
import {selectAppModel} from 'apptile-core';
import Tooltip from '../../components-v2/base/Tooltip/Index';
import commonStyles from '../../styles-v2/commonStyles';
import theme from '../../styles-v2/theme';
import CodeEditor, {ICustomHints} from '../codeEditor/codeEditor';
import {EditorModes} from '../codeEditor/config/EditorModes';
import apptileGlobals from '../codeEditor/defs/apptileGlobals.json';
import lodash from '../codeEditor/defs/lodash.json';
import _ from 'lodash'
import moment from 'moment';

type CodeInputProps = {
  value?: unknown;
  defaultValue?: unknown;
  placeholder?: unknown;
  onChange: (editor: unknown, data: unknown, value: string) => void;
  customHints?: ICustomHints;
  propertyName?: string;
  singleLine?: boolean;
  noOfLines?: number;
};

function createCircularReplacer() {
  // const keys = [];
  const ancestors = [];
  return function(key, value) {
    if (typeof value !== 'object' || value === null) {
      return value;
    }
    
    while (ancestors.length > 0 && ancestors.at(-1) !== this) {
      ancestors.pop();
      // keys.pop();
    }

    if (ancestors.includes(this)) {
      // console.log("Circlular object: ", keys, ancestors);
      return "[Circular]";
    }
    
    ancestors.push(value);
    // keys.push(key);
    return value;
  }
}

const CodeInput = forwardRef<{isFocused: () => boolean}, CodeInputProps>(({
  value,
  defaultValue,
  placeholder,
  propertyName,
  onChange,
  customHints,
  singleLine,
  noOfLines = singleLine ? 1 : 0,
}, ref) => {
  const editorState = useSelector(selectEditorState);
  const appModel = useSelector(selectAppModel);
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const isDynamicString = isJSBinding(value);

  const dependencyGraph = appModel.dependencyGraph;

  const modelDocs = {'!name': 'jsModel'};
  const currentPluginSel = editorState.selectedPluginConfigSel;
  let compiledBindingFn = null;
  const sel = editorState.selectedPluginConfigSel?.slice() ?? [];
  const pageKey = sel[0];

  if (currentPluginSel) {
    const scratchMemory: JSModel = {
        $global: {},
        unresolved: true,
        $context: {},
        hasCurrentPage: false,
        currentPage: {},
        hasIndex: false,
        i: 0
      };
    const jsModel = getModelJSForPlugin(appModel, currentPluginSel, scratchMemory);
    Object.entries(jsModel || {}).map(([key, value]) => {
      const pluginType = _.get(value, 'pluginType') || _.get(value, '0.pluginType');
      const doc = resolvePluginDocs(pluginType);
      if (doc) Object.assign(modelDocs, {[key]: doc});
    });

    
    const pageId = appModel.pageKeysToId?.get(sel[0]);
    sel[0] = pageId;
    const node = dependencyGraph?.depGraph?.nodes?.get(sel.join('.')) ?? undefined;
    let bindingKey = value;
    if (node?.namespace) {
      bindingKey = node.namespace + '::' + value;
    }
    compiledBindingFn = dependencyGraph?.compiledBindingFns?.get(bindingKey) ?? null;
  }
  
  const storeGlobal = () => {
    const __binding_debug = {
      compiledBindingFn,
      $pageContext: appModel.jsModel[pageKey]?.plugins,
      $appJSModel: appModel.jsModel,
      currentPage: appModel.jsModel[pageKey],
      _,
      moment,
    }

    window.__binding_debug = __binding_debug;
    console.log("Binding:\n " + value);
    console.log("Transpiled function:\n " + __binding_debug.compiledBindingFn.transpiled);
    console.log(`__binding_debug.compiledBindingFn.compiled(__binding_debug.$pageContext, __binding_debug.$appJSModel, 0, __binding_debug.currentPage, __binding_debug._, __binding_debug.moment)`);
  }
  
  let hintFcn = useCallback((editor, options) => {
    const cursor = editor.getCursor();
    const token = editor.getTokenAt(cursor);
    const dependencyGraph = appModel.dependencyGraph;
    const sel = editorState.selectedPluginConfigSel?.slice() ?? [];
    const pageId = appModel.pageKeysToId?.get(sel[0]);
    sel[0] = pageId;
    const node = dependencyGraph?.depGraph?.nodes?.get(sel.join('.')) ?? undefined;
    let candidates;
    let plugins = appModel.jsModel[pageKey]?.plugins ?? {};
    if (node && node.namespace) {
      // const moduleUUID = plugins[node.namespace]?.moduleUUID;
      // candidates = appConfig.getIn(['modules', moduleUUID, 'inputs']) || [];
      // candidates = candidates.map(it => ({ text: it, displayText: "-> " + it}));
      candidates = [];
      const namespacePrefix = node.namespace + '::';
      candidates = candidates.concat(
        Object.keys(plugins)
          .filter(it => it.startsWith(namespacePrefix))
          .map(it => it.substring(namespacePrefix.length))
      )
    } else {
      candidates = appModel.dependencyGraph.globalScopeIdentifiers;      
      candidates = candidates.concat('currentPage', '_');
      const pluginNames = Object.keys(plugins).filter(it => !it.includes("::"));
      candidates = candidates.concat(pluginNames);
    }
    
    let insertStartLocation = editor.posFromIndex(token.start);
    let insertEndLocation = editor.posFromIndex(token.end);
    if (token.type == 'variable') {
      candidates = candidates.filter(it => it.startsWith(token.string))
    } else if (token.type == null && token.string == '{{') {
      insertStartLocation = editor.posFromIndex(token.end);
    } else if ((token.type == null && token.string == ".") || token.type == 'property') {
      const context = [];
      if (token.string === ".") {
        insertStartLocation = editor.posFromIndex(token.end);
      }

      let tok = token;
      
      if (token.string === "." && editor.getTokenAt(editor.posFromIndex(tok.start)).string === "]") {
        // get the close bracket token
        tok = editor.getTokenAt(editor.posFromIndex(tok.start));
        // get the index token
        tok = editor.getTokenAt(editor.posFromIndex(tok.start));
        const indexToken = tok;
        // get the open bracket token
        tok = editor.getTokenAt(editor.posFromIndex(tok.start));
        // get the property before the open bracket
        tok = editor.getTokenAt(editor.posFromIndex(tok.start));
        context.push(tok.string);
        while(tok.type == 'property' || tok.string == ".") {
          let prevToken = editor.getTokenAt(editor.posFromIndex(tok.start));
          if (prevToken.string === '.') {
            prevToken = editor.getTokenAt(editor.posFromIndex(prevToken.start));
          }

          if (prevToken.type == "property" || prevToken.type == "variable") {
            context.unshift(prevToken.string);
            tok = prevToken;
          } else {
            break;
          }
        }     
        
        if (node?.namespace) {
          const variable = context.shift()
          context.unshift(node.namespace + '::' + variable);
        }
        
        let contextArray = _.get(appModel.jsModel, context);

        if (!contextArray) {
          contextArray = _.get(plugins, context);        
        }
        
        let itemIndex = 0;
        if (indexToken.type === "number") {
          itemIndex = parseInt(indexToken.string);
        }

        if (!Array.isArray(contextArray)) {
          candidates = [];
        } else if (typeof contextArray[itemIndex] === "object" && contextArray[itemIndex]) {
          candidates = Object.keys(contextArray[0]);
        } else {
          candidates = []
        } 
      } else {
        while(tok.type == 'property' || tok.string == ".") {
          let prevToken = editor.getTokenAt(editor.posFromIndex(tok.start));
          if (prevToken.string === '.') {
            prevToken = editor.getTokenAt(editor.posFromIndex(prevToken.start));
          }

          if (prevToken.type == "property" || prevToken.type == "variable") {
            context.unshift(prevToken.string);
            tok = prevToken;
          } else {
            break;
          }
        }
        
        if (node?.namespace) {
          const variable = context.shift();
          context.unshift(node.namespace + '::' + variable);
        }
        
        let contextObj = _.get(appModel.jsModel, context);
        if (!contextObj) {
          contextObj = _.get(plugins, context);        
        }

        if (Array.isArray(contextObj)) {
          candidates = ["length"];
        } else {
          candidates = Object.keys(contextObj)
          if (token.type == "property") {
            candidates = candidates.filter(it => it.startsWith(token.string));        
          }
        }       
      }
    } else if (token.type === null && (token.string === "[" || token.string === "]")) {
      candidates = [];
    } 

    return {
      list: candidates,
      from: insertStartLocation,
      to: insertEndLocation
    }
  }, [appModel, pageKey, editorState]);
  if (!window.ENABLE_CUSTOM_HINTING) {
    hintFcn = null;  
  }

  let result = 'Evaluation not done';

   if (compiledBindingFn) {
    try {
      result = compiledBindingFn.compiled(
        appModel.jsModel[pageKey].plugins, 
        appModel.jsModel,
        0,
        appModel.jsModel[pageKey],
        _,
        moment
      );
      if (typeof result === 'function') {
        result = result();
      }
      result = JSON.stringify(result, createCircularReplacer(), 1);
    } catch (err) {
      console.error("Failed during evaluation: ", err);
    }
  } else {
    result = "Binding not found";
  }   

  return (
    <View style={{flex: 1}}>
      <CodeEditor
        {...{
          onChange: onChange,
          defaultValue: defaultValue ? `${defaultValue}` : value ? `${value}` : '',
          // value: value ? `${value}` : '',
          options: {
            lineNumbers: false,
            mode: {name: EditorModes.TEXT_WITH_BINDING},
            autoCloseBrackets: true,
            matchBrackets: true,
            autohint: true,
            highlightSelectionMatches: true,
            extraKeys: {'Ctrl-Space': 'autocomplete'},
            viewportMargin: Infinity,
            lineWrapping: !singleLine,
            autoRefresh: true,
          },
          editorSize: [
            '100%',
            noOfLines > 0 ? `${(theme.LINE_HEIGHT + (noOfLines == 1 ? 5 : 3)) * noOfLines + 16}px` : 'auto',
          ],
          placeholder: placeholder ? `${placeholder}` : '',
          ternDefs: [lodash, apptileGlobals, modelDocs],
          customHints: customHints ? customHints : null,
          onFocus: () => setPreviewVisible(true),
          hintFcn: hintFcn,
          forwardedRef: ref,
        }}
      />
      {previewVisible && isDynamicString && (
        <View style={styles.previewValue}>
          <Button title="log" onPress={storeGlobal}></Button>
          <Button title="close" onPress={() => setPreviewVisible(false)}></Button>
          <Text>Result: {result}</Text>
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  previewValue: {
    overflow: 'scroll',
    borderTopColor: '#cee1da',
    borderTopWidth: 0.2,
    paddingTop: 5,
    paddingHorizontal: 6,
    maxWidth: 200,
    maxHeight: 300
  },
  previewText: {
    color: theme.PRIMARY_COLOR,
    fontSize: 10,
    fontWeight: '200',
    fontFamily: 'monospace',
    marginTop: 4,
  },
});

export default CodeInput;
