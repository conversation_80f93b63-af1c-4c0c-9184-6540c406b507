import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Text, TextInput, TouchableOpacity, View} from 'react-native';
import {deleteModuleOutput, renameModuleOutput, updateModuleOutput} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import CodeInput from '../../codeEditor/codeInput';
import {PIModuleEditorProps} from './moduleEditor';

import {styles} from './moduleEditorStyles';
export interface ModuleOutputItemProps extends PIModuleEditorProps {
  moduleUUID: string;
  propertyName: string;
  propertyValue: string;
  onUpdateValue: typeof updateModuleOutput;
  onDeleteOutput: typeof deleteModuleOutput;
  onRenameOutput: typeof renameModuleOutput;
}
const ModuleOutputItem: React.FC<ModuleOutputItemProps> = props => {
  const [isRenaming, setRenaming] = useState(false);
  const {moduleUUID, propertyName, propertyValue, onUpdateValue, onDeleteOutput, onRenameOutput} = props;
  const onValueChange = useCallback(
    value => {
      onUpdateValue(moduleUUID, propertyName, value);
    },
    [onUpdateValue, moduleUUID, propertyName],
  );
  const onDelete = useCallback(() => {
    onDeleteOutput(moduleUUID, propertyName);
  }, [onDeleteOutput, moduleUUID, propertyName]);

  const setEditingName = useCallback(() => {
    setRenaming(true);
  }, [setRenaming]);
  const inputRef = useRef<TextInput>(null);
  useEffect(() => {
    inputRef.current?.focus();
  }, [isRenaming]);

  const onRename = useCallback(
    e => {
      onRenameOutput(moduleUUID, propertyName, e.target.value);
    },
    [onRenameOutput, moduleUUID, propertyName],
  );

  return (
    <View style={styles.itemContainer}>
      <View style={styles.rowContainer}>
        {isRenaming ? (
          <TextInput
            onBlur={() => setRenaming(false)}
            ref={inputRef}
            style={styles.nameInput}
            defaultValue={propertyName}
            blurOnSubmit={true}
            onSubmitEditing={onRename}
          />
        ) : (
          <>
            <Text style={styles.labelText}>{propertyName}</Text>
            <TouchableOpacity onPress={setEditingName}>
              <MaterialCommunityIcons name="pencil" size={18} />
            </TouchableOpacity>
          </>
        )}
        <TouchableOpacity onPress={onDelete} style={[styles.deleteIcon]}>
          <MaterialCommunityIcons name="delete-circle-outline" size={18} />
        </TouchableOpacity>
      </View>
      <CodeInput
        defaultValue={propertyValue}
        placeholder="{{myWidget1.value}}"
        onChange={(editor: unknown, data: unknown, value: string) => onValueChange(value)}
      />
    </View>
  );
};

export {ModuleOutputItem};
