import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Text, TextInput, TouchableOpacity, View} from 'react-native';
import {deleteModuleProperty, renameModuleProperty} from 'apptile-core';
import {ModulePropertyType} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import {EditorProps} from '../../../../app/common/EditorControlTypes';
import CodeInput from '../../codeEditor/codeInput';
import {PIModuleEditorProps} from './moduleEditor';
import {styles} from './moduleEditorStyles';

export interface ModulePropertyItemProps extends PIModuleEditorProps {
  moduleUUID: string;
  propertyName: string;
  propertyType: ModulePropertyType;
  propertyValue: string;
  onUpdateValue: EditorProps['pluginConfigUpdate'];
  onDeleteProperty: typeof deleteModuleProperty;
  onRenameProperty: typeof renameModuleProperty;
}
const ModulePropertyItem: React.FC<ModulePropertyItemProps> = props => {
  const [isRenaming, setRenaming] = useState(false);
  const {
    pluginId,
    pageId,
    moduleUUID,
    propertyName,
    propertyType,
    propertyValue,
    onUpdateValue,
    onDeleteProperty,
    onRenameProperty,
  } = props;

  const onValueChange = useCallback(
    value => {
      onUpdateValue(pluginId, pageId, {[propertyName]: value});
    },
    [onUpdateValue, pluginId, pageId, propertyName],
  );
  const onDelete = useCallback(() => {
    onDeleteProperty(moduleUUID, propertyName, propertyType);
  }, [onDeleteProperty, moduleUUID, propertyType, propertyName]);

  const setEditingName = useCallback(() => {
    setRenaming(true);
  }, [setRenaming]);
  const inputRef = useRef<TextInput>(null);
  useEffect(() => {
    inputRef.current?.focus();
  }, [isRenaming]);

  const onRename = useCallback(
    e => {
      onRenameProperty(moduleUUID, propertyType, propertyName, e.target.value);
      logger.info('Renaming Property to ', e.target.value);
    },
    [onRenameProperty, moduleUUID, propertyType, propertyName],
  );

  return (
    <View style={styles.itemContainer}>
      <View style={styles.rowContainer}>
        {isRenaming ? (
          <TextInput
            onBlur={() => setRenaming(false)}
            ref={inputRef}
            style={styles.nameInput}
            defaultValue={propertyName}
            blurOnSubmit={true}
            onSubmitEditing={onRename}
          />
        ) : (
          <>
            <Text style={styles.labelText}>{propertyName}</Text>
            <TouchableOpacity onPress={setEditingName}>
              <MaterialCommunityIcons name="pencil" size={18} />
            </TouchableOpacity>
          </>
        )}
        <TouchableOpacity onPress={onDelete} style={[styles.deleteIcon]}>
          <MaterialCommunityIcons name="delete-circle-outline" size={18} />
        </TouchableOpacity>
      </View>
      <CodeInput
        defaultValue={propertyValue}
        placeholder="{{myWidget1.value}}"
        onChange={(editor: unknown, data: unknown, value: string) => onValueChange(value)}
      />
    </View>
  );
};

export {ModulePropertyItem};
