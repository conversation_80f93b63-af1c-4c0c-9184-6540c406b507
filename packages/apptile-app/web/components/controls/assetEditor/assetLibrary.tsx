import {EditorRootState} from '@/root/web/store/EditorRootState';
import theme from '@/root/web/styles-v2/theme';
import {default as React, useEffect, useState} from 'react';
import {ActivityIndicator, FlatList, StyleSheet, Text, View} from 'react-native';
import {useSelector} from 'react-redux';
import {apptileState} from '../../../../app/store/ApptileReducer';
import {fetchAppAssets, fetchAppAssetsLoadMore} from '../../../actions/editorActions';
import AssetCard from './assetCard';

interface IAssetLibrary {
  apptileState: apptileState;
  updateSelectedAssetId: (assetId: string, url?: string) => void;
  fetchAppAssets: typeof fetchAppAssets;
  fetchAppAssetsLoadMore: typeof fetchAppAssetsLoadMore;
  previouslySelectedAssetId: string;
  onNewAsset: () => void;
}

const AssetLibrary: React.FC<IAssetLibrary> = props => {
  const {
    apptileState,
    onNewAsset,
    fetchAppAssets,
    updateSelectedAssetId,
    fetchAppAssetsLoadMore,
    previouslySelectedAssetId,
  } = props;
  const assetUploader = useSelector((state: EditorRootState) => state.asset.assetUploader);
  const assetIds = useSelector((state: EditorRootState) => state.asset.assetIds);

  const [selectedAsset, setSelectedAsset] = useState<string>('');

  const appId = apptileState.appId as string;

  const {isFetching, isEndOfCatalogue, currentPage} = assetUploader;

  function handleOnEndReached() {
    if (!isEndOfCatalogue) {
      fetchAppAssetsLoadMore(appId, currentPage + 1, 15);
    }
  }

  useEffect(() => {
    fetchAppAssets(appId, 1, 15);
  }, [fetchAppAssets, appId]);

  const onSelectAsset = (assetId: string, url?: string) => {
    if (assetId == 'new-asset') {
      onNewAsset();
    } else {
      setSelectedAsset(assetId);
      updateSelectedAssetId(assetId, url);
    }
  };

  const RenderItem = ({item, index}: any) => {
    return <AssetCard {...{selectedAsset, assetId: item, onSelectAsset, previouslySelectedAssetId}} index={index} />;
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={assetIds}
        renderItem={RenderItem}
        numColumns={5}
        keyExtractor={(item, index) => String(index)}
        ListFooterComponent={() => {
          if (isFetching) {
            return <ActivityIndicator />;
          } else {
            return null;
          }
        }}
        horizontal={false}
        onEndReached={handleOnEndReached}
        onEndReachedThreshold={0}
        contentContainerStyle={styles.list}
        columnWrapperStyle={styles.column}
      />
    </View>
  );
};

export default AssetLibrary;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  list: {
    flex: 1,
    justifyContent: 'flex-start',
    gap: 24,
  },
  column: {
    flexShrink: 1,
    justifyContent: 'flex-start',
    gap: 24,
  },
  listItemWrapper: {
    width: '22%',
    borderWidth: 2,
    borderRadius: 8,
    borderColor: '#ccc',
    overflow: 'hidden',
    margin: 10,
    padding: 4,
  },
  listItemSelected: {
    borderWidth: 2,
    borderColor: '#2196f3',
  },
  listItemImage: {
    width: '100%',
    minHeight: 150,
  },
  headingContainer: {
    flexDirection: 'row',
  },
  heading: {
    flex: 1,
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '700',
    fontSize: 15,
    lineHeight: 17,
    marginTop: 8,
    marginBottom: 16,
  },
});
