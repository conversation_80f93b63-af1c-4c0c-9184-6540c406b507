import {EditorRootState} from '@/root/web/store/EditorRootState';
import theme from '@/root/web/styles-v2/theme';
import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, Text, View, Image} from 'react-native';
import Button from '../../../components-v2/base/Button';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {connect, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {EditorProps} from '../../../../app/common/EditorControlTypes';
import {datasourceTypeModelSel, MaterialCommunityIcons} from 'apptile-core';
import {AppDispatch} from '../../../../app/store';
import {apptileState} from '../../../../app/store/ApptileReducer';
import {fetchAppAssets, fetchAppAssetsLoadMore, saveImageRecord} from '../../../actions/editorActions';
import {AssetReduxState} from '../../../store/AssetReducer';
import AssetLibrary from './assetLibrary';
import AssetUploader from './assetUploader';
import Modal from '@/root/web/components-v2/base/Modal';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import _ from 'lodash';
import AutoFetchLibrary from './autoFetchLibrary';

export interface PIAssetEditorProps<ConfigType = 'assetEditor'> extends EditorProps {
  showDialog: boolean;
  aspectRatio?: number;
  currentAssetId: string;
  currentURL?: string;
  apptileState: apptileState;
  assetState: AssetReduxState;
  fetchAppAssets: typeof fetchAppAssets;
  fetchAppAssetsLoadMore: typeof fetchAppAssetsLoadMore;
  saveImageRecord: typeof saveImageRecord;
  onCloseDialog: (show: boolean) => void;
  onSelectAsset: (assetId: string, url?: string) => void;
  onChangeURL?: (url: string) => void;
  bulk?: boolean;
  label?: string;
  askURL?: boolean;
}

const AssetChooseDialog: React.FC<PIAssetEditorProps> = props => {
  const {
    showDialog,
    currentAssetId,
    label,
    saveImageRecord,
    onCloseDialog,
    onSelectAsset,
    apptileState,
    fetchAppAssets,
    askURL = true,
    bulk = false,
  } = props;

  const downloadImageFromUrl = async imageUrl => {
    const isValidImageUrl = url => {
      return /^https?:\/\/.*\.(jpg|jpeg|png|gif|bmp|webp|svg)(\?.*)?$/i.test(url);
    };

    const extractFileName = url => {
      try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        const name = pathname.substring(pathname.lastIndexOf('/') + 1);
        return name || 'downloaded-image';
      } catch {
        return 'downloaded-image';
      }
    };

    if (!imageUrl || !isValidImageUrl(imageUrl)) {
      throw new Error('Invalid image URL. Must be a direct link to an image file.');
    }

    try {
      const response = await fetch(imageUrl, {mode: 'cors'});

      if (!response.ok) {
        throw new Error(`Failed to fetch image. Status: ${response.status}`);
      }

      const contentType = response.headers.get('Content-Type');
      if (!contentType || !contentType.startsWith('image')) {
        throw new Error('URL does not point to a valid image.');
      }

      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = extractFileName(imageUrl);
      const file = new File([blob], extractFileName(imageUrl), {type: contentType});
      setFiles(file);
      setShowUploader(true);

      // uploadAppAsset({file});
    } catch (error) {
      throw new Error(`Image download failed: ${error.message}`);
    }
  };

  const uploaderRef = useRef<any>(null);
  const [showUploader, setShowUploader] = useState(true);
  const [disableButtons, setDisableButtons] = useState(false);
  const [selectedAssetId, setSelectedAssetId] = useState(currentAssetId);
  const [headerText, setHeaderText] = useState('Image Library');
  const [currentURL, setCurrentURL] = useState(props.currentURL);
  const [showCrop, setShowCrop] = useState(false);
  const assetState = useSelector((state: EditorRootState) => state.asset);
  const appId = apptileState.appId as string;
  const [cropPreview, setCropPreview] = useState(false);
  const [showAutoFetched, setShowAutoFetched] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [selectedImageUrl, setSelectedImageUrl] = useState('');

  //state for showing selected image from upload-image button first
  const [showOriginal, setShowOriginal] = useState(false);

  //Fetching the current assets
  useEffect(() => {
    fetchAppAssets(appId, 1, 15);
  }, [appId, fetchAppAssets]);

  useEffect(() => {
    setHeaderText('Image Library');
  }, [setHeaderText]);

  const {assetIds} = assetState;

  // Use of the below state is just to make assetCrop component set the cropped image in the assetUploader component. The actual uploading is done in the assetUploader component itself.
  const [startCropUpload, setStartCropUpload] = useState(false);
  const [skipCrop, setSkipCrop] = useState(false);

  const startUpload = () => {
    uploaderRef?.current();
  };

  useEffect(() => {
    setShowCrop(false);
    setStartCropUpload(false);
  }, [setShowCrop, setStartCropUpload]);

  const updateSelectedAssetId = (assetId: string, url?: string) => {
    setCurrentURL(url ?? props.currentURL);
    setSelectedAssetId(assetId);
  };
  const updateSelectedImage = (url: string) => {
    setSelectedImageUrl(url);
  };

  const onInsertAsset = () => {
    onSelectAsset(selectedAssetId, currentURL);
    saveImageRecord(selectedAssetId);
    onCloseDialog(false);
  };

  const handleBack = () => {
    if (showOriginal) {
      setShowCrop(false);
      setShowOriginal(false);
      setHeaderText('Upload Image');
    } else {
      setShowOriginal(true);
      setShowCrop(true);
      setHeaderText('Upload Image');
    }
  };

  return (
    <Modal
      onVisibleChange={(visible: boolean) => {
        onCloseDialog(visible);
      }}
      visible={showDialog}
      disableOutsideClick={true}
      content={
        <View style={[theme.modal, styles.dialogStyle]}>
          <View style={styles.modalTitleBar}>
            <Text style={[commonStyles.heading, styles.modalTitle]}>{headerText}</Text>
            <TouchableOpacity onPress={() => onCloseDialog(false)} style={[styles.closeButton]}>
              <MaterialCommunityIcons name="close" size={16} />
            </TouchableOpacity>
          </View>
          {showDialog && (
            <View style={styles.dialogBody}>
              {!showUploader && !showAutoFetched && (
                <AssetLibrary
                  previouslySelectedAssetId={currentAssetId}
                  {...props}
                  updateSelectedAssetId={updateSelectedAssetId}
                  onNewAsset={() => {
                    setShowUploader(true);
                  }}
                />
              )}
              {!showUploader && showAutoFetched && (
                <AutoFetchLibrary
                  updateSelectedImage={updateSelectedImage}
                  onNewAsset={() => {
                    setShowUploader(true);
                  }}
                />
              )}
              {showUploader && (
                <AssetUploader
                  {...props}
                  uploaderRef={uploaderRef}
                  closeUploader={() => {
                    setShowUploader(false);
                    setShowAutoFetched(false);
                  }}
                  startUpload={startUpload}
                  currentURL={currentURL}
                  onChangeURL={setCurrentURL}
                  bulk={bulk}
                  skipCrop={skipCrop}
                  setSkipCrop={setSkipCrop}
                  changeHeader={setHeaderText}
                  disableButtons={setDisableButtons}
                  setShowCrop={setShowCrop}
                  showCrop={showCrop}
                  askURL={askURL}
                  cropPreview={cropPreview}
                  startCropUpload={startCropUpload}
                  setStartCropUpload={setStartCropUpload}
                  setCropPreview={setCropPreview}
                  showOriginal={showOriginal}
                  setShowOriginal={setShowOriginal}
                  ifile={files}
                  deleteIFile={() => {
                    setFiles([]);
                  }}
                />
              )}
            </View>
          )}

          <View style={styles.dialogFooter}>
            {!cropPreview && !showCrop && showUploader && !_.isEmpty(assetIds) ? (
              <View style={{display: 'flex', flexDirection: 'row', gap: 10}}>
                <Button
                  variant="PILL"
                  disabled={disableButtons}
                  containerStyles={styles.commonButtonStyles}
                  color="SECONDARY"
                  size="MEDIUM"
                  icon="file-image-outline"
                  onPress={() => {
                    setShowUploader(false);
                    setShowAutoFetched(false);
                    setHeaderText('Image Library');
                  }}>
                  Go To Library
                </Button>

                <Button
                  variant="PILL"
                  disabled={disableButtons}
                  containerStyles={styles.commonButtonStyles}
                  color="SECONDARY"
                  size="MEDIUM"
                  icon="file-image-outline"
                  onPress={() => {
                    setShowUploader(false);
                    setShowAutoFetched(true);
                    setHeaderText('Images from Websites');
                  }}>
                  Images from Website
                </Button>
              </View>
            ) : (
              <></>
            )}

            {!cropPreview && !showCrop && !showUploader && !_.isEmpty(assetIds) && showAutoFetched ? (
              <View style={{display: 'flex', flexDirection: 'row', gap: 10}}>
                <Button
                  variant="PILL"
                  color="SECONDARY"
                  size="MEDIUM"
                  onPress={() => {
                    setShowUploader(true);
                    setHeaderText('Upload Image');
                  }}>
                  + Upload
                </Button>
                <Button
                  variant="PILL"
                  disabled={disableButtons}
                  containerStyles={styles.commonButtonStyles}
                  color="SECONDARY"
                  size="MEDIUM"
                  icon="file-image-outline"
                  onPress={() => {
                    setShowUploader(false);
                    setShowAutoFetched(false);
                    setHeaderText('Image Library');
                  }}>
                  Go To Library
                </Button>
              </View>
            ) : (
              <></>
            )}

            {!cropPreview && !showCrop && !showUploader && !_.isEmpty(assetIds) && !showAutoFetched ? (
              <View style={{display: 'flex', flexDirection: 'row', gap: 10}}>
                <Button
                  variant="PILL"
                  color="SECONDARY"
                  size="MEDIUM"
                  onPress={() => {
                    setShowUploader(true);
                    setHeaderText('Upload Image');
                  }}>
                  + Upload
                </Button>
                <Button
                  variant="PILL"
                  disabled={disableButtons}
                  containerStyles={styles.commonButtonStyles}
                  color="SECONDARY"
                  size="MEDIUM"
                  icon="file-image-outline"
                  onPress={() => {
                    setShowUploader(false);
                    setShowAutoFetched(true);
                    setHeaderText('Images from Websites');
                  }}>
                  Images from Website
                </Button>
              </View>
            ) : (
              <></>
            )}
            {/* {currentURL && currentURL != props.currentURL && (
              <Button
                variant="FILLED-PILL"
                size="LARGE"
                color="PRIMARY"
                disabled={!currentURL || disableButtons}
                onPress={() => {
                  props.onChangeURL(currentURL);
                }}>
                Select Url
              </Button>
            )} */}
            {!showUploader &&
              (!showAutoFetched ? (
                <Button
                  size="LARGE"
                  containerStyles={styles.commonButtonStyles}
                  disabled={!selectedAssetId || disableButtons}
                  onPress={() => {
                    onInsertAsset();
                  }}>
                  Select Image
                </Button>
              ) : (
                <Button
                  size="LARGE"
                  containerStyles={styles.commonButtonStyles}
                  disabled={showAutoFetched && selectedImageUrl === ''}
                  onPress={() => {
                    downloadImageFromUrl(selectedImageUrl);
                  }}>
                  Add to Library
                </Button>
              ))}
            {showCrop && !cropPreview && (
              <>
                <Button
                  variant="PILL"
                  disabled={disableButtons}
                  color="SECONDARY"
                  icon="arrow-left"
                  size="MEDIUM"
                  onPress={() =>
                    //   {
                    //   setShowCrop(false);
                    //   setHeaderText('Upload Image');
                    // }
                    handleBack()
                  }>
                  Back
                </Button>

                {showOriginal ? (
                  <View style={{flexDirection: 'row', gap: 10}}>
                    <Button
                      size="MEDIUM"
                      color="SECONDARY"
                      disabled={disableButtons}
                      onPress={() => {
                        setShowOriginal(false);
                        setHeaderText('Crop & Adjust');
                      }}
                      icon="crop">
                      <Text style={styles.cropButton}>Crop</Text>
                    </Button>
                    <Button
                      id="UploadImageButton"
                      onPress={() => {
                        setSkipCrop(true);
                      }}
                      style={styles.uploadButton}>
                      <Text style={styles.buttonText}>Upload</Text>
                    </Button>
                  </View>
                ) : (
                  <TouchableOpacity
                    disabled={disableButtons}
                    onPress={() => {
                      setCropPreview(true);
                      setShowCrop(true);
                      setHeaderText('Upload Cropped Image');
                    }}
                    style={styles.uploadButton}>
                    <Text style={styles.buttonText}>Apply</Text>
                  </TouchableOpacity>
                )}
              </>
            )}
            {cropPreview && (
              <>
                <Button
                  variant="PILL"
                  disabled={disableButtons}
                  color="SECONDARY"
                  size="MEDIUM"
                  onPress={() => {
                    setCropPreview(false);
                    setShowCrop(true);
                    setHeaderText('Upload Image');
                  }}>
                  Undo
                </Button>

                <Button
                  size="LARGE"
                  disabled={disableButtons}
                  onPress={() => {
                    setStartCropUpload(true);
                    //setShowCrop(false);
                  }}>
                  Upload to library
                </Button>
              </>
            )}
          </View>
        </View>
      }
    />
  );
};

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return {
    ...bindActionCreators(
      {
        fetchAppAssets,
        fetchAppAssetsLoadMore,
        saveImageRecord,
      },
      dispatch,
    ),
  };
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    apptileState: state.apptile,
    assetState: state.asset,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(AssetChooseDialog);

const styles = StyleSheet.create({
  modalTitleBar: {
    flexShrink: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  commonButtonStyles: {},
  modalTitle: {
    fontSize: 24,
    fontWeight: theme.FONT_WEIGHT_BOLD,
    lineHeight: 28,
  },
  cropButtonsContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  dialogStyle: {
    flex: 1,
    flexDirection: 'column',
    width: 720,
    minWidth: 500,
    height: 435,
    minHeight: 400,
    backgroundColor: '#fff',
    padding: 32,
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  closeButton: {
    alignSelf: 'flex-end',
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
    marginLeft: 'auto',
  },
  dialogBody: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'column',
    backgroundColor: '#fff',
    height: 324,
    overflow: 'scroll',
  },
  dialogFooter: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    minHeight: 32,
  },
  tabWrapper: {flex: 1, flexDirection: 'row'},
  tabTitle: {
    padding: 10,
  },
  activeTabTitle: {
    borderBottomColor: '#2196f3',
    borderBottomWidth: 2,
  },
  codeInputStyle: {
    flex: 4,
    borderRightWidth: 1,
    borderRadius: 5,
    borderBottomRightRadius: 0,
    borderTopRightRadius: 0,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
  container: {
    padding: 8,
    flex: 1,
    flexDirection: 'row',
  },
  uploadbtn: {
    padding: 6,
    backgroundColor: '#2196f3',
    color: '#fff',
    flex: 1,
  },
  uploadButton: {
    elevation: 8,
    backgroundColor: 'black',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 30,
  },
  buttonText: {
    color: 'white',
  },
  cropButton: {
    fontWeight: '500',
  },
});
