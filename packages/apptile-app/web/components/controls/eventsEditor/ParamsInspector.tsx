import Immutable from 'immutable';
import {debounce} from 'lodash';
import React, {FC, useCallback, useEffect, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import CodeEditor from '../../codeEditor/codeEditor';
import CodeInput from '../../codeEditor/codeInput';
import {EditorModes} from '../../codeEditor/config/EditorModes';

type ParamsInspectorProps = {
  onParamsChange: (value: any) => void;
  params: Immutable.Map<string, string>;
};

const ParamsInspector: FC<ParamsInspectorProps> = ({params, onParamsChange}) => {
  const [parameters, setParameters] = useState(params);
  useEffect(() => {
    setParameters(params);
  }, [params]);
  const onDelete = useCallback(
    name => {
      const newParameters = parameters.delete(name);
      setParameters(newParameters);
      onParamsChange(newParameters);
    },
    [parameters, onParamsChange],
  );

  const onValueChange = useCallback(
    (paramName, newVal) => {
      let updatedValues = parameters.set(paramName, newVal);
      setParameters(updatedValues);
      onParamsChange(updatedValues);
    },
    [onParamsChange, parameters],
  );
  const debouncedOnValueChange = debounce((paramName, newVal) => onValueChange(paramName, newVal), 450);

  const onNameChange = useCallback(
    (paramName, newVal) => {
      let updatedValues = parameters;
      updatedValues = updatedValues.mapKeys(key => {
        if (key === paramName) return newVal;
        return key;
      });
      setParameters(updatedValues);
      onParamsChange(updatedValues);
    },
    [onParamsChange, parameters],
  );
  const debouncedOnNameChange = debounce((paramName, newVal) => onNameChange(paramName, newVal), 450);

  const onAddParam = useCallback(() => {
    let i = 1;
    let paramName = `Param${i}`;
    const paramNames = parameters.keySeq().toArray();
    while (true) {
      if (!paramNames.includes(paramName)) break;
      i++;
      paramName = `Param${i}`;
    }
    const newParameters = parameters.set(paramName, '');
    setParameters(newParameters);
    onParamsChange(newParameters);
  }, [parameters, onParamsChange]);

  return (
    <View style={styles.paramWrapper}>
      <View style={styles.editorInputLabelBox}>
        <Text style={styles.editorInputLabel}>Parameters</Text>

        <Pressable style={[styles.addParamsButton]} onPress={() => onAddParam()}>
          <Text style={styles.addParamsButtonText}>+ Add Params</Text>
        </Pressable>
      </View>

      <View style={styles.paramsEditor}>
        {parameters &&
          parameters.entrySeq().map(([paramName, paramValue], key) => {
            return (
              <View key={paramName + '' + key} style={styles.paramEditorRow}>
                <View style={styles.codeInputStyle}>
                  <CodeInput
                    defaultValue={paramName}
                    onChange={(editor: unknown, data: unknown, value: string) =>
                      debouncedOnNameChange(paramName, value)
                    }
                  />
                </View>

                <View style={styles.codeInputStyle}>
                  <CodeInput
                    defaultValue={paramValue}
                    onChange={(editor: unknown, data: unknown, value: string) =>
                      debouncedOnValueChange(paramName, value)
                    }
                  />
                </View>
                <Pressable
                  style={styles.deleteButton}
                  onPress={() => {
                    onDelete(paramName);
                  }}>
                  <MaterialCommunityIcons name="close" color="#808080" size={20} />
                </Pressable>
              </View>
            );
          })}
      </View>
    </View>
  );
};

export default ParamsInspector;

const styles = StyleSheet.create({
  editorInputLabelBox: {fontSize: 14, margin: 5, flexDirection: 'row'},
  paramWrapper: {flexDirection: 'column'},
  paramsEditor: {flexDirection: 'column'},
  editorInputLabel: {
    flex: 1,
    padding: 5,
  },
  addParamsButton: {
    flex: 1,
  },
  addParamsButtonText: {textAlign: 'right', flex: 1, padding: 5, color: '#3c92dc'},
  paramEditorRow: {flexDirection: 'row', borderWidth: 1, borderRadius: 5, borderColor: '#ccc', margin: 5},
  codeInputStyle: {
    flex: 4,
    borderRightWidth: 1,
    borderRadius: 5,
    borderBottomRightRadius: 0,
    borderTopRightRadius: 0,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
  deleteButton: {flex: 1, padding: 4, textAlign: 'center'},
});
