import _, {debounce} from 'lodash';
import React, {useCallback} from 'react';
import {TextInput, View, StyleSheet, Text} from 'react-native';
import {EventHandlerConfig} from 'apptile-core';
import {PluginWithGlobalAvailability} from '../../../../selectors/PluginSelectors';
import DropDownControl from '../../DropDownControl';
import CodeInput from '../../../codeEditor/codeInput';
import CheckboxControl from '../../CheckboxControl';

export interface SetValueEventEditorProps {
  event: EventHandlerConfig;
  plugins: PluginWithGlobalAvailability;
  onConfigUpdate: (eventConfig: EventHandlerConfig) => void;
}

const SetValueEventEditor: React.FC<SetValueEventEditorProps> = props => {
  const {event, plugins, onConfigUpdate} = props;

  const onPluginChangeHandler = useCallback(
    pluginId => {
      const pluginconf = _.find(plugins, plugin => plugin.pluginId === pluginId);
      const newState = event.set('pluginId', pluginId).set('isGlobalPlugin', pluginconf ? pluginconf.isGlobal : true);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate, plugins],
  );

  const onisGlobalChangeHandler = useCallback(
    value => {
      const newState = event.set('isGlobalPlugin', value);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate, plugins],
  );

  const onPropValueChangeHandler = useCallback(
    (value, key) => {
      const newState = event.set(key, value);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );
  const debouncedOnPropValueChangeHandler = debounce((value, key) => onPropValueChangeHandler(value, key), 450);
  return (
    <>
      <DropDownControl
        label="Plugin"
        defaultValue=""
        value={event.pluginId}
        onChange={onPluginChangeHandler}
        options={plugins}
        valueKey="pluginId"
      />
      <View style={styles.rowLayout}>
        <Text style={styles.labelText}>Property Name</Text>
        <View style={styles.CodeInputStyle}>
          <TextInput
            style={styles.inputStyle}
            placeholder="value"
            defaultValue={event.prop}
            onChange={(e: any) => debouncedOnPropValueChangeHandler(e.target.value, 'prop')}
          />
        </View>
      </View>
      <View style={styles.rowLayout}>
        <Text style={styles.labelText}>Property Value</Text>
        <View style={styles.CodeInputStyle}>
          <CodeInput
            placeholder="{{textInput1.value}}"
            value={event.value}
            onChange={(editor: unknown, data: unknown, value: string) =>
              debouncedOnPropValueChangeHandler(value, 'value')
            }
          />
        </View>
      </View>
      <View style={styles.rowLayout}>
        <CheckboxControl
          label="Is Global Plugin"
          value={event?.get('isGlobalPlugin') ?? false}
          onChange={(value: boolean) => onisGlobalChangeHandler(value)}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: 2,
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
  inputStyle: {
    flex: 1,
    flexGrow: 4,
    padding: 8,
    fontSize: 12,
    fontFamily: 'monospace',
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
});

export default SetValueEventEditor;
