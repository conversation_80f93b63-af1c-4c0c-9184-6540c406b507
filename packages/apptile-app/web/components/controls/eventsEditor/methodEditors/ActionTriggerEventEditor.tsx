import _, {debounce, isObject} from 'lodash';
import React, {useCallback} from 'react';
import {StyleSheet, View} from 'react-native';
import {EventHandlerConfig} from 'apptile-core';
import DropDownControl from '../../DropDownControl';
import {useSelector} from 'react-redux';
import {selectGlobalAndPagePluginsWithAction} from '../../../../selectors/PluginSelectors';
import {PluginAction} from '../../../../../app/plugins/triggerAction';
import {pluginEditorComponents} from '../../../pluginEditorComponents';
import Immutable from 'immutable';
import {fetchInputFieldsForUIEditor} from '../../../../../app/plugins/datasource/utils';
import CheckboxControl from '../../CheckboxControl';

export interface ActionTriggerEventEditorProps {
  event: EventHandlerConfig;
  onConfigUpdate: (eventConfig: EventHandlerConfig) => void;
}

const ActionTriggerEventEditor: React.FC<ActionTriggerEventEditorProps> = function (props) {
  const {event, onConfigUpdate} = props;
  const actionPlugins = useSelector(selectGlobalAndPagePluginsWithAction);
  let triggetActions: PluginAction[] = [];
  if (actionPlugins && actionPlugins.length > 0 && event.pluginId) {
    const matchedActions = actionPlugins.filter(x => {
      return (x.pluginId === event.get('pluginId') && x.isGlobal === event.get('isGlobalPlugin')) || (x?.namespace && x?.namespace?.pluginId === event.get('pluginId') && x.isGlobal === event.get('isGlobalPlugin'));
    });
    if (matchedActions && matchedActions.length > 0) {
      triggetActions = matchedActions[0]?.actions;
    }
  }

  let actionInpurtQueryParams = null;
  let currentActionName: string;
  if (triggetActions && event.get('value')) {
    const matchedActionMetadata = triggetActions.filter(x => x.name === event.get('value'));
    if (matchedActionMetadata && matchedActionMetadata.length > 0) {
      const inputParams = matchedActionMetadata[0].metadata?.editableInputParams;
      actionInpurtQueryParams = fetchInputFieldsForUIEditor(inputParams);
      currentActionName = matchedActionMetadata[0].name;
    }
  }

  const onPluginChangeHandler = useCallback(
    pluginId => {
      const pluginconf = _.find(actionPlugins, plugin => plugin.pluginId === pluginId);
      const newState = event.set('pluginId', pluginId).set('isGlobalPlugin', pluginconf ? pluginconf.isGlobal : false);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate, actionPlugins],
  );

  const onisGlobalChangeHandler = useCallback(
    value => {
      const newState = event.set('isGlobalPlugin', value);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate, actionPlugins],
  );

  const onActionChangeHandler = useCallback(
    actionName => {
      const newState = event.setIn(['value'], actionName);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );

  const onPropValueChangeHandler = useCallback(
    (value, key) => {
      const actionParams = event.get('params') || Immutable.Map();
      const updateActionParams = actionParams.set(key, value);
      const newState = event.set('params', updateActionParams);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );

  const CodeInputEditor = pluginEditorComponents.codeInput;

  const debouncedOnPropValueChangeHandler = debounce((value, key) => onPropValueChangeHandler(value, key), 450);
  return (
    <>
      <DropDownControl
        label="Plugin"
        defaultValue=""
        value={event.pluginId}
        onChange={onPluginChangeHandler}
        options={actionPlugins}
        valueKey="pluginId"
      />
      <DropDownControl
        label="Action"
        defaultValue=""
        value={event.get('value')}
        onChange={onActionChangeHandler}
        valueKey="name"
        options={triggetActions}
      />
      <View style={styles.rowLayout}>
        <CheckboxControl
          label="Is Global Plugin"
          value={event?.get('isGlobalPlugin') ?? false}
          onChange={(value: boolean) => onisGlobalChangeHandler(value)}
        />
      </View>
      {/* <View style={styles.rowLayout}>
        <Text style={styles.labelText}>Params Object</Text>
        <View style={styles.CodeInputStyle}>
          <CodeInput
            placeholder="{{{'key':'value'}}"
            value={event.value}
            onChange={(editor: unknown, data: unknown, value: string) =>
              debouncedOnPropValueChangeHandler(value, 'value')
            }
          />
        </View>
      </View> */}
      {actionInpurtQueryParams &&
        actionInpurtQueryParams.map(propEditor => {
          const eventValue = event.get('params') ? event.get('params').get(propEditor.name) : '';
          const ParamPropertyEditor = pluginEditorComponents[propEditor.type];
          return (
            <ParamPropertyEditor
              key={currentActionName + '.' + propEditor.name}
              value={eventValue}
              configProps={propEditor.props}
              name={propEditor.name}
              defaultValue={eventValue}
              onChange={(value: string) => debouncedOnPropValueChangeHandler(value, propEditor.name)}
            />
          );
        })}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: 2,
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
  inputStyle: {
    flex: 1,
    flexGrow: 4,
    padding: 8,
    fontSize: 12,
    fontFamily: 'monospace',
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
});

export default ActionTriggerEventEditor;
