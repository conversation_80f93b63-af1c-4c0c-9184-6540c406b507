import {debounce} from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View, Text} from 'react-native';
import {EventHandlerConfig, ScreenConfigParams} from 'apptile-core';
import {PluginWithGlobalAvailability} from '../../../selectors/PluginSelectors';
import CodeInput from '../../codeEditor/codeInput';
import CheckboxControl from '../CheckboxControl';
import DropDownControl from '../DropDownControl';
import {actionOptions, moduleEventOption} from './actionOptions';
import ActionTriggerEventEditor from './methodEditors/ActionTriggerEventEditor';
import AnalyticsEventEditor from './methodEditors/AnalyticsEventEditor';
import ForwardModuleEventEditor from './methodEditors/ForwardModuleEventEditor';
import NavigateEventEditor from './methodEditors/NavigateEventEditor';
import QueryEventEditor from './methodEditors/QueryEventEditor';
import SetPageTitleEventEditor from './methodEditors/SetPageTitleEventEditor';
import SetValueEventEditor from './methodEditors/SetValueEventEditor';
import ToastEventEditor from './methodEditors/ToastEventEditor';

export interface EventEditorProps {
  eventId: number;
  event: EventHandlerConfig;
  children?: React.ReactNode;
  triggers: string[];
  plugins: PluginWithGlobalAvailability;
  queries: PluginWithGlobalAvailability;
  screens: ScreenConfigParams[];
  onEventUpdate: (index: number, event: EventHandlerConfig) => void;
  inModule: boolean;
  parentModuleEvents: string[];
}

const EventInspector: React.FC<EventEditorProps> = props => {
  const {eventId, event, triggers, plugins, queries, screens, onEventUpdate, inModule, parentModuleEvents} =
    props;
  const [eventHandler, setEventHandler] = useState<EventHandlerConfig>(event);

  const methodActionOptions = inModule ? actionOptions.concat([moduleEventOption]) : actionOptions;
  useEffect(() => {
    setEventHandler(event);
  }, [event]);

  const onEventLabelChange = useCallback(
    value => {
      const newState = eventHandler.set('label', value);
      setEventHandler(newState);
      onEventUpdate(eventId, newState);
    },
    [eventHandler, eventId, onEventUpdate],
  );

  const onMethodChangeHandler = useCallback(
    value => {
      const eventMethod = value;
      const eventType = actionOptions.find(v => v.method === eventMethod)?.type;
      const newState = eventHandler.set('type', eventType).set('method', eventMethod);
      setEventHandler(newState);
      onEventUpdate(eventId, newState);
    },
    [eventHandler, eventId, onEventUpdate],
  );
  const onHasConditionChangeHandler = useCallback(
    value => {
      const hasCondition = !!value;
      const newState = eventHandler.set('hasCondition', hasCondition);
      setEventHandler(newState);
      onEventUpdate(eventId, newState);
    },
    [eventHandler, eventId, onEventUpdate],
  );
  const onConditionChangeHandler = useCallback(
    value => {
      const condition = value;
      const newState = eventHandler.set('condition', condition);
      setEventHandler(newState);
      onEventUpdate(eventId, newState);
    },
    [eventHandler, eventId, onEventUpdate],
  );
  const deboucedOnConditionChangeHandler = debounce(value => onConditionChangeHandler(value), 450);

  const onEventHandlerUpdate = useCallback(
    event => {
      setEventHandler(event);
      onEventUpdate(eventId, event);
    },
    [eventId, onEventUpdate],
  );

  return (
    <View style={styles.editorWindow}>
      <View style={styles.editorInputItem}>
        <DropDownControl
          label="Trigger"
          defaultValue=""
          value={eventHandler.label}
          onChange={onEventLabelChange}
          options={triggers}
        />
      </View>

      <View style={styles.editorInputItem}>
        <DropDownControl
          label="Action"
          defaultValue=""
          value={eventHandler.method}
          onChange={onMethodChangeHandler}
          options={methodActionOptions}
          valueKey="method"
          nameKey="label"
        />
      </View>
      <View style={styles.editorInputItem}>
        <CheckboxControl
          label="Has Condition"
          value={!!eventHandler.hasCondition}
          onChange={onHasConditionChangeHandler}
        />
      </View>
      {eventHandler.hasCondition && (
        <View style={styles.rowLayout}>
          <Text style={styles.labelText}>Condition</Text>
          <View style={styles.CodeInputStyle}>
            <CodeInput
              placeholder="{{textInput1.value}}"
              value={eventHandler.condition}
              onChange={(editor: unknown, data: unknown, value: string) => deboucedOnConditionChangeHandler(value)}
            />
          </View>
        </View>
      )}
      {(() => {
        switch (eventHandler.method) {
          case 'executeQuery':
          case 'getNextPage':
            return <QueryEventEditor event={eventHandler} queries={queries} onConfigUpdate={onEventHandlerUpdate} />;
          case 'setValue':
            return <SetValueEventEditor event={eventHandler} plugins={plugins} onConfigUpdate={onEventHandlerUpdate} />;
          case 'navigate':
          case 'navigateReset':
            return <NavigateEventEditor event={eventHandler} screens={screens} onConfigUpdate={onEventHandlerUpdate} />;
          case 'setPageTitle':
            return <SetPageTitleEventEditor event={eventHandler} onConfigUpdate={onEventHandlerUpdate} />;
          case 'triggerToast':
            return <ToastEventEditor event={eventHandler} onConfigUpdate={onEventHandlerUpdate} />;
          case 'triggerAction':
            return <ActionTriggerEventEditor event={eventHandler} onConfigUpdate={onEventHandlerUpdate} />;
          case 'forwardModuleEvent':
            return (
              <ForwardModuleEventEditor
                event={eventHandler}
                moduleEvents={parentModuleEvents}
                onConfigUpdate={onEventHandlerUpdate}
              />
            );
          case 'sendPageAnalytics':
          case 'sendTrackAnalytics':
            return <AnalyticsEventEditor event={eventHandler} onConfigUpdate={onEventHandlerUpdate} />;
          default:
            return <></>;
        }
      })()}
    </View>
  );
};

export default EventInspector;

const styles = StyleSheet.create({
  editorWindow: {
    flex: 1,
    backgroundColor: '#fff',
    flexBasis: 'auto',
    flexDirection: 'column',
    flexShrink: 0,
  },
  editorInputItem: {flex: 1, flexDirection: 'column', margin: 5},
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 11,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
});
