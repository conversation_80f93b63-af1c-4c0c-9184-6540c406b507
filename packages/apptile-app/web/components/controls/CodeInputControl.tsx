import React, {useCallback, useEffect, useRef, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import theme from '@/root/web/styles-v2/theme';
import CodeInput from '../codeEditor/codeInput';
import commonStyles from '../../styles-v2/commonStyles';

interface CodeInputControlProps {
  value: string;
  label?: string;
  defaultValue?: string | number;
  placeholder?: string | number;
  onChange: (value: string) => void;
  name?: string;
  singleLine?: boolean;
  validationPattern?: string;
  validationError?: string;
  noOfLines?: number;
}

const CodeInputControl: React.FC<CodeInputControlProps> = ({
  value,
  label,
  defaultValue,
  placeholder,
  name,
  onChange,
  singleLine = false,
  validationPattern = '',
  validationError = 'Validation Failed In Text Input',
  noOfLines,
}) => {
  const onValueChange = useCallback(
    newVal => {
      onChange(newVal);
    },
    [onChange],
  );
  const [codeValue, setCodeValue] = useState(value);
  const inputTextAreaRef = useRef < { isFocused: () => boolean }>(null);
  const testRegex = useCallback((pattern: string, value: string) => {
    let regex = pattern,
      options = '';
    if (pattern.trim().startsWith('/')) {
      regex = pattern.slice(1, pattern.lastIndexOf('/'));
      options = pattern.slice(pattern.lastIndexOf('/') + 1);
    }
    try {
      return new RegExp(regex, options).test(value);
    } catch (e) {
      return true;
    }
  }, []);
  useEffect(() => {
    if (inputTextAreaRef.current && !inputTextAreaRef.current?.isFocused()) {
      setCodeValue(value);
    }
  }, [value]);
  return (
    <View style={styles.container}>
      <View style={styles.controlContainer}>
        {label && <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>{label}</Text>}
        <View style={[styles.CodeInputStyle, !!label && commonStyles.inputContainer]}>
          {/* <View style={styles.icon}>
        <MaterialCommunityIcons name="dots-horizontal" size={22} color="white" />
      </View> */}
          <CodeInput
            value={codeValue}
            defaultValue={defaultValue}
            placeholder={placeholder}
            propertyName={name}
            singleLine={singleLine}
            onChange={(editor: unknown, data: unknown, value: string) => {
              setCodeValue(value);
              if (validationPattern == '' || testRegex(validationPattern, value)) onValueChange(value);
            }}
            noOfLines={noOfLines}
            ref={inputTextAreaRef}
          />
        </View>
      </View>
      <View>
        {validationPattern != '' && !testRegex(validationPattern, codeValue) && (
          <Text style={[commonStyles.baseText, {color: theme.ERROR_BACKGROUND, paddingTop: 8, textAlign: 'center'}]}>
            {validationError}
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.PRIMARY_MARGIN,
    width: '100%',
  },
  controlContainer: {
    flexDirection: 'row',
  },
  icon: {
    width: 28,
    height: 28,
    marginTop: 4,
    marginLeft: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#535353',
    borderRadius: 6,
  },
  CodeInputStyle: {
    flex: 1,
    flexDirection: 'row',
    borderRadius: 6,
    backgroundColor: theme.INPUT_BACKGROUND,
  },
});

export default CodeInputControl;
