import React, {useState, useCallback, useEffect} from 'react';
import {currencyList, Currency, countriesList} from '@/root/web/common/currencyConstants';
import Immutable from 'immutable';
import SortableList from '../SortableList';
import DropDownControl from './DropDownControl';
import theme from '../../styles-v2/theme';
import {datasourceTypeModelSel, MaterialCommunityIcons} from 'apptile-core';
import {EditorRootState} from '../../store/EditorRootState';
import {useSelector} from 'react-redux';
import {View, StyleSheet, Text} from 'react-native';
import Button from '../../components-v2/base/Button';
import commonStyles from '../../styles-v2/commonStyles';

const shopifyModelSel = (state: EditorRootState) => datasourceTypeModelSel(state, 'shopifyV_22_10');

interface CurrencySelectorControlProps {
  value?: any; // Could be Currency[], Immutable.List, or Immutable.Map
  onChange: (newList: Currency[] | string) => void;
  label?: string;
}

function toJSArray(val: any): Currency[] {
  if (!val) return [];
  if (Array.isArray(val)) return val as Currency[];
  if (Immutable.List.isList(val)) return val.toJS() as Currency[];
  if (Immutable.Map.isMap(val)) return val.toList().toJS() as Currency[];
  return [];
}

const styles = StyleSheet.create({
  currencyItemStyle: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
    background: theme.INPUT_BACKGROUND,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    borderRadius: theme.INPUT_BORDER_RADIUS,
    paddingVertical: 4,
    paddingHorizontal: 12,
  },

  currencyItemTextStyle: {
    flex: 1,
    fontFamily: theme.FONT_FAMILY,
    fontSize: theme.FONT_SIZE,
    color: theme.CONTROL_INPUT_COLOR,
    fontWeight: 500,
    letterSpacing: 0.2,
  },

  removeButtonStyle: {
    marginLeft: 12,
    color: theme.ERROR,
    background: 'none',
    border: 'none',
    cursor: 'pointer',
    fontWeight: 500,
    fontSize: 13,
    borderRadius: 4,
    padding: '4px 8px',
    transition: 'background 0.2s',
  },
});

const CurrencyListItem: React.FC<{
  itemVal: Currency;
  itemKey: string;
  onRemove: (code: string) => void;
}> = ({itemVal, itemKey, onRemove}) => (
  <View key={itemKey} style={styles.currencyItemStyle}>
    <Text style={styles.currencyItemTextStyle}>
      {itemVal.name} ({itemVal.symbol})
    </Text>
    <MaterialCommunityIcons name="trash-can-outline" color="#D80707" size={20} onPress={() => onRemove(itemVal.code)} />
  </View>
);

const defaultCurrencyValue = 'Select Currency'; // Default value for the dropdown
const defaultCountryValue = 'Select Country'; // Default value for the country dropdown
const CurrencySelectorControl: React.FC<CurrencySelectorControlProps> = ({value = [], onChange, label}) => {
  const [selectedCurrency, setSelectedCurrency] = useState<string>('');
  const [selectedCountry, setSelectedCountry] = useState<string>('');
  const valueArray = toJSArray(value);
  const ShopifyDSModel = useSelector(shopifyModelSel);
  const enabledCurrencies = ShopifyDSModel?.get('shop')?.paymentSettings?.enabledPresentmentCurrencies;
  // Only show currencies that are enabled for the shop
  const availableCurrencies = currencyList
    .filter(currency => enabledCurrencies?.includes(currency.code))
    .filter(currency => !valueArray?.some(listedCurrency => listedCurrency.code === currency.code));

  console.log('Available Currencies:', enabledCurrencies);
  const handleAdd = () => {
    const currency = currencyList.find(c => c.code === selectedCurrency);
    if (currency) {
      // Ensure no duplicates by code
      const newList = [...valueArray, {...currency, countryCode: selectedCountry}].filter(
        (item, index, arr) => arr?.findIndex(v => v.code === item.code) === index,
      );
      onChange('');
      onChange(newList);
      setSelectedCurrency('');
      setSelectedCountry('');
    }
  };

  const handleRemove = (code: string) => {
    // Remove the currency and ensure the result is a flat array
    const newList = valueArray.filter(c => c.code !== code);
    onChange('');
    onChange(newList);
  };

  // For SortableList: data is [key, value] pairs
  const sortableData: [string, Currency][] = valueArray.map(c => [c.code, c]);

  // When order changes
  const handleSortChange = useCallback(
    (reordered: [string, Currency][]) => {
      const newOrder = reordered.map(([, val]) => val);
      onChange(''); // Quick hack: clear first to prevent duplicates
      onChange(newOrder);
    },
    [onChange],
  );

  return (
    <View
      style={[
        {
          marginVertical: theme.PRIMARY_MARGIN,
          minHeight: theme.PRIMARY_HEIGHT,
        },
        commonStyles.controlContainer,
      ]}>
      {label && <Text style={[commonStyles.labelText]}>{label}</Text>}
      <View
        style={{flexDirection: 'row', justifyContent: 'space-between', gap: 8, alignItems: 'center', marginBottom: 4}}>
        <DropDownControl
          options={countriesList}
          value={selectedCountry}
          defaultValue={defaultCountryValue}
          onChange={setSelectedCountry}
          nameKey="country"
          valueKey="countryCode"
          label={undefined}
        />
        <DropDownControl
          options={availableCurrencies}
          value={selectedCurrency}
          defaultValue={defaultCurrencyValue}
          onChange={setSelectedCurrency}
          nameKey="name"
          valueKey="code"
          label={undefined}
        />
        <Button
          onPress={handleAdd}
          disabled={
            !selectedCurrency ||
            !selectedCountry ||
            selectedCurrency === defaultCurrencyValue ||
            selectedCountry === defaultCountryValue
          }
          color={'CTA'}>
          Add
        </Button>
      </View>
      <SortableList
        dragKey="currency-list"
        data={sortableData}
        onChange={handleSortChange}
        itemComponent={({itemVal, itemKey}) => (
          <CurrencyListItem itemVal={itemVal} itemKey={itemKey} onRemove={handleRemove} />
        )}
      />
    </View>
  );
};

export default CurrencySelectorControl;
