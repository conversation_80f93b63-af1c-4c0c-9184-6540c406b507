import _ from 'lodash';
import React from 'react';
import {StyleSheet, View, Text, Pressable} from 'react-native';
import commonStyles from '../../styles-v2/commonStyles';
import theme from '../../styles-v2/theme';
import NumericInputControl from './NumericInputControl';
import RangeSliderControl from './RangeSliderControl';

interface AspectRatioControlProps {
  value: string;
  label?: string;
  defaultValue?: string | number;
  placeholder?: string | number;
  onChange: (value: string) => void;
  name?: string;
  singleLine?: boolean;
  validationPattern?: string;
  validationError?: string;
  prefix?: string;
  suffix?: string;
}

const AspectRatioControl: React.FC<AspectRatioControlProps> = ({value = '1', label, onChange}) => {
  const calculateValue = (height: any) => 800 / (height == 0 ? 800 : height);
  const debouncedOnCustomPropChange = _.debounce((value: any) => onChange(value), 150);
  const currentValue = value == '1' ? '1' : value == '1.33' ? '2' : value == '0.75' ? '3' : '4';
  return (
    <View style={{flexDirection: 'row', flex: 1, width: '100%'}}>
      {label && (
        <View style={commonStyles.labelContainer}>
          <Text style={[commonStyles.labelText]}>{label}</Text>
        </View>
      )}
      <View style={[commonStyles.inputContainer, styles.inputContainer, !label && {width: '100%'}]}>
        <View style={[styles.ratioInputContainer, commonStyles.input, {width: '100%'}]}>
          <Pressable
            style={[styles.toggleButton, currentValue == '1' && styles.toggleButtonActive]}
            onPress={() => onChange('1')}>
            <View style={[styles.icon, styles.squareIcon, currentValue == '1' && styles.activeIcon]} />
            <Text style={[commonStyles.inputText, currentValue == '1' && styles.activeText]}>Square</Text>
          </Pressable>
          <Pressable
            style={[styles.toggleButton, currentValue == '2' && styles.toggleButtonActive]}
            onPress={() => onChange('1.33')}>
            <View style={[styles.icon, styles.landscapeIcon, currentValue == '2' && styles.activeIcon]} />
            <Text style={[commonStyles.inputText, currentValue == '2' && styles.activeText]}>Landscape</Text>
          </Pressable>
          <Pressable
            style={[styles.toggleButton, currentValue == '3' && styles.toggleButtonActive]}
            onPress={() => onChange('0.75')}>
            <View style={[styles.icon, styles.potraitIcon, currentValue == '3' && styles.activeIcon]} />
            <Text style={[commonStyles.inputText, currentValue == '3' && styles.activeText]}>Portrait</Text>
          </Pressable>
          <Pressable
            style={[styles.toggleButton, currentValue == '4' && styles.toggleButtonActive]}
            onPress={() => onChange('1.01')}>
            <View style={[styles.icon, styles.customIcon, currentValue == '4' && styles.activeIcon]}>
              <Text
                style={[
                  commonStyles.inputText,
                  {color: theme.CONTROL_PLACEHOLDER_COLOR, fontSize: 10, textAlign: 'center'},
                  currentValue == '4' && styles.activeText,
                ]}>
                C
              </Text>
            </View>
            <Text style={[commonStyles.inputText, currentValue == '4' && styles.activeText]}>Custom</Text>
          </Pressable>
        </View>
        <View style={{marginTop: 4}}>
          {currentValue == '4' && (
            <RangeSliderControl
              minRange="0.1"
              maxRange="10"
              steps="0.1"
              value={value}
              onChange={debouncedOnCustomPropChange}
            />
          )}
        </View>
        {/* <View style={[styles.ratioInputContainer, {width: '100%'}]}>
          <View style={[styles.widthBoxContainer]}>
            <View style={styles.widthBox}>
              <View
                style={[
                  commonStyles.input,
                  {flex: 1, alignItems: 'center', justifyContent: 'center', width: '100%', marginVertical: 4},
                ]}>
                <Text style={[commonStyles.baseText, {color: theme.CONTROL_PLACEHOLDER_COLOR}]}>800px</Text>
              </View>
              <Text style={[commonStyles.baseText, {color: theme.CONTROL_PLACEHOLDER_COLOR}]}>Width</Text>
            </View>
          </View>
          <View style={styles.centerText}>
            <Text style={[commonStyles.baseText, {fontSize: 20, paddingBottom: 14}]}> / </Text>
          </View>
          <View style={styles.inputBox}>
            <NumericInputControl
              value={`${800 / Number(value)}`}
              defaultValue={`${800 / Number(value)}`}
              placeholder={'Height'}
              onChange={height => {
                debouncedOnCustomPropChange(`${calculateValue(Number(height))}`);
              }}
              noUnit={true}
            />
            <Text
              style={[
                commonStyles.baseText,
                {fontSize: 12, textAlign: 'center'},
                {color: theme.CONTROL_PLACEHOLDER_COLOR},
              ]}>
              Height
            </Text>
          </View>
        </View> */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  widthBoxContainer: {
    width: '42%',
  },
  widthBox: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputBox: {
    minHeight: theme.PRIMARY_HEIGHT,
    width: '42%',
  },
  inputContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    paddingVertical: 2,
  },
  ratioInputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 2,
    flexWrap: 'wrap',
  },
  centerText: {
    alignSelf: 'center',
    width: '10%',
  },
  rightText: {
    alignSelf: 'center',
    width: '22%',
  },
  toggleButton: {
    padding: 3,
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'column',
    flexBasis: 'auto',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 3,
    width: '50%',
  },
  toggleButtonActive: {
    backgroundColor: '#FFFFFF',
    borderRadius: theme.INPUT_BORDER_RADIUS,
    boxShadow: '0px 2px 2px rgba(0, 0, 0, 0.25)',
    textAlign: 'center',
  },
  activeText: {
    color: theme.CONTROL_ACTIVE_COLOR,
    fontWeight: theme.FONT_WEIGHT_BOLD,
  },
  icon: {
    height: 20,
    width: 20,
    borderWidth: 2,
    borderColor: theme.CONTROL_PLACEHOLDER_COLOR,
    borderRadius: 4,
  },
  activeIcon: {
    borderColor: theme.CONTROL_ACTIVE_COLOR,
  },
  squareIcon: {
    margin: 3,
  },
  potraitIcon: {
    margin: 2,
    width: 17,
    height: 26,
  },
  landscapeIcon: {
    marginHorizontal: 2,
    height: 17,
    width: 26,
    marginVertical: 5,
  },
  customIcon: {
    height: 24,
    width: 24,
    margin: 2,
    padding: 3,
    textAlign: 'center',
  },
});

export default AspectRatioControl;
