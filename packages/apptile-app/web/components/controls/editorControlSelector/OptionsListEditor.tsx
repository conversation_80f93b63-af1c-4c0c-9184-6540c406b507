import _ from 'lodash';
import {debounce} from 'lodash';
import React, {FC, useCallback, useEffect, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import CodeInput from '../../codeEditor/codeInput';

type OptionsListEditorProps = {
  onOptionsChange: (value: string[]) => void;
  options: string[];
};

const OptionsListEditor: FC<OptionsListEditorProps> = ({options, onOptionsChange}) => {
  const [optionsList, setOptionsList] = useState(options);
  useEffect(() => {
    setOptionsList(options);
  }, [options]);
  const onDelete = useCallback(
    idx => {
      const newOptions = optionsList;
      _.pullAt(newOptions, [idx]);
      setOptionsList(newOptions);
      onOptionsChange(newOptions);
    },
    [optionsList, onOptionsChange],
  );

  const onValueChange = useCallback(
    (idx, newVal) => {
      let updatedValues = optionsList.slice();
      updatedValues[idx] = newVal;
      setOptionsList(updatedValues);
      onOptionsChange(updatedValues);
    },
    [onOptionsChange, optionsList],
  );
  const debouncedOnValueChange = debounce((idx, newVal) => onValueChange(idx, newVal), 450);

  const onAddOption = useCallback(() => {
    const newOptions = optionsList.slice();
    newOptions.push(`option${optionsList.length + 1}`);
    setOptionsList(newOptions);
    onOptionsChange(newOptions);
  }, [optionsList, onOptionsChange]);

  return (
    <View style={styles.optionWrapper}>
      <View style={styles.editorInputLabelBox}>
        <Text style={styles.editorInputLabel}>Options</Text>

        <Pressable style={[styles.addOptionsButton]} onPress={() => onAddOption()}>
          <Text style={styles.addOptionsButtonText}>+ Add Options</Text>
        </Pressable>
      </View>

      <View style={styles.optionsEditor}>
        {optionsList &&
          optionsList.map((optionValue, key) => {
            return (
              <View key={key} style={styles.optionEditorRow}>
                <View style={styles.codeInputStyle}>
                  <CodeInput
                    defaultValue={optionValue}
                    onChange={(editor: unknown, data: unknown, value: string) => debouncedOnValueChange(key, value)}
                  />
                </View>
                <Pressable
                  style={styles.deleteButton}
                  onPress={() => {
                    onDelete(key);
                  }}>
                  <MaterialCommunityIcons name="close" color="#808080" size={20} />
                </Pressable>
              </View>
            );
          })}
      </View>
    </View>
  );
};

export default OptionsListEditor;

const styles = StyleSheet.create({
  editorInputLabelBox: {fontSize: 14, margin: 5, flexDirection: 'row'},
  optionWrapper: {flexDirection: 'column'},
  optionsEditor: {flexDirection: 'column'},
  editorInputLabel: {
    flex: 1,
    padding: 5,
  },
  addOptionsButton: {
    flex: 1,
  },
  addOptionsButtonText: {textAlign: 'right', flex: 1, padding: 5, color: '#3c92dc'},
  optionEditorRow: {flexDirection: 'row', borderWidth: 1, borderRadius: 5, borderColor: '#ccc', margin: 5},
  codeInputStyle: {
    flex: 4,
    borderRightWidth: 1,
    borderRadius: 5,
    borderBottomRightRadius: 0,
    borderTopRightRadius: 0,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
  deleteButton: {flex: 1, padding: 4, textAlign: 'center'},
});
