import {PropertyEditorConfig, PropertyEditorControlsList, PropertyEditorTypes} from 'apptile-core';
import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {View} from 'react-native';
import CheckboxControl from '../CheckboxControl';
import DropDownControl from '../DropDownControl';
import CodeInputConfigEditor from './controlTypeConfigEditors/CodeInputConfigEditor';
import DropdownConfigEditor from './controlTypeConfigEditors/DropdownConfigEditor';
import FormatInputConfigEditor from './controlTypeConfigEditors/FormatInputConfigEditor';
import ListEditorConfigEditor from './controlTypeConfigEditors/ListEditorConfigEditor';
import RadioGroupConfigEditor from './controlTypeConfigEditors/RadioGroupConfigEditor';
import RangeSliderConfigEditor from './controlTypeConfigEditors/RangeSliderConfigEditor';
import RadioGroupControl from '../RadioGroupControl';
import RangeSliderControl from '../RangeSliderControl';
import CodeInputControlV2 from '../../controls-v2/CodeInputControl';

export interface PropertyEditorControlSelectorProps {
  editorRecord: PropertyEditorConfig<any> | null;
  onChange: (editorConfig: PropertyEditorConfig<any>) => void;
}

const editorOptions = _.toPairs(PropertyEditorControlsList).map(val => {
  return {key: val[0], value: val[1]};
});

const getControlConfigProps = (
  controltype: PropertyEditorTypes | string,
  oldConfigProps: any,
): PropertyEditorConfig<any> => {
  let configProps = {};
  switch (controltype) {
    case 'codeInput':
      configProps = {
        singleLine: true,
      };
      break;
    case 'quadInput':
      configProps = {
        options: ['top', 'right', 'bottom', 'left'],
        layout: 'plus',
      };
      break;
    case 'typographyInput':
      configProps = {
        disableExport: true,
      };
      break;
    case 'borderRadiusEditor':
      configProps = {
        options: ['borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomRightRadius', 'borderBottomLeftRadius'],
      };
      break;
    case 'paddingEditor':
      controltype = 'trblValuesEditor';
      configProps = {
        options: ['paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft'],
      };
      break;
    case 'alignmentEditor':
      controltype = 'alignmentEditor';
      configProps = {};
      break;
    case 'marginEditor':
      controltype = 'trblValuesEditor';
      configProps = {
        options: ['marginTop', 'marginRight', 'marginBottom', 'marginLeft'],
      };
      break;
    case 'cloudinaryEditor':
      configProps = {
        urlProperty: oldConfigProps?.urlProperty || 'value',
        disableBinding: true,
      };
      break;
    case 'borderEditor':
      controltype = 'trblValuesEditor';
      configProps = {
        options: ['borderTopWidth', 'borderRightWidth', 'borderBottomWidth', 'borderLeftWidth'],
      };
      break;
    case 'checkbox':
      configProps = {};
      break;
    case 'rangeSliderInput':
      configProps = {
        minRange: 1,
        maxRange: 24,
        steps: 1,
      };
      break;
    case 'radioGroup':
      configProps = {
        schema: [
          {key: 'icon', type: 'codeInput'},
          {key: 'text', type: 'codeInput'},
          {key: 'value', type: 'codeInput'},
        ],
        options:
          oldConfigProps &&
          oldConfigProps.options &&
          Array.isArray(oldConfigProps.options) &&
          (typeof oldConfigProps.options[0] === 'string' ||
            (oldConfigProps.options[0].value && oldConfigProps.options[0].text))
            ? oldConfigProps.options
            : [{icon: '', text: '', value: ''}],
        disableBinding: true,
      };
      break;
    case 'dropDown':
      configProps = {
        options: [],
        nameKey: 'name',
        valueKey: 'value',
        gatingKey: 'gating',
        disableBinding: true,
      };
      break;
    case 'jsonInput':
      configProps = {};
      break;
    case 'customList':
      configProps = {
        schema: [],
      };
      break;
    case 'colorInput':
    case 'assetEditor':
      configProps = {
        urlProperty: oldConfigProps?.urlProperty || 'value',
        assetProperty: oldConfigProps?.assetProperty || 'assetId',
        sourceTypeProperty: oldConfigProps?.sourceTypeProperty || 'sourceType',
        disableBinding: true,
      };
      break;
    case 'dateAndTimeInput':
      configProps = {
        disableBinding: true,
      };
      break;
    case 'customData':
      configProps = {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              key: {
                type: 'string',
              },
            },
          },
        },
      };
      break;
    case 'shopifyCollectionHandleControl':
    case 'listEditor':
    default:
      configProps = {};
      break;
  }
  return {
    label: '',
    type: controltype,
    defaultValue: undefined,
    props: configProps,
  };
};

const PropertyEditorControlSelector: React.FC<PropertyEditorControlSelectorProps> = props => {
  const {editorRecord, onChange} = props;
  const {type, props: editorProps} = editorRecord ?? {};

  const [editorType, setEditorType] = useState(type);
  useEffect(() => {
    setEditorType(type);
  }, [type]);
  const [configProps, setConfigProps] = useState(editorProps);

  const onValueChange = useCallback(
    newVal => {
      setEditorType(newVal);
      const editorConfigRecord = getControlConfigProps(newVal, editorProps);
      editorConfigRecord.props = {...editorProps, ...editorConfigRecord.props};
      setConfigProps(editorConfigRecord.props);
      onChange(editorConfigRecord);
    },
    [editorProps, onChange],
  );

  const onConfigPropsChange = useCallback(
    configProps => {
      setConfigProps(configProps);

      onChange({...getControlConfigProps(editorType), props: configProps});
    },
    [editorType, onChange],
  );

  return (
    <View style={[{flex: 1}]}>
      <DropDownControl
        label="Type"
        defaultValue={editorType}
        value={editorType}
        onChange={onValueChange}
        options={editorOptions}
        valueKey="key"
        nameKey="value"
        disableBinding={true}
      />
      {(() => {
        switch (editorType) {
          case 'radioGroup':
            return <RadioGroupConfigEditor configProps={configProps} onConfigUpdate={onConfigPropsChange} />;
          case 'rangeSliderInput':
            return <RangeSliderConfigEditor configProps={configProps} onConfigUpdate={onConfigPropsChange} />;
          case 'listEditor':
            return <ListEditorConfigEditor configProps={configProps} onConfigUpdate={onConfigPropsChange} />;
          case 'dropDown':
            return <DropdownConfigEditor configProps={configProps} onConfigUpdate={onConfigPropsChange} />;
          case 'codeInput':
            return <CodeInputConfigEditor configProps={configProps} onConfigUpdate={onConfigPropsChange} />;
          case 'formatInput':
            return <FormatInputConfigEditor configProps={configProps} onConfigUpdate={onConfigPropsChange} />;
          case 'numericInput':
            return (
              <CheckboxControl
                value={configProps?.noUnit}
                label={'Numeric without unit'}
                onChange={(value: boolean) => onConfigPropsChange({...configProps, noUnit: value})}
              />
            );
          case 'assetEditor':
            return (
              <CheckboxControl
                value={configProps?.urlOnly}
                label={'Return URL only'}
                onChange={(value: boolean) => onConfigPropsChange({...configProps, noUnit: value})}
              />
            );
          case 'checkbox':
            return (
              <>
                <CheckboxControl
                  value={configProps?.fullSizeLabel}
                  label={'Full Length Label'}
                  onChange={(value: boolean) => onConfigPropsChange({...configProps, fullSizeLabel: value})}
                />
                <CheckboxControl
                  value={configProps?.reverse}
                  label={'Reverse Toggle'}
                  onChange={(value: boolean) => onConfigPropsChange({...configProps, reverse: value})}
                />
              </>
            );
          case 'codemirrorInput':
            return (
              <>
                <RangeSliderControl
                  minRange="0"
                  maxRange="16"
                  value={`${configProps?.noOfLines}`}
                  label={'Number Of Lines'}
                  onChange={(value: string) => onConfigPropsChange({...configProps, noOfLines: value})}
                />
                <CheckboxControl
                  value={!!configProps?.ignoreBinding}
                  label={'Ignore Binding'}
                  onChange={(value: boolean) => onConfigPropsChange({...configProps, ignoreBinding: value})}
                />
                <RadioGroupControl
                  value={configProps?.language}
                  label={'Language'}
                  disableBinding={true}
                  options={[
                    {text: 'JS', value: 'javascript'},
                    {text: 'Json', value: 'json'},
                    {text: 'HTML', value: 'html'},
                    {text: 'CSS', value: 'css'},
                  ]}
                  onChange={(value: string) => onConfigPropsChange({...configProps, language: value})}
                />
              </>
            );
          case 'customData':
            return (
              <>
                <CodeInputControlV2
                  value={`${JSON.stringify(configProps?.schema, null, 2)}`}
                  label={'Schema'}
                  onChange={(value: boolean) => {
                    try {
                      const parsedValue = JSON.parse(value);
                      onConfigPropsChange({...configProps, schema: parsedValue});
                    } catch (e) {
                      console.error('Invalid JSON format', e);
                    }
                  }}
                />
              </>
            );
          default:
            return <></>;
        }
      })()}
    </View>
  );
};

export default PropertyEditorControlSelector;
