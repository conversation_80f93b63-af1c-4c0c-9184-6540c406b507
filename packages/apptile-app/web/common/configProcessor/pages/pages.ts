import {AppConfig, PageConfig, ScreenConfig} from 'apptile-core';
import {getScreensInNavConfig} from '@/root/web/selectors/EditorSelectors';
import Immutable from 'immutable';
import {pageParamProcessorMap} from './pageParamProcessorMap';
import {themeOnboardingPageId} from '../../onboardingConstants';
import {AI_TILE_PAGE_ID} from '../../tileConstants';

export const pages = (value: Immutable.Map<string, PageConfig>, config: AppConfig) => {
  const construct: Record<string, PageConfig> = {};
  let errors: any = [];
  const screens: ScreenConfig[] = getScreensInNavConfig(config.navigation.rootNavigator);

  value.forEach((pageConfig, pageKey) => {
    const pageId = pageConfig.get('pageId');
    const activeScreen = screens?.find((e: ScreenConfig) => e.screen == pageId);

    if (pageKey !== themeOnboardingPageId && pageKey !== AI_TILE_PAGE_ID) {
      const processedValue = pageProcessor(pageConfig, activeScreen);
      construct[pageKey] = processedValue.value;
      errors = [...errors, ...processedValue.errors];
    }
  });

  return {value: Immutable.Map(construct), errors};
};

const pageProcessor = (value: PageConfig, activeScreen: ScreenConfig) => {
  const construct: Record<string, unknown> = {};
  let errors: any = [];

  value
    .toSeq()
    .toMap()
    .forEach((val, key) => {
      if (pageParamProcessorMap[key]) {
        const processedValue = pageParamProcessorMap[key](val, value.get('pageId'), activeScreen);
        construct[key] = processedValue.value;
        errors = [...errors, ...processedValue.errors];
      } else {
        construct[key] = val;
      }
    });

  return {value: new PageConfig(construct), errors};
};
