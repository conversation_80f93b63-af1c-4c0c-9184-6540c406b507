import {BindingError, ModuleCreationParams, ModuleRecord, PluginSubType, Selector} from 'apptile-core';
import Immutable from 'immutable';

export interface ITileCacheInfo {
  isFetched: boolean;
  isFetching: boolean;
  record?: ModuleRecord;
}
const defaultTileCacheInfo: ITileCacheInfo = {
  isFetched: false,
  isFetching: false,
  record: undefined,
};
export class TileCacheInfo extends Immutable.Record(defaultTileCacheInfo, 'tileCacheInfo') {}

interface TilesCacheShape {
  tiles: Immutable.OrderedMap<string, ITileCacheInfo>;
}

const defaultTilesCacheShape: TilesCacheShape = {
  tiles: Immutable.OrderedMap<string, ITileCacheInfo>(),
};

export class TilesCacheRecord extends Immutable.Record(defaultTilesCacheShape, 'tilesCache') {
  constructor(params: Partial<TilesCacheShape>) {
    super(params);
  }

  isTileCached(moduleUUID: string): boolean {
    return this.tiles.has(moduleUUID) && !!this.tiles.get(moduleUUID)?.isFetched;
  }
  initTileCache(moduleUUID: string): TilesCacheType {
    return this.tiles.has(moduleUUID) ? this : this.setIn(['tiles', moduleUUID], new TileCacheInfo({isFetching: true}));
  }
  setTileCache(moduleUUID: string, moduleRecord: ModuleRecord): TilesCacheType {
    return this.setIn(
      ['tiles', moduleUUID],
      new TileCacheInfo({isFetching: false, isFetched: true, record: moduleRecord}),
    );
  }
  getTileCacheRecord(moduleUUID: string): ModuleRecord | undefined {
    return this.isTileCached(moduleUUID)
      ? (this.getIn(['tiles', moduleUUID, 'record']) as ModuleRecord | undefined)
      : undefined;
  }
}

export type TilesCacheType = Omit<
  TilesCacheRecord,
  | 'set'
  | 'update'
  | 'merge'
  | 'mergeDeep'
  | 'mergeWith'
  | 'mergeDeepWith'
  | 'delete'
  | 'setIn'
  | 'updateIn'
  | 'mergeIn'
  | 'mergeDeepIn'
  | 'deleteIn'
  | 'remove'
  | 'removeIn'
>;

export type IconType = 'MaterialCommunityIcons' | 'ApptileWebIcons' | 'MaterialIcons' | 'FontAwesome' | 'Ionicons';


interface EditorStateParams {
  selectedPluginConfigSel?: Selector | null;
  selectedNavComponentSel?: Selector | null;
  selectedPageId: string | null;
  selectedPageType: string | null;
  isSaving: boolean;
  isPropertyInspectorOpen: boolean;
  isPluginListingOpen: boolean;
  selectedSettingsSel: string | null;
  isThemeEditorOpen: boolean;
  isTilesBrowserOpen: boolean;
  registeredPlugins: Immutable.Set<string>;
  moduleCreationParams: ModuleCreationParams;
  activeAttachmentId: string | undefined;
  activeAttachmentKey: string | undefined;
  bindingErrors: Immutable.Map<string, BindingError>;
  showBindingErrors: boolean;
}

const defaultEditorParams: EditorStateParams = {
  selectedPluginConfigSel: null,
  selectedNavComponentSel: null,
  selectedPageId: null,
  selectedPageType: 'screen',
  isSaving: false,
  isPropertyInspectorOpen: false,
  selectedSettingsSel: null,
  isPluginListingOpen: true,
  isThemeEditorOpen: false,
  isTilesBrowserOpen: false,
  registeredPlugins: Immutable.Set<PluginSubType>(),
  moduleCreationParams: {
    pageId: undefined,
    pageKey: undefined,
    pluginId: undefined,
    variablesBySelector: {},
    inputSelectorStrings: [],
    modulePluginIds: [],
    moduleParentContainerId: undefined,
    isOpen: false,
  },
  activeAttachmentId: undefined,
  activeAttachmentKey: undefined,
  bindingErrors: Immutable.Map<string, BindingError>(),
  showBindingErrors: process.env.IS_WEB_ENV ? !!localStorage.getItem('showBindingErrors') || false : false,
};

export class EditorState extends Immutable.Record(defaultEditorParams, 'editor') {
  selectPlugin(pluginConfigSel: Selector) {
    return this.set('selectedNavComponentSel', null)
      .set('selectedPluginConfigSel', pluginConfigSel)
      .set('selectedPageId', null);
  }
  selectSettings(settingsSel: any) {
    return this.set('selectedPluginConfigSel', null)
      .set('selectedNavComponentSel', null)
      .set('selectedSettingsSel', settingsSel)
      .set('selectedPageId', null);
  }
  selectNavComponent(navComponentSel: any[]) {
    return this.set('selectedPluginConfigSel', null)
      .set('selectedNavComponentSel', navComponentSel)
      .set('selectedPageId', null);
  }
  selectPage(pageId: string) {
    return this.set('selectedPluginConfigSel', null).set('selectedNavComponentSel', null).set('selectedPageId', pageId);
  }

  selectPageType(pageType: string) {
    return this.set('selectedPageType', pageType);
  }

  deselectPlugin() {
    return this.set('selectedPluginConfigSel', null);
  }

  openOneTab(key: keyof EditorStateParams) {
    const editorTabKeys: Array<keyof EditorStateParams> = [
      'isTilesBrowserOpen',
      'isPluginListingOpen',
      'isThemeEditorOpen',
      'isPropertyInspectorOpen',
    ];

    let result = this;
    for (let index = 0; index < editorTabKeys.length; index++) {
      result = result.set(editorTabKeys[index], false);
    }

    return result.set(key, true);
  }

  recordBindingError(binding: string, payload: BindingError) {
    if (!this.bindingErrors.has(binding)) {
      return this.set('bindingErrors', this.bindingErrors.set(binding, payload));
    } else {
      return this;
    }
  }

  deleteBindingError(binding: string) {
    return this.set('bindingErrors', this.bindingErrors.delete(binding));
  }

  openPropertyInspector() {
    return this.openOneTab('isPropertyInspectorOpen');
  }
  openPluginListing() {
    return this.openOneTab('isPluginListingOpen');
  }
  openThemeEditor() {
    return this.openOneTab('isThemeEditorOpen');
  }
  openTilesBrowser() {
    return this.openOneTab('isTilesBrowserOpen');
  }

  registerPlugins(pluginsList: PluginSubType[]) {
    let registeredPlugins = this.registeredPlugins;
    pluginsList.forEach(pluginType => {
      registeredPlugins = registeredPlugins.add(pluginType);
    });
    return this.set('registeredPlugins', registeredPlugins);
  }
  openModuleCreationDialog() {
    return this.setIn(['moduleCreationParams', 'isOpen'], true);
  }
  closeModuleCreationDialog() {
    return this.setIn(['moduleCreationParams', 'isOpen'], false);
  }
  setModuleCreationParams(params: Partial<ModuleCreationParams>) {
    return this.mergeIn(['moduleCreationParams'], params);
  }

  setActiveAttachmentId(attahcmentId: string) {
    return this.set('activeAttachmentId', attahcmentId);
  }

  setActiveAttachmentKey(attachmentKey: string) {
    return this.set('activeAttachmentKey', attachmentKey);
  }

  toggleBindingError() {
    const currentValue = this.showBindingErrors;
    if (process.env.IS_WEB_ENV) {
      if (!currentValue) {
        localStorage.setItem('showBindingErrors', 'true');
      } else {
        localStorage.setItem('showBindingErrors', '');
      }
    }

    return this.set('showBindingErrors', !currentValue);
  }
}
