import standardScreens from './screenConstants';

export const AI_TILE_PAGE_ID = 'AITILETEMPPAGE';

const CommonTileTags = {
  'image-banner': 'Image',
  'video-banner': 'Video',
};

export const TileTagsNavigation = {
  //'': 'All',
  products: 'Products',
  collections: 'Collections',

  blogs: 'Blog',
  // countdown: 'Countdown',
  // faqs: 'FAQs',
  ...CommonTileTags,
  others: 'Others',
};

export const NonDataSourceTileIntegrations = ['nativeBlogs', 'whatsAppLauncher'];

type ScreenSpecificTagMap = {
  [K in (typeof standardScreens)[number]]: {
    [key: string]: string;
  };
};
type ScreenSpecificCommon = {
  [K in (typeof standardScreens)[number]]: boolean;
};

const screenNameToCommon: Partial<ScreenSpecificCommon> = {
  Product: true,
  Collection: true,
  Menu: true,
  ContactUs: true,
};
const screenNameToTagMap: Partial<ScreenSpecificTagMap> = {
  Product: {
    pdp: 'PDP',
  },
  Collection: {
    plp: 'PLP',
  },
  Menu: {
    menu: 'Menu',
  },
  Wishlist: {
    wishlist: 'Wishlist',
  },
  ContactUs: {
    'contact-us': 'Contact us',
  },
  Login: {
    login: 'Login',
  },
  ResetPassword: {
    'reset-password': 'Reset Password',
  },
  Register: {
    register: 'Register',
  },
  PrivacyPolicy: {
    'web-pages': 'Privacy Policy',
    others: 'Others',
  },
  TermsOfService: {
    'web-pages': 'Terms of service',
    others: 'Others',
  },
  AboutUs: {
    'web-pages': 'About us',
    others: 'Others',
  },
  RefundPolicy: {
    'web-pages': 'Refund Policy',
    others: 'Others',
  },
};

export function getMappedTileTagsForScreen(screenName: string) {
  let tags = {...screenNameToTagMap[screenName], ...(screenNameToCommon[screenName] ? CommonTileTags : {})} ?? {};
  return tags;
}
