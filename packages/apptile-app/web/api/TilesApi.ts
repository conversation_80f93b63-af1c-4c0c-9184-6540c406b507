import {AxiosPromise} from 'axios';
import _ from 'lodash';
import {Api} from './Api';
import {ITileSaveInterface, ITilesListResponse, ITileUpdateInterface, ITileVariantSaveInterface} from './ApiTypes';

export default class TilesApi {
  static baseURL = '/api/tiles';
  static getApiUrl() {
    return Api.API_SERVER + TilesApi.baseURL;
  }

  static getTemplate(uuid: string): AxiosPromise<any> {
    return Api.get(TilesApi.getApiUrl() + `/${uuid}`);
  }
  static getTileStatus(uuid: string): AxiosPromise<any> {
    return Api.get(TilesApi.getApiUrl() + `/status/${uuid}`);
  }
  static getTileVariants(uuid: string, hidden?: boolean): AxiosPromise<any> {
    return Api.get(TilesApi.getApiUrl() + `/variants/${uuid}${hidden ? '?hidden=true' : ''}`);
  }
  static getTiles(tags: string[], offset = 0, limit = 50): AxiosPromise<ITilesListResponse> {
    return Api.get(TilesApi.getApiUrl(), {params: {tags: _.each(tags, _.toUpper).join(','), offset, limit}});
  }

  static createTileTemplate(tileSaveRecord: ITileSaveInterface): AxiosPromise<any> {
    return Api.post(TilesApi.getApiUrl(), tileSaveRecord, {withCredentials: true});
  }
  static updateTileMeta(tileUpdateRecord: ITileUpdateInterface): AxiosPromise<any> {
    const {id: uuid, ...rest} = tileUpdateRecord;
    return Api.put(TilesApi.getApiUrl() + `/${uuid}`, rest, {withCredentials: true});
  }
  static updateTileTemplate(tileUpdateRecord: ITileUpdateInterface): AxiosPromise<any> {
    const {oldId: uuid} = tileUpdateRecord;
    return Api.post(TilesApi.getApiUrl() + `/${uuid}/version`, tileUpdateRecord, {withCredentials: true});
  }

  static createTileVariantTemplate(variantSaveRecord: ITileVariantSaveInterface): AxiosPromise<any> {
    const {tileId: uuid} = variantSaveRecord;
    return Api.post(TilesApi.getApiUrl() + `/${uuid}/variant`, variantSaveRecord, {withCredentials: true});
  }
  static updateTileVariantMeta(variantUpdateRecord: Omit<ITileVariantSaveInterface, 'data'>): AxiosPromise<any> {
    const {tileId: uuid, id: variantUUID} = variantUpdateRecord;
    return Api.put(TilesApi.getApiUrl() + `/${uuid}/variant/${variantUUID}`, variantUpdateRecord, {
      withCredentials: true,
    });
  }
  static updateTileVariantTemplate(variantUpdateRecord: ITileVariantSaveInterface): AxiosPromise<any> {
    const {tileId: uuid, id: variantUUID} = variantUpdateRecord;
    return Api.post(TilesApi.getApiUrl() + `/${uuid}/variant/${variantUUID}/version`, variantUpdateRecord, {
      withCredentials: true,
    });
  }
  static createOrgTileVariantTemplate(variantSaveRecord: ITileVariantSaveInterface): AxiosPromise<any> {
    const {tileId: uuid} = variantSaveRecord;
    return Api.post(TilesApi.getApiUrl() + `/${uuid}/orgVariant`, variantSaveRecord, {withCredentials: true});
  }
  static updateOrgTileVariantTemplate(variantUpdateRecord: ITileVariantSaveInterface): AxiosPromise<any> {
    const {tileId: uuid, id: variantUUID} = variantUpdateRecord;
    return Api.post(TilesApi.getApiUrl() + `/${uuid}/orgVariant/${variantUUID}/version`, variantUpdateRecord, {
      withCredentials: true,
    });
  }
}
