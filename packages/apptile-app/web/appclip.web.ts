import {shouldInitWebLogrocket} from './globalvariables';
import 'react-native-gesture-handler';
import {AppRegistry} from 'react-native';
import {name as appName} from '../app.json';
import {setAppConstants} from 'apptile-core';
import queryString from 'query-string';
import livelyScript from './lively.txt';
import shoppableFeedScript from './lively-shoppableFeed.txt';
import WebAppClip from './WebAppClip';

console.log('glvarcheck', shouldInitWebLogrocket);
const parsedURL = queryString.parseUrl(window.location.href);
const {appId, screenName, params: rawParams} = parsedURL?.query ?? {};

setAppConstants({APPTILE_APP_ID: appId});

const initialProps: Record<string, any> = {};
if (screenName) initialProps.screenName = screenName;

if (rawParams) {
  try {
    initialProps.params = typeof rawParams === 'string' ? JSON.parse(rawParams) : rawParams;
  } catch (error) {
    console.error('Failed to parse params:', error);
    initialProps.params = rawParams;
  }
}

// Register and run the app
AppRegistry.registerComponent(appName, () => WebAppClip);
AppRegistry.runApplication(appName, {
  initialProps,
  rootTag: document.getElementById('pre-root'),
});

window.reloadLively = new Function(livelyScript);
window.reloadShoppableFeeds = new Function(shoppableFeedScript);
