import {AppConfig, ModuleEditorConfig, PageConfig, PluginConfig} from 'apptile-core';
import {immutableDeepEqualsFn} from 'apptile-core';
import {NAMESPACE_SPERATOR} from 'apptile-core';
import {GetRegisteredPluginInfo} from 'apptile-core';
import {pagePluginsSelector, selectAppConfig} from 'apptile-core';
import Immutable from 'immutable';
import _ from 'lodash';
import {createSelector} from 'reselect';
import {getScreensInNavConfig} from './EditorSelectors';

const EMPTY_LIST = Immutable.List<PluginConfig>();
export const selectModulePluginsInApp = createSelector(
  pagePluginsSelector,
  (pagePlugins: Immutable.List<PluginConfig>): Immutable.List<PluginConfig> =>
    pagePlugins?.filter(pluginConfig => pluginConfig.subtype === 'ModuleInstance') ?? EMPTY_LIST,
  {
    memoizeOptions: {
      resultEqualityCheck: immutableDeepEqualsFn,
    },
  },
);

export const selectModulePluginsInPage = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  (appConfig?: AppConfig) => {
    const pagePluginsMap = {};
    Object.entries(appConfig?.pages?.toJS()).map(([pageName, page]) => ({
      [pageName]: Object.values(page.plugins).forEach(plugin =>
        plugin.subtype === 'ModuleInstance'
          ? pagePluginsMap[pageName]
            ? pagePluginsMap[pageName].push(plugin)
            : (pagePluginsMap[pageName] = [plugin])
          : null,
      ),
    }));
    return pagePluginsMap;
  },
  {
    memoizeOptions: {
      resultEqualityCheck: immutableDeepEqualsFn,
    },
  },
);

const pluginMandatoryCheck = (pluginConfig: PluginConfig, appConfig: AppConfig, pageConfig: PageConfig): boolean => {
  let check = false;
  if (pluginConfig?.subtype == 'ModuleInstance') {
    const moduleUUID = pluginConfig?.config?.get('moduleUUID');
    const moduleRecord = appConfig?.modules?.get(moduleUUID);
    const editors = moduleRecord?.editors;
    const basicEditors = moduleRecord?.basicEditors;
    const styleEditors = moduleRecord?.styleEditors;
    let mandatoryEditors: Immutable.OrderedMap<string, ModuleEditorConfig> = Immutable.OrderedMap();
    styleEditors?.map((editorRecord: ModuleEditorConfig, key: string) => {
      if (editorRecord.mandatory) {
        mandatoryEditors = mandatoryEditors.set(key, editorRecord);
      }
    });
    basicEditors?.map((editorRecord: ModuleEditorConfig, key: string) => {
      if (editorRecord.mandatory) {
        mandatoryEditors = mandatoryEditors.set(key, editorRecord);
      }
    });
    editors?.map((editorRecord: ModuleEditorConfig, key: string) => {
      if (editorRecord.mandatory) {
        mandatoryEditors = mandatoryEditors.set(key, editorRecord);
      }
    });
    const childNamespace = pluginConfig?.config?.get('childNamespace');
    const pagePlugins = pageConfig?.plugins;
    mandatoryEditors?.map((editorRecord: ModuleEditorConfig) => {
      const selector = _.clone(editorRecord.selector);
      selector[0] = childNamespace + NAMESPACE_SPERATOR + selector[0];
      const currentPlugin = pagePlugins?.get(selector[0]);
      const editorType = editorRecord?.editorType;
      let defaultValueCheck = false;
      if (editorType?.type == 'assetEditor') {
        const baseSelector = selector.slice(0, -1);
        const sourceTypeProperty = pagePlugins?.getIn([...baseSelector, editorType?.props?.sourceTypeProperty]);
        if (sourceTypeProperty?.toLowerCase() == 'url') {
          selector[selector.length - 1] = editorType?.props?.urlProperty;
        } else {
          selector[selector.length - 1] = editorType?.props?.assetProperty;
        }
      }
      const storedValue = pagePlugins?.getIn(selector);
      if (editorType?.type == 'formatInput') {
        if (editorType.props.prefix + editorType.props.suffix == storedValue) defaultValueCheck = true;
      }

      if (editorType?.type == 'listEditor') {
        storedValue?.map((e: any) => {
          if (e.sourceType?.toLowerCase() == 'url') {
            if (!e.url) defaultValueCheck = true;
          } else {
            if (!e.assetId) defaultValueCheck = true;
          }
          if (!e.navEntityId || !e.navEntityType || !e.resizeMode || !e.title) {
            defaultValueCheck = true;
          }
        });
      }

      if (currentPlugin?.type == 'widget') {
        const widgetType = currentPlugin?.subtype;
        const pluginConfig = GetRegisteredPluginInfo(widgetType)?.config();
        if (pluginConfig) {
          if (pluginConfig.getIn(selector.slice(selector.indexOf('config') + 1)) == storedValue)
            defaultValueCheck = true;
        }
      }
      if (!storedValue || defaultValueCheck) check = true;
    });
  }
  return check;
};

export const selectMandatoryCheck = () =>
  createSelector(selectAppConfig, (appConfig: AppConfig) => {
    const mandatoryField: any = {check: false, pages: {}};
    if (appConfig) {
      const pages = appConfig?.pages;
      const pageIds = pages?.keySeq()?.toArray();
      for (let i in pageIds) {
        const pageId = pageIds[i];
        const pageConfig = pages.get(pageId);
        if (pageConfig) {
          const pluginIds = pageConfig?.plugins?.keySeq()?.toArray();
          for (let j in pluginIds) {
            const pluginId = pluginIds[j];
            const pluginConfig = pageConfig?.plugins.get(pluginId);
            if (pluginConfig) {
              const rootNavigator = appConfig?.navigation.rootNavigator;
              const screens = rootNavigator ? getScreensInNavConfig(rootNavigator) : [];
              const currentCheck = pluginMandatoryCheck(pluginConfig, appConfig, pageConfig);
              if (currentCheck) {
                if (mandatoryField.pages[pageId]) mandatoryField.pages[pageId].plugins.push(pluginConfig?.id);
                else if (screens.find(e => e.screen == pageId)) {
                  mandatoryField.check = currentCheck;
                  mandatoryField.pages[pageId] = {
                    screen: screens.find(e => e.screen == pageId)?.title ?? pageId,
                    plugins: [pluginConfig?.id],
                  };
                }
              }
            }
          }
        }
      }
    }
    return mandatoryField;
  });

export const selectModuleMandatoryCheck = (pageId: string, pluginId: string) =>
  createSelector(selectAppConfig, (appConfig: AppConfig) => {
    let check = false;
    const pageConfig = appConfig?.pages?.get(pageId);
    if (pageConfig) {
      const plugins = pageConfig?.plugins;
      const pluginConfig = plugins?.get(pluginId);
      if (pluginConfig) {
        check = pluginMandatoryCheck(pluginConfig, appConfig, pageConfig);
        return check;
      }
    }
    return check;
  });
