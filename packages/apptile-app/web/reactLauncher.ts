import * as Sentry from "@sentry/react";
import 'react-native-gesture-handler';
import {AppRegistry} from 'react-native';

import {name as appName} from '../app.json';
import WebAppRoot from './WebApp';
import { SentryHelper, logger } from 'apptile-core';
logger.info("Logger initialized");

export function main() {
  SentryHelper.init();
  AppRegistry.registerComponent(appName, () => WebAppRoot);

  AppRegistry.runApplication(appName, {
    initialProps: {},
    rootTag: document.getElementById('app-root'),
  });
}
