import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {
  selectAppSettingsForKey,
  BrandSettingsTypes,
  HeaderSettingsTypes,
  modelUpdateAction,
  ScreenConfigParams,
  DispatchActions,
  selectAppConfig,
} from 'apptile-core';
import CollapsiblePanel from '../../components/CollapsiblePanel';
import {useDispatch, useSelector} from 'react-redux';
import {updateSettingsValue} from 'apptile-core';
import _ from 'lodash';
import {
  GLOBAL_LOADER_SETTINGS_KEY,
  LOADER_SETTINGS_GLOBAL_APPLOADER_KEY,
  PDP_BORDER_RADIUS,
  PDP_HIDE_WISHLIST,
  PDP_IMAGE_ASPECT_RATIO_KEY,
  PDP_IMAGE_RESIZE_KEY,
  PDP_SETTINGS_KEY,
  PLP_CARD_HEIGHT_KEY,
  P<PERSON>_IMAGE_ASPECT_RATIO_KEY,
  P<PERSON>_NUM_COLS_KEY,
  PLP_SETTINGS_KEY,
} from 'apptile-core';
import {SettingsConfig} from 'apptile-core';
import LoaderIdSelectorControl from '../../components/controls/LoaderIdSelectorControl';
import CodeInputControl from '../../components/controls/CodeInputControl';
import RadioGroupControl from '../../components/controls/RadioGroupControl';
import CheckboxControl from '../../components/controls/CheckboxControl';
import Button from '../../components-v2/base/Button';
import {saveAppState} from '../../actions/editorActions';
import {selectScreensInNav} from '../../selectors/EditorSelectors';
import DropDownControl from '../../components/controls/DropDownControl';
import IconChooserControl from '../../components/controls/IconChooserControl';
import ForceUpdateBanner from './ForceUpdateBanner';
import { EditorRootState } from '../../store/EditorRootState';
const BRAND_LOGO_WIDTH = BrandSettingsTypes.BRAND_LOGO_WIDTH,
  BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY,
  BRAND_TILE_TAG = BrandSettingsTypes.BRAND_TILE_TAG,
  BRAND_TILE_TAG_DISPLAY = BrandSettingsTypes.BRAND_TILE_TAG_DISPLAY,
  BRAND_EXPOSED_TILES = BrandSettingsTypes.BRAND_EXPOSED_TILES;
const {
  HEADER_SETTINGS_KEY,
  HEADER_TEMPLATE_ID,
  HEADER_LOGO_RESIZE,
  HEADER_LEFT_ICON,
  HEADER_LEFT_ICON_SHOW,
  HEADER_LEFT_ICON_NAVIGATION,
  HEADER_RIGHT_ICON,
  HEADER_RIGHT_ICON_SHOW,
  HEADER_RIGHT_ICON_NAVIGATION,
  HEADER_CART_ICON_SHOW,
  HEADER_WISHLIST_ICON_SHOW,
  HEADER_CART_ICON,
  HEADER_WISHLIST_ICON,
  HEADER_CART_ICON_NAVIGATION,
  HEADER_WISHLIST_ICON_NAVIGATION,
  HEADER_ICON_SIZE,
} = HeaderSettingsTypes;

const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
const GlobalAppSettingsEditor: React.FC = props => {
  const appConfig = useSelector(selectAppConfig);
  const loaderSettings: SettingsConfig = useSelector(settingsSelector(GLOBAL_LOADER_SETTINGS_KEY));
  const appLoaderPageId: string | null = loaderSettings.getSettingValue(LOADER_SETTINGS_GLOBAL_APPLOADER_KEY);

  // PLP Settings
  const plpSettings: SettingsConfig = useSelector(settingsSelector(PLP_SETTINGS_KEY));
  const plpImageAspectRatio: string | null = plpSettings.getSettingValue(PLP_IMAGE_ASPECT_RATIO_KEY);
  const plpCardHeight: string | null = plpSettings.getSettingValue(PLP_CARD_HEIGHT_KEY);
  const plpNoOfCols: string | null = plpSettings.getSettingValue(PLP_NUM_COLS_KEY);

  // Brand Settings
  const pdpSettings: SettingsConfig = useSelector(settingsSelector(PDP_SETTINGS_KEY));
  const pdpImageAspectRatio: string | null = pdpSettings.getSettingValue(PDP_IMAGE_ASPECT_RATIO_KEY);
  const pdpImageResize: string | null = pdpSettings.getSettingValue(PDP_IMAGE_RESIZE_KEY);
  const pdpHideWishlist: boolean = pdpSettings.getSettingValue(PDP_HIDE_WISHLIST) ?? false;
  const pdpBorderRadius: string | null = pdpSettings.getSettingValue(PDP_BORDER_RADIUS);

  // Brand Settings
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const brandLogoSize: string | null = brandSettings.getSettingValue(BRAND_LOGO_WIDTH);
  const brandTileTag: string | null = brandSettings.getSettingValue(BRAND_TILE_TAG);
  const brandTileTagDisplay: string | null = brandSettings.getSettingValue(BRAND_TILE_TAG_DISPLAY);
  const brandExposedTiles: string | null = brandSettings.getSettingValue(BRAND_EXPOSED_TILES);

  // Header Settings
  const headerSettings: SettingsConfig = useSelector(settingsSelector(HEADER_SETTINGS_KEY));
  const headerTemplateId: number = headerSettings.getSettingValue(HEADER_TEMPLATE_ID) ?? 0;
  const headerLogoResize: string | null = headerSettings.getSettingValue(HEADER_LOGO_RESIZE) ?? 'contain';
  const headerLeftIcon: string | null = headerSettings.getSettingValue(HEADER_LEFT_ICON) ?? '';
  const headerLeftIconShow: boolean = headerSettings.getSettingValue(HEADER_LEFT_ICON_SHOW) ?? false;
  const headerLeftIconNavigation: string | null = headerSettings.getSettingValue(HEADER_LEFT_ICON_NAVIGATION);
  const headerRightIcon: string | null = headerSettings.getSettingValue(HEADER_RIGHT_ICON) ?? '';
  const headerRightIconShow: boolean = headerSettings.getSettingValue(HEADER_RIGHT_ICON_SHOW) ?? false;
  const headerRightIconNavigation: string | null = headerSettings.getSettingValue(HEADER_RIGHT_ICON_NAVIGATION);
  const headerCartIconShow: boolean = headerSettings.getSettingValue(HEADER_CART_ICON_SHOW) ?? false;
  const headerWishlistIconShow: boolean = headerSettings.getSettingValue(HEADER_WISHLIST_ICON_SHOW) ?? false;
  const headerCartIcon: string | null = headerSettings.getSettingValue(HEADER_CART_ICON) ?? '';
  const headerWishlistIcon: string | null = headerSettings.getSettingValue(HEADER_WISHLIST_ICON) ?? '';
  const headerCartIconNavigation: string | null = headerSettings.getSettingValue(HEADER_CART_ICON_NAVIGATION);
  const headerWishlistIconNavigation: string | null = headerSettings.getSettingValue(HEADER_WISHLIST_ICON_NAVIGATION);
  const headerIconSize: string | null = headerSettings.getSettingValue(HEADER_ICON_SIZE) ?? '20';

  const dispatch = useDispatch();

  const [appLoaderId, setAppLoaderId] = useState(appLoaderPageId);

  // PLP States
  const [plpImageAspect, setPLPImageAspect] = useState(plpImageAspectRatio);
  const [plpCardHeightValue, setPLPCardHeightValue] = useState(plpCardHeight);
  const [plpNoOfCol, setPLPNoOfCol] = useState(plpNoOfCols);

  // PDP States
  const [pdpImageAspect, setPDPImageAspect] = useState(pdpImageAspectRatio);
  const [pdpImageResizeMode, setPDPImageResize] = useState(pdpImageResize);
  const [pdpHideWishlistIcon, setPDPHideWishlist] = useState(pdpHideWishlist);
  const [pdpBorderRadiusValue, setPDPBorderRadius] = useState(pdpBorderRadius);

  // Brand States
  const [brandLogoWidth, setBrandLogoWidth] = useState(brandLogoSize);
  const [brandTileSpecificTag, setBrandTileSpecificTag] = useState(brandTileTag);
  const [brandTileSpecificTagDisplay, setBrandTileSpecificTagDisplay] = useState(brandTileTagDisplay);
  const [brandSpecificTiles, setBrandSpecificTiles] = useState(brandExposedTiles);

  //Header States
  const [headerTemplateValue, setHeaderTemplate] = useState(headerTemplateId);
  const [headerLogoResizeValue, setHeaderLogoResize] = useState(headerLogoResize);
  const [headerLeftIconValue, setHeaderLeftIcon] = useState(headerLeftIcon);
  const [headerLeftIconShowValue, setHeaderLeftIconShow] = useState(headerLeftIconShow);
  const [headerLeftIconNavigationValue, setHeaderLeftIconNavigation] = useState(headerLeftIconNavigation);
  const [headerRightIconValue, setHeaderRightIcon] = useState(headerRightIcon);
  const [headerRightIconShowValue, setHeaderRightIconShow] = useState(headerRightIconShow);
  const [headerRightIconNavigationValue, setHeaderRightIconNavigation] = useState(headerRightIconNavigation);
  const [headerCartIconShowValue, setHeaderCartIconShow] = useState(headerCartIconShow);
  const [headerWishlistIconShowValue, setHeaderWishlistIconShow] = useState(headerWishlistIconShow);
  const [headerCartIconValue, setHeaderCartIcon] = useState(headerCartIcon);
  const [headerWishlistIconValue, setHeaderWishlistIcon] = useState(headerWishlistIcon);
  const [headerCartIconNavigationValue, setHeaderCartIconNavigation] = useState(headerCartIconNavigation);
  const [headerWishlistIconNavigationValue, setHeaderWishlistIconNavigation] = useState(headerWishlistIconNavigation);
  const [headerIconSizeValue, setHeaderIconSize] = useState(headerIconSize);

  useEffect(() => {
    if (appLoaderId !== appLoaderPageId) {
      setAppLoaderId(appLoaderPageId);
    }
  }, [appLoaderPageId]);
  useEffect(() => {
    if (plpImageAspect !== plpImageAspectRatio) {
      setPLPImageAspect(plpImageAspectRatio);
    }
  }, [plpImageAspectRatio]);
  useEffect(() => {
    if (plpCardHeightValue !== plpCardHeight) {
      setPLPCardHeightValue(plpCardHeight);
    }
  }, [plpCardHeight]);
  useEffect(() => {
    if (plpNoOfCol !== plpNoOfCols) {
      setPLPNoOfCol(plpNoOfCols);
    }
  }, [plpNoOfCols]);
  useEffect(() => {
    if (pdpImageAspect !== pdpImageAspectRatio) {
      setPDPImageAspect(pdpImageAspectRatio);
    }
  }, [pdpImageAspectRatio]);
  useEffect(() => {
    if (pdpImageResizeMode !== pdpImageResize) {
      setPDPImageResize(pdpImageResize);
    }
  }, [pdpImageResize]);
  useEffect(() => {
    if (pdpHideWishlistIcon !== pdpHideWishlist) {
      setPDPHideWishlist(pdpHideWishlist);
    }
  }, [pdpHideWishlist]);
  useEffect(() => {
    if (pdpBorderRadiusValue !== pdpBorderRadius) {
      setPDPBorderRadius(pdpBorderRadius);
    }
  }, [pdpBorderRadius]);
  useEffect(() => {
    if (brandLogoWidth !== brandLogoSize) {
      setBrandLogoWidth(brandLogoSize);
    }
  }, [brandLogoSize]);
  useEffect(() => {
    if (brandTileSpecificTag !== brandTileTag) {
      setBrandTileSpecificTag(brandTileTag);
    }
  }, [brandTileTag]);
  useEffect(() => {
    if (brandTileSpecificTagDisplay !== brandTileTagDisplay) {
      setBrandTileSpecificTagDisplay(brandTileTagDisplay);
    }
  }, [brandTileTagDisplay]);
  useEffect(() => {
    if (brandSpecificTiles !== brandExposedTiles) {
      setBrandSpecificTiles(brandExposedTiles);
    }
  }, [brandExposedTiles]);

  // Synchronize header settings
  useEffect(() => {
    if (headerTemplateValue !== headerTemplateId) {
      setHeaderTemplate(headerTemplateId);
    }
  }, [headerTemplateId]);

  useEffect(() => {
    if (headerLogoResizeValue !== headerLogoResize) {
      setHeaderLogoResize(headerLogoResize);
    }
  }, [headerLogoResize]);
  useEffect(() => {
    if (headerLeftIconValue !== headerLeftIcon) {
      setHeaderLeftIcon(headerLeftIcon);
    }
  }, [headerLeftIcon]);

  useEffect(() => {
    if (headerLeftIconShowValue !== headerLeftIconShow) {
      setHeaderLeftIconShow(headerLeftIconShow);
    }
  }, [headerLeftIconShow]);

  useEffect(() => {
    if (headerLeftIconNavigationValue !== headerLeftIconNavigation) {
      setHeaderLeftIconNavigation(headerLeftIconNavigation);
    }
  }, [headerLeftIconNavigation]);

  useEffect(() => {
    if (headerRightIconValue !== headerRightIcon) {
      setHeaderRightIcon(headerRightIcon);
    }
  }, [headerRightIcon]);

  useEffect(() => {
    if (headerRightIconShowValue !== headerRightIconShow) {
      setHeaderRightIconShow(headerRightIconShow);
    }
  }, [headerRightIconShow]);

  useEffect(() => {
    if (headerRightIconNavigationValue !== headerRightIconNavigation) {
      setHeaderRightIconNavigation(headerRightIconNavigation);
    }
  }, [headerRightIconNavigation]);

  useEffect(() => {
    if (headerCartIconShowValue !== headerCartIconShow) {
      setHeaderCartIconShow(headerCartIconShow);
    }
  }, [headerCartIconShow]);

  useEffect(() => {
    if (headerWishlistIconShowValue !== headerWishlistIconShow) {
      setHeaderWishlistIconShow(headerWishlistIconShow);
    }
  }, [headerWishlistIconShow]);

  useEffect(() => {
    if (headerCartIconValue !== headerCartIcon) {
      setHeaderCartIcon(headerCartIcon);
    }
  }, [headerCartIcon]);

  useEffect(() => {
    if (headerWishlistIconValue !== headerWishlistIcon) {
      setHeaderWishlistIcon(headerWishlistIcon);
    }
  }, [headerWishlistIcon]);

  useEffect(() => {
    if (headerCartIconNavigationValue !== headerCartIconNavigation) {
      setHeaderCartIconNavigation(headerCartIconNavigation);
    }
  }, [headerCartIconNavigation]);

  useEffect(() => {
    if (headerWishlistIconNavigationValue !== headerWishlistIconNavigation) {
      setHeaderWishlistIconNavigation(headerWishlistIconNavigation);
    }
  }, [headerWishlistIconNavigation]);
  useEffect(() => {
    if (headerIconSizeValue !== headerIconSize) {
      setHeaderIconSize(headerIconSize);
    }
  }, [headerIconSize]);

  const onAppLoaderUpdated = useCallback(
    newAppLoaderId => {
      setAppLoaderId(newAppLoaderId);
      dispatch(updateSettingsValue(GLOBAL_LOADER_SETTINGS_KEY, LOADER_SETTINGS_GLOBAL_APPLOADER_KEY, newAppLoaderId));
    },
    [dispatch],
  );
  const onPLPAspectUpdate = useCallback(
    value => {
      setPLPImageAspect(`${!Number(value) ? '' : Number(value)}`);
      dispatch(
        updateSettingsValue(PLP_SETTINGS_KEY, PLP_IMAGE_ASPECT_RATIO_KEY, `${!Number(value) ? '' : Number(value)}`),
      );
    },
    [dispatch],
  );
  const onPLPHeightUpdate = useCallback(
    value => {
      setPLPCardHeightValue(`${!Number(value) ? '' : Number(value)}`);
      dispatch(updateSettingsValue(PLP_SETTINGS_KEY, PLP_CARD_HEIGHT_KEY, `${!Number(value) ? '' : Number(value)}`));
    },
    [dispatch, setPLPCardHeightValue],
  );
  const onPLPColsUpdate = useCallback(
    value => {
      setPLPNoOfCol(`${!Number(value) ? '' : Number(value)}`);
      dispatch(updateSettingsValue(PLP_SETTINGS_KEY, PLP_NUM_COLS_KEY, `${!Number(value) ? '' : Number(value)}`));
    },
    [dispatch],
  );
  const onPDPAspectUpdate = useCallback(
    value => {
      setPDPImageAspect(`${!Number(value) ? '' : Number(value)}`);
      dispatch(
        updateSettingsValue(PDP_SETTINGS_KEY, PDP_IMAGE_ASPECT_RATIO_KEY, `${!Number(value) ? '' : Number(value)}`),
      );
    },
    [dispatch],
  );
  const onPDPResizeUpdate = useCallback(
    value => {
      setPDPImageResize(value);
      dispatch(updateSettingsValue(PDP_SETTINGS_KEY, PDP_IMAGE_RESIZE_KEY, value));
    },
    [dispatch],
  );
  const onPDPHideWishlistUpdate = useCallback(
    value => {
      setPDPHideWishlist(value);
      dispatch(updateSettingsValue(PDP_SETTINGS_KEY, PDP_HIDE_WISHLIST, value));
    },
    [dispatch],
  );
  const onPDPBorderRadiusUpdate = useCallback(
    value => {
      setPDPBorderRadius(value);
      dispatch(updateSettingsValue(PDP_SETTINGS_KEY, PDP_BORDER_RADIUS, value));
    },
    [dispatch],
  );
  const onBrandLogoSizeUpdate = _.debounce(value => {
    setBrandLogoWidth(value);
    dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_LOGO_WIDTH, value));
    const modelUpdate = [
      {
        selector: ['Apptile', 'brandLogoSize'],
        newValue: value,
      },
    ];
    dispatch(modelUpdateAction(modelUpdate, undefined, true));
  }, 400);
  const onBrandTileTagUpdate = _.debounce(value => {
    setBrandTileSpecificTag(value);
    dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_TILE_TAG, value));
  }, 400);
  const onBrandTileTagDisplayUpdate = _.debounce(value => {
    setBrandTileSpecificTagDisplay(value);
    dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_TILE_TAG_DISPLAY, value));
  }, 400);
  const onBrandExposedTilesUpdate = _.debounce(value => {
    setBrandSpecificTiles(value);
    dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_EXPOSED_TILES, value));
  }, 400);

  const onHeaderTemplateUpdate = useCallback(
    value => {
      setHeaderTemplate(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_TEMPLATE_ID, value));
    },
    [dispatch],
  );
  const onHeaderLogoResizeUpdate = useCallback(
    value => {
      setHeaderLogoResize(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_LOGO_RESIZE, value));
    },
    [dispatch],
  );
  const onHeaderLeftIconUpdate = useCallback(
    value => {
      setHeaderLeftIcon(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_LEFT_ICON, value));
    },
    [dispatch],
  );

  const onHeaderLeftIconShowUpdate = useCallback(
    value => {
      setHeaderLeftIconShow(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_LEFT_ICON_SHOW, value));
    },
    [dispatch],
  );

  const onHeaderLeftIconNavigationUpdate = useCallback(
    value => {
      setHeaderLeftIconNavigation(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_LEFT_ICON_NAVIGATION, value));
    },
    [dispatch],
  );

  const onHeaderRightIconUpdate = useCallback(
    value => {
      setHeaderRightIcon(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_RIGHT_ICON, value));
    },
    [dispatch],
  );

  const onHeaderRightIconShowUpdate = useCallback(
    value => {
      setHeaderRightIconShow(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_RIGHT_ICON_SHOW, value));
    },
    [dispatch],
  );

  const onHeaderRightIconNavigationUpdate = useCallback(
    value => {
      setHeaderRightIconNavigation(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_RIGHT_ICON_NAVIGATION, value));
    },
    [dispatch],
  );

  const onHeaderCartIconShowUpdate = useCallback(
    value => {
      setHeaderCartIconShow(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_CART_ICON_SHOW, value));
    },
    [dispatch],
  );

  const onHeaderWishlistIconShowUpdate = useCallback(
    value => {
      setHeaderWishlistIconShow(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_WISHLIST_ICON_SHOW, value));
    },
    [dispatch],
  );

  const onHeaderCartIconUpdate = useCallback(
    value => {
      setHeaderCartIcon(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_CART_ICON, value));
    },
    [dispatch],
  );

  const onHeaderWishlistIconUpdate = useCallback(
    value => {
      setHeaderWishlistIcon(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_WISHLIST_ICON, value));
    },
    [dispatch],
  );

  const onHeaderCartIconNavigationUpdate = useCallback(
    value => {
      setHeaderCartIconNavigation(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_CART_ICON_NAVIGATION, value));
    },
    [dispatch],
  );

  const onHeaderWishlistIconNavigationUpdate = useCallback(
    value => {
      setHeaderWishlistIconNavigation(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_WISHLIST_ICON_NAVIGATION, value));
    },
    [dispatch],
  );
  const onHeaderIconSizeUpdate = useCallback(
    value => {
      setHeaderIconSize(value);
      dispatch(updateSettingsValue(HEADER_SETTINGS_KEY, HEADER_ICON_SIZE, value));
    },
    [dispatch],
  );
  const onBlueprintUpdate = useCallback(
    value => {
      let newAppConfig = appConfig?.set('blueprintUUID', value);
      dispatch({
        type: DispatchActions.UPDATE_APP_CONFIG,
        payload: newAppConfig,
      });
    },
    [dispatch, appConfig],
  );

  const screens = (useSelector(selectScreensInNav) || []).map((s: ScreenConfigParams) => ({
    name: s.title,
    value: s.screen,
  }));


  return (
    <View style={styles.container}>
      <View style={{height: 'auto', flex: 1}}>
        <CollapsiblePanel title="App Settings" isOpen={false}>
          <View style={{overflow: 'scroll', paddingTop: 8}}>
            <CollapsiblePanel title="Header" isOpen={false}>
              <View style={{overflow: 'scroll', paddingTop: 8}}>
                <CodeInputControl
                  value={brandLogoWidth ?? ''}
                  onChange={onBrandLogoSizeUpdate}
                  defaultValue={''}
                  label={'Brand Logo Width'}
                />
                <DropDownControl
                  value={`${headerTemplateValue}` || '0'}
                  options={[
                    {value: '0', name: 'Default'},
                    {value: '1', name: 'Simpler'},
                  ]}
                  nameKey="name"
                  valueKey="value"
                  defaultValue={''}
                  disableBinding={true}
                  label={'Template'}
                  onChange={onHeaderTemplateUpdate}
                />
                <RadioGroupControl
                  value={headerLogoResizeValue ?? ''}
                  onChange={onHeaderLogoResizeUpdate}
                  options={[
                    {value: 'cover', text: 'fill'},
                    {value: 'contain', text: 'fit'},
                  ]}
                  label={'Logo Aspect Ratio'}
                  disableBinding={true}
                />
                <CodeInputControl
                  value={headerIconSizeValue ?? ''}
                  onChange={onHeaderIconSizeUpdate}
                  defaultValue={''}
                  label={'Icon Size'}
                />

                <CollapsiblePanel title="Left Icon" isOpen={false}>
                  <IconChooserControl
                    value={headerLeftIconValue?.iconName ?? ''}
                    config={{iconType: headerLeftIconValue?.iconType}}
                    onBulkValueChange={onHeaderLeftIconUpdate}
                    defaultValue={''}
                    label={'Left Icon'}
                  />
                  <CheckboxControl
                    value={headerLeftIconShowValue}
                    onChange={onHeaderLeftIconShowUpdate}
                    label={'Show Left Icon'}
                  />
                  <DropDownControl
                    label={'Left Icon Navigation'}
                    defaultValue={headerLeftIconNavigationValue}
                    value={headerLeftIconNavigationValue}
                    options={screens}
                    nameKey={'name'}
                    valueKey={'value'}
                    disableBinding={true}
                    onChange={onHeaderLeftIconNavigationUpdate}
                  />
                </CollapsiblePanel>
                <CollapsiblePanel title="Right Icon" isOpen={false}>
                  <IconChooserControl
                    value={headerRightIconValue?.iconName ?? ''}
                    config={{iconType: headerRightIconValue?.iconType}}
                    onBulkValueChange={onHeaderRightIconUpdate}
                    defaultValue={''}
                    label={'Right Icon'}
                  />
                  <CheckboxControl
                    value={headerRightIconShowValue}
                    onChange={onHeaderRightIconShowUpdate}
                    label={'Show Right Icon'}
                  />
                  <DropDownControl
                    label={'Right Icon Navigation'}
                    defaultValue={headerRightIconNavigationValue}
                    value={headerRightIconNavigationValue}
                    options={screens}
                    nameKey={'name'}
                    valueKey={'value'}
                    disableBinding={true}
                    onChange={onHeaderRightIconNavigationUpdate}
                  />
                </CollapsiblePanel>
                <CollapsiblePanel title="Cart Icon" isOpen={false}>
                  <CheckboxControl
                    value={headerCartIconShowValue}
                    onChange={onHeaderCartIconShowUpdate}
                    label={'Show Cart Icon'}
                  />
                  <IconChooserControl
                    value={headerCartIconValue?.iconName ?? ''}
                    config={{iconType: headerCartIconValue?.iconType}}
                    onBulkValueChange={onHeaderCartIconUpdate}
                    defaultValue={''}
                    label={'Cart Icon'}
                  />
                  <DropDownControl
                    label={'Cart Icon Navigation'}
                    defaultValue={headerCartIconNavigationValue}
                    value={headerCartIconNavigationValue}
                    options={screens}
                    nameKey={'name'}
                    valueKey={'value'}
                    disableBinding={true}
                    onChange={onHeaderCartIconNavigationUpdate}
                  />
                </CollapsiblePanel>
                <CollapsiblePanel title="Wishlist Icon" isOpen={false}>
                  <CheckboxControl
                    value={headerWishlistIconShowValue}
                    onChange={onHeaderWishlistIconShowUpdate}
                    label={'Show Wishlist Icon'}
                  />
                  <IconChooserControl
                    value={headerWishlistIconValue?.iconName ?? ''}
                    config={{iconType: headerWishlistIconValue?.iconType}}
                    onBulkValueChange={onHeaderWishlistIconUpdate}
                    defaultValue={''}
                    label={'Wishlist Icon'}
                  />
                  <DropDownControl
                    label={'Wishlist Icon Navigation'}
                    defaultValue={headerWishlistIconNavigationValue}
                    value={headerWishlistIconNavigationValue}
                    options={screens}
                    nameKey={'name'}
                    valueKey={'value'}
                    disableBinding={true}
                    onChange={onHeaderWishlistIconNavigationUpdate}
                  />
                </CollapsiblePanel>
              </View>
            </CollapsiblePanel>
            <CollapsiblePanel title="App Loaders" isOpen={false}>
              <View style={{overflow: 'scroll', paddingTop: 8}}>
                <LoaderIdSelectorControl
                  value={appLoaderId ?? ''}
                  onChange={onAppLoaderUpdated}
                  defaultValue={''}
                  label={'Global App Loader'}
                />
              </View>
            </CollapsiblePanel>
            <CollapsiblePanel title="PLP" isOpen={false}>
              <View style={{overflow: 'scroll', paddingTop: 8}}>
                <CodeInputControl
                  value={plpImageAspect ?? ''}
                  onChange={onPLPAspectUpdate}
                  defaultValue={''}
                  label={'Image Aspect Ratio'}
                />
                <CodeInputControl
                  value={plpCardHeightValue ?? ''}
                  onChange={onPLPHeightUpdate}
                  defaultValue={''}
                  label={'Card Height'}
                />
                <CodeInputControl
                  value={plpNoOfCol ?? ''}
                  onChange={onPLPColsUpdate}
                  defaultValue={''}
                  label={'No Of Columns'}
                />
              </View>
            </CollapsiblePanel>
            <CollapsiblePanel title="PDP" isOpen={false}>
              <View style={{overflow: 'scroll', paddingTop: 8}}>
                <CodeInputControl
                  value={pdpImageAspect ?? ''}
                  onChange={onPDPAspectUpdate}
                  defaultValue={''}
                  label={'Image Aspect Ratio'}
                />
                <RadioGroupControl
                  value={pdpImageResizeMode ?? ''}
                  onChange={onPDPResizeUpdate}
                  options={[
                    {value: 'cover', text: 'fill'},
                    {value: 'contain', text: 'fit'},
                  ]}
                  label={'Image Aspect Ratio'}
                  disableBinding={true}
                />
                <CheckboxControl
                  value={pdpHideWishlistIcon ?? false}
                  onChange={onPDPHideWishlistUpdate}
                  label={'Hide Wishlist'}
                />
                <CodeInputControl
                  value={pdpBorderRadiusValue ?? ''}
                  onChange={onPDPBorderRadiusUpdate}
                  defaultValue={''}
                  label={'CTA Border Radius'}
                />
                <CodeInputControl
                  value={appConfig?.get('blueprintUUID') ?? ''}
                  onChange={onBlueprintUpdate}
                  defaultValue={''}
                  label={'BlueprintId'}
                />
              </View>
            </CollapsiblePanel>
            <CollapsiblePanel title="Brand" isOpen={false}>
              <View style={{overflow: 'scroll', paddingTop: 8}}>
                <CodeInputControl
                  value={brandTileSpecificTag ?? ''}
                  onChange={onBrandTileTagUpdate}
                  defaultValue={''}
                  label={'Brand Specific Tag'}
                />
                <CodeInputControl
                  value={brandTileSpecificTagDisplay ?? ''}
                  onChange={onBrandTileTagDisplayUpdate}
                  defaultValue={''}
                  label={'Brand Tag Display Text'}
                />
                <CodeInputControl
                  value={brandSpecificTiles ?? ''}
                  onChange={onBrandExposedTilesUpdate}
                  defaultValue={''}
                  label={'Brand Exposed Tiles'}
                />
              </View>
            </CollapsiblePanel>
            <ForceUpdateBanner />
            <Button
              onPress={() => {
                if (confirm('Are you sure you want to force save?')) {
                  dispatch(saveAppState(false, true, 'FORCE SAVED', true, true));
                }
              }}>
              FORCE SAVE
            </Button>
          </View>
        </CollapsiblePanel>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    paddingHorizontal: 5,
    flexBasis: 'auto',
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },
});
export default GlobalAppSettingsEditor;
