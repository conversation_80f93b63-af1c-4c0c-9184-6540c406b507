import { GET_CART_DETAILS } from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/shoppingCart';
import { ICartLineItem } from '@/root/app/plugins/datasource/ShopifyV_22_10/types';
import _ from 'lodash';
import React, { useCallback, useEffect } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { datasourceTypeModelSel } from '../../../../apptile-core/selectors/AppModelSelector';
import { LINE_ITEM_TYPE, addLineItems, clearLineItems, setCart } from '../../actions/cartAssistActions';
import { EditorRootState } from '../../store/EditorRootState';
import theme from '../../styles-v2/theme';
import { TransfromCartDetails } from './utils';
import Tooltip from '../../components-v2/base/SimpleTooltip';

const Cart = () => {
  let queryRunner: any;
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const customer = useSelector((state: EditorRootState) => state.cartAssist.customer);
  const existingLineItems = useSelector((state: EditorRootState) => state.cartAssist.existingLineItems);
  const appId = useSelector((state: EditorRootState) => state.apptile.appId) as string;

  const cartId = customer?.metafields?.find((metafield: any) => metafield.key == 'smart_cart_id')?.value; // ! Fetch using admin API

  const shopifyModelSel = (state: EditorRootState) => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const ShopifyDSModel = shopifyModelSel ? shopifyModelSel(store.getState()) : null;
  if (!queryRunner) queryRunner = ShopifyDSModel?.get('queryRunner');

  const getCartDetailsCallback = useCallback(
    async (cartData: any) => {
      const cart = await TransfromCartDetails(cartData, queryRunner);
      dispatch(addLineItems(cart.lines, LINE_ITEM_TYPE.EXISTING));
      dispatch(setCart(cart));
    },
    [cartId, queryRunner, dispatch],
  );

  useEffect(() => {
    if(_.isEmpty(customer)) navigate(-1)
  }, [])

  useEffect(() => {
    dispatch(clearLineItems());
    cartId &&
      (async () => {
        try {
          const response = await queryRunner?.runQuery('query', GET_CART_DETAILS, {
            cartId: `gid://shopify/Cart/${cartId}`,
          });
          response?.data && getCartDetailsCallback(response.data);
        } catch (error) {
          console.log('ERROR IN FETCHING CART: ', error);
        }
      })();
  }, [customer, cartId]);

  if (!existingLineItems?.length || !cartId) {
    return (
      <View style={styles.noResults}>
        <Image style={styles.emptyImage} source={require('../../assets/images/snapshot-no-result.png')} />
        <Text style={styles.text}>No products found</Text>
      </View>
    );
  }

  return (
    <View style={styles.wrapper}>
      {existingLineItems.length > 0 && (
        <View style={{ flex: 1 }}>
          <Text style={[styles.text, {fontSize: 18}]}>Existing Products</Text>
          <View style={styles.existingItemsWrapper}>
            {existingLineItems.map((line: ICartLineItem, i) => (
              <View key={i} style={[styles.lineItemContainer]}>

                <Image resizeMethod="cover" source={line.variant.featuredImage} style={styles.lineItemImage} />

                <View style={{flexDirection: 'column', gap: 6, flex: 1}}>

                  <Tooltip
                    tooltip={line.variant.product.title}
                    position="bottom"
                    toolTipMenuStyles={{height: 'fit-content', right: 40, paddingVertical: 4}}
                    containerStyles={{ zIndex: 2 }}
                  >
                    <Text numberOfLines={1} ellipsizeMode='tail' style={[styles.text, {fontSize: 15}]}>{line.variant.product.title}</Text>
                  </Tooltip>

                  <View style={{ flexDirection: 'column', gap: 3, zIndex: 1 }}>
                    <Tooltip
                      tooltip={line.variant.title}
                      position="bottom"
                      toolTipMenuStyles={{height: 'fit-content', right: 40, paddingVertical: 4}}
                    >
                      <Text numberOfLines={1} ellipsizeMode='tail' style={[styles.text, {fontSize: 12, color: '#6B6B6B'}]}>{line.variant.title}</Text>
                    </Tooltip>
                    <Text style={[styles.text, {fontSize: 12, color: '#6B6B6B'}]}>Quantity: {line.quantity}</Text>
                  </View> 

                  <Text style={[styles.text, {fontSize: 15}]}>${line.variant.salePrice}</Text>

                </View>

              </View>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

export default Cart;

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    marginTop: 20,
    paddingHorizontal: 16
  },
  text: {
    fontFamily: theme.FONT_FAMILY,
  },
  emptyImage: {
    width: 150,
    height: 150,
  },
  noResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
  lineItemContainer: {
    maxHeight: 101,
    padding: 14,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: theme.INPUT_BORDER,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 14,
    width: '32%',
  },
  lineItemImage: {
    width: 56,
    height: 78,
    overflow: 'hidden',
    borderRadius: 4,
  },
  existingItemsWrapper: {
    flex: 1,
    alignContent: 'baseline',
    flexDirection: 'row',
    marginTop: 16,
    gap: 10,
    flexWrap: 'wrap',
    overflow: 'scroll',
  },
});
