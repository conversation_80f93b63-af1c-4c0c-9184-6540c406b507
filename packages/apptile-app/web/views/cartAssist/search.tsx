import { debounce } from 'lodash';
import theme from '../../styles-v2/theme';
import { I_CART_ASSIST_CUSTOMER } from './types';
import { useDispatch, useSelector } from 'react-redux';
import { CartAssistApi } from '../../api/CartAssistApi';
import commonStyles from '../../styles-v2/commonStyles';
import { TransformCustomersWithMetafields } from './utils';
import React, { useEffect, useRef, useState } from 'react';
import { EditorRootState } from '../../store/EditorRootState';
import { ApptileWebIcon } from '../../icons/ApptileWebIcon.web';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { setCart, setCustomer } from '../../actions/cartAssistActions';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import { makeToast } from '../../actions/toastActions';
import Button from '../../components-v2/base/Button';
import { useNavigate } from 'react-router-dom'

const Search = () => {
  const scrollViewRef = useRef();
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const [query, setQuery] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);
  const [customers, setCustomers] = useState<I_CART_ASSIST_CUSTOMER[] | any>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<I_CART_ASSIST_CUSTOMER | null>()

  const appId = useSelector((state: EditorRootState) => state.apptile.appId) as string;

  const handleClickOutside = (event: any) => (scrollViewRef.current && !scrollViewRef.current?.contains(event.target)) && setIsEditing(false);
  const handleKeyDown = (event: any) => (event.key === 'Escape') && setIsEditing(false);
  const debouncedSetQuery = debounce(setQuery, 150);

  useEffect(() => {
    dispatch(setCart({}));
    dispatch(setCustomer({}))
  }, [])

  useEffect(() => {
    if (isEditing) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isEditing]);

  useEffect(() => {
    (async () => {
      CartAssistApi.searchCustomers(appId, query, 20, 'Apptile-CartAssist').then(response => {
        const customer = TransformCustomersWithMetafields(response.data)
        setCustomers(customer)
      }).catch(error => {
        console.log("ERROR IN FETCHING CUSTOMERS: ", error)
        dispatch(makeToast({
        content: "Cannot fetch customers",
        appearances: "error"
      }))})
    })()
  }, [query]);

  const handlePressablePress = () => {
    setIsEditing(!isEditing)
    setSelectedCustomer(null)
    dispatch(setCustomer({}))
  }

  const handleCustomerPress = (customer: any) => {
    setIsEditing(false);
    dispatch(setCustomer(customer))
    setSelectedCustomer(customer);
  };

  const handleConfirmation = () => {
    if(!selectedCustomer) handlePressablePress()
    else navigate('../cart')
  }

  return (
    <View style={styles.wrapper}>

      <View style={{ flexDirection: 'row', gap: 10, height: '100%' }}>

        <View style={styles.container}>

          <Pressable onPress={handlePressablePress} style={[commonStyles.input, styles.inputContainer]}>
            <View style={{ paddingVertical: 2, flexDirection: 'row', alignItems: 'center', gap: 10 }}>
              <ApptileWebIcon
                style={{ position: 'absolute', marginLeft: 2, zIndex: 1 }}
                name={'magnify'}
                size={14}
                color={theme.CONTROL_INPUT_COLOR}
              />
              {selectedCustomer ? (
                <View style={styles.confirmDetailsContainer}>
                  <View style={styles.confirmDetails}>
                    <Text style={[styles.text, { fontSize: 14 }]}>{selectedCustomer.displayName}</Text>
                    <Text style={[styles.text, { fontSize: 12, color: '#7B7B7B' }]}>{selectedCustomer.email}</Text>
                  </View>
                </View>
              ) : (
                <CodeInputControlV2
                  placeholder={'Search a customer'}
                  singleLine={true}
                  defaultValue={query}
                  inputStyles={{ paddingLeft: 26 }}
                  containerStyles={{ width: '100%' }}
                  onChange={function (value: string): void {
                    debouncedSetQuery(value);
                  }}
                />
              )}
            </View>
          </Pressable>

          {isEditing ? (
            <View style={styles.scrollView} ref={scrollViewRef}>
              {customers.length > 0 ? customers.map((customer: any, i) => (
                <Pressable key={i} style={styles.customer} onPress={() => handleCustomerPress(customer)}>
                  <View>
                    <Image resizeMode="cover" source={customer.imageUrl} style={styles.customerImage} />
                  </View>
                  <View style={{ flexDirection: 'column', gap: 6 }}>
                    <Text style={[styles.text, { fontSize: 12 }]}>{customer.displayName}</Text>
                    <Text style={[styles.text, { fontSize: 11, color: '#7B7B7B' }]}>{customer.email}</Text>
                  </View>
                </Pressable>
              )) : (
                <View style={styles.noResults}>
                  <Image style={styles.emptyImage} source={require('../../assets/images/snapshot-no-result.png')} />
                  <Text style={styles.text}>No customers found.</Text>
                </View>
              )}
            </View>
          ) : null}

        </View>

        <View style={{marginTop: 7}}>
          <Button onPress={handleConfirmation}>
            {selectedCustomer ? "Confirm" : "Browse"}
          </Button>
        </View> 

      </View>
    </View>
  );
};

export default Search;

const styles = StyleSheet.create({
  wrapper: {
    marginTop: 20,
    flex: 1,
  },
  text: {
    fontFamily: theme.FONT_FAMILY
  },
  noResults: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
  emptyImage: {
    width: 150,
    height: 150,
  },
  editorInputItem: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  container: {
    flex: 1,
    width: '100%',
    flexDirection: 'column',
    gap: 20,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    paddingHorizontal: 10,
    paddingVertical: 1,
  },
  scrollView: {
    marginBottom: 16,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: theme.CONTROL_BORDER,
    borderRadius: 8,
    padding: 16,
    overflow: 'scroll',
    gap: 24,
    flex: 1
  },
  customer: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'center',
  },
  customerImage: {
    width: 32,
    height: 32,
    overflow: 'hidden',
    borderRadius: 9999,
  },
  confirmDetailsContainer: {
    flex: 1,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
    alignItems: 'center',
    marginLeft: 24,
  },
  confirmDetails: {
    backgroundColor: '##f3f3f3',
    paddingVertical: 14,
    paddingHorizontal: 8,
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
});