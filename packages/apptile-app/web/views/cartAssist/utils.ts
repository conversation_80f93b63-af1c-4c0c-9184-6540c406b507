import { EditorRootState } from "../../store/EditorRootState";
import { GET_PRODUCT } from "@/root/app/plugins/datasource/ShopifyV_22_10/queries/product";
import { datasourceTypeModelSel } from "../../../../apptile-core/selectors/AppModelSelector";
import { flattenConnection } from "@/root/app/plugins/datasource/ShopifyV_22_10/utils/utils";
import { I_CART_ASSIST_CART, I_CART_ASSIST_CUSTOMER, I_CART_ASSIST_LINE_ITEM } from "./types";
import { ICartLineItem, IProductDetail } from "@/root/app/plugins/datasource/ShopifyV_22_10/types";
import { TransformCartLineItem } from "@/root/app/plugins/datasource/ShopifyV_22_10/transformers/cartTransformer";
import { TransformProductDetail } from "@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer";

export function TranformCustomerWithMetafields(rawData: any) {
  return {
    ...rawData,
    metafields: rawData.metafields.edges.map((edge: any) => edge.node)
  } as I_CART_ASSIST_CUSTOMER
}
export function TransformCustomersWithMetafields(rawData: any) {
  return rawData.edges.map((edge: any) => {
    const node = edge.node;
    const imageUrl = node.image ? node.image.url : null;
    const metafields = node.metafields.edges.map((edge: any) => edge.node);
    return {
      id: node.id,
      displayName: node.displayName,
      email: node.email,
      firstName: node.firstName,
      lastName: node.lastName,
      phone: node.phone,
      imageUrl: imageUrl, 
      metafields
    };
  }) as I_CART_ASSIST_CUSTOMER[]
}
export async function TransfromCartDetails(cartData: any, queryRunner?: any)  {
  const shopifyModelSel = (state: EditorRootState) => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const ShopifyDSModel = shopifyModelSel ? shopifyModelSel(store.getState()) : null;
  const countryCode = ShopifyDSModel.get('countryCode')
  const languageCode = ShopifyDSModel.get('languageCode')
  const transformedData = flattenConnection(cartData?.cart?.lines)?.map(line =>
    TransformCartLineItem(line, {countryCode, languageCode}),
  );
  const lines = transformedData;
  const updatedLines = await Promise.all(
    lines?.map(async (line: any) => {
      const response = await queryRunner?.runQuery('query', GET_PRODUCT, {
        productId: line.variant.product.id,
      });

      const result = TransformProductDetail(response.data.product, {countryCode, languageCode});
      const transformedProductIntoCartLine = TransformProductToCartLineItem(result!, line)
      return transformedProductIntoCartLine
    }),
  );

  const cartBody = {
    ...cartData.cart,
    lines: updatedLines,
  };

  return cartBody as I_CART_ASSIST_CART
}
export function TransformProductToCartLineItem(product: IProductDetail, line?: ICartLineItem): I_CART_ASSIST_LINE_ITEM {
  return {
    cartLineId: line?.cartLineId || product.variants[0].id,
    quantity: line?.quantity || 1,
    lineItemDiscount: line?.lineItemDiscount || 0,
    displayLineItemDiscount: line?.displayLineItemDiscount || '',
    subscriptionProduct: line?.subscriptionProduct || undefined,
    variant: {
      product: {
        title: line?.variant.product.title || product.title,
        handle: line?.variant.product.handle || product.handle,
        totalInventory: line?.variant.product.totalInventory || product.totalInventory,
        id: line?.variant.product.id || product.id, 
        productType: line?.variant.product.productType || product.productType,
        vendor: line?.variant.product.vendor || product.vendor,
        tags: line?.variant.product.tags || product.tags,
        variants: product.variants.map(variant => ({
          id: variant.id,
          availableForSale: variant.availableForSale,
          sku: variant.sku || '',
          featuredImage: variant.featuredImage,
          image: {
            id: variant.image.id,
            altText: variant.image.altText,
            src: variant.image.src
          },
          title: variant.title,
          weight: variant.weight,
          weightUnit: variant.weightUnit,
          isInStock: variant.isInStock,
          quantityAvailableForSale: variant.quantityAvailableForSale,
          variantOptions: variant.variantOptions.map(option => ({
            name: option.name,
            value: option.value
          })),
          price: variant.price || 0,
          salePrice: variant.salePrice,
          displayPrice: variant.displayPrice || '',
          displaySalePrice: variant.displaySalePrice,
          metafields: variant.metafields || [],
          _raw: {}
        }))
      },
      id: line?.variant.id || product.variants[0].id,
      title: line?.variant.title || product.variants[0].title,
      availableForSale: line?.variant.availableForSale || product.variants[0].availableForSale,
      sku: line?.variant.sku || product.variants[0].sku,
      weight: line?.variant.weight || product.variants[0].weight,
      weightUnit: line?.variant.weightUnit || product.variants[0].weightUnit,
      quantityAvailableForSale: line?.variant.quantityAvailableForSale || product.variants[0].quantityAvailableForSale,
      isInStock: line?.variant.isInStock || product.variants[0].isInStock,
      featuredImage: line?.variant.featuredImage || product.variants[0].featuredImage,
      price: line?.variant.price || product.variants[0].price,
      salePrice: line?.variant.salePrice || product.variants[0].salePrice,
      displayPrice: line?.variant.displayPrice || product.variants[0].displayPrice,
      displaySalePrice: line?.variant.displaySalePrice || product.variants[0].displaySalePrice,
      metafields: line?.variant.metafields || product.variants[0].metafields,
      image: {
        id: product.variants[0].image.id,
        altText: product.variants[0].image.altText,
        src: product.variants[0].image.src
      },
      variantOptions: line?.variant.variantOptions || product.variants[0].variantOptions
    },
  };
}
export function TransformToUpdate(cartItems: I_CART_ASSIST_LINE_ITEM[]) {
  return cartItems?.map((line) => ({
    id: line?.cartLineId,
    quantity: line?.quantity,
    merchandiseId: line?.variant?.id,
    // Selling Plan skipped for now (! Might Use later)
  }))
}
export function TransformToAdd(cartItems: I_CART_ASSIST_LINE_ITEM[]) {
  return cartItems?.map((line) => ({
    quantity: line?.quantity,
    merchandiseId: line?.variant.id
    // Selling Plan skipped for now (! Might Use later)
  }))
}
export function TransformToDelete(cartItems: I_CART_ASSIST_LINE_ITEM[]) {
  return cartItems?.map(line => line.cartLineId)
}
