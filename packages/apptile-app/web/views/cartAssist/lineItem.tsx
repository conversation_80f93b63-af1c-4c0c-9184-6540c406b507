import React from 'react';
import { Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import { useDispatch } from 'react-redux';
import Icon from '../../../../apptile-core/icons';
import { LINE_ITEM_TYPE, QUANTITY_UPDATE_TYPE, removeLineItem, updateLineItemQuantity } from '../../actions/cartAssistActions';
import theme from '../../styles-v2/theme';
import { I_CART_ASSIST_LINE_ITEM } from './types';
import VariantPicker from './variantPicker';
import Tooltip from '../../components-v2/base/SimpleTooltip';

const LineItem = ({line, zIndex, itemType}: {line: I_CART_ASSIST_LINE_ITEM; zIndex: number, itemType: LINE_ITEM_TYPE}) => {
  const dispatch = useDispatch();
  const onDeleteItem = () => dispatch(removeLineItem(line, itemType));
  const handleQuantityUpdate = (quantityType: QUANTITY_UPDATE_TYPE) => dispatch(updateLineItemQuantity(line, quantityType, itemType))

  return (
    <View style={[styles.lineItemContainer, { zIndex: zIndex }]}>
      <Pressable style={styles.deleteItem} onPress={onDeleteItem}>
        <Icon color={theme.INPUT_BORDER} iconType="MaterialIcons" name="delete" size={20} />
      </Pressable>

      <View style={styles.titleContainer}>
        <Image resizeMethod="cover" source={line.variant.featuredImage} style={styles.lineItemImage} />
        <View style={{flexDirection: 'column', gap: 6, flex: 1}}>
          <Tooltip
            tooltip={line.variant.product.title}
            position="bottom"
            toolTipMenuStyles={{height: 'fit-content', right: 40, paddingVertical: 4}}
            containerStyles={{ zIndex: 2 }}
          >
            <Text numberOfLines={1} ellipsizeMode='tail' style={[styles.text, {fontSize: 15}]}>{line.variant.product.title}</Text>
          </Tooltip>
          <Text style={[styles.text, {fontSize: 15}]}>${line.variant.salePrice}</Text>
        </View>
      </View>

      {/* INFO: Out of stock variants will not be displayed */}
      <View style={styles.variantWrapper}>
        <VariantPicker 
          lineType={itemType}
          line={line}
          options={
            line.variant.product.variants
            .filter((variant: any) => variant.availableForSale == true)
            .map((variant: any) => variant.title)
          }
        />
        <View style={styles.quantityContainer}>
          <Text style={[styles.text, {fontSize: 12}]}>Select Quantity</Text>
          <View style={styles.quantityControl}>
            <Pressable
              style={[line.quantity <= 1 && styles.webCursorNotAllowed, styles.quantityIcon]}
              disabled={line.quantity <= 1}
              onPress={() => handleQuantityUpdate(QUANTITY_UPDATE_TYPE.DECREASE)}>
              <Icon color='black' iconType="AntDesign" name="minus" size={14} />
            </Pressable>
            <Text style={[styles.text, {fontSize: 12}]}>{line.quantity}</Text>
            <Pressable
              style={[
                (line.quantity >= line?.variant?.quantityAvailableForSale! || false) && styles.webCursorNotAllowed,
                styles.quantityIcon,
              ]}
              disabled={line.quantity >= line?.variant?.quantityAvailableForSale! || false}
              onPress={() => handleQuantityUpdate(QUANTITY_UPDATE_TYPE.INCREASE)}>
              <Icon color='black' iconType="MaterialIcons" name="add" size={14} />
            </Pressable>
          </View>
        </View>
      </View>
    </View>
  );
};

export default LineItem;

const styles = StyleSheet.create({
  text: {
    fontFamily: theme.FONT_FAMILY,
  },
  lineItemContainer: {
    padding: 20,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: theme.INPUT_BORDER,
    borderRadius: 4,
    flexDirection: 'column',
    width: '32%',
    position: 'relative',
  },
  lineItemImage: {
    width: 56,
    height: 78,
    overflow: 'hidden',
    borderRadius: 4,
  },
  deleteItem: {
    position: 'absolute',
    top: 10,
    right: 14,
    ...Platform.select({
      web: {
        zIndex: 10,
      },
    }),
  },
  variantWrapper: {
    marginTop: 14,
    gap: 10,
  },
  titleContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  quantityIcon: {
    padding: 8,
  },
  quantityControl: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: theme.INPUT_BORDER,
    borderRadius: 4,
    flex: 1,
    maxWidth: 150
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 20,
    flex: 1,
  },
  webCursorNotAllowed: {
    ...Platform.select({
      web: {
        cursor: 'not-allowed',
      },
    }),
  },
});
