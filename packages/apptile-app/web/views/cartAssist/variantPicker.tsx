import {Pressable, StyleSheet, Text, View} from 'react-native';
import theme from '../../styles-v2/theme';
import Icon from '../../../../apptile-core/icons';
import {useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import {I_CART_ASSIST_LINE_ITEM} from './types';
import {makeToast} from '../../actions/toastActions';
import {LINE_ITEM_TYPE, updateLineItemVariant} from '../../actions/cartAssistActions';

const VariantPicker = ({
  options,
  line,
  lineType,
}: {
  options: string[];
  line: I_CART_ASSIST_LINE_ITEM;
  lineType: LINE_ITEM_TYPE;
}) => {
  const dispatch = useDispatch();
  const existingLineItems = useSelector((state: EditorRootState) => state.cartAssist.existingLineItems);
  const newLineItems = useSelector((state: EditorRootState) => state.cartAssist.newLineItems);

  const [selectedVariant, setSelectedVariant] = useState(line.variant.title);
  const [isOpen, setOpen] = useState(false);
  const [viewHeight, setViewHeight] = useState(0);

  const onLayout = (event: any) => {
    const {height} = event.nativeEvent.layout;
    setViewHeight(height);
  };

  const handleVariantChange = (variantTitle: string) => {
    const isExist = [...existingLineItems, ...newLineItems].some(
      (lineItem: I_CART_ASSIST_LINE_ITEM) => lineItem.variant.title === variantTitle,
    );
    if (isExist) {
      dispatch(
        makeToast({
          content: 'Variant already exists in the cart',
          appearances: 'warning',
        }),
      );
      return;
    } else {
      dispatch(updateLineItemVariant(line, variantTitle, lineType));
    }
    setSelectedVariant(variantTitle);
    setOpen(false);
  };

  return (
    <View style={styles.wrapper}>
      <View style={{flex: 1}}>
        <Text>Selct a Variant</Text>
      </View>

      <Pressable onLayout={onLayout} style={styles.trigger} onPress={() => setOpen(!isOpen)}>
        <Text numberOfLines={1} ellipsizeMode='tail' style={[styles.text, {fontSize: 12}]}>{selectedVariant}</Text>
        <Icon name="chevron-down" iconType="Octicons" size={13} color="black" />

        {isOpen && (
          <View style={[styles.dropDownContainer, {top: viewHeight + 5}]}>
            {options.map((option: string) => (
              <Pressable style={styles.variantContainer} onPress={() => handleVariantChange(option)}>
                <Text>{option}</Text>
              </Pressable>
            ))}
          </View>
        )}
      </Pressable>
    </View>
  );
};

export default VariantPicker;

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    zIndex: 1,
  },
  text: {
    fontFamily: theme.FONT_FAMILY,
  },
  trigger: {
    flex: 1,
    maxWidth: 150,
    padding: 10,
    gap: 6,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    borderRadius: 4,
    backgroundColor: 'white',
    position: 'relative',
  },
  dropDownContainer: {
    position: 'absolute',
    flex: 1,
    width: '100%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    borderRadius: 4,
    right: 0,
    backgroundColor: 'white',
    maxHeight: 180,
    overflow: 'scroll',
  },
  variantContainer: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: theme.INPUT_BORDER,
    borderStyle: 'solid',
  },
});
