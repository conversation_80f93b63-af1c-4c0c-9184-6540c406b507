import * as ProductGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import { TransformGetProductsPaginatedQuery } from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import { IProductDetail, IProductVariant } from '@/root/app/plugins/datasource/ShopifyV_22_10/types';
import { processShopifyGraphqlQueryResponse } from '@/root/app/plugins/datasource/utils';
import { debounce } from 'lodash';
import React, { useEffect, useState } from 'react';
import { Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from '../../../../apptile-core/icons';
import { datasourceTypeModelSel } from '../../../../apptile-core/selectors/AppModelSelector';
import { LINE_ITEM_TYPE, addLineItem } from '../../actions/cartAssistActions';
import { makeToast } from '../../actions/toastActions';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import { ApptileWebIcon } from '../../icons/ApptileWebIcon.web';
import { replaceProductImage } from '../../integrations/shopify/ShopifyObjectCache';
import { EditorRootState } from '../../store/EditorRootState';
import theme from '../../styles-v2/theme';
import { I_CART_ASSIST_LINE_ITEM } from './types';
import { TransformProductToCartLineItem } from './utils';

const ProductPicker = ({setOpenProductModal}: {setOpenProductModal: (value: boolean) => void}) => {
  const [query, setQuery] = useState('');
  const [products, setProducts] = useState([]);
  const [isEditing, setEditing] = useState(false);
  const [currentProduct, setCurrentProduct] = useState<IProductDetail>();
  const [isVariantOpen, setIsVariantOpen] = useState(false)

  const existingLineItems = useSelector((state: EditorRootState) => state.cartAssist.existingLineItems)
  const newLineItems = useSelector((state: EditorRootState) => state.cartAssist.newLineItems)

  const dispatch = useDispatch();
  const debouncedSetQuery = debounce(setQuery, 300);

  let queryRunner;
  const shopifyModelSel = (state: EditorRootState) => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const ShopifyDSModel = shopifyModelSel ? shopifyModelSel(store.getState()) : null;
  if (!queryRunner) queryRunner = ShopifyDSModel?.get('queryRunner');

  const getProducts = async (query?: string) => {
    if (queryRunner && queryRunner.runQuery) {
      const countryCode = ShopifyDSModel?.getIn(['shop', 'paymentSettings', 'countryCode']) ?? 'US';
      try {
        const queryResponse = await queryRunner.runQuery('query', ProductGqls.EXTENDED_SEARCH_PRODUCTS_FOR_DROPDOWN, {
          first: 20,
          query: query,
          countryCode,
        });
        const {transformedData: queryTransformedData} = processShopifyGraphqlQueryResponse(
          queryResponse,
          {transformer: TransformGetProductsPaginatedQuery},
          ShopifyDSModel?.get('shop') ?? {},
          ShopifyDSModel,
        );
        if (query && queryTransformedData.length == 0) return getProducts();
        setProducts(replaceProductImage(queryTransformedData));
        return queryTransformedData;
      } catch (error) {
        console.log('ERROR IN FETCHING PRODUCTS: ', error);
        setProducts([]);
      }
    } else {
      console.log('ERROR IN LOADING QUERY RUNNER');
      return setProducts([]);
    }
  };

  useEffect(() => {
    (async () => {
      const products = await getProducts(query);
      setProducts(products);
    })();
  }, [query]);

  const handleProductListPress = (item: IProductDetail) => {
    if(item.variants.every(variant => variant.availableForSale == false)) {

      return dispatch(
        makeToast({
          content: "Product's variants are out of stock",
          appearances: 'warning',
        }),
      );
    }
    setCurrentProduct(item); 
    setIsVariantOpen(true)
  }

  const handleProductPress = (item: IProductDetail, variant: IProductVariant) => {
    // Checking if the selected variant already exists in the existingLineItems & newLineItems
    const isExist = [...existingLineItems, ...newLineItems].some(
      (lineItem: I_CART_ASSIST_LINE_ITEM) => lineItem.variant.id === variant.id
    );
    if(isExist) return dispatch(makeToast({
      content: "Variant already exists in the cart",
      appearances: 'warning'
    }))
    const transformedLineItemFromProduct = TransformProductToCartLineItem(item);
    dispatch(addLineItem(transformedLineItemFromProduct, LINE_ITEM_TYPE.NEW, variant));
    setOpenProductModal(false);
  };

  return (
    <View>
      {isVariantOpen && currentProduct ? (
        <View style={[styles.variantWrapper]}>
          <View style={[styles.goBack]}>
            <Pressable style={{gap: 4, flexDirection: 'row', alignItems: 'center'}} onPress={() => setIsVariantOpen(false)}>
              <Icon color="black" iconType="AntDesign" name="arrowleft" size={16} />
              <Text style={[styles.text, {fontSize: 11}]}>Go Back</Text>
            </Pressable>
            <Pressable onPress={() => setOpenProductModal(false)}>
              <Icon color="black" iconType="AntDesign" name="close" size={12} />
            </Pressable>
          </View>
          <View style={styles.variantTitleContainer}>
            <View style={[styles.variantTitle]}>
              <Image resizeMethod="cover" source={currentProduct.featuredImage} style={styles.productImage} />
              <Text style={[styles.text, {fontSize: 12, color: '#535353'}]}>{currentProduct.title}</Text>
            </View>
            <Text style={[styles.text, {fontSize: 11, fontWeight: '600'}]}>SELECT A VARIANT</Text>
          </View>
          <View style={[styles.productWrapper, { marginTop: 0}]}>
            {/* INFO: OUT OF STOCK VARIANT WILL NOT BE DISPLAYED */}
            {currentProduct?.variants?.map((variant: IProductVariant, i) => {
              return variant.availableForSale && (
                <Pressable key={i} style={styles.variantsContainer} onPress={() => handleProductPress(currentProduct, variant)}>
                  <Text style={[styles.text]}>{variant.title}</Text>
                </Pressable>
              )
            })}
          </View> 
        </View>
      ) : (
        <View style={styles.wrapper}>
          <View style={[styles.goBack, { padding: 0 }]}>
            <Text style={[styles.text, {fontSize: 11, fontWeight: '500'}]}>ADD PRODUCT TO CART</Text>
            <Pressable onPress={() => setOpenProductModal(false)}>
              <Icon color="black" iconType="AntDesign" name="close" size={12} />
            </Pressable>
          </View>
          <View style={{width: '100%', marginTop: 8}}>
            <Pressable onPress={() => setEditing(!isEditing)}>
              <ApptileWebIcon
                style={{position: 'absolute', marginTop: 15, marginLeft: 6, zIndex: 1}}
                name={'magnify'}
                size={14}
                color={theme.CONTROL_INPUT_COLOR}
              />
              <CodeInputControlV2
                placeholder={'Search a product'}
                singleLine={true}
                value={query}
                inputStyles={{paddingLeft: 26}}
                containerStyles={{width: '100%'}}
                onChange={function (value: string): void {
                  debouncedSetQuery(value);
                }}
              />
            </Pressable>
            <View style={styles.productWrapper}>
              {products && products?.length > 0 ? (
                products?.map((item: IProductDetail, i: any) => (
                  <Pressable key={i} style={styles.productContainer} onPress={() => handleProductListPress(item)}>
                    <Image resizeMethod="cover" source={item.featuredImage} style={styles.productImage} />
                    <Text style={[styles.text, {fontSize: 12, color: '#535353'}]}>{item.title}</Text>
                  </Pressable>
                ))
              ) : (
                <View style={styles.noResults}>
                  <Image style={styles.emptyImage} source={require('../../assets/images/snapshot-no-result.png')} />
                  <Text style={styles.text}>No results found</Text>
                </View>
              )}
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default ProductPicker;

const styles = StyleSheet.create({
  wrapper: {
    height: 400,
    width: 350,
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 18,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: theme.CONTROL_BORDER,
    borderRadius: 4,
    position: 'relative',
    ...Platform.select({
      web: {
        zIndex: 9999,
      },
    }),
  },
  text: {
    fontFamily: theme.FONT_FAMILY,
    ...Platform.select({
      web: {
        zIndex: 9999,
      },
    }),
  },
  productWrapper: {
    height: 275,
    overflow: 'scroll',
    marginTop: 8,
  },
  productContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 14,
    borderBottomWidth: 1,
    borderBottomColor: theme.INPUT_BORDER,
    borderStyle: 'solid',
    paddingVertical: 4,
    marginLeft: 2,
  },
  productImage: {
    width: 32,
    height: 32,
    borderRadius: 4,
  },
  noResults: {
    flex: 1,
    marginBottom: 50,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
  emptyImage: {
    width: 75,
    height: 75,
  },
  goBack: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    padding: 14,
  },
  variantWrapper: {
    height: 400,
    width: 350,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: theme.CONTROL_BORDER,
    borderRadius: 4,
    backgroundColor: 'white',
  },
  variantTitleContainer: {
    backgroundColor: '#F3F3F3',
    padding: 12,
    gap: 8,
    borderRadius: 4
  },
  variantTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    borderBottomColor: theme.INPUT_BORDER,
    borderBottomWidth: 1,
    borderStyle: 'solid',
    paddingBottom: 8,
  },
  variantsContainer: {
    padding: 12,
    borderBottomColor: theme.INPUT_BORDER,
    borderBottomWidth: 1,
    borderStyle: 'solid',
  },
});
