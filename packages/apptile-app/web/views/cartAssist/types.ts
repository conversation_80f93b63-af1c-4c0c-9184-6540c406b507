import { ICart, ICartLineItem, ICustomer, IProduct, IProductVariant } from "@/root/app/plugins/datasource/ShopifyV_22_10/types"

export type I_CART_ASSIST_METAFIELDS = {
  key: string;
  namespace: string;
  value: string;
}
export type I_CART_ASSIST_CUSTOMER = Pick<ICustomer, 'id' | 'email' | 'firstName' | 'lastName' | 'phone'> & {
  image: {
    url: string
  }
  displayName: string
  metafields: I_CART_ASSIST_METAFIELDS[]
}
export type I_CART_ASSIST_LINE_ITEM = ICartLineItem & {
  variant: {
    product: Pick<IProduct, 'title' | 'handle' | 'totalInventory' | 'id' | 'productType' | 'vendor' | 'tags'> & {
      variants: IProductVariant[]
    }
  }
} 
export type I_CART_ASSIST_CART = ICart & {
  lines: I_CART_ASSIST_LINE_ITEM[]
}
