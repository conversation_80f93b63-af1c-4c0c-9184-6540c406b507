import TextElement from '@/root/web/components-v2/base/TextElement';
import TextInput from '@/root/web/components-v2/base/TextInput';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {IFormData} from '..';
import Tooltip from '@/root/web/components-v2/base/Tooltip/Index';
import {MaterialCommunityIcons} from 'apptile-core';
import theme from '@/root/web/styles-v2/theme';

const AppDetails: React.FC<{onFormChange: (data: string, field: string) => void; formData: IFormData}> = ({
  onFormChange,
  formData,
}) => {
  return (
    <View style={styles.wrapper}>
      <TextElement color="SECONDARY" fontSize="md" style={styles.marginBottom20}>
        We just need a few more details before we can take your app live!
      </TextElement>
      <View style={styles.contentWrapper}>
        <View style={styles.inputWrapper}>
          <View style={styles.marginBottom10}>
            <TextElement fontSize="sm" style={styles.marginBottom5} color="SECONDARY">
              App name*
            </TextElement>
            <TextInput
              value={formData.appName}
              onChangeText={(data: string) => onFormChange(data, 'appName')}
              style={styles.titleInput}
              placeholder="Apptile"
            />
          </View>
          <View style={styles.marginBottom10}>
            <View style={styles.inputTitleWrapper}>
              <TextElement fontSize="sm" style={styles.marginBottom5} color="SECONDARY">
                App subtitle*
              </TextElement>
              <Tooltip
                visible={true}
                tooltipPosition={'Top'}
                tooltip={
                  <View style={styles.tooltip}>
                    <TextElement lineHeight="md" fontSize="xs" color="SECONDARY">
                      Short app description displayed on the app store. Max 30 characters.
                    </TextElement>
                  </View>
                }>
                <MaterialCommunityIcons name={'information-outline'} size={15} />
              </Tooltip>
            </View>
            <TextInput
              value={formData.appSubtitle}
              onChangeText={(data: string) => onFormChange(data, 'appSubtitle')}
              style={styles.titleInput}
              placeholder="Build no-code apps"
            />
          </View>
          <View style={styles.marginBottom10}>
            <View style={styles.inputTitleWrapper}>
              <TextElement fontSize="sm" style={styles.marginBottom5} color="SECONDARY">
                Privacy Policy*
              </TextElement>
              <Tooltip
                visible={true}
                tooltipPosition={'Top'}
                tooltip={
                  <View style={styles.tooltip}>
                    <TextElement lineHeight="md" fontSize="xs" color="SECONDARY">
                      Please share your privacy policy link here. Required for Apple & Play store submission.
                    </TextElement>
                  </View>
                }>
                <MaterialCommunityIcons name={'information-outline'} size={15} />
              </Tooltip>
            </View>
            <TextInput
              value={formData.privacyPolicy}
              onChangeText={(data: string) => onFormChange(data, 'privacyPolicy')}
              style={styles.titleInput}
              placeholder="https://apptile.com/privacy-policy"
            />
          </View>
        </View>
        <View style={styles.descriptionWrapper}>
          <View style={styles.inputTitleWrapper}>
            <TextElement fontSize="sm" color="SECONDARY" style={[styles.marginBottom5]}>
              App description*
            </TextElement>
            <Tooltip
              visible={true}
              tooltipPosition={'Top'}
              tooltip={
                <View style={styles.tooltip}>
                  <TextElement lineHeight="md" fontSize="xs" color="SECONDARY">
                    Provide an engaging description that highlights the features and functionality of your app.
                  </TextElement>
                </View>
              }>
              <MaterialCommunityIcons name={'information-outline'} size={15} />
            </Tooltip>
          </View>
          <TextInput
            value={formData.appDescription}
            onChangeText={(data: string) => onFormChange(data, 'appDescription')}
            multiline={true}
            style={[styles.descriptionInput]}
            placeholder="Design your brand’s unique mobile app experience in seconds"
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    paddingVertical: 30,
  },
  inputTitleWrapper: {flexDirection: 'row', alignItems: 'center'},
  marginBottom20: {
    marginBottom: 20,
  },
  titleInput: {width: 191, padding: 8, fontSize: 12},
  descriptionInput: {width: 263, height: 150, padding: 8, fontSize: 12},
  marginBottom10: {
    marginBottom: 10,
  },
  marginBottom5: {
    marginBottom: 5,
  },
  contentWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  descriptionWrapper: {},
  inputWrapper: {},
  tooltip: {
    maxWidth: 207,
    flexDirection: 'column',
    gap: 6,
    padding: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: '#FFFFFF',
  },
});

export default AppDetails;
