import {MaterialCommunityIcons} from 'apptile-core';
import Button from '@/root/web/components-v2/base/Button';
import TextElement from '@/root/web/components-v2/base/TextElement';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {ContactUs} from '../../hubspot';

const Publishing: React.FC<{hasDeveloperAccount: string}> = ({hasDeveloperAccount}) => {
  return (
    <View style={styles.wrapper}>
      {hasDeveloperAccount === 'No' ? (
        <>
          <MaterialCommunityIcons name={'calendar-month'} color={'#000000'} size={70} style={styles.marginBottom20} />
          <TextElement color="SECONDARY" fontSize="md" style={[styles.marginBottom20, {textAlign: 'center'}]}>
            Thank you for sharing the App details. Please book a call with us to publish your app on the Google Play &
            Apple App Store.
          </TextElement>
          {/* <View style={styles.contentWrapper}>
            <TextElement color="SECONDARY" fontSize="sm" style={styles.marginBottom5}>
              Your publishing request is in queue.
            </TextElement>
            <TextElement color="SECONDARY" fontSize="sm" style={styles.marginBottom20}>
              We will be in touch to confirm the app launch timeline.
            </TextElement>
          </View>
          <TextElement color="SECONDARY" fontSize="sm" lineHeight="md" style={styles.marginBottom20}>
            Please drop us a review while we publish your app.
          </TextElement> */}
          {/* <ContactUs
            org={'apptile'}
            color="CTA"
            calendar={'app-publish'}
            buttonText="Schedule my call"
            containerStyles={{width: 200, marginTop: 24}}
          /> */}
          <Button
            color="CTA"
            onPress={() => {
              window.open('https://meetings.hubspot.com/apptile/app-publish');
            }}
            containerStyles={{marginTop: 24, width: 200}}>
            Schedule my call
          </Button>
        </>
      ) : (
        <>
          <TextElement color="SECONDARY" fontSize="sm" style={{textAlign: 'center', marginBottom: 40, marginTop: 60}}>
            In order to launch your app on the Apple App Store and Google Play Store you will need to provide us with
            admin access to an Apple Developer Account and Google Play Developer Account registered as an organization.
          </TextElement>
          <TextElement style={{textAlign: 'center', paddingHorizontal: 30}} color="SECONDARY" fontSize="sm">
            Please Book a slot where we will share further instructions and publish your app!
          </TextElement>
          <ContactUs
            org={'apptile'}
            color="CTA"
            calendar={'app-publish'}
            buttonText="Book a slot"
            containerStyles={{width: 115, marginTop: 24}}
          />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    paddingVertical: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  marginBottom20: {
    marginBottom: 20,
  },
  titleInput: {width: 191, padding: 8, fontSize: 12},
  marginBottom5: {
    marginBottom: 5,
  },
  contentWrapper: {
    marginBottom: 35,
    alignItems: 'center',
  },
  descriptionWrapper: {},
  inputWrapper: {},
});

export default Publishing;
