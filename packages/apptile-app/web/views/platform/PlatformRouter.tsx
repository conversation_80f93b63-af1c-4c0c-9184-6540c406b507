import React, {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {Route, Routes, useNavigate} from 'react-router';
import {Navigate} from 'react-router-dom';
import {fetchOrgs, userInit} from '../../actions/editorActions';
import {EditorRootState} from '../../store/EditorRootState';
import AuthFailed from '../auth/components/AuthFailed';
import EditorContainer from '../editor/containers/EditorContainer';

import PaymentConfirmation from '../subscription/PaymentConfirmation';
import DashboardContainer from './dashboard/DashboardContainer';
import EditorContainerV2 from '../editor-v2';
import NotFound from './pages/NotFound';
import StoreHome from '../store-home-v2';
import Onboarding from '../onboarding';
import {ProductFruits} from 'react-product-fruits';
import {EditorOnboardingRouter} from '../editorOnboarding/editorOnboardingRouter';
import ThemePreviewScreen from '../onboarding/ThemePreview';
import {SegmentIdentify} from './components/SegmentIdentify';
import {getOrgId} from '../../selectors/OnboardingSelector';
import {IntercomUpdate} from './components/IntercomUpdate';
import {LogRocketIdentify} from './components/LogRocketIdentify';
import {RemoveTrailingSlash} from '../editor/components/RemoveTrailingSlash';
import {AppForkResolver} from '../editor/components/AppForkResolver';
import {AppBranchResolver} from '../editor/components/AppBranchResolver';
import NetworkUnavailable from './pages/NetworkUnavailable';
import PlatformLoader from './pages/PlatformLoader';
import LiveSellingApp from '../../LiveSellingApp';

const PlatformRouter: React.FC = ({}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {userLoggedIn, userFetched, user, networkReachable, userFetching} = useSelector(
    (state: EditorRootState) => state.user,
  );
  const orgId = useSelector(getOrgId);

  useEffect(() => {
    dispatch(userInit());
    dispatch(fetchOrgs());
  }, [dispatch]);

  useEffect(() => {
    if (networkReachable && userFetched && !userLoggedIn) {
      navigate('/unauthorized');
    }
  }, [navigate, userLoggedIn, userFetched, networkReachable]);

  return (
    <>
      {userFetching && <PlatformLoader />}
      {networkReachable && (
        <PlatformRoutes userFetched={userFetched} userLoggedIn={userLoggedIn} user={user} orgId={orgId} />
      )}
      {!networkReachable && <NetworkUnavailable />}
    </>
  );
};

const PlatformRoutes = (props: any) => {
  const {userFetched, userLoggedIn, user, orgId} = props;
  return (
    <>
      <RemoveTrailingSlash />
      <Routes>
        {userFetched && userLoggedIn && (
          <>
            {/* <Route path="/live-selling/*" element={<LiveSellingApp />} /> */}
            <Route path="/dashboard/:orgId" element={<DashboardContainer />} />
            <Route path="/dashboard/:orgId/app/:id/components" element={<StoreHome />} />
            <Route
              path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/legacy"
              element={<Navigate to="../studio" relative="path" />}
            />
            <Route path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/studio/*" element={<EditorContainer />} />
            {/* //editor onboarding */}
            <Route
              path="/editor-onboarding/:orgId/app/:id/f/:forkId/b/:branchName/*"
              element={<EditorOnboardingRouter />}
            />
            <Route path="/editor-onboarding/:orgId/app/:id/f/:forkId" element={<AppBranchResolver />} />
            <Route path="/editor-onboarding/:orgId/app/:id" element={<AppForkResolver />} />
            <Route path="/dashboard/:orgId/app/:appId/theme-preview/:slug" element={<ThemePreviewScreen />} />
            {/* //Editor */}
            <Route path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/*" element={<EditorContainerV2 />} />
            <Route path="/dashboard/:orgId/app/:id/f/:forkId" element={<AppBranchResolver />} />
            <Route path="/dashboard/:orgId/app/:id" element={<AppForkResolver />} />
            <Route path="/dashboard/*" element={<DashboardContainer />} />
            <Route path="/payment-confirmation" element={<PaymentConfirmation />} />
            <Route path="/unauthorized" element={<AuthFailed />} />
            <Route path="/onboarding/:orgId/app/:appId/f/:forkId/b/:branchName/*" element={<Onboarding />} />
            <Route path="/onboarding/:orgId/app/:id/f/:forkId" element={<AppBranchResolver />} />
            <Route path="/onboarding/:orgId/app/:id" element={<AppForkResolver />} />
            <Route path="*" element={<NotFound />} />
          </>
        )}

        {userFetched && !userLoggedIn && (
          <>
            <Route path="*" element={<NotFound />} />
            <Route path="/unauthorized" element={<AuthFailed />} />
          </>
        )}
      </Routes>

      {userFetched && userLoggedIn && (
        <>
          <ProductFruits
            workspaceCode="0WeCURB2xuMZzmYZ"
            language="en"
            user={{
              username: user?.email as string,
              email: user?.email,
              firstname: user?.firstname,
              lastname: user?.lastname,
            }}
          />
        </>
      )}
      {/* All the analytics identify calls */}
      {orgId && (
        <>
          <SegmentIdentify orgId={orgId} />
          <IntercomUpdate orgId={orgId} />
          <LogRocketIdentify orgId={orgId} />
        </>
      )}
    </>
  );
};

export default PlatformRouter;
