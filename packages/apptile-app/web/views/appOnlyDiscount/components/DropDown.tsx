import React from 'react';
import theme from '@/root/web/styles-v2/theme';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {View} from 'react-native';

const webStyle = {
  select: {
    backgroundColor: theme.TERTIARY_BACKGROUND,
    width: '100%',
    padding: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    border: 'none',
    fontFamily: theme.FONT_FAMILY,
    fontSize: 12,
    color: '#000',
  },
};

type DropDownProps = {
  value: string | null;
  options: {label: string; value: string | null}[];
  onSelect: (value: string) => void;
  disabled?: boolean;
  label?: string;
};

export const DropDown: React.FC<DropDownProps> = props => {
  const {value, options, onSelect, disabled, label} = props;

  return (
    <View style={[{flex: 1}, disabled && {opacity: '50%'}]}>
      <TextElement color="SECONDARY" fontSize="sm" style={{marginBottom: 10}}>
        {label}
      </TextElement>
      <select disabled={disabled} style={webStyle.select} onChange={sel => onSelect(sel.target.value)} value={value}>
        {options.map(item => {
          return (
            <option value={item.value} key={item.value}>
              {item.label}
            </option>
          );
        })}
      </select>
    </View>
  );
};
