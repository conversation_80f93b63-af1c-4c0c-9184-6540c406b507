import React from 'react';
import {View, TouchableOpacity, Text, ScrollView, Image} from 'react-native';
import theme from '@/root/web/styles-v2/theme';
import {Icon} from 'apptile-core';

const webStyle = {
  dropdownContainer: {
    position: 'relative',
    width: 100,
  },
  selected: {
    backgroundColor: theme.TERTIARY_BACKGROUND,
    padding: 7,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    fontFamily: theme.FONT_FAMILY,
    fontSize: 12,
    color: 'black',
    cursor: 'pointer',
    borderTopRightRadius:0, 
    borderBottomRightRadius:0
  },
  optionsContainer: {
    width: '100%',
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 8,
    boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
    zIndex: 100,
    // height: 85,
    // maxHeight: 100,
    // overflow: 'scroll',
  },
  option: {
    flexDirection: 'row',
    padding: 10,
    borderBottom: '1px solid #eaeaea',
    cursor: 'pointer',
    backgroundColor: 'white',
    color: 'black',
    justifyContent: 'flex-start',
  },
  optionSelected: {
    backgroundColor: '#f0f0f0',
    color: 'white',
  },
};

type DropDownProps = {
  value?: string | null;
  options: {image: string; label: string; value: string | null}[];
  onSelect: (value: string) => void;
  disabled?: boolean;
  label?: string;
  isOpen:boolean;
  setIsOpen:any;
};

export const DropDown: React.FC<DropDownProps> = props => {
  const {value, options, onSelect, disabled, label,isOpen,setIsOpen} = props;
  const selectedOption = options.find(option => option.value === value);

  return (
    <View style={{...webStyle.dropdownContainer, width: isOpen ? 120 : 60}}>
      {label && <Text style={{marginBottom: 10, color: 'black'}}>{label}</Text>}
      <TouchableOpacity style={webStyle.selected} onPress={() => !disabled && setIsOpen(!isOpen)} disabled={disabled}>
        <View style={{flexDirection: 'row', justifyContent: 'space-between', width: '100%'}}>
          <Image style={{width: 22, height: 22}} source={selectedOption?.image} />
          <Icon
            name="down"
            iconType="AntDesign"
            size={14}
            color={'black'}
            style={{
              margin: 0,
              fontWeight: 300,
              alignSelf: 'center',
              alignContent: 'center',
            }}
          />
        </View>
      </TouchableOpacity>

      {isOpen && (
        <ScrollView style={webStyle.optionsContainer}>
          {options.map(option => (
            <TouchableOpacity
              key={option.value}
              style={[webStyle.option, value === option.value && webStyle.optionSelected]}
              onPress={() => {
                onSelect(option.value || '');
                setIsOpen(false);
              }}>
              {option?.image && <Image style={{width: 22, height: 22}} source={option.image} />}
              <Text style={{marginLeft: 8}}>{option.label}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}
    </View>
  );
};
