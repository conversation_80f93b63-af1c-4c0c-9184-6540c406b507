import Button from '@/root/web/components-v2/base/Button';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import theme from '@/root/web/styles-v2/theme';
import {Icon} from 'apptile-core';
import React, {useEffect, useState} from 'react';
import {View, Text, Image, FlatList, StyleSheet, Pressable} from 'react-native';

export const ProductTable: React.FC<{
  item: {
    id: string;
    title: string;
    price: number;
    currencyCode: string;
    totalInventory: number;
    collections: string;
    image: string;
  }[];
  onProductDel: (product: any) => void;
  onProductBulkDel: (product: any) => void;
}> = ({item, onProductDel, onProductBulkDel}) => {
  const [checkedProducts, setCheckedProducts] = useState<string[]>([]);
  const [productStockFilter, setProductStockFilter] = useState([0, 1]);
  const [filteredProducts, setFilteredProducts] = useState(item);
  useEffect(() => {
    setFilteredProducts(item);
  }, [item]);

  const resetFilters = () => {
    setProductStockFilter([0, 1]);
    setCheckedProducts([]);
  }

  const checkedIcon = () => (
    <Icon iconType="MaterialCommunityIcons" name="checkbox-outline" size={20} color="#1060E0" />
  );
  const uncheckedIcon = () => (
    <Icon iconType="MaterialCommunityIcons" name="checkbox-blank-outline" size={20} color="#D8D8D8" />
  );

  const [showCollectionPopover, setShowCollectionPopover] = useState(false);

  const onStockFilterApply = () => {
    setShowCollectionPopover(false);
    if (productStockFilter.includes(1) && productStockFilter.includes(0)) {
      setFilteredProducts(item);
    } else if (productStockFilter.includes(1)) {
      setFilteredProducts(item.filter(product => product.totalInventory > 0));
    } else if (productStockFilter.includes(0)) {
      setFilteredProducts(item.filter(product => product.totalInventory <= 0));
    }
  };

  const renderItem = ({item}) => (
    <View style={styles.row}>
      <View style={styles.cellSmall}>
        {checkedProducts.includes(item.id) ? (
          <Pressable
            style={styles.checkBox}
            onPress={() => {
              setCheckedProducts(checkedProducts.filter(product => product !== item.id));
            }}>
            {checkedIcon()}
          </Pressable>
        ) : (
          <Pressable
            style={styles.checkBox}
            onPress={() => {
              setCheckedProducts([...checkedProducts, item.id]);
            }}>
            {uncheckedIcon()}
          </Pressable>
        )}
      </View>
      <View style={styles.cellSmall}>
        <Image source={{uri: item.image}} style={styles.image} />
      </View>
      <Text style={styles.cell} numberOfLines={2}>
        {item.title}
      </Text>
      <Text style={styles.cell}>
        {item.price} {item.currencyCode}
      </Text>
      <Text numberOfLines={2} style={styles.cell}>
        {item.collections ?? 'Some collection'}
      </Text>
      <Text style={styles.cell}>{item.totalInventory} Products</Text>
      <View style={styles.cellSmall}>
        <Pressable onPress={() => onProductDel(item)}>
          <Icon iconType="MaterialCommunityIcons" name="delete-outline" size={20} color="#595959" />
        </Pressable>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {checkedProducts.length === 0 && (
        <View style={styles.headerRow}>
          <Text style={[styles.headerCellSmall, styles.headerCellText]}></Text>
          <Text style={[styles.headerCellSmall, styles.headerCellText]}></Text>
          <Text style={[styles.headerCell, styles.headerCellText]}>Product</Text>
          <Text style={[styles.headerCell, styles.headerCellText]}>Price</Text>
          <Text style={[styles.headerCell, styles.headerCellText]}>Collections</Text>
          <View style={[styles.headerCell, styles.headerCellSortWrapper]}>
            <Text style={styles.headerCellText}>Inventory</Text>
            <PopoverComponent
              trigger={
                <Pressable onPress={() => setShowCollectionPopover(!showCollectionPopover)}>
                  <Icon
                    iconType="MaterialCommunityIcons"
                    name="filter-variant"
                    size={20}
                    color={productStockFilter?.length === 2 || productStockFilter?.length === 0 ? '#595959' : '#1060E0'}
                  />
                </Pressable>
              }
              visible={showCollectionPopover}
              onVisibleChange={() => {}}>
              <View style={styles.stockRadiogroupWrapper}>
                <View style={styles.stockFilterWrapper}>
                  <Pressable
                    onPress={() => {
                      productStockFilter.includes(1)
                        ? setProductStockFilter(productStockFilter.filter(stock => stock !== 1))
                        : setProductStockFilter([...productStockFilter, 1]);
                    }}>
                    <Icon
                      iconType="MaterialCommunityIcons"
                      name={productStockFilter.includes(1) ? 'checkbox-marked' : 'checkbox-blank-outline'}
                      size={20}
                      color={productStockFilter.includes(1) ? '#1060E0' : '#D8D8D8'}
                    />
                  </Pressable>
                  <Text>In stock</Text>
                </View>
                <View style={styles.stockFilterWrapper}>
                  <Pressable
                    onPress={() => {
                      productStockFilter.includes(0)
                        ? setProductStockFilter(productStockFilter.filter(stock => stock !== 0))
                        : setProductStockFilter([...productStockFilter, 0]);
                    }}>
                    <Icon
                      iconType="MaterialCommunityIcons"
                      name={productStockFilter.includes(0) ? 'checkbox-marked' : 'checkbox-blank-outline'}
                      size={20}
                      color={productStockFilter.includes(0) ? '#1060E0' : '#D8D8D8'}
                    />
                  </Pressable>
                  <Text>Out of stock</Text>
                </View>
                <Button color="PRIMARY" variant="PILL" size="SMALL" onPress={onStockFilterApply}>
                  Apply
                </Button>
              </View>
            </PopoverComponent>
          </View>
          <Text style={[styles.headerCellSmall, styles.headerCellText]}></Text>
        </View>
      )}
      {checkedProducts.length > 0 && (
        <View style={styles.headerRow}>
          <View style={{flex: '1/8'}}>
            {filteredProducts.length === checkedProducts.length ? (
              <View style={styles.headerDeleteWrapper}>
                <Pressable
                  onPress={value => {
                    setCheckedProducts([]);
                  }}
                  style={{flex: 1}}>
                  {checkedIcon()}
                </Pressable>
                <Button
                  color="PRIMARY"
                  variant="PILL"
                  size="SMALL"
                  onPress={() => {
                    onProductBulkDel(checkedProducts);
                    setCheckedProducts([]);
                    resetFilters();
                  }}>
                  Delete all
                </Button>
              </View>
            ) : (
              <View style={styles.headerDeleteWrapper}>
                <Pressable
                  onPress={value => {
                    setCheckedProducts(filteredProducts.map(product => product.id));
                  }}
                  style={{flex: 1}}>
                  {uncheckedIcon()}
                </Pressable>
                <Button
                  color="PRIMARY"
                  variant="PILL"
                  size="SMALL"
                  onPress={() => {
                    onProductBulkDel(checkedProducts);
                    setCheckedProducts([]);
                    resetFilters();
                  }}>
                  Delete selected
                </Button>
              </View>
            )}
          </View>
        </View>
      )}
      <FlatList data={filteredProducts} renderItem={renderItem} keyExtractor={item => item.id} />
    </View>
  );
};

const CollectionFilter = () => {
  const [selectedCollections, setSelectedCollections] = useState<string[]>([]);
  return <View></View>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E3E3E3',
    overflow: 'auto',
  },
  stockFilterWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  stockRadiogroupWrapper: {
    padding: 16,
    width: 200,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E3E3E3',
    paddingVertical: 16,
    backgroundColor: '#F7F7F7',
    marginBottom: 8,
    gap: 8,
    paddingHorizontal: 16,
    height: 48,
    alignItems: 'center',
  },
  checkBox: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
  },
  headerCell: {
    width: '19%',
  },
  headerCellSortWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  deleteHeaderWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerCellText: {
    fontWeight: '500',
    textAlign: 'left',
    fontFamily: theme.FONT_FAMILY,
    color: '#616161',
  },
  headerCellSmall: {
    width: '7%',
    alignSelf: 'flex-start',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    gap: 8,
    paddingHorizontal: 16,
  },
  cell: {
    width: '19%',
    textAlign: 'left',
    fontSize: 12,
    fontFamily: theme.FONT_FAMILY,
    color: '#595959',
  },
  cellSmall: {
    width: '7%',
    textAlign: 'left',
  },
  image: {
    width: 30,
    height: 40,
  },
  headerDeleteWrapper: {flexDirection: 'row', alignItems: 'center', gap: 24},
});

export default ProductTable;
