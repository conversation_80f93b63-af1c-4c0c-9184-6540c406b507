export const shopifyProductTransformer = (data: any) => {
  if (!data) {
    return null;
  }
  return {
    id: data.id,
    price: data.priceRange?.maxVariantPrice?.amount,
    currencyCode: data.priceRange?.minVariantPrice?.currencyCode,
    totalInventory: data.totalInventory,
    collections: data.collections?.edges?.map(edge => edge.node.title).join(', '),
    productOptions: data.options?.map(option => ({
      name: option.name,
      values: option.values,
    })),
    variants: data.variants?.edges?.map(edge => ({
      id: edge.node.id,
      title: edge.node.title,
      price: edge.node.price?.amount,
      currencyCode: edge.node.price?.currencyCode,
      inventoryQuantity: edge.node.quantityAvailable,
      availableForSale: edge.node.availableForSale,
      currentlyNotInStock: edge.node.currentlyNotInStock,
      image: edge.node.image?.url,
      imageMeta: edge.node.image,
      barcode: edge.node.barcode,
      variantOptions: edge.node.selectedOptions?.map(edge => ({name: edge.name, value: edge.value})),
    })),
  };
};
