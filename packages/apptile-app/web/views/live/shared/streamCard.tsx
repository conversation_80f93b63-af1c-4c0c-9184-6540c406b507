import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, Pressable, StyleProp, ViewStyle, Image, TouchableOpacity} from 'react-native';

import {Icon} from 'apptile-core';
import Button from '@/root/web/components-v2/base/Button';
import theme from '@/root/web/styles-v2/theme';

import TextElement from '@/root/web/components-v2/base/TextElement';
import Tooltip from '@/root/web/components-v2/base/SimpleTooltip';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {useDispatch} from 'react-redux';
import {displayStreamDeleteConfirm} from '@/root/web/actions/liveSellingActions';
import {ILiveStream} from '@/root/web/api/LivelyApiTypes';

const styles = StyleSheet.create({
  heading: {
    color: theme.TEXT_COLOR,
  },
  description: {
    marginTop: 5,
    lineHeight: 20,
    color: theme.TEXT_COLOR,
  },
  alignCenter: {
    alignItems: 'center',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  streamCardContainer: {
    width: '19%',
    marginRight: '0.8%',
    marginBottom: 15,
    backgroundColor: '#fff',
    borderRadius: 6,
    overflow: 'hidden',
  },
  streamShowcase: {
    height: 130,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicatorContainer: {
    position: 'absolute',
    zIndex: 1,
    top: 10,
    right: 10,
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  indicator: {
    color: theme.PRIMARY_COLOR,
  },
  relative: {
    position: 'relative',
  },
  contentContainer: {
    width: '100%',
    padding: 13,
    flex: 1,
    alignItems: 'flex-start',
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  title: {
    flexShrink: 1,
    color: '#000',
    marginBottom: 5,
    fontSize: 15,
  },
  description: {
    color: '#64748b',
    marginBottom: 10,
    flexShrink: 1,
  },
  imgContainer: {
    width: '100%',
    height: 130,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  row: {
    flexDirection: 'row',
  },
  flexOne: {
    flex: 1,
  },
  ctaButton: {
    width: 30,
    height: 30,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 5,
  },
  joinButton: {
    backgroundColor: theme.PRIMARY_COLOR,
  },
  endButton: {
    backgroundColor: theme.ERROR_BACKGROUND,
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  commonText: {fontSize: 12, fontWeight: '400', color: '#000'},
  buttonTextStyles: {fontSize: 11, fontWeight: '500'},
  hoverColors: {
    color: '#005BE4',
  },
});

type StreamCardProps = {
  streamInfo: ILiveStream;
  heading: string;
  description: string;
  imageUrl: string;
  status?: 'ENDED' | 'LIVE' | 'SCHEDULED' | 'PENDING';
  startTime?: string;
  streaming_duration?: string;
  onJoinPress?: () => void;
  onModeratePress?: () => void;
  onStopPress?: () => void;
  onEditPress?: () => void;
  productInfo?: Array<any>;
  streamTo?: Array<any>;
  wrapperStyles?: StyleProp<ViewStyle>;
  streamShowcaseStyles?: StyleProp<ViewStyle>;
  onContainerPress?: () => void;
  idUpcoming?: boolean;
};

export const StreamCard = React.forwardRef<View, StreamCardProps>((props, ref) => {
  const {
    streamInfo,
    heading,
    imageUrl,
    status,
    startTime,
    wrapperStyles,
    streamShowcaseStyles,
    streaming_duration,
    streamTo,
    productInfo,
    idUpcoming,
  } = props;
  const [timeRemaining, setTimeRemaining] = useState(getTimeRemaining());
  const dispatch = useDispatch();

  const [hover, setHover] = useState(false);
  const borderWidthSharedValue = useSharedValue(0);
  const shadowSharedValue = useSharedValue(0);
  const onModeratePress = () => {
    props.onModeratePress && props.onModeratePress();
  };

  const onJoinPress = () => {
    props.onJoinPress && props.onJoinPress();
  };

  const onStopPress = () => {
    if (idUpcoming) {
      dispatch(displayStreamDeleteConfirm(streamInfo.streaming_id, ''));
    } else {
      dispatch(displayStreamDeleteConfirm(streamInfo.streaming_id, streamInfo?.recording?.file_info?.file_id));
    }
  };

  const onEditPress = () => {
    props.onEditPress && props.onEditPress();
  };

  const onContainerPress = () => {
    props.onContainerPress && props.onContainerPress();
  };

  const staticDate = new Date(startTime!);
  function getTimeRemaining() {
    const now = new Date();
    const difference = staticDate - now;
    return difference;
  }

  useEffect(() => {
    setTimeRemaining(getTimeRemaining());
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(getTimeRemaining());
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (hover) {
      borderWidthSharedValue.value = 1;
      shadowSharedValue.value = 10;
    } else {
      borderWidthSharedValue.value = 1;
      shadowSharedValue.value = 0;
    }
  }, [hover]);

  const withTimingFunction = (animationWidth: any) =>
    withTiming(animationWidth.value, {
      duration: 50,
      easing: Easing.cubic,
    });

  const animatedBorderWidth = useAnimatedStyle(() => {
    return {
      borderWidth: withTimingFunction(borderWidthSharedValue),
      // borderColor: withSpring(borderColor),
    };
  }, [borderWidthSharedValue]);

  return (
    <Animated.View
      onMouseEnter={() => status == 'ENDED' && setHover(true)}
      onMouseLeave={() => status == 'ENDED' && setHover(false)}
      ref={ref}
      style={[
        styles.streamCardContainer,
        wrapperStyles,
        {borderColor: hover ? '#005BE4' : '#cccccc'},
        animatedBorderWidth,
      ]}>
      <View style={[styles.streamShowcase, streamShowcaseStyles]}>
        <Pressable style={styles.imgContainer} onPress={onContainerPress}>
          <Image
            style={[imageUrl ? styles.cardImage : {width: 80, height: 50}]}
            resizeMode={imageUrl ? 'cover' : 'contain'}
            source={imageUrl ? {uri: imageUrl} : require('@/root/web/assets/images/logo.png')}
          />
        </Pressable>
        {status && status !== 'ENDED' && (
          <View style={styles.indicatorContainer}>
            {status == 'LIVE' && (
              <Icon color={theme.PRIMARY_COLOR} iconType="MaterialCommunityIcons" name="circle" size={12} />
            )}
            <TextElement fontSize="xs" style={[styles.indicator, status === 'SCHEDULED' && {color: '#DAA51B'}]}>
              {status}
            </TextElement>
          </View>
        )}
      </View>
      <Pressable style={[styles.contentContainer]} onPress={onContainerPress}>
        <View style={[styles.row, styles.spaceBetween, {width: '100%'}]}>
          <View style={{flex: 1}}>
            <TextElement fontWeight="500" style={[styles.title, {color: hover && '#005BE4'}]}>
              {heading || 'Live Stream'}
            </TextElement>
          </View>
          {(status == 'LIVE' || status == 'SCHEDULED' || status == 'ENDED') && (
            <Pressable style={[]} onPress={onStopPress}>
              <Tooltip
                tooltip={status == 'ENDED' ? 'Delete Stream' : 'End Stream'}
                position="left"
                toolTipMenuStyles={{width: 80, height: 40, right: 30}}>
                <Icon color={theme.ERROR_BACKGROUND} iconType="MaterialIcons" name="delete" size={20} />
              </Tooltip>
            </Pressable>
          )}
        </View>
        <View style={{flexDirection: 'row', gap: 5, alignItems: 'center', marginTop: 8}}>
          <Text style={[commonStyles.baseText, styles.commonText, {fontWeight: '500'}]}>Start:</Text>
          <Text style={[commonStyles.baseText, styles.commonText]}>
            {new Date(startTime!).toLocaleDateString('en-US', {month: 'short', day: '2-digit', year: '2-digit'})} |{' '}
            {new Date(startTime!).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit'})}
          </Text>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 8, gap: 5}}>
          {streaming_duration && (
            <>
              <Text style={[commonStyles.baseText, styles.commonText]}>{streaming_duration}</Text>
              <Icon iconType="MaterialCommunityIcons" name="circle" size={3} color={'#000'} />
            </>
          )}
          <Text style={[commonStyles.baseText, styles.commonText]}>{productInfo?.length ?? 0} Products</Text>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10, gap: 7}}>
          <Image style={{width: 15, height: 15}} source={require('@/root/web/assets/images/app_icon1.png')} />
          {streamTo?.includes('instagram') && (
            <Image style={{width: 15, height: 15}} source={require('@/root/web/assets/images/instagram_icon.png')} />
          )}
          {streamTo?.includes('facebook') && (
            <Image style={{width: 15, height: 15}} source={require('@/root/web/assets/images/facebook_icon.png')} />
          )}
        </View>
        {status !== 'LIVE' && status === 'SCHEDULED' && timeRemaining > 0 && (
          <View style={{marginTop: 16}}>
            <Button
              textStyles={styles.buttonTextStyles}
              innerContainerStyles={{minHeight: 0}}
              containerStyles={{paddingHorizontal: 10}}
              color={'PRIMARY'}
              variant={'PILL'}
              onPress={onEditPress}>
              Edit Stream
            </Button>
          </View>
        )}
        {(status === 'LIVE' || status === 'SCHEDULED') && timeRemaining < 0 && (
          <View style={[styles.row, {gap: 5, marginTop: 16, flex: 1, flexWrap: 'wrap'}]}>
            <Button
              textStyles={styles.buttonTextStyles}
              innerContainerStyles={{minHeight: 0}}
              containerStyles={{paddingHorizontal: 10}}
              color="CTA"
              onPress={onJoinPress}>
              Join as host
            </Button>
            <Button
              textStyles={styles.buttonTextStyles}
              innerContainerStyles={{minHeight: 0}}
              containerStyles={{paddingHorizontal: 10}}
              color={'PRIMARY'}
              variant={'PILL'}
              onPress={onModeratePress}>
              Join as moderator
            </Button>
          </View>
        )}
      </Pressable>
    </Animated.View>
  );
});
