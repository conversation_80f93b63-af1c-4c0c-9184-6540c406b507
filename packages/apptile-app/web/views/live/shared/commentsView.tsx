import TextElement from '@/root/web/components-v2/base/TextElement';
import CodeInputControlV2 from '@/root/web/components/controls-v2/CodeInputControl';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import {zegoExpEngine} from '../zegoEngine';
import {addPinnedComment, removePinnedComment} from '../../../actions/liveSellingActions';
import React, {useEffect, useRef, useState} from 'react';
import {ActivityIndicator, Image} from 'react-native';
import {StyleSheet, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {LivelyApi} from '@/root/web/api/LivelyApi';
import {Text} from 'react-native';
import {Pressable} from 'react-native';
import theme from '@/root/web/styles-v2/theme';
import {CommentPlatform} from '../liveStream';
import {DropDown} from './DropDown';
import {Icon} from 'apptile-core';
import _ from 'lodash';

export interface ICommentViewProps {
  facebookComments: any;
  comments: any;
  instagramComments: any;
  commentsHistoryUpdate: any;
  streamInfo: any;
  addComment: any;
  appMessagesToPush: any;
  streamId: any;
  commentPlatformType: CommentPlatform;
  setComments: any;
  getFacebookComments: any;
  roomId: string;
  role: number;
  facebookCommentsLoading: boolean;
  allComments?: any;
  instagramCommentsLoading: boolean;
  addInstagramComments: any;
  isShadowCommentsEnabled?: boolean;
  muteComment?: any;
  setShowPolls?: any;
  activePoll?: any;
  isPollingEnabled?: boolean;
}

const CommentsView: React.FC<ICommentViewProps> = props => {
  const {
    facebookComments,
    comments,
    instagramComments,
    commentsHistoryUpdate,
    streamInfo,
    addComment,
    appMessagesToPush,
    streamId,
    commentPlatformType,
    setComments,
    getFacebookComments,
    addInstagramComments,
    roomId,
    role,
    facebookCommentsLoading,
    instagramCommentsLoading,
    allComments,
    isShadowCommentsEnabled,
    muteComment,
    setShowPolls,
    activePoll,
    isPollingEnabled,
  } = props;
  const dispatch = useDispatch();
  const authToken = useSelector(state => state.liveSelling.auth.authToken);
  const facebookStream = useSelector(state => state.liveSelling.facebookTokens[streamId]);
  const facebookTokenError = facebookStream?.error;
  const instagramStream = useSelector(state => state.liveSelling.instagramTokens[streamId]);
  const instagramTokenError = instagramStream?.error;
  const fbChatMessagesRef = useRef(null);
  const chatMessagesRef = useRef(null);
  const instaChatMessagesRef = useRef(null);
  const allChatMessagesRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [newMessages, setNewMessages] = useState(true);

  const [sendMesaageTo, setSendMessageTo] = useState('APP');
  const [inputMsg, setInputMsg] = useState('');
  const [facebookInputMsg, setFacebookInputMsg] = useState('');
  const [instagramInputMsg, setInstagramInputMsg] = useState('');
  const [allInputMsg, setAllInputMsg] = useState('');
  const [facebookMsgSending, setFacebookMsgSending] = useState(false);
  const [instagramMsgSending, setInstagramMsgSending] = useState(false);
  const [popOverId, setPopOverId] = useState('');
  const [isAtBottom, setIsAtBottom] = useState(false);

  // const scrollToBottomComments = () => {
  //   if (chatMessagesRef.current) {
  //     chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
  //   }
  // };
  const scrollToBottomInstaComments = () => {
    if (instaChatMessagesRef.current) {
      instaChatMessagesRef.current.scrollTop = instaChatMessagesRef.current.scrollHeight;
      setIsAtBottom(true);
      setNewMessages(false); // Hide the new message notification
    }
  };
  const scrollToBottomFbComments = () => {
    if (fbChatMessagesRef.current) {
      fbChatMessagesRef.current.scrollTop = fbChatMessagesRef.current.scrollHeight;
      setIsAtBottom(true);
      setNewMessages(false); // Hide the new message notification
    }
  };
  const scrollToBottomAllComments = () => {
    if (allChatMessagesRef.current) {
      allChatMessagesRef.current.scrollTop = allChatMessagesRef.current.scrollHeight;
      setIsAtBottom(true);
      setNewMessages(false); // Hide the new message notification
    }
  };

  const scrollToBottom = () => {
    if (chatMessagesRef.current) {
      const messageContainer = chatMessagesRef.current;
      messageContainer.scrollTop = messageContainer.scrollHeight;
      setIsAtBottom(true);
      setNewMessages(false); // Hide the new message notification
    }
  };

  const handleScroll = type => () => {
    const messageContainer =
      type == CommentPlatform.ALL
        ? allChatMessagesRef.current
        : type == CommentPlatform.FACEBOOK
        ? fbChatMessagesRef.current
        : type == CommentPlatform.INSTAGRAM
        ? instaChatMessagesRef.current
        : chatMessagesRef.current;
    const isUserAtBottom =
      messageContainer.scrollHeight - messageContainer.scrollTop <= messageContainer.clientHeight + 50;

    setIsAtBottom(isUserAtBottom);

    // If user scrolls up, and new messages come in, we show the notification
    if (!isUserAtBottom) {
      setNewMessages(true);
    }
  };

  // Whenever new messages arrive, scroll to bottom if the user is at the bottom
  useEffect(() => {
    if (isAtBottom) {
      scrollToBottom();
    }
  }, [comments, isAtBottom]);

  useEffect(() => {
    if (isAtBottom) {
      scrollToBottomFbComments();
    }
  }, [facebookComments, isAtBottom]);

  useEffect(() => {
    if (isAtBottom) {
      scrollToBottomInstaComments();
    }
  }, [facebookComments, isAtBottom]);

  useEffect(() => {
    if (isAtBottom) {
      scrollToBottomAllComments();
    }
  }, [allComments, isAtBottom]);

  useEffect(() => {
    if (allChatMessagesRef.current) {
      scrollToBottomAllComments();
    }
    if (fbChatMessagesRef.current) {
      scrollToBottomFbComments();
    }
    if (instaChatMessagesRef.current) {
      scrollToBottomInstaComments();
    }
    if (chatMessagesRef.current) {
      scrollToBottom();
    }
  }, [fbChatMessagesRef.current, chatMessagesRef.current, instaChatMessagesRef.current, allChatMessagesRef.current]);

  let pinnedComment = useSelector(state => state.liveSelling.pinnedComments[streamId]);

  const handlePopOver = (id: string) => {
    popOverId.length > 1 ? setPopOverId('') : setPopOverId(id);
  };
  const handlePinComment = (comment: any) => {
    dispatch(addPinnedComment(comment, streamId));
    setPopOverId('');
  };
  const handleUnPinComment = () => {
    dispatch(removePinnedComment(streamId));
    setPopOverId('');
  };
  const handleDeleteComment = comment => {
    deleteComment(comment);
    setPopOverId('');
  };
  const deleteComment = (newComment: any) => {
    setComments(comments.filter(comment => comment.id != newComment.id));
  };

  const handleComment = (message: string) => {
    let obj = {
      name: 'You',
      comment: message,
      imgUrl: streamInfo?.streaming_thumbnail,
      id: `${new Date().getTime()}${Math.floor(Math.random() * 10000)}`,
      source: 'HOST',
      isPinned: false,
      from: 'APP',
    };
    if (message.length > 0) {
      addComment(obj);
      setInputMsg('');
      setAllInputMsg('');
      zegoExpEngine.sendBarrageMessage(roomId, JSON.stringify(obj));
      appMessagesToPush.current = appMessagesToPush.current.concat([
        {
          comment: obj.comment,
          user_type: obj.source == 'HOST' ? 'host' : obj.source == 'MODERATOR' ? 'moderator' : 'viewer',
          user_name: obj?.name,
          from: 'APP',
        },
      ]);
      setTimeout(() => {
        commentsHistoryUpdate(streamId);
      }, 300);
    }
  };

  const handleFacebookComment = async (message: string) => {
    try {
      const response = await LivelyApi.sendFacebookComment(facebookStream?.token, facebookStream?.fb_video_id, message);
      if (!response.status == 200) {
        throw new Error('Network response was not ok');
      }
      setFacebookMsgSending(false);
      setTimeout(getFacebookComments, 300);
    } catch (error) {
      console.error('Error sending fb message:', error);
      setFacebookMsgSending(false);
    }
  };

  const handleInstagramComment = async (message: string) => {
    try {
      const response = await LivelyApi.sendInstagramComment(authToken, streamInfo?.insta_auth_id, streamId, message);
      if (!response.status == 200) {
        throw new Error('Network response was not ok');
      }
      setInstagramMsgSending(false);
      const data = response?.data?.data;
      const newComment = {
        name: data.comment.user.full_name,
        comment: message,
        imgUrl: '',
        id: data.comment.pk,
        source: 'HOST',
        isReplied: false,
        from: 'INSTAGRAM',
      };
      addInstagramComments([newComment]);
    } catch (error) {
      console.error('Error sending insta message:', error);
      setInstagramMsgSending(false);
    }
  };

  const instagramView = () => (
    <View style={styles.commentSection}>
      {!instagramTokenError && (
        <>
          <View style={{flex: 1, maxHeight: 100, paddingTop: 10, overflow: 'auto', flexGrow: 0, flexBasis: 'auto'}}>
            {pinnedComment && pinnedComment?.from !== 'APP' && pinnedComment?.from !== 'FACEBOOK' && (
              <View key={pinnedComment.id} style={[styles.commentContainer, {borderColor: '#1261e1', width: '100%'}]}>
                <View style={{flex: 1, flexBasis: 'auto', width: '90%'}}>
                  <View style={[styles.rowLayout, styles.alignCenter]}>
                    <View style={{padding: 2}}>
                      <Image
                        source={
                          pinnedComment.imgUrl
                            ? {uri: pinnedComment.imgUrl}
                            : require('@/root/web/assets/images/apptile_icon.png')
                        }
                        style={{height: 35, width: 35, borderRadius: 50}}
                      />
                    </View>
                    <View style={styles.commentBox}>
                      <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        <TextElement color={'SECONDARY'} fontSize="xs" fontWeight="600">
                          {pinnedComment.name}
                        </TextElement>
                        <ApptileWebIcon name={'pin-fill'} size={15} style={{marginLeft: 10}} />
                      </View>
                      <TextElement style={styles.commentText} color={'SECONDARY'} fontSize="sm">
                        {pinnedComment.comment}
                      </TextElement>
                    </View>
                  </View>
                </View>
                <View style={{flex: 1, flexBasis: 'auto', flexGrow: 0, width: '10%'}}>
                  <Pressable onPress={() => handlePopOver(pinnedComment.id)}>
                    <ApptileWebIcon name={'menu-vertical'} size={20} />
                  </Pressable>
                </View>
                {popOverId == pinnedComment.id && (
                  <View style={styles.popOverContainer}>
                    <Pressable
                      style={[styles.rowLayout, {alignItems: 'center'}]}
                      onPress={() => handleUnPinComment(pinnedComment)}>
                      <Image source={require('@/root/web/assets/icons/pin-fill.svg')} style={{width: 20, height: 20}} />
                      <Text style={{marginLeft: 10}}>Unpin</Text>
                    </Pressable>
                  </View>
                )}
              </View>
            )}
          </View>
          <View
            onScroll={handleScroll(CommentPlatform.INSTAGRAM)}
            ref={instaChatMessagesRef}
            style={styles.chatMessageWrapper}>
            {instagramCommentsLoading && (
              <TextElement color={'SECONDARY'} fontSize={'sm'}>
                Updating comments...
              </TextElement>
            )}
            {instagramComments.map((comment, index) => (
              <View key={comment.id} style={styles.commentContainer}>
                <CommentCard
                  platform={CommentPlatform.INSTAGRAM}
                  comment={comment}
                  comments={instagramComments}
                  key={`instagram-${index}`}
                />
                <View style={{flex: 1, flexBasis: 'auto', flexGrow: 0, width: '10%'}}>
                  {pinnedComment?.id != comment?.id && (
                    <Pressable onPress={() => handlePopOver(comment.id)}>
                      <ApptileWebIcon name={'menu-vertical'} size={20} />
                    </Pressable>
                  )}
                </View>
                {pinnedComment?.id != comment?.id && popOverId == comment.id && (
                  <View style={styles.popOverContainer}>
                    <Pressable
                      style={[styles.rowLayout, {alignItems: 'center', zIndex: 10}]}
                      onPress={() => handlePinComment({...comment, from: 'Instagram'})}>
                      <ApptileWebIcon name={'pin-outline'} size={20} />
                      <Text style={{marginLeft: 10}}>Pin</Text>
                    </Pressable>
                    {/* <Pressable
                              style={[styles.rowLayout, {alignItems: 'center', zIndex: 10, marginTop: 8}]}
                              onPress={() => handleDeleteComment(comment)}>
                              <ApptileWebIcon name={'delete'} size={20} />
                              <Text style={{marginLeft: 10}}>Delete</Text>
                            </Pressable> */}
                  </View>
                )}
              </View>
            ))}
          </View>
          {newMessages && (
            <Pressable
              style={{
                width: '25px',
                height: '25px',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'absolute',
                bottom: '70px',
                right: '22px',
              }}
              onPress={() => scrollToBottomInstaComments()}>
              <Icon
                name={'chevron-down'}
                iconType={'MaterialCommunityIcons'}
                size={20}
                color={'#444'}
                style={{
                  margin: 0,
                  verticalAlign: 'middle',
                  backgroundColor: '#eee',
                  borderRadius: 550,
                  fontWeight: 300,
                  padding: 4,
                  borderWidth: 2,
                  borderColor: '#e5e5e5',
                }}
              />
            </Pressable>
          )}
          <View style={{flexDirection: 'row', width: '100%', alignItems: 'center'}}>
            <View style={styles.inputIconContainer}>
              <Image style={{width: 22, height: 22}} source={require('@/root/web/assets/images/logos_instagram.png')} />
            </View>
            <CodeInputControlV2
              containerStyles={styles.codeInputCustomerStyles}
              inputStyles={styles.codeInputStyles}
              placeholder={'Type a message in Instagram'}
              value={instagramInputMsg}
              onChange={setInstagramInputMsg}
              disabled={instagramMsgSending}
              noOfLines={1}
              onKeyDown={(e: any, ref: any) => {
                if (e.key === 'Enter') {
                  setInstagramMsgSending(true);
                  setTimeout(() => setInstagramInputMsg(''), 100);
                  handleInstagramComment(instagramInputMsg);
                  if (ref?.current) setTimeout(() => (ref.current!.value = ''), 100);
                }
              }}
            />
          </View>
        </>
      )}
      {!!instagramTokenError && (
        <View style={{alignItems: 'center', justifyContent: 'center', gap: 25}}>
          <TextElement color={'SECONDARY'} fontSize={'lg'} fontWeight={'600'}>
            Oops
          </TextElement>
          <TextElement color={'SECONDARY'} fontSize={'sm'}>
            Instagram not ready yet
          </TextElement>
        </View>
      )}
    </View>
  );

  const facebookView = () => (
    <View style={styles.commentSection}>
      {!facebookTokenError && (
        <>
          <View style={{flex: 1, maxHeight: 100, paddingTop: 10, overflow: 'auto', flexGrow: 0, flexBasis: 'auto'}}>
            {pinnedComment && pinnedComment?.from !== 'APP' && (
              <View key={pinnedComment.id} style={[styles.commentContainer, {borderColor: '#1261e1', width: '100%'}]}>
                <View style={{flex: 1, flexBasis: 'auto', width: '90%'}}>
                  <View style={[styles.rowLayout, styles.alignCenter]}>
                    <View style={{padding: 2}}>
                      <Image
                        source={
                          pinnedComment.imgUrl
                            ? {uri: pinnedComment.imgUrl}
                            : require('@/root/web/assets/images/apptile_icon.png')
                        }
                        style={{height: 35, width: 35, borderRadius: 50}}
                      />
                    </View>
                    <View style={styles.commentBox}>
                      <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        <TextElement color={'SECONDARY'} fontSize="xs" fontWeight="600">
                          {pinnedComment.name}
                        </TextElement>
                        <ApptileWebIcon name={'pin-fill'} size={15} style={{marginLeft: 10}} />
                      </View>
                      <TextElement style={styles.commentText} color={'SECONDARY'} fontSize="sm">
                        {pinnedComment.comment}
                      </TextElement>
                    </View>
                  </View>
                </View>
                <View style={{flex: 1, flexBasis: 'auto', flexGrow: 0, width: '10%'}}>
                  <Pressable onPress={() => handlePopOver(pinnedComment.id)}>
                    <ApptileWebIcon name={'menu-vertical'} size={20} />
                  </Pressable>
                </View>
                {popOverId == pinnedComment.id && (
                  <View style={styles.popOverContainer}>
                    <Pressable
                      style={[styles.rowLayout, {alignItems: 'center'}]}
                      onPress={() => handleUnPinComment(pinnedComment)}>
                      <Image source={require('@/root/web/assets/icons/pin-fill.svg')} style={{width: 20, height: 20}} />
                      <Text style={{marginLeft: 10}}>Unpin</Text>
                    </Pressable>
                  </View>
                )}
              </View>
            )}
          </View>
          <View
            onScroll={handleScroll(CommentPlatform.FACEBOOK)}
            ref={fbChatMessagesRef}
            style={styles.chatMessageWrapper}>
            {facebookCommentsLoading && (
              <TextElement color={'SECONDARY'} fontSize={'sm'}>
                Updating comments...
              </TextElement>
            )}
            {facebookComments.map((comment, index) => (
              <View key={comment.id} style={styles.commentContainer}>
                <CommentCard
                  platform={CommentPlatform.FACEBOOK}
                  comment={comment}
                  comments={facebookComments}
                  key={`facebook-${index}`}
                />
                <View style={{flex: 1, flexBasis: 'auto', flexGrow: 0, width: '10%'}}>
                  {pinnedComment?.id != comment?.id && (
                    <Pressable onPress={() => handlePopOver(comment.id)}>
                      <ApptileWebIcon name={'menu-vertical'} size={20} />
                    </Pressable>
                  )}
                </View>
                {pinnedComment?.id != comment?.id && popOverId == comment.id && (
                  <View style={styles.popOverContainer}>
                    <Pressable
                      style={[styles.rowLayout, {alignItems: 'center', zIndex: 10}]}
                      onPress={() => handlePinComment({...comment, from: 'Facebook'})}>
                      <ApptileWebIcon name={'pin-outline'} size={14} />
                      <Text style={{marginLeft: 10, fontSize: 12}}>Pin Comment</Text>
                    </Pressable>
                    {/* <Pressable
                              style={[styles.rowLayout, {alignItems: 'center', zIndex: 10, marginTop: 8}]}
                              onPress={() => handleDeleteComment(comment)}>
                              <ApptileWebIcon name={'delete'} size={20} />
                              <Text style={{marginLeft: 10}}>Delete</Text>
                            </Pressable> */}
                  </View>
                )}
              </View>
            ))}
          </View>
          {newMessages && (
            <Pressable
              style={{
                width: '25px',
                height: '25px',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'absolute',
                bottom: '70px',
                right: '22px',
              }}
              onPress={() => scrollToBottomFbComments()}>
              <Icon
                name={'chevron-down'}
                iconType={'MaterialCommunityIcons'}
                size={20}
                color={'#444'}
                style={{
                  margin: 0,
                  verticalAlign: 'middle',
                  backgroundColor: '#eee',
                  borderRadius: 550,
                  fontWeight: 300,
                  padding: 4,
                  borderWidth: 2,
                  borderColor: '#e5e5e5',
                }}
              />
            </Pressable>
          )}
          <View style={{flexDirection: 'row', width: '100%', alignItems: 'center'}}>
            <View style={styles.inputIconContainer}>
              <Image style={{width: 22, height: 22}} source={require('@/root/web/assets/images/facebook_icon.png')} />
            </View>
            <CodeInputControlV2
              containerStyles={styles.codeInputCustomerStyles}
              inputStyles={styles.codeInputStyles}
              placeholder={'Type a message in Facebook'}
              value={facebookInputMsg}
              onChange={setFacebookInputMsg}
              disabled={facebookMsgSending}
              noOfLines={1}
              onKeyDown={(e: any, ref: any) => {
                if (e.key === 'Enter') {
                  setFacebookMsgSending(true);
                  setTimeout(() => setFacebookInputMsg(''), 100);
                  handleFacebookComment(facebookInputMsg);
                  if (ref.current) setTimeout(() => (ref.current!.value = ''), 100);
                }
              }}
            />
          </View>
        </>
      )}
      {!!facebookTokenError && (
        <View style={{alignItems: 'center', justifyContent: 'center', gap: 25}}>
          <TextElement color={'SECONDARY'} fontSize={'lg'} fontWeight={'600'}>
            Oops
          </TextElement>
          <TextElement color={'SECONDARY'} fontSize={'sm'}>
            Facebook not ready yet
          </TextElement>
        </View>
      )}
    </View>
  );

  const appView = () => (
    <View style={[styles.commentSection]}>
      <View style={{flex: 1}}>
        <View style={{flex: 1, maxHeight: 100, paddingTop: 10, overflow: 'auto', flexGrow: 0, flexBasis: 'auto'}}>
          {pinnedComment && pinnedComment?.from === 'APP' && (
            <View key={pinnedComment.id} style={[styles.commentContainer, {borderColor: '#1261e1', width: '100%'}]}>
              <View style={{flex: 1, flexBasis: 'auto', width: '90%'}}>
                <View style={[styles.rowLayout, styles.alignCenter]}>
                  <View style={{padding: 2}}>
                    <Image
                      source={
                        pinnedComment.imgUrl
                          ? {uri: pinnedComment.imgUrl}
                          : require('@/root/web/assets/images/apptile_icon.png')
                      }
                      style={{height: 35, width: 35, borderRadius: 50}}
                    />
                  </View>
                  <View style={styles.commentBox}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                      <TextElement color={'SECONDARY'} fontSize="xs" fontWeight="600">
                        {pinnedComment.name}
                      </TextElement>
                      <ApptileWebIcon name={'pin-fill'} size={15} style={{marginLeft: 10}} />
                    </View>
                    <TextElement style={styles.commentText} color={'SECONDARY'} fontSize="sm">
                      {pinnedComment.comment}
                    </TextElement>
                  </View>
                </View>
              </View>
              <View style={{flex: 1, flexBasis: 'auto', flexGrow: 0, width: '10%'}}>
                <Pressable onPress={() => handlePopOver(pinnedComment.id)}>
                  <ApptileWebIcon name={'menu-vertical'} size={20} />
                </Pressable>
              </View>
              {popOverId == pinnedComment.id && (
                <View style={styles.popOverContainer}>
                  <Pressable
                    style={[styles.rowLayout, {alignItems: 'center'}]}
                    onPress={() => handleUnPinComment(pinnedComment)}>
                    <Image source={require('@/root/web/assets/icons/pin-fill.svg')} style={{width: 20, height: 20}} />
                    <Text style={{marginLeft: 10}}>Unpin</Text>
                  </Pressable>
                  {/* <Pressable
                            style={[styles.rowLayout, {alignItems: 'center', marginTop: 8}]}
                            onPress={() => handleDeleteComment(comment)}>
                            <Image
                              source={require('@/root/web/assets/icons/delete.svg')}
                              style={{width: 20, height: 20}}
                            />
                            <Text style={{marginLeft: 10}}>Delete</Text>
                          </Pressable> */}
                </View>
              )}
            </View>
          )}
        </View>
        <View onScroll={handleScroll(CommentPlatform.APP)} ref={chatMessagesRef} style={styles.chatMessageWrapper}>
          {comments.map((comment, index) => (
            <View key={comment.id} style={styles.commentContainer}>
              <CommentCard
                platform={CommentPlatform.APP}
                comment={comment}
                comments={comments}
                key={`comment-${index}`}
              />
              <View style={{flex: 1, flexBasis: 'auto', flexGrow: 0, width: '10%'}}>
                {pinnedComment?.id != comment?.id && (
                  <Pressable onPress={() => handlePopOver(comment.id)}>
                    <ApptileWebIcon name={'menu-vertical'} size={20} />
                  </Pressable>
                )}
              </View>
              {pinnedComment?.id != comment?.id && popOverId == comment.id && (
                <View style={[styles.popOverContainer]}>
                  <View style={{flexDirection: 'column', alignItems: 'center'}}>
                    <Pressable
                      style={[styles.rowLayout, {alignItems: 'center', zIndex: 10}]}
                      onPress={() => handlePinComment({...comment, from: 'APP'})}
                      disabled={comment.muted}>
                      <ApptileWebIcon name={'pin-outline'} size={14} color={comment.muted ? '#9d9d9d' : '#000'} />
                      <Text style={[{marginLeft: 10, fontSize: 12}, comment.muted && {color: '#9d9d9d'}]}>
                        Pin comment
                      </Text>
                    </Pressable>
                    {role == 2 &&
                      (comment.source === 'HOST' ? false : comment.user_type === 'host' ? false : true) &&
                      isShadowCommentsEnabled && (
                        <Pressable
                          style={[styles.rowLayout, {alignItems: 'center', zIndex: 10, marginTop: 8, width: '100%'}]}
                          onPress={() => {
                            setPopOverId('');
                            muteComment({...comment, from: 'APP'});
                          }}>
                          {comment.muted ? (
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                              <View style={{flexDirection: 'column', alignItems: 'center'}}>
                                <Icon name={'volume-2'} iconType={'Feather'} size={14} color={'#444'} />
                              </View>
                              <Text style={{marginLeft: 10, fontSize: 12}}>Unmute User</Text>
                            </View>
                          ) : (
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                              <View style={{flexDirection: 'column', alignItems: 'center'}}>
                                <Icon name={'volume-x'} iconType={'Feather'} size={14} color={'#444'} />
                              </View>
                              <Text style={{marginLeft: 10, fontSize: 12}}>Mute User</Text>
                            </View>
                          )}
                        </Pressable>
                      )}
                  </View>
                  {/* <Pressable
                            style={[styles.rowLayout, {alignItems: 'center', zIndex: 10, marginTop: 8}]}
                            onPress={() => handleDeleteComment(comment)}>
                            <ApptileWebIcon name={'delete'} size={20} />
                            <Text style={{marginLeft: 10}}>Delete</Text>
                          </Pressable> */}
                </View>
              )}
            </View>
          ))}
        </View>
      </View>
      {newMessages && (
        <Pressable
          style={{
            width: '25px',
            height: '25px',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            bottom: '70px',
            right: '22px',
          }}
          onPress={() => scrollToBottom()}>
          <Icon
            name={'chevron-down'}
            iconType={'MaterialCommunityIcons'}
            size={20}
            color={'#444'}
            style={{
              margin: 0,
              verticalAlign: 'middle',
              backgroundColor: '#eee',
              borderRadius: 550,
              fontWeight: 300,
              padding: 4,
              borderWidth: 2,
              borderColor: '#e5e5e5',
            }}
          />
        </Pressable>
      )}

      <View style={{flexDirection: 'row', width: '100%', alignItems: 'center'}}>
        <View style={styles.inputIconContainer}>
          <Image style={{width: 22, height: 22}} source={require('@/root/web/assets/images/app_icon1.png')} />
        </View>
        <CodeInputControlV2
          containerStyles={role == 1 ? styles.codeInputCustomerStyles : {flex: 1}}
          inputStyles={role == 1 ? styles.codeInputStyles : {borderRadius: 0}}
          placeholder={'Type a message in App'}
          value={inputMsg}
          onChange={setInputMsg}
          noOfLines={1}
          onKeyDown={(e: any, ref: any) => {
            if (e.key === 'Enter') {
              handleComment(inputMsg);
              setTimeout(() => setInputMsg(''), 100);
              if (ref.current) setTimeout(() => (ref.current!.value = ''), 100);
            }
          }}
        />
        {role == 2 && isPollingEnabled && (
          <Pressable
            onPress={() => setShowPolls(true)}
            style={[
              styles.inputIconContainer,
              {
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
                borderTopRightRadius: 8,
                borderBottomRightRadius: 8,
                position: 'relative',
              },
            ]}>
            <Icon name={'bar-chart-2'} iconType={'Feather'} size={20} color={'#B6B6B6'} />

            {!_.isEmpty(activePoll) && (
              <View
                style={{
                  width: 10,
                  height: 10,
                  borderRadius: 10,
                  backgroundColor: 'red',
                  position: 'absolute',
                  top: 4,
                  right: 4,
                }}
              />
            )}
          </Pressable>
        )}
      </View>
    </View>
  );

  const allCommentsView = () => (
    <View style={[styles.commentSection]}>
      <View style={{flex: 1}}>
        <View style={{flex: 1, maxHeight: 100, paddingTop: 10, overflow: 'auto', flexGrow: 0, flexBasis: 'auto'}}>
          {pinnedComment && (
            <View key={pinnedComment.id} style={[styles.commentContainer, {borderColor: '#1261e1', width: '100%'}]}>
              <View style={{flex: 1, flexBasis: 'auto', width: '90%'}}>
                <View style={[styles.rowLayout, styles.alignCenter]}>
                  <View style={{padding: 2}}>
                    <Image
                      source={
                        pinnedComment.imgUrl
                          ? {uri: pinnedComment.imgUrl}
                          : require('@/root/web/assets/images/apptile_icon.png')
                      }
                      style={{height: 35, width: 35, borderRadius: 50}}
                    />
                  </View>
                  <View style={styles.commentBox}>
                    <View style={{flexDirection: 'row', gap: 7, alignItems: 'center', marginBottom: 5}}>
                      <TextElement color={'SECONDARY'} fontSize="xs" fontWeight="600">
                        {pinnedComment.name}
                      </TextElement>
                      <Icon
                        name="ellipse"
                        iconType="Ionicons"
                        size={7}
                        color={"rgba(133, 133, 133, 1)'"}
                        style={{
                          margin: 0,
                          fontWeight: 600,
                          alignSelf: 'center',
                          alignContent: 'center',
                        }}
                      />
                      <TextElement
                        style={[
                          styles.commentName,
                          {color: 'rgba(133, 133, 133, 1)', marginBottom: 0, alignSelf: 'center'},
                        ]}
                        fontSize="xs"
                        fontWeight="500">
                        {pinnedComment.from === 'APP'
                          ? 'App'
                          : pinnedComment.from === 'INSTAGRAM'
                          ? 'Instagram'
                          : 'Facebook'}
                      </TextElement>
                      <ApptileWebIcon name={'pin-fill'} size={15} style={{marginLeft: 10}} />
                    </View>
                    <TextElement style={styles.commentText} color={'SECONDARY'} fontSize="sm">
                      {pinnedComment.comment}
                    </TextElement>
                  </View>
                </View>
              </View>
              <View style={{flex: 1, flexBasis: 'auto', flexGrow: 0, width: '10%'}}>
                <Pressable onPress={() => handlePopOver(pinnedComment.id)}>
                  <ApptileWebIcon name={'menu-vertical'} size={20} />
                </Pressable>
              </View>
              {popOverId == pinnedComment.id && (
                <View style={styles.popOverContainer}>
                  <Pressable
                    style={[styles.rowLayout, {alignItems: 'center'}]}
                    onPress={() => handleUnPinComment(pinnedComment)}>
                    <Image source={require('@/root/web/assets/icons/pin-fill.svg')} style={{width: 20, height: 20}} />
                    <Text style={{marginLeft: 10}}>Unpin</Text>
                  </Pressable>
                  {/* <Pressable
                            style={[styles.rowLayout, {alignItems: 'center', marginTop: 8}]}
                            onPress={() => handleDeleteComment(comment)}>
                            <Image
                              source={require('@/root/web/assets/icons/delete.svg')}
                              style={{width: 20, height: 20}}
                            />
                            <Text style={{marginLeft: 10}}>Delete</Text>
                          </Pressable> */}
                </View>
              )}
            </View>
          )}
        </View>
        <View onScroll={handleScroll(CommentPlatform.ALL)} ref={allChatMessagesRef} style={styles.chatMessageWrapper}>
          {allComments.map((comment, index) => (
            <View key={comment.id} style={styles.commentContainer}>
              <AllCommentCard
                platform={CommentPlatform.ALL}
                comment={comment}
                comments={allComments}
                key={`all-${index}`}
              />
              <View style={{flex: 1, flexBasis: 'auto', flexGrow: 0, width: '10%'}}>
                {pinnedComment?.id != comment?.id && (
                  <Pressable onPress={() => handlePopOver(comment.id)}>
                    <ApptileWebIcon name={'menu-vertical'} size={20} />
                  </Pressable>
                )}
              </View>
              {pinnedComment?.id != comment?.id && popOverId == comment.id && (
                <View style={styles.popOverContainer}>
                  <View style={{flexDirection: 'column', alignItems: 'center'}}>
                    <Pressable
                      style={[styles.rowLayout, {alignItems: 'center', zIndex: 10}]}
                      onPress={() => handlePinComment(comment)}
                      disabled={comment.muted}>
                      <ApptileWebIcon name={'pin-outline'} size={14} color={comment.muted ? '#9d9d9d' : '#000'} />
                      <Text style={[{marginLeft: 10, fontSize: 12}, comment.muted && {color: '#9d9d9d'}]}>
                        Pin comment
                      </Text>
                    </Pressable>
                    {role == 2 &&
                      comment.from == 'APP' &&
                      (comment.source === 'HOST' ? false : comment.user_type === 'host' ? false : true) &&
                      isShadowCommentsEnabled && (
                        <Pressable
                          style={[styles.rowLayout, {alignItems: 'center', zIndex: 10, marginTop: 8, width: '100%'}]}
                          onPress={() => {
                            setPopOverId('');
                            muteComment({...comment, from: 'APP'});
                          }}>
                          {comment.muted ? (
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                              <View style={{flexDirection: 'column', alignItems: 'center'}}>
                                <Icon name={'volume-2'} iconType={'Feather'} size={14} color={'#444'} />
                              </View>
                              <Text style={{marginLeft: 10, fontSize: 12}}>Unmute User</Text>
                            </View>
                          ) : (
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                              <View style={{flexDirection: 'column', alignItems: 'center'}}>
                                <Icon name={'volume-x'} iconType={'Feather'} size={14} color={'#444'} />
                              </View>
                              <Text style={{marginLeft: 10, fontSize: 12}}>Mute User</Text>
                            </View>
                          )}
                        </Pressable>
                      )}
                  </View>
                </View>
              )}
            </View>
          ))}
        </View>
      </View>
      {newMessages && (
        <Pressable
          style={{
            width: '25px',
            height: '25px',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            bottom: '70px',
            right: '22px',
          }}
          onPress={() => scrollToBottomAllComments()}>
          <Icon
            name={'chevron-down'}
            iconType={'MaterialCommunityIcons'}
            size={20}
            color={'#444'}
            style={{
              margin: 0,
              verticalAlign: 'middle',
              backgroundColor: '#eee',
              borderRadius: 550,
              fontWeight: 300,
              padding: 4,
              borderWidth: 2,
              borderColor: '#e5e5e5',
            }}
          />
        </Pressable>
      )}

      <View style={{flexDirection: 'row', width: '100%', alignItems: 'center'}}>
        <DropDown
          value={sendMesaageTo}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          options={[
            {value: 'APP', label: 'App', image: require('@/root/web/assets/images/app_icon1.png')},
            {value: 'FACEBOOK', label: 'Facebook', image: require('@/root/web/assets/images/facebook_icon.png')},
            {value: 'INSTAGRAM', label: 'Instagram', image: require('@/root/web/assets/images/logos_instagram.png')},
          ]}
          onSelect={(type: string) => {
            setSendMessageTo(type);
          }}
        />
        <CodeInputControlV2
          placeholder={`Type a message in ${sendMesaageTo.toLowerCase()}`}
          value={allInputMsg}
          onChange={setAllInputMsg}
          noOfLines={1}
          disabled={
            (sendMesaageTo === 'FACEBOOK' && facebookMsgSending) ||
            (sendMesaageTo === 'INSTAGRAM' && instagramMsgSending)
          }
          containerStyles={role == 1 ? {width: isOpen ? '70%' : '84%'} : {flex: 1}}
          inputStyles={role == 1 ? styles.codeInputStyles : {borderRadius: 0}}
          onKeyPress={(e: any, ref: any) => {
            if (e.key === 'Enter') {
              if (sendMesaageTo === 'APP') {
                handleComment(allInputMsg);
                setTimeout(() => setAllInputMsg(''), 100);
              } else if (sendMesaageTo === 'FACEBOOK') {
                setFacebookMsgSending(true);
                // setTimeout(() => setFacebookInputMsg(''), 100);
                setTimeout(() => setAllInputMsg(''), 100);
                handleFacebookComment(allInputMsg);
              } else if (sendMesaageTo === 'INSTAGRAM') {
                setInstagramMsgSending(true);
                setTimeout(() => setAllInputMsg(''), 100);
                handleInstagramComment(allInputMsg);
              }

              if (ref.current) setTimeout(() => (ref.current!.value = ''), 100);
            }
          }}
        />
        {role == 2 && isPollingEnabled && (
          <Pressable
            onPress={() => setShowPolls(true)}
            style={[
              styles.inputIconContainer,
              {
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
                borderTopRightRadius: 8,
                borderBottomRightRadius: 8,
                position: 'relative',
              },
            ]}>
            <Icon name={'bar-chart-2'} iconType={'Feather'} size={20} color={'#B6B6B6'} />

            {!_.isEmpty(activePoll) && (
              <View
                style={{
                  width: 10,
                  height: 10,
                  borderRadius: 10,
                  backgroundColor: 'red',
                  position: 'absolute',
                  top: 4,
                  right: 4,
                }}
              />
            )}
          </Pressable>
        )}
      </View>
    </View>
  );

  const requiredCommentView = () => {
    switch (commentPlatformType) {
      case CommentPlatform.FACEBOOK:
        return facebookView();
      case CommentPlatform.INSTAGRAM:
        return instagramView();
      case CommentPlatform.APP:
        return appView();
      case CommentPlatform.ALL:
        return allCommentsView();
      default:
        return allCommentsView();
    }
  };

  return requiredCommentView();
};

export const AllCommentCard = ({comments, comment, key, platform}) => {
  let CommentName;
  switch (platform) {
    case CommentPlatform.APP:
      CommentName = comment.source === 'HOST' ? 'Host' : comment.name;
      break;
    case CommentPlatform.FACEBOOK:
      CommentName = comment.source === 'HOST' ? 'Host' : comment.name;
      break;
    case CommentPlatform.ALL:
      CommentName = comment.source === 'HOST' ? 'Host' : comment.name;
      break;
    default:
      CommentName = comment.source === 'HOST' ? 'Host' : comment.name;
  }
  return (
    <View key={key} style={{flex: 1, flexBasis: 'auto', width: '90%'}}>
      <View style={[styles.rowLayout]}>
        {comment.muted && (
          <View style={{flexDirection: 'column', alignItems: 'center', justifyContent: 'center', marginRight: 8}}>
            <Icon name="volume-x" iconType="Feather" size={16} color={'#9D9D9D'} />
          </View>
        )}
        <View style={{padding: 2}}>
          <Image
            source={comments.imgUrl ? {uri: comments.imgUrl} : require('@/root/web/assets/images/apptile_icon.png')}
            style={{height: 35, width: 35, borderRadius: 50}}
          />
        </View>
        <View style={styles.commentBox}>
          <View style={{flexDirection: 'row', gap: 7, alignItems: 'center', marginBottom: 5}}>
            <TextElement
              color={'SECONDARY'}
              style={[
                styles.commentName,
                CommentName === 'Host' && styles.hostCommentName,
                comment.muted && {color: '#9D9D9D'},
                {marginBottom: 0},
              ]}
              fontSize="xs"
              fontWeight="600">
              {CommentName}
            </TextElement>
            <Icon
              name="ellipse"
              iconType="Ionicons"
              size={7}
              color={"rgba(133, 133, 133, 1)'"}
              style={{
                margin: 0,
                fontWeight: 600,
                alignSelf: 'center',
                alignContent: 'center',
              }}
            />
            <TextElement
              style={[styles.commentName, {color: 'rgba(133, 133, 133, 1)', marginBottom: 0, alignSelf: 'center'}]}
              fontSize="xs"
              fontWeight="500">
              {comment.from === 'APP' ? 'App' : comment.from === 'INSTAGRAM' ? 'Instagram' : 'Facebook'}
            </TextElement>
          </View>
          <TextElement
            style={[styles.commentText, comment.muted && {color: '#9D9D9D'}]}
            color={'SECONDARY'}
            fontSize="sm">
            {comment.comment}
          </TextElement>
        </View>
      </View>
    </View>
  );
};

export const CommentCard = ({comments, comment, key, platform}) => {
  let CommentName;
  switch (platform) {
    case CommentPlatform.APP:
      CommentName = comment.source !== 'USER' ? 'Host' : comment.name;
      break;
    case CommentPlatform.FACEBOOK:
      CommentName = comment.name === 'YOU' ? 'Host' : comment.name;
      break;
    case CommentPlatform.INSTAGRAM:
      CommentName = comment.name === 'YOU' ? 'Host' : comment.name;
      break;
    case CommentPlatform.ALL:
      CommentName = comment.name === 'YOU' || comment.source !== 'USER' ? 'Host' : comment.name;
      break;
    default:
      CommentName = comment.name;
  }
  return (
    <View key={key} style={{flex: 1, flexBasis: 'auto', width: '90%'}}>
      <View style={[styles.rowLayout]}>
        {comment.muted && (
          <View style={{flexDirection: 'column', alignItems: 'center', justifyContent: 'center', marginRight: 8}}>
            <Icon name="volume-x" iconType="Feather" size={16} color={'#9D9D9D'} />
          </View>
        )}
        <View style={{padding: 2}}>
          <Image
            source={comments.imgUrl ? {uri: comments.imgUrl} : require('@/root/web/assets/images/apptile_icon.png')}
            style={{height: 35, width: 35, borderRadius: 50}}
          />
        </View>
        <View style={styles.commentBox}>
          <View style={styles.commentBoxHeader}>
            <TextElement
              color={'SECONDARY'}
              style={[
                styles.commentName,
                CommentName === 'Host' && styles.hostCommentName,
                comment.muted && {color: '#9D9D9D'},
              ]}
              fontSize="xs"
              fontWeight="600">
              {CommentName}
            </TextElement>
          </View>
          <TextElement
            style={[styles.commentText, comment.muted && {color: '#9D9D9D'}]}
            color={'SECONDARY'}
            fontSize="sm">
            {comment.comment}
          </TextElement>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  commentSection: {
    flex: 1,
  },
  rowLayout: {
    flexDirection: 'row',
  },
  alignCenter: {
    alignItems: 'center',
  },
  commentContainer: {
    paddingVertical: 8,
    paddingHorizontal: 10,
    marginBottom: 10,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
    width: '100%',
  },
  chatMessageWrapper: {flex: 1, overflow: 'scroll', paddingTop: 10, paddingRight: 5},
  commentBox: {
    marginLeft: 6,
    flexDirection: 'column',
    flex: 1,
  },
  commentBoxHeader: {
    flexDirection: 'row',
    gap: 6,
    alignItems: 'center',
    marginBottom: 5,
  },
  commentText: {
    flexWrap: 'wrap',
    width: '100%',
  },
  popOverContainer: {
    position: 'absolute',
    top: -4,
    right: 52,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 10,
    borderWidth: 1,
    backgroundColor: 'white',
    borderColor: theme.INPUT_BORDER,
    zIndex: 10,
    elevation: 10,
  },
  commentName: {
    alignSelf: 'flex-start',
  },
  hostCommentName: {
    color: '#FFF',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    backgroundColor: theme.PRIMARY_COLOR,
  },
  inputIconContainer: {
    padding: 6,
    backgroundColor: theme.TERTIARY_BACKGROUND,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  codeInputCustomerStyles: {
    width: '90%',
  },
  codeInputStyles: {
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
  },
});

export default CommentsView;
