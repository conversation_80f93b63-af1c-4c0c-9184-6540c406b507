import React, {useEffect, useRef, useState} from 'react';
import {View, StyleSheet, ScrollView, Text, ActivityIndicator, Image} from 'react-native';

import theme from '@/root/web/styles-v2/theme';
import {DefaultRootState, useDispatch, useSelector} from 'react-redux';
import commonStyles from '../../styles-v2/commonStyles';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import TextInput from '../../components-v2/base/TextInput';
import Button from '@/root/web/components-v2/base/Button';
import {loginToLively} from '../../actions/liveSellingActions';
import Dashboard from './dashboard';
import LogRocket from 'logrocket';
import {initZego, zegoExpEngine} from './zegoEngine';
import {planFeaturesList, setAppConstants} from 'apptile-core';
import {fetchMySubscription, fetchPlanList, setEditorFeatures, USER_INIT_FETCHED} from '../../actions/editorActions';
import {selectCurrentPlanWithDetails} from '../../selectors/BillingSelector';
import {EditorRootState} from '../../store/EditorRootState';
import {checkApptileEmailSelector} from '../../selectors/FeatureGatingSelector';
import {whitelistedEmails} from '../../common/featureGatingConstants';
import _ from 'lodash';
import UserApi from '../../api/UserApi';
import {LOGIN_MESSAGES} from '../../../app/common/utils/apiErrorMessages/specificFeature';
import {makeToast} from '@/root/web/actions/toastActions';

import ModalComponent from '../../components-v2/base/Modal';
import {DashboardUpgradeModal} from './dashboardUpgradeModal';
import axios from 'axios';
import TextElement from '../../components-v2/base/TextElement';
import { geteErrorMessage, handleToast } from './shared/CommonError';
import { COMMON_ERROR_MESSAGE } from '@/root/app/common/utils/apiErrorMessages/generalMessages';

const Login = () => {
  const dispatch = useDispatch();
  const urlParams = new URLSearchParams(window?.location?.search);
  const liveLoginEmail = urlParams.get('liveLoginEmail');
  const appId = urlParams.get('app-id');

  const [loading, setLoading] = useState(false);

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const dashboardCheckIntervalRef = useRef(null);

  const loginStatus = useSelector(state => state.liveSelling.auth.status);
  const loginError = useSelector(state => state.liveSelling.auth.loginError);


  const customerLogin = () => {
    setError('');
    if (!email.trim() && !password.trim()) {
      setError(LOGIN_MESSAGES.ERROR.EMPTY_FIELDS);
    } else if (!email.trim()) {
      setError(LOGIN_MESSAGES.ERROR.EMPTY_EMAIL);
    } else if (!password.trim()) {
      setError(LOGIN_MESSAGES.ERROR.EMPTY_PASSWORD);
    } else {
      dispatch(loginToLively({email, password, appId, buildRedirection: true}));
    }
  };

  useEffect(() => {
    if(loginError === LOGIN_MESSAGES.ERROR.LOGIN_FAILED){
      setError(LOGIN_MESSAGES.ERROR.LOGIN_FAILED)
    }
  },[loginError]) 

  useEffect(() => {
    if (loginStatus === 'LOADING') {
      setLoading(true);
    } else if (loginStatus === 'SUCCESS' || loginStatus === 'FAILED') {
      setLoading(false);
    }
  }, [loginStatus]);

  const authToken = useSelector((state: DefaultRootState) => state.liveSelling.auth.authToken);

  useEffect(() => {
    if (liveLoginEmail) {
      setEmail(liveLoginEmail ?? '');
      if (!zegoExpEngine) {
        try {
          initZego();
        } catch (err) {
          dispatch(
            makeToast({
              content: LOGIN_MESSAGES.ERROR.ZEGO_INIT_FAILED,
              appearances: 'error',
            }),
          );
        }
      }
      LogRocket.identify(liveLoginEmail, {
        email: liveLoginEmail,
        buildnumber: (window as any).BN_FOR_LOGROCKET,
      });
    }
  }, [liveLoginEmail]);

  useEffect(() => {
    console.log('zegoExpEngine initZego');
    initZego();
  }, []);

  const currentPlan = useSelector(selectCurrentPlanWithDetails);

  const basePlan = currentPlan?.basePlan ?? currentPlan;

  let editorFeatures =
    _.find(planFeaturesList, (e: any) => e.serverCode === basePlan?.name)?.allowedFeatures ??
    planFeaturesList.CORE.allowedFeatures;

  const {userFetched, user} = useSelector((state: EditorRootState) => state.user);
  const isApptileUser = useSelector(checkApptileEmailSelector);
  if (userFetched && user?.email && typeof user?.email === 'string') {
    if (isApptileUser || whitelistedEmails.includes(user?.email))
      editorFeatures = planFeaturesList.ENTERPRISE.allowedFeatures;
  }
  const getUser = async () => {
    try {
      const response = await UserApi.fetchUserInit();
      const userData = response.data;
      dispatch({
        type: USER_INIT_FETCHED,
        payload: userData,
      });
    } catch (err) {
      const errorMessage = geteErrorMessage(err, "getUser - Login")
      if(errorMessage !== COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR){
        handleToast(errorMessage, dispatch)
      }
    }
  };

  useEffect(() => {
    if (!authToken) dispatch(fetchPlanList());

    if (appId && authToken) {
      console.log('Build - ', (window as any).BN_FOR_LOGROCKET);
      dispatch(fetchMySubscription(appId as string));
      getUser();
    }
  }, [authToken, appId, dispatch]);

  useEffect(() => {
    dispatch(setEditorFeatures(editorFeatures));
  }, [dispatch, editorFeatures]);

  const [showDashboardUpgradeModal, setShowDashboardUpgradeModal] = React.useState(false);

  const checkDashboard = () => {
    const url = window.location.href;
    axios
      .get(url.replace(window.location?.pathname, ''), {
        headers: {
          Pragma: 'no-cache',
          Expires: -1,
          'Cache-Control': 'no-cache',
        },
      })
      .then(function (rawData) {
        const htmlData = rawData.data;
        const regex = new RegExp('((BUILDNUMBERSTART---)([\\S]{1,})(---BUILDNUMBEREND))', 'gmi');
        const buildTag = htmlData.match(regex)?.[0]?.slice(19, -17);
        if (!buildTag) {
          console.log('Not able to check build number.');
        } else {
          if (buildTag != window?.PLATFORM_BUILD_NUMBER) {
            setShowDashboardUpgradeModal(true);
          } else {
            console.log('Dashboard is on latest tag');
          }
        }
      })
      .catch(function (err) {
        console.log('Error while checking latest dashboard version', err);
      });
  };

  useEffect(() => {
    // 5 Min Interval
    checkDashboard();
    dashboardCheckIntervalRef.current = setInterval(checkDashboard, 5 * 60 * 1000);
    return async () => {
      clearInterval(dashboardCheckIntervalRef.current);
      dashboardCheckIntervalRef.current = null;
    };
  }, []);

  return (
    <>
      {authToken ? (
        <Dashboard />
      ) : (
        <>
          <View style={styles.wrapper}>
            <View style={styles.ImageContainer}>
              <Image
                style={styles.image}
                source={require('@/root/web/assets/images/liveSellingDashboardLoginImage.png')}
              />
            </View>
            <View style={[styles.contentContainer, {opacity: loading ? 0.5 : 1}]}>
              <View style={styles.LoginConatiner}>
                <Image style={styles.logo} source={require('@/root/web/assets/icons/apptile-live.svg')} />
                <TextElement
                  style={{
                    fontSize: 18,
                    textAlign: 'center',
                    marginBottom: 30,
                    marginTop: 10,
                    color: '#000',
                    fontWeight: 500,
                  }}>
                  Login To Broadcaster App
                </TextElement>
                <View style={styles.InputConatiner}>
                  <TextElement style={styles.InputLabel}>E-mail</TextElement>
                  <CodeInputControlV2
                    placeholder="Email"
                    singleLine={true}
                    value={email}
                    onChange={!loading && setEmail}
                    inputStyles={{padding: 10}}
                    containerStyles={{marginTop: 9}}
                  />
                </View>
                <View style={[styles.InputConatiner, {marginTop: 10}]}>
                  <TextElement style={styles.InputLabel}>Password</TextElement>
                  {/* <CodeInputControlV2
                    placeholder="Password"
                    singleLine={true}
                    value={password}
                    onChange={!loading && setPassword}
                    inputStyles={{padding: 10}}
                  /> */}
                  <View style={[commonStyles.inputContainer, {width: '100%', marginTop: 10}]}>
                    <TextInput
                      placeholder="Password"
                      secureTextEntry={true}
                      style={[
                        commonStyles.input,
                        {padding: 8, width: '100%', outline: 'none', fontSize: 12, color: theme.CONTROL_INPUT_COLOR},
                      ]}
                      onChange={(e: any) => !loading  && setPassword(e.target.value)}
                    />
                  </View>
                </View>
                {error && (
                  <TextElement style={[commonStyles.errorText, {color: 'red', fontSize: 12}]}>{error}</TextElement>
                )}
                <View style={{justifyContent: 'center', alignItems: 'center', marginTop: 30}}>
                  <Button
                    disabled={loading}
                    color={'CTA'}
                    innerContainerStyles={styles.loginBtn}
                    onPress={() => customerLogin()}>
                    Login
                  </Button>
                </View>
              </View>
              {loading && (
                <View
                  style={[
                    styles.rowLayout,
                    {
                      position: 'absolute',
                      backgroundColor: 'transparent',
                      width: '100%',
                      minHeight: '100px',
                      justifyContent: 'center',
                      alignItems: 'center',
                    },
                  ]}>
                  <ActivityIndicator size={20} />
                </View>
              )}
            </View>
          </View>
        </>
      )}
      {showDashboardUpgradeModal && (
        <ModalComponent
          onVisibleChange={setShowDashboardUpgradeModal}
          visible={showDashboardUpgradeModal}
          content={
            <DashboardUpgradeModal
              onConfirmPress={() => {
                setShowDashboardUpgradeModal(false);
              }}
            />
          }
        />
      )}
    </>
  );
};

export default Login;

const styles = StyleSheet.create({
  wrapper: {
    width: '-webkit-fill-available;',
    height: '-webkit-fill-available;',
    backgroundColor: theme.PRIMARY_BACKGROUND,
    justifyContent: 'center',
    flexDirection: 'row',
  },
  LoginConatiner: {
    backgroundColor: '#fff',
    paddingHorizontal: 60,
    borderTopRightRadius: 15,
    borderBottomRightRadius: 15,
    justifyContent: 'center',
    height: 533,
  },
  contentContainer: {
    justifyContent: 'center',
    width: 498,
    height: '100%',
  },
  ImageContainer: {
    // borderTopLeftRadius: 20,
    // borderBottomLeftRadius: 20,
    justifyContent: 'center',
  },
  image: {
    width: 446,
    height: 533,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  logo: {
    width: 223,
    height: 41,
    marginVertical: 10,
    alignSelf: 'center',
  },
  InputLabel: {
    fontSize: 12,
    fontWeight: '400',
    color: 'black',
  },
  InputConatiner: {
    marginVertical: 10,
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  loginBtn: {
    paddingLeft: 40,
    paddingRight: 40,
  },
});
