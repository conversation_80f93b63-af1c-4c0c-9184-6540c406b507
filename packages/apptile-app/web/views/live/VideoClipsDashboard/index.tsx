import React, {useState, useEffect, useRef} from 'react';
import {View, Text, StyleSheet, Pressable, ScrollView, Image} from 'react-native';
import LiveStreamHeader from '../shared/LiveStreamHeader';
import {useNavigate} from 'react-router';
import theme from '@/root/web/styles-v2/theme';
import commonStyles from '../../../styles-v2/commonStyles';
import {Icon} from 'apptile-core';
import TextElement from '../../../components-v2/base/TextElement';
import {useDispatch, useSelector} from 'react-redux';
import {LivelyApi} from '../../../api/LivelyApi';
import apolloQueryRunner from '@/root/app/plugins/datasource/ApolloWrapper/model';
import {ShopifyItemObjectPicker} from '../../../integrations/shopify/components/ShopifyItemPicker';
import Loader from '../shared/Loader';
import {VIDEO_CLIPPING} from '../../../../app/common/utils/apiErrorMessages/specificFeature';
import {makeToast} from '@/root/web/actions/toastActions';
import _ from 'lodash-es';
import {handleApiError} from '../shared/CommonError';
import {COMMON_ERROR_MESSAGE} from '../../../../app/common/utils/apiErrorMessages/generalMessages';
import NetworkErrorPage from '../shared/NetworkErrorPage';

export const VideoClipsDashbaord: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const authToken = useSelector(state => state.liveSelling?.auth?.authToken);
  const [activeProduct, setActiveProduct] = useState({});
  const [queryRunner, setQueryRunner] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isNetworkError, setIsNetworkError] = useState(false);

  useEffect(() => {
    if (authToken) {
      setLoading(true);
      LivelyApi.getShopifyCreds(authToken)
        .then(response => {
          const newQueryRunner = apolloQueryRunner();
          newQueryRunner
            .initClient(`https://${response?.data?.data?.store_name}/api/2024-07/graphql.json`, (_, {headers}) => {
              return {
                headers: {
                  ...headers,
                  'X-Shopify-Storefront-Access-Token': response?.data?.data?.access_token,
                },
              };
            })
            .then(async () => {
              setQueryRunner(newQueryRunner);
              setLoading(false);
            });
        })
        .catch(error => {
          if (error.code === 'ERR_NETWORK') {
            setIsNetworkError(true);
          }
          setLoading(false);
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'getShopifyCreds', false);
        });
    }
  }, [authToken]);

  const handleSetProduct = (activeProduct: any) => {
    if (!_.isEmpty(activeProduct)) {
      setActiveProduct(activeProduct);
      navigate(`/live-selling/video-clips/dashboard/product/${encodeURIComponent(activeProduct.id)}/clips-details`);
      dispatch(
        makeToast({
          content: VIDEO_CLIPPING.SUCCESS.SELECTION_SUCCESS,
          appearances: 'success',
          duration: 3000,
        }),
      );
    } else {
      dispatch(
        makeToast({
          content: VIDEO_CLIPPING.ERROR.SELECTION_ERROR,
          appearances: 'error',
          duration: 3000,
        }),
      );
    }
  };

  return (
    <>
      <LiveStreamHeader showActionButtons={false} />
      {isNetworkError ? (
        <NetworkErrorPage />
      ) : (
        <View style={styles.container}>
          <ScrollView style={{width: '100%'}} contentContainerStyle={styles.wrapper}>
            <View style={styles.contentContainer}>
              <Pressable onPress={() => navigate(-1)} style={styles.BackButton}>
                <Icon
                  name="chevron-left"
                  iconType="MaterialCommunityIcons"
                  size={24}
                  color={'rgba(136, 136, 136, 1)'}
                  style={{margin: 0}}
                />
                <Text
                  style={[
                    commonStyles.baseText,
                    {
                      fontSize: '14px',
                      fontWeight: 500,
                      color: 'rgba(136, 136, 136, 1)',
                    },
                  ]}>
                  BACK
                </Text>
              </Pressable>
              <TextElement
                style={{textAlign: 'left', width: '100%', marginTop: 15, fontSize: 22}}
                color="SECONDARY"
                fontWeight="600">
                Video Clips
              </TextElement>
              <TextElement
                style={{textAlign: 'left', width: '100%', marginTop: 8, opacity: 0.7, fontSize: 14}}
                color="SECONDARY"
                fontWeight="400">
                View and select video clips to show in product page in app
              </TextElement>

              <View style={styles.productConatiner}>
                <View style={styles.innerProductConatiner}>
                  <TextElement
                    style={{
                      textAlign: 'left',
                      width: '100%',
                      paddingBottom: 20,
                      borderBottomWidth: 1,
                      borderBottomColor: 'rgba(206, 206, 206, 1)',
                      fontSize: 16,
                    }}
                    color="SECONDARY"
                    fontWeight="500">
                    Choose a product
                  </TextElement>

                  {loading ? (
                    <View style={{marginBottom: 40, marginTop: 40}}>
                      <Loader />
                    </View>
                  ) : (
                    <>
                      <TextElement
                        style={{textAlign: 'left', width: '100%', marginTop: 25, opacity: 0.7, fontSize: 14}}
                        color="SECONDARY"
                        fontWeight="400">
                        Choose a product to see video clips
                      </TextElement>
                      <View style={[styles.itemPickerWrapper]}>
                        <ShopifyItemObjectPicker
                          itemType={'product'}
                          value={activeProduct?.handle || ''}
                          onChange={val => handleSetProduct(val)}
                          label={''}
                          name={''}
                          queryRunner={queryRunner}
                        />
                      </View>
                    </>
                  )}
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  customerProductCardNew: {
    width: '-webkit-fill-available',
    marginRight: 12,
    marginLeft: 22,
    height: 70,
    borderRadius: 12,
    backgroundColor: '#00000040',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    top: 70,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    position: 'absolute',
  },
  customerProductCard: {
    position: 'absolute',
    bottom: 30,
    width: 240,
    height: 60,
    borderRadius: 10,
    backgroundColor: '#00000040',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#A5A5A5',
  },
  customerAddButtonNew: {
    height: 30,
    borderRadius: 27,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#000',
    marginRight: 10,
    marginBottom: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  customerAddButton: {
    height: 30,
    borderRadius: 27,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#A5A5A5',
    marginRight: 10,
    marginBottom: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  customerAddButtonV2: {
    height: 35,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 5,
    backgroundColor: '#000',
  },
  customerLabelText: {
    fontSize: 12,
    color: '#FFF',
    overflow: 'hidden',
    fontWeight: '600',
    marginBottom: 5,
  },
  itemPickerWrapper: {width: '100%', borderColor: 'transparent', marginTop: 14},
  customerProductCardV2: {
    width: 150,
    borderRadius: 10,
    backgroundColor: '#0005',
    justifyContent: 'center',
    marginTop: 10,
    padding: 10,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ffffff99',
  },
  rootContainer: {
    width: '100%',
    height: '90%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    alignItems: 'center',
    flex: 1,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  contentContainer: {
    width: '95%',
    marginBottom: 25,
    paddingTop: 20,
  },
  BackButton: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 5,
  },
  StreamConatiner: {
    marginTop: 20,
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  spacer: {
    height: 10,
    width: '100%',
  },
  productConatiner: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 10,
    marginTop: 20,
  },
  innerProductConatiner: {
    width: '100%',
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 20,
    paddingTop: 20,
  },
  productDetailsConatiner: {
    width: 'fit-content',
    borderWidth: 2,
    padding: 10,
    marginTop: 15,
    flexDirection: 'row',
    gap: 15,
    justifyContent: 'flex-start',
    borderRadius: 6,
    borderColor: 'rgba(206, 206, 206, 1)',
  },
});

export default VideoClipsDashbaord;
