import {PageTransitions} from 'apptile-core';
import {PluginEditorsConfig} from '../../../app/common/EditorControlTypes';

export const navigationDefaultEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'pageIdSelector',
      name: 'screen',
      defaultValue: '',
      props: {
        label: 'Page Id',
        placeholder: 'Page Id of screen',
        options: [],
      },
      hidden: config => config.get('type') !== 'screen',
    },
    {
      type: 'headerIdSelector',
      name: 'header',
      defaultValue: '',
      props: {
        label: 'Header Id',
        placeholder: 'Header Id attached to screen',
        options: [],
      },
      hidden: config => config.get('type') !== 'screen',
    },
    {
      type: 'codeInput',
      name: 'nativeTemplate',
      defaultValue: '',
      props: {
        label: 'Native Page Template',
      },
      hidden: config => config.get('name') !== 'Product' && config.get('name') !== 'Collection',
    },
    {
      type: 'codeInput',
      name: 'title',
      props: {
        label: 'Title',
        placeholder: 'Home',
      },
      hidden: config => config.get('type') !== 'screen',
    },
    {
      type: 'checkbox',
      name: 'showTitleBar',
      props: {
        label: 'Show Title Bar',
      },
      hidden: config => config.get('type') !== 'screen',
    },
    {
      type: 'checkbox',
      name: 'isModal',
      props: {
        label: 'Display as Modal',
      },
      hidden: config => config.get('type') !== 'screen',
    },
    {
      type: 'checkbox',
      name: 'detachInActiveScreens',
      props: {
        label: 'Detach Inactive Screens (DEV ONLY!! DO NOT USE WITHOUT TOP TAB NAVIGATOR)',
      },
      hidden: config => config.get('type') === 'screen',
    },
    {
      type: 'checkbox',
      name: 'isTransparentModal',
      props: {
        label: 'Display as Modal Popup screen',
      },
      hidden: config => config.get('type') !== 'screen' || !config.get('isModal'),
    },
    {
      type: 'iconChooserInput',
      name: 'iconName',
      props: {
        label: 'Icon',
        placeholder: 'help',
      },
      hidden: config => !(config.get('type') === 'screen' || config.get('navigatorType') === 'topTab'),
    },
    // {
    //   type: 'codeInput',
    //   name: 'name',
    //   props: {
    //     label: 'Navigator Name',
    //     placeholder: 'Navigator name eg: Home',
    //   },
    //   hidden: config => config.get('type') !== 'navigator',
    // },
    {
      type: 'radioGroup',
      name: 'navigatorType',
      props: {
        label: 'Navigator Type',
        options: ['stack', 'tab', 'topTab'],
      },
      hidden: config => config.get('type') !== 'navigator',
    },
    {
      type: 'checkbox',
      name: 'unmountOnBlur',
      props: {
        label: 'Unmount On Blur',
      },
      hidden: config => config.get('type') !== 'screen',
    },
    {
      type: 'dropDown',
      name: 'transition',
      props: {
        label: 'Transition',
        options: Object.values(PageTransitions),
      },
    },
  ],
};
