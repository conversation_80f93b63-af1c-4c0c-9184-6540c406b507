import {connect} from 'react-redux';
import {bindActionCreators} from 'redux';
import {selectModulesCache} from 'apptile-core';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {AppDispatch} from '../../../../app/store';
import {deleteModuleTemplate, saveModuleTemplate, duplicateModuleTemplate} from '../../../actions/editorActions';
import PluginListingControl from '../components/PluginListingControl';

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return bindActionCreators({saveModuleTemplate, deleteModuleTemplate, duplicateModuleTemplate}, dispatch);
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    registeredPlugins: state.editor.registeredPlugins,
    modulesCache: selectModulesCache(state),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(PluginListingControl);
