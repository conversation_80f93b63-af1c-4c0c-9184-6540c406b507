import React, {createContext, forwardRef, useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import {findNodeHandle, StyleSheet, View, ScrollView} from 'react-native';
import {useDrag, useDrop} from 'react-dnd';
import {getEmptyImage} from 'react-dnd-html5-backend';
import usePluginListing, {PluginListingFolder} from '../../../common/hooks/usePluginListing';
import {groupBy} from 'lodash';
import CollapsiblePanel from '../../../components/CollapsiblePanel';
import PluginListingItemControl from './PluginListingItemControl';
import useEndDrag from '@/root/app/common/utils/useEndDrag';
import {ModuleRecords} from 'apptile-core';
import TilesExportDialog from '../../tilesExport';


const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  pluginGrid: {
    flex: 1,
    height: 'auto',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  draggable: {
    width: 50,
    height: 50,
    backgroundColor: 'blue',
  },
  receiver: {
    width: 200,
    height: 200,
    margin: 5,
    backgroundColor: 'green',
  },
  receiving: {
    borderColor: 'red',
    borderStyle: 'dashed',
    borderWidth: 2,
  },
});

const ItemTypes = {
  BOX: 'box',
};

const DragBox: React.FC<any> = ({}) => {
  const viewRef = useRef<View | null>(null);
  const [{isDragging}, dragRef, dragPreviewRef] = useDrag({
    type: ItemTypes.BOX,
    item: () => ({
      msg: 'hello',
    }),
    collect: monitor => ({
      isDragging: !!monitor.isDragging(),
    }),
  });
  useEffect(() => {
    dragPreviewRef(getEmptyImage(), {captureDraggingState: true});
  }, []);
  const setDragRefs = useCallback(ref => {
    viewRef.current = ref;
    dragRef(ref && findNodeHandle(ref));
  }, []);

  return (
    <View
      ref={setDragRefs}
      style={[
        styles.draggable,
        {
          opacity: isDragging ? 0.5 : 1,
        },
      ]}
    />
  );
};

const DropBox: React.FC<any> = ({id, children, style, ...props}) => {
  const viewRef = useRef<View | null>(null);
  const [{isOver}, dropRef] = useDrop({
    accept: ItemTypes.BOX,
    drop: (item, monitor) => {
      if (!monitor.getDropResult()) {
        logger.info(`Received in ${id}`, item);
        return {msg: `Received ${item.msg}`};
      } else return null;
    },
    collect: monitor => ({
      isOver: !!monitor.isOver({shallow: true}),
    }),
  });
  // useEffect(() => {
  //   dragPreviewRef(getEmptyImage());
  // });
  const setDragRefs = useCallback(ref => {
    viewRef.current = ref;
    dropRef(ref && findNodeHandle(ref));
  }, []);

  return (
    <View {...props} ref={setDragRefs} style={[...style, isOver ? styles.receiving : {}]}>
      {children}
    </View>
  );
};

export function generatePluginListingForModules(modulesCache: ModuleRecords): PluginListingFolder {
  if (!modulesCache) return [];
  const modulesFolder: PluginListingFolder = modulesCache
    .map((moduleRecord, moduleUUID) => {
      return {
        labelPrefix: 'module',
        name: moduleRecord.moduleName,
        type: 'widget',
        description: '',
        defaultHeight: 'auto',
        defaultWidth: 'auto',
        section: 'Modules',
        icon: '',
        layout: {},
        pluginType: 'ModuleInstance',
        moduleUUID,
      };
    })
    .valueSeq()
    .toArray();
  return modulesFolder;
}
interface PluginListingControlProps {
  modulesCache: ModuleRecords;
  saveModuleTemplate: (moduleUUID: string) => void;
  deleteModuleTemplate: (moduleUUID: string) => void;
  duplicateModuleTemplate: (moduleUUID: string) => void;
}

const PluginListingControl: React.FC<PluginListingControlProps> = ({
  modulesCache,
  saveModuleTemplate,
  deleteModuleTemplate,
  duplicateModuleTemplate,
}) => {
  const allPlugins = generatePluginListingForModules(modulesCache).concat(usePluginListing('hidden'));

  const groupedPlugins = useMemo(() => {
    return groupBy(allPlugins, 'section');
  }, [allPlugins]);
  const [moduleUUIDToSave, setModuleUUIDToSave] = useState('');
  const [bTileSaving, setTileSaving] = useState(false);

  const onCloseTileSaveDialog = useCallback(() => {
    setTileSaving(false);
    setModuleUUIDToSave('');
  }, []);
  const saveTile = useCallback((moduleUUID: string) => {
    setModuleUUIDToSave(moduleUUID);
    setTileSaving(true);
  }, []);

  const onDragEnd = useEndDrag();
  const renderSection = (title: string, plugins: PluginListingFolder) => {
    return (
      <CollapsiblePanel key={title} title={title} isOpen={true}>
        <View style={styles.pluginGrid}>
          {plugins.map(plugin => (
            <PluginListingItemControl
              key={plugin.moduleUUID ?? plugin.name}
              {...plugin}
              onDragEnd={onDragEnd}
              saveModuleTemplate={saveTile}
              deleteModuleTemplate={deleteModuleTemplate}
              duplicateModuleTemplate={duplicateModuleTemplate}
            />
          ))}
        </View>
      </CollapsiblePanel>
    );
  };
  return (
    <View style={styles.container}>
      <TilesExportDialog
        key={moduleUUIDToSave}
        isOpen={bTileSaving}
        moduleUUID={moduleUUIDToSave}
        onClose={onCloseTileSaveDialog}
      />
      <ScrollView style={{flex: 1}}>
        {Object.entries(groupedPlugins).map(([section, plugins]) =>
          plugins.length ? renderSection(section, plugins) : null,
        )}
      </ScrollView>
    </View>
  );
};

export default PluginListingControl;
