import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  Button,
  StyleSheet,
  Text,
  Pressable,
  Image,
  TextInput as TextInputReact,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';

import {MaterialCommunityIcons} from 'apptile-core';
import {selectModuleByUUID} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {exportTile, updateTileInfo, updateTile, exportPage} from '../../actions/editorActions';
import TilesApi from '../../api/TilesApi';
import commonStyles from '../../styles-v2/commonStyles';
import CodeInputControl from '../../components/controls/CodeInputControl';
import TextElement from '../../components-v2/base/TextElement';
import AssetChooseDialog from '../../components/controls/assetEditor/assetChooseDialog';
import DropDownControl from '../../components/controls/DropDownControl';
import {IntegrationCodesMappingWithDataSource} from '@/root/app/plugins/datasource/constants';
import {selectScreensInNav} from '../../selectors/EditorSelectors';

interface IntegrationTileSaveDialogProps {
  moduleUUID: string;
  onClose: () => void;
}

const IntegrationTileSaveDialog: React.FC<IntegrationTileSaveDialogProps> = props => {
  const {onClose, moduleUUID} = props;
  const dispatch = useDispatch();
  const {
    exportPage: exportPageToServer,
    exportTile: exportTileToServer,
    updateTileInfo: updateTileDetails,
    updateTile: updateTileVersion,
  } = useMemo(() => bindActionCreators({exportPage, exportTile, updateTileInfo, updateTile}, dispatch), [dispatch]);

  const moduleRecord = useSelector(state => selectModuleByUUID(state, moduleUUID));
  const [moduleName, setModuleName] = useState(moduleRecord?.moduleName);
  const [coverImage, setCoverImage] = useState('');
  const [tileDetails, setTileDetails] = useState(null);
  const [tags, setTags] = useState<string[]>([]);
  const [tagsStr, setTagsString] = useState<string>('');
  const [isLoading, setLoading] = useState(true);
  const [isNew, setNew] = useState(true);
  const [isDeleted, setIsDeleted] = useState(false);
  const [imageAssetId, setImageAssetId] = useState('');
  const [showPopover, setShowPopover] = React.useState(false);
  const [secretCode, setSecretCode] = useState('');
  const [variants, setVariants] = useState('');
  const [integrationCode, setIntegrationCode] = useState('');
  const integrations = useSelector(state => state?.integration?.allIntegrationsById);
  const [integrationData, setIntegrationData] = useState(null);
  const screens = useSelector(selectScreensInNav);
  const [pagesList, setPagesList] = useState<string[]>([]);
  const [selectedPage, setSelectedPage] = useState('');

  useEffect(() => {
    const tags = tagsStr.split(',').map(str => str.trim());
    setTags(tags);
  }, [tagsStr]);
  useEffect(() => {
    setModuleName(moduleRecord?.moduleName);
    setTagsString(moduleRecord?.getTagsString() ?? '');
  }, [moduleRecord]);
  useEffect(() => {
    setModuleName(tileDetails?.name);
    setTagsString(tileDetails?.tags?.join(',') ?? '');
    setCoverImage(tileDetails?.coverImage ?? '');
  }, [tileDetails]);
  useEffect(() => {
    TilesApi.getTileStatus(moduleUUID)
      .then(resp => {
        setTileDetails(resp.data?.tile);
        setLoading(false);
        setNew(false);
        if (resp.data.status === 'deleted') {
          setIsDeleted(true);
        } else {
          setIsDeleted(false);
        }
      })
      .catch(() => {
        setTileDetails(null);
        setIsDeleted(false);
        setLoading(false);
        setNew(true);
      });
  }, [moduleUUID]);

  const exportPagesAndTiles = useCallback(() => {
    if (moduleName && moduleUUID && moduleRecord) {
      pagesList.map(screenName => {
        const pageData = screens.find(e => e.name === screenName);
        if (pageData?.screen) {
          exportPageToServer(pageData?.screen, {
            name: pageData?.screen,
            coverImage:
              'https://cdn.apptile.io/ce0249d7-4cae-4e5e-828c-226f982bb0e7/84b99d55-ef9a-48b3-9bf3-e94649ff792c/original.png',
            tags: [integrationCode],
            navigationInfo: pageData,
            id: '',
            integrationName: '',
          });
        }
      });
      if (!tags.includes(integrationCode)) tags.push(integrationCode);
      exportTileToServer(moduleUUID, {
        id: moduleRecord.moduleUUID,
        name: moduleName,
        coverImage,
        tags,
        secretCode,
      });
    }
  }, [
    coverImage,
    exportPageToServer,
    exportTileToServer,
    integrationCode,
    moduleName,
    moduleRecord,
    moduleUUID,
    pagesList,
    screens,
    secretCode,
    tags,
  ]);

  const appConfig = useSelector(selectAppConfig);

  useEffect(() => {
    const imageRecord = appConfig?.getImageId(imageAssetId);
    const assetSourceValue = imageRecord?.fileUrl ?? null;
    if (assetSourceValue) setCoverImage(assetSourceValue);
  }, [appConfig, imageAssetId]);

  useEffect(() => {
    setIntegrationData(
      Object.keys(integrations)
        .map(e => integrations[e])
        ?.find(e => e.integrationCode === integrationCode),
    );
  }, [integrationCode, integrations]);

  return (
    <View style={styles.dialogStyle}>
      <View style={styles.dialogHeader}>
        <Text>Create Module</Text>
        <TouchableOpacity onPress={onClose} style={[styles.closeButton]}>
          <MaterialCommunityIcons name="close-circle-outline" size={24} />
        </TouchableOpacity>
      </View>
      {isLoading ? (
        <ActivityIndicator />
      ) : (
        <View style={[{flex: 1, overflowY: 'auto'}, styles.dialogBody]}>
          <View style={styles.rowContainer}>
            <DropDownControl
              options={Object.keys(IntegrationCodesMappingWithDataSource)}
              value={integrationCode ?? ''}
              defaultValue={integrationCode ?? ''}
              label={'Integration Code'}
              onChange={setIntegrationCode}
              disableBinding
            />
          </View>
          {integrationCode && !integrationData && (
            <Text style={commonStyles.errorText}>
              This integration is not yet ready for self serve please complete the merchandising
            </Text>
          )}
          {integrationCode && integrationData && (
            <>
              <View style={styles.rowContainer}>
                <CodeInputControl value={moduleName ?? ''} label={'Tile Name'} onChange={setModuleName} />
              </View>
              <View style={styles.rowContainer}>
                <View style={commonStyles.labelContainer}>
                  <Text style={commonStyles.labelText}>Cover Image</Text>
                </View>
                <View style={commonStyles.inputContainer}>
                  {!coverImage ? (
                    <Pressable style={[styles.imageContainer, styles.upload]} onPress={() => setShowPopover(true)}>
                      <TextElement fontSize="md" lineHeight="md" color="PRIMARY">
                        + UPLOAD
                      </TextElement>
                    </Pressable>
                  ) : (
                    <Pressable style={[styles.imageContainer]} onPress={() => setShowPopover(true)}>
                      <Image resizeMode="contain" source={{uri: coverImage}} style={styles.image} />
                    </Pressable>
                  )}
                </View>
                <AssetChooseDialog
                  askURL={false}
                  currentAssetId={''}
                  onCloseDialog={val => {
                    setShowPopover(val);
                  }}
                  showDialog={showPopover}
                  onSelectAsset={setImageAssetId}
                />
              </View>
              <View style={styles.rowContainer}>
                <CodeInputControl value={tagsStr ?? ''} label={'Tile Tags'} onChange={setTagsString} />
              </View>
              <View style={styles.rowContainer}>
                <View style={commonStyles.labelContainer}>
                  <Text style={commonStyles.labelText}>Secret Code</Text>
                </View>
                <View style={commonStyles.inputContainer}>
                  <TextInputReact
                    style={[commonStyles.input, {paddingHorizontal: 4, paddingVertical: 7}]}
                    secureTextEntry={true}
                    value={secretCode ?? ''}
                    onChange={(event: any) => {
                      setSecretCode(event?.target?.value ?? '');
                    }}
                  />
                </View>
              </View>
            </>
          )}
          {integrationCode && integrationData && (
            <View style={{flex: 1}}>
              <Text style={[commonStyles.heading, {marginVertical: 10}]}>Export Pages</Text>
              {pagesList?.map((page, index) => (
                <View
                  key={`integrationPages-${index}`}
                  style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', gap: 10}}>
                  <Text>
                    {index + 1}. {page}
                  </Text>
                  <Text>
                    <MaterialCommunityIcons
                      name="trash-can-outline"
                      size={25}
                      onPress={() => {
                        const newList = [...pagesList];
                        newList.splice(index, 1);
                        setPagesList(newList);
                      }}
                    />
                  </Text>
                </View>
              ))}
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  gap: 10,
                  marginVertical: 10,
                }}>
                <DropDownControl
                  options={screens}
                  nameKey={'name'}
                  valueKey={'name'}
                  defaultValue={selectedPage}
                  value={selectedPage}
                  onChange={setSelectedPage}
                />
                <Button
                  title="Add Page"
                  onPress={() => {
                    if (!pagesList.includes(selectedPage)) {
                      const newList = [...pagesList, selectedPage];
                      setPagesList(newList);
                    }
                  }}
                />
              </View>
            </View>
          )}
        </View>
      )}
      {isDeleted ? (
        <View style={styles.dialogFooter}>
          <Text style={commonStyles.baseText}>This Tile is deleted</Text>
        </View>
      ) : (
        <View style={styles.dialogFooter}>
          {isNew ? (
            <Button disabled={moduleName === ''} title="Export Tile & Pages" onPress={exportPagesAndTiles} />
          ) : (
            <>
              <Text>TILE ALREADY EXPORTED</Text>
            </>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  dialogStyle: {
    flex: 1,
    flexDirection: 'column',
    // width: 800,
    // minWidth: 400,
    // // height: 'auto',
    minHeight: 600,
    flexBasis: 'auto',
    backgroundColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  dialogHeader: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    padding: 10,
    backgroundColor: '#eee',
  },
  closeButton: {
    alignSelf: 'flex-end',
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    marginLeft: 'auto',
  },
  dialogBody: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: '#fff',
    padding: 10,
    alignItems: 'stretch',
  },
  textHeading: {
    flex: 1,
    fontSize: 16,
    margin: 10,
  },
  textSubtitle: {
    fontSize: 10,
    fontColor: '#888',
  },
  textInput: {flex: 1, padding: 4, borderColor: '#ccc', borderWidth: 1},
  rowContainer: {
    flex: 0,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    height: 'auto',
    flexBasis: 'auto',
  },
  inputRow: {
    flex: 0,
    flexBasis: 'auto',
    flexDirection: 'row',
    height: 40,
    width: '100%',
    borderRadius: 4,
    padding: 5,
    alignItems: 'center',
    alignContent: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    // shadowColor: '#000',
    // shadowOffset: {width: 1, height: 2},
    // shadowRadius: 4,
    // shadowOpacity: 0.3,
  },
  inputCell: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
  },
  selectorRow: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    height: 'auto',
    marginBottom: 5,
    flexGrow: 0,
    flexShrink: 0,
  },
  selectorCell: {
    flex: 1,
    flexBasis: '30%',
    width: '30%',
    height: 'auto',
    margin: 4,
  },
  selectorText: {
    fontSize: 10,
  },
  dialogFooter: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    padding: 4,
    paddingLeft: 10,
    paddingRight: 10,
    flexGrow: 0,
    flexShrink: 0,
    borderTopColor: '#ccc',
    borderTopWidth: 1,
    justifyContent: 'flex-end',
  },
  imageContainer: {
    position: 'relative',
    width: 350,
    height: 250,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
    marginVertical: 4,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
});

export default IntegrationTileSaveDialog;
