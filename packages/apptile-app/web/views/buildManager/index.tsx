import React, {useEffect, useState} from 'react';
import {Route, Routes} from 'react-router';
import {View, StyleSheet} from 'react-native';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import PublishFlow from '../publishFlowV1/components/publishFlow';

import {Assets} from './assets';
import {Settings} from './settings';
import {Secrets} from './secrets';
import {BuildConfig} from './buildConfig';
import {BuildList} from './buildList';
import {useSelector} from 'react-redux';
import Banner from './Banner';

import {BuildPlatformContextProvider} from './context';
import {useBuildPlatformContext} from './context';
import {EditorRootState} from '../../store/EditorRootState';

export const BuildManagerRouter: React.FC = () => {

  const {user, userFetched} = useSelector(state => state.user);


  const [isApptileUser, setIsApptileUser] = useState(false);

  useEffect(() => {
    if (userFetched && user?.email && typeof user?.email === 'string') {
      if (user.email.endsWith('@apptile.io') || user.email.endsWith('@apptile.com')) {
        setIsApptileUser(true);
      }
    }
  }, [user.email, userFetched]);

  return (
    <>
      {isApptileUser && (
        <BuildPlatformContextProvider>
          <Routes>
            <Route path="dashboard" element={<BuildDashBoard />} />
            <Route path="list" element={<BuildList />} />
          </Routes>
        </BuildPlatformContextProvider>
      )}
    </>
  );
};

const BuildDashBoard = () => {
  console.log("Multiverse of madness")
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const {featureFlags, updateFeatureFlags} = useBuildPlatformContext();

  React.useEffect(() => {
    if (featureFlags === null) {
      const fetchFeatureFlags = async appID => {
        try {
          const response = await BuildManagerApi.getActiveFeatureFlags(appID);
          if (!response.status || response.status >= 400) {
            throw new Error(`Failed to fetch feature flags: ${response.statusText}`);
          }

          const flags = await response.data;

          // Transform the array into an object with key-value pairs
          const transformedFlags = flags.reduce((acc: Record<string, any>, flag: {key: string; value: any}) => {
            acc[flag.key] = flag.value;
            return acc;
          }, {});

          // Update feature flags in the context
          updateFeatureFlags(transformedFlags);
        } catch (error) {
          console.error('Error fetching feature flags:', error);
        }
      };

      fetchFeatureFlags(appId);
    }
  }, [featureFlags]);

  const [screen, setScreen] = React.useState<'SETTINGS' | 'ASSETS' | 'SECRETS' | 'BUILD_CONFIG'>('SETTINGS');

  console.log(featureFlags, 'Build Context');

  return (
    <View style={{height: '100%', flex: 1, overflowY: 'scroll'}}>
      {featureFlags?.messages && (
        <>
          {featureFlags?.messages?.success && <Banner type="success" message={featureFlags?.messages?.success} />}
          {featureFlags?.messages?.warning && <Banner type="warning" message={featureFlags?.messages?.warning} />}
          {featureFlags?.messages?.info && <Banner type="info" message={featureFlags?.messages?.info} />}
          {featureFlags?.messages?.error && <Banner type="error" message={featureFlags?.messages?.error} />}
        </>
      )}
      <View style={[styles.alignCenter, styles.pageContainer]}>
        <View style={styles.container}>
          {screen === 'SETTINGS' && <Settings setScreen={setScreen} />}
          {screen === 'ASSETS' && <Assets setScreen={setScreen} />}
          {screen === 'SECRETS' && <Secrets setScreen={setScreen} />}
          {screen === 'BUILD_CONFIG' && <BuildConfig setScreen={setScreen} />}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  alignCenter: {
    alignItems: 'center',
  },
  container: {
    width: 1000,
    marginVertical: 40,
    alignItems: 'center',
  },
  pageContainer: {
    backgroundColor: '#f1f5f9',
    flex: 1,
    flexGrow: 1,
    flexShrink: 0,
    flexBasis: 'auto',
  },
});
