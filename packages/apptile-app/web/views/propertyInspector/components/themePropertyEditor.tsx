import React from 'react';
import {useTheme} from 'apptile-core';
import {View} from 'react-native';
import {useDispatch} from 'react-redux';
import {updateThemeConfig, setThemeConfig} from '@/root/web/actions/themeActions';
import {bindActionCreators} from 'redux';
import {debounce} from 'lodash';
import Immutable from 'immutable';
import {ThemeEntityEditorProps} from 'apptile-core';
import {toRespectiveValue} from 'apptile-core';
import {pluginEditorComponents} from '@/root/web/components/pluginEditorComponents';
import _ from 'lodash';

export const isRemovable = (val: any) => {
  let remove = false;

  if (_.isString(val) || _.isNumber(val)) {
    let newVal = val;

    if (_.isNumber(val)) {
      newVal = String(val);
    }

    if (_.isEmpty(_.trim(newVal as string))) {
      remove = true;
    }
  } else {
    if (typeof val != 'boolean' && _.isEmpty(val)) {
      remove = true;
    }
  }

  return remove;
};

const ThemePropertyEditor: React.FC<ThemeEntityEditorProps> = ({
  editor: {name, type, props: configProps, defaultValue},
  configPathSelector,
}) => {
  const {themeEvaluator} = useTheme();
  const dispatch = useDispatch();

  const EditorComponent = pluginEditorComponents[type as keyof typeof pluginEditorComponents];
  const updatePathSel = configPathSelector.slice(0, -1); //.concat(name);
  const configSelector = updatePathSel.join('.');
  const config = themeEvaluator(configSelector) === configSelector ? {} : themeEvaluator(configSelector);
  const value =
    themeEvaluator(configPathSelector.join('.')) !== configPathSelector.join('.')
      ? themeEvaluator(configPathSelector.join('.'))
      : undefined;

  const {updateThemeConfig: updateConfig, setThemeConfig: setConfig} = React.useMemo(
    () => bindActionCreators({updateThemeConfig, setThemeConfig}, dispatch),
    [dispatch],
  );

  const debouncedUpdateConfig = debounce(
    (sel, update: any, remove?: any) =>
      updateConfig({
        selector: sel,
        update,
        remove,
      }),
    300,
  );

  const onChange = React.useCallback(
    (v: unknown, debounced = false, remove = false) => {
      /**
       * ! Theme Note
       * ! Theme config only contains value which is required so removing the key won't cause any issue
       * ! Careful when implementing this behavior in any other place in appconfig except style
       */

      if (!remove) {
        remove = isRemovable(v);
      }

      if (debounced) {
        debouncedUpdateConfig(updatePathSel, remove ? {} : {[name]: getDataType(v)}, remove ? [name] : undefined);
      } else {
        updateConfig({
          selector: updatePathSel,
          update: remove ? {} : {[name]: getDataType(v)},
          remove: remove ? [name] : undefined,
        });
      }
    },
    [debouncedUpdateConfig, name, updateConfig, updatePathSel],
  );

  const onSetRawValue = React.useCallback(
    (v: unknown) => {
      setConfig({
        selector: configPathSelector,
        value: v,
      });
    },
    [setConfig, configPathSelector],
  );

  const onCustomPropChange = React.useCallback(
    (key: string, v: unknown, debounced = false, remove = false) => {
      // ! Read above `Theme Note`

      if (!remove) {
        remove = isRemovable(v);
      }

      if (debounced) {
        debouncedUpdateConfig(updatePathSel, remove ? {} : {[key]: getDataType(v)}, remove ? [key] : undefined);
      } else {
        updateConfig({
          selector: updatePathSel,
          update: remove ? {} : {[key]: getDataType(v)},
          remove: remove ? [key] : undefined,
        });
      }
    },
    [debouncedUpdateConfig, updatePathSel, updateConfig],
  );

  if (!EditorComponent) {
    return <></>;
  }

  return (
    <View style={{flexDirection: 'row', alignItems: 'center'}}>
      <View style={{flex: 1}}>
        <EditorComponent
          configProps={{...configProps}}
          name={name}
          defaultValue={themeEvaluator(defaultValue) !== defaultValue ? themeEvaluator(defaultValue) : ''}
          value={value}
          id={name}
          configPathSelector={configPathSelector}
          onChange={onChange}
          config={Immutable.Map(config)}
          onCustomPropChange={onCustomPropChange}
          onUpdateRawValue={onSetRawValue}
          inTheme
        />
      </View>
    </View>
  );
};

const getDataType = (val: any) => {
  if (val) {
    return toRespectiveValue(val);
  }
  return val;
};

export default ThemePropertyEditor;
