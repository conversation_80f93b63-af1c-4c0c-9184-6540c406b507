import Immutable from 'immutable';
import _ from 'lodash';
import React, {useEffect, useState} from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import {strsel} from 'apptile-core';
import {EditorProps, PluginEditorsConfig} from 'apptile-core';
import {resolvePluginEditorsConfig} from 'apptile-core';
import CollapsiblePanel from '../../../components/CollapsiblePanel';
import ModuleEditor from '../../../components/controls/moduleEditor/moduleEditor';
import {navigationDefaultEditors} from '../../navigation/navigationEditors';
import {pageDefaultEditors} from '../../pagesList/pageEditors';
import PluginPropertyEditor from './PluginPropertyEditor';
import PagePropertyEditor from './PagePropertyEditor';
import NavigationPropertyEditor from './NavigationPropertyEditor';

interface PIControlsFormProps extends EditorProps {}

const PIControlsForm: React.FC<PIControlsFormProps> = ({...props}) => {
  const {
    pluginSelector,
    navComponentSelector,
    pageId,
    selectedPluginConfig,
    selectedNavComponent,
    selectedPageId,
    selectedPageConfig,
  } = props;
  const [editors, setEditors] = useState<PluginEditorsConfig<any>>({});
  const [navEditors, setNavEditors] = useState<PluginEditorsConfig<any>>({});
  const [pageEditors, setPageEditors] = useState<PluginEditorsConfig<any>>({});
  const pluginId = pluginSelector ? pluginSelector[pluginSelector.length - 1] : null;
  const inModule = !_.isEmpty(selectedPluginConfig?.namespace?.getNamespace());
  useEffect(() => {
    if (selectedPluginConfig != null) {
      const pluginEditors: PluginEditorsConfig<any> = resolvePluginEditorsConfig(selectedPluginConfig.subtype);
      setEditors(pluginEditors);
    } else {
      setEditors({});
    }
  }, [selectedPluginConfig]);
  useEffect(() => {
    if (selectedNavComponent != null) {
      setNavEditors(navigationDefaultEditors);
    } else {
      setNavEditors([]);
    }
  }, [selectedNavComponent]);
  useEffect(() => {
    if (selectedPageConfig != null) {
      setPageEditors(pageDefaultEditors);
    } else {
      setPageEditors({});
    }
  }, [selectedPageConfig]);

  const selectedNavConfig = Immutable.fromJS(selectedNavComponent);

  return (
    <>
      {selectedPluginConfig != null && (
        <View style={styles.container}>
          <View style={{height: 'auto', flex: 1}}>
            {Object.entries(editors).map(([section, propEditors], i) => {
              return (
                <CollapsiblePanel key={i} title={section} isOpen={true} index={i}>
                  {propEditors.map(propEditor => {
                    if (propEditor.hidden?.(selectedPluginConfig.config)) return null;

                    let configPathSelector = [selectedPluginConfig.id, 'config', propEditor.name];
                    let configWithProp = selectedPluginConfig.config;

                    switch (propEditor.type) {
                      case 'analyticsEditor':
                        configPathSelector = [selectedPluginConfig.id, propEditor.name];
                        configWithProp = selectedPluginConfig;
                        break;
                      case 'layoutEditor':
                        configPathSelector = [selectedPluginConfig.id, propEditor.name];
                        configWithProp = selectedPluginConfig.layout;
                        break;
                      case 'animationsEditor':
                        configPathSelector = [selectedPluginConfig.id, propEditor.name];
                        configWithProp = selectedPluginConfig.animations;
                        break;
                      case 'moduleEditor':
                        return (
                          <ModuleEditor
                            {...props}
                            key={pluginId + propEditor.name}
                            propertyEditor={propEditor}
                            pageId={pageId}
                            pluginId={pluginId}
                            inModule={inModule}
                            entityConfig={selectedPluginConfig}
                            configPathSelector={configPathSelector}
                          />
                        );
                      case 'stylesEditor':
                        configWithProp = selectedPluginConfig.getIn(['config', 'style'], Immutable.Map());
                        break;
                    }
                    return (
                      <PluginPropertyEditor
                        key={pluginId + propEditor.name + i}
                        editor={propEditor}
                        entityConfig={selectedPluginConfig}
                        config={configWithProp}
                        pageId={pageId}
                        pluginId={pluginId}
                        configPathSelector={configPathSelector}
                        inModule={inModule}
                      />
                    );
                  })}
                </CollapsiblePanel>
              );
            })}
          </View>
        </View>
      )}
      {selectedNavComponent && (
        <View style={styles.container}>
          <View style={{height: 'auto', flex: 1}}>
            {Object.entries(navEditors).map(([section, propEditors], i) => {
              return (
                <CollapsiblePanel key={i} title={section} isOpen={true} index={i}>
                  {propEditors.map(propEditor => {
                    if (propEditor.hidden?.(selectedNavConfig)) return;
                    const navComponentId = _.last(navComponentSelector);
                    const configPathSelector = [navComponentId, propEditor.name];
                    return (
                      <NavigationPropertyEditor
                        key={strsel(navComponentSelector) + propEditor.name}
                        editor={propEditor}
                        entityConfig={selectedNavConfig}
                        config={selectedNavConfig}
                        pageId={null}
                        pluginId={null}
                        configPathSelector={configPathSelector}
                        navSelector={navComponentSelector}
                      />
                    );
                  })}
                </CollapsiblePanel>
              );
            })}
          </View>
        </View>
      )}
      {selectedPageConfig && (
        <View style={styles.container}>
          <View style={{height: 'auto', flex: 1}}>
            {Object.entries(pageEditors).map(([section, propEditors], i) => {
              return (
                <CollapsiblePanel key={i} title={section} isOpen={true} index={i}>
                  {propEditors.map(propEditor => {
                    if (propEditor.hidden?.(selectedPageConfig)) return;
                    const configPathSelector = [selectedPageConfig.pageId, propEditor.name];
                    return (
                      <PagePropertyEditor
                        key={selectedPageId + propEditor.name}
                        editor={propEditor}
                        entityConfig={selectedPageConfig}
                        config={selectedPageConfig}
                        pageId={selectedPageId}
                        configPathSelector={configPathSelector}
                        pluginId={null}
                      />
                    );
                  })}
                </CollapsiblePanel>
              );
            })}
          </View>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    ...Platform.select({
      web: {display: 'block'},
      default: {
        flex: 1,
        flexDirection: 'column',
        justifyContent: 'flex-start',
      },
    }),
    padding: 5,
    fontSize: 12,
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },
});

export default PIControlsForm;
