import {MaterialCommunityIcons} from 'apptile-core';
import Box from '@/root/web/components-v2/base/Box';
import Icon from '@/root/web/components-v2/base/Icon';
import TextElement from '@/root/web/components-v2/base/TextElement';
import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import Button from '@/root/web/components-v2/base/Button';
import {toggleDeleteIntegrationModal} from '@/root/web/actions/editorActions';
import {useDispatch} from 'react-redux';
import {IFetchIntegrationResponse} from '@/root/web/api/ApiTypes';

export const minSplitWidth = 1273;

export const EditorHeaderComponent: React.FC<{
  title: string;
  iconUrl?: string;
  isNewIntegration?: boolean;
  headingText?: string;
  isConnected?: boolean;
}> = ({title, iconUrl, isNewIntegration = false, headingText, isConnected = true}) => {
  const configureText = headingText ? headingText : isNewIntegration ? 'Connect to' : 'Configure';
  return (
    <Box style={{alignItems: 'flex-start', width: '100%'}}>
      {iconUrl && (
        <View style={[styles.flexDirectionRow, {justifyContent: 'space-between', width: '100%'}]}>
          <View style={styles.flexDirectionRow}>
            <Image source={{uri: iconUrl}} resizeMode={'contain'} style={styles.cardContentIcon} />
            <TextElement fontWeight="600" fontSize="lg" color="SECONDARY">
              {configureText} {title}
            </TextElement>
          </View>
          {isConnected && (
            <Box style={styles.flexDirectionRow}>
              <Icon name={'check-circle'} size={'sm'} color={'PRIMARY'} />
              <Box style={{paddingLeft: 4}}>
                <TextElement fontWeight="500" fontSize="sm" color="PRIMARY">
                  CONNECTED
                </TextElement>
              </Box>
            </Box>
          )}
        </View>
      )}
    </Box>
  );
};

export const EditorFooterComponent: React.FC<{
  integration: IFetchIntegrationResponse | undefined;
}> = ({integration}) => {
  const dispatch = useDispatch();
  const isActive = _.get(integration, ['appIntegrations', 0, 'active'], null);
  const handleRemove = () => {
    dispatch(toggleDeleteIntegrationModal(true));
  };
  return (
    <View style={[styles.buttonInputWrapper, !isActive ? {justifyContent: 'flex-end'} : {}]}>
      {isActive && (
        <Button onPress={handleRemove} variant={'TEXT'} textStyles={styles.btnPlainText}>
          DISCONNECT
        </Button>
      )}
    </View>
  );
};

export const IntegrationConnectedStatusComponent: React.FC<{title?: string; isConnected?: boolean; style?: any}> = ({
  title = 'Added',
  isConnected = true,
  style = {},
}) => {
  return (
    <View style={[styles.setupStatusBox, style]}>
      <View style={styles.setupStatusIcon}>
        <MaterialCommunityIcons name={isConnected ? 'check-circle-outline' : 'cancel'} size={20} />
      </View>
      <Text style={styles.setupStatusText}>{title}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  setupStatusBox: {flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center'},
  setupStatusText: {fontWeight: '500', fontSize: 10, lineHeight: 12},
  setupStatusIcon: {paddingLeft: 4},
  cardContentIcon: {
    position: 'relative',
    width: 30,
    height: 30,
    marginRight: 10,
  },
  buttonInputWrapper: {
    width: '100%',
    borderRadius: 5,
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 20,
    alignItems: 'center',
    bottom: 0,
  },
  btnPlainText: {
    color: '#FF0000',
    fontWeight: '500',
    fontSize: 14,
  },
  tilesAlert: {
    backgroundColor: '#E7EFF9',
    padding: 8,
    borderWidth: 1,
    borderColor: '#B4CCEC',
    borderRadius: 4,
    marginTop: 20,
    width: '100%',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  subHeading: {fontSize: 12, lineHeight: 14, fontWeight: '400'},
  pressIcon: {marginLeft: 8},
  flexDirectionRow: {flexDirection: 'row', alignItems: 'center'},
});
