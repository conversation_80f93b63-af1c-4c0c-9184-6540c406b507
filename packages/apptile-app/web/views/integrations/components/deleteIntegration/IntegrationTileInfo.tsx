import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import {size} from 'lodash';
import React from 'react';
import {Text, View, ScrollView, StyleSheet} from 'react-native';

const IntegrationTileInfo = (props: any) => {
  const {tempTiles} = props;
  const numberOfTiles = tempTiles.totalTiles === 1 ? `${tempTiles.totalTiles} tile` : `${tempTiles.totalTiles} tiles`;
  const numberOfPages = size(tempTiles.data) === 1 ? `${size(tempTiles.data)} page` : `${size(tempTiles.data)} pages`;
  return (
    <>
      <View style={styles.tilesAlert}>
        <Text style={styles.subHeading}>{`To disconnect remove ${numberOfTiles} across ${numberOfPages}`}</Text>
      </View>
      <ScrollView contentContainerStyle={styles.wrapper} style={styles.tilesSection} horizontal>
        {Object.keys(tempTiles.data).map(key => {
          return <TileDetails key={key} name={key} data={tempTiles.data[key]} />;
        })}
      </ScrollView>
    </>
  );
};

const TileDetails = (props: {name: string; data: string[]}) => {
  const {name, data} = props;
  return (
    <View style={styles.tileRow}>
      <View style={styles.tileContainer}>
        <ApptileWebIcon name={'pages'} size={16} style={styles.tileImg} />
        <Text style={styles.pageName}>{name}</Text>
      </View>
      <ScrollView style={styles.tileWrapper}>
        {data.map((tile: string) => {
          return (
            <View style={[styles.tileContainer, styles.tileSpacing]} key={tile}>
              <ApptileWebIcon name={'tiles'} size={16} style={styles.tileImg} />{' '}
              <Text style={styles.tileName}>{tile}</Text>
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  subHeading: {fontSize: 12, lineHeight: 14, fontWeight: '400'},
  screenMapping: {
    flex: 1,
    flexDirection: 'row',
  },
  tilesAlert: {
    backgroundColor: '#FAEDEA',
    padding: 8,
    borderWidth: 1,
    borderColor: '#E6CBC3',
    borderRadius: 4,
    marginTop: 20,
  },
  tilesSection: {
    borderWidth: 1,
    borderColor: '#BFBFBF',
    borderRadius: 8,
    paddingHorizontal: 56,
    paddingVertical: 32,
    marginTop: 16,
  },
  pageName: {
    fontWeight: '600',
  },
  tileName: {
    fontSize: 12,
    fontWeight: '400',
  },
  tileImg: {
    height: 9,
    width: 9,
    marginRight: 10,
  },
  tileContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  tileSpacing: {
    marginLeft: 16,
    marginTop: 9,
  },
  tileRow: {
    paddingHorizontal: 16,
  },
  tileWrapper: {
    maxHeight: 100,
  },
  wrapper: {
    flex: 1,
    alignItems: 'center',
  },
});

export default IntegrationTileInfo;
