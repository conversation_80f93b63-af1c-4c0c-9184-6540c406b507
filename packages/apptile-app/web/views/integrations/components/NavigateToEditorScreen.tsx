import {MaterialCommunityIcons} from 'apptile-core';
import {getNavigationContext} from 'apptile-core';
import {IFetchIntegrationResponse} from '@/root/web/api/ApiTypes';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {useNavigate} from '@/root/web/routing.web';
import React from 'react';
import {Pressable, StyleProp, StyleSheet, TextStyle, ViewStyle} from 'react-native';

interface INavigateToEditorScreen {
  integration?: IFetchIntegrationResponse;
  screenName?: string;
  customText?: string;
  textStyles?: StyleProp<TextStyle>;
  containerStyles?: StyleProp<ViewStyle>;
}

export const NavigateToEditorScreen: React.FC<INavigateToEditorScreen> = ({
  integration,
  screenName,
  customText,
  textStyles,
  containerStyles,
}) => {
  const navigate = useNavigate();

  const onNavigationClick = () => {
    const context = getNavigationContext();
    context.navigate(screenName);
    navigate('../../tiles');
  };

  const {title} = integration ?? {};
  if (!integration || !screenName) return <></>;
  return (
    <Pressable onPress={onNavigationClick} style={[styles.wrapper, containerStyles]}>
      <TextElement style={[styles.subHeading, textStyles]} color="SECONDARY">
        {customText ? customText : <> {title} tiles have been enabled. Get back to your design to use those tiles </>}
      </TextElement>
      <MaterialCommunityIcons color="#000000" name={'open-in-new'} size={18} />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  subHeading: {fontSize: 16, lineHeight: 14, fontWeight: '500', marginRight: 5},
  wrapper: {
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
});
