import Button from '@/root/web/components-v2/base/Button';
import _ from 'lodash';
import React, {useEffect} from 'react';
import {Pressable, StyleSheet, Text, TextInput, View} from 'react-native';
import {fetchPage, toggleDeleteIntegrationModal} from '@/root/web/actions/editorActions';
import {useDispatch} from 'react-redux';
import Icon from '@/root/web/components-v2/base/Icon';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {MaterialCommunityIcons} from 'apptile-core';
import {IHelpDockLink, IIntegrationInputConfig} from '../integrationEditorConfig';
interface IntegrationFormBuilderProps {
  formConfig: Record<string, any>;
  helpDockLinks: IHelpDockLink[];
  defaultValues?: Record<string, any>;
  onSubmit: () => void;
  loading: boolean;
  integrationCode?: string | undefined;
  isNewIntegration?: boolean;
  integration: any;
  tempPages?: any[];
  onChange: (event: any, key: string) => void;
  onClear?: (key: string) => void;
  isEditMode?: boolean;
  updatedValues?: any;
  protectedFields?: any;
  isDisabled: boolean;
}

const IntegrationFormBuilder: React.FC<IntegrationFormBuilderProps> = props => {
  const {
    formConfig,
    helpDockLinks,
    defaultValues = {},
    loading,
    isNewIntegration,
    integration,
    onChange,
    onClear,
    isEditMode = false,
    updatedValues = {},
    protectedFields = {},
    isDisabled,
  } = props;

  const dispatch = useDispatch();
  const isActive = _.get(integration, ['appIntegrations', 0, 'active'], null);

  const onSubmit = () => {
    if (props.onSubmit) {
      props.onSubmit();
    }
  };

  useEffect(() => {
    if (isNewIntegration) {
      const tagName = _.get(integration, 'integrationCode', null);
      dispatch(fetchPage([tagName], true, true, false));
    }
  }, []);

  const renderForm = () => {
    let formUI = formConfig.map((editorInput: IIntegrationInputConfig) => {
      let key = editorInput.key;
      let placeholder = editorInput.placeholder;
      let type = editorInput.type || 'text';

      // check if field value is edited once or not
      const applyValue = updatedValues[key] !== undefined ? updatedValues[key] : defaultValues[key];
      const defaultValue = applyValue !== undefined ? applyValue : editorInput?.defaultValue;

      // check if masked credentials are coming from server. If yes, then make the field read only
      const isEditable =
        protectedFields[key] && type === 'secret'
          ? (applyValue || '').split('').every((char: string) => char === '*')
          : false;

      let input = null;
      switch (type) {
        case 'secret':
          isEditMode
            ? (input = (
                <View style={styles.secretContainer}>
                  <TextInput
                    editable={isEditMode ? !isEditable : true}
                    autoCorrect={false}
                    key={key}
                    placeholder={placeholder}
                    defaultValue={defaultValue}
                    onChangeText={text => {
                      onChange(text, key);
                    }}
                    selectTextOnFocus
                    style={styles.textInput}
                    value={applyValue}
                    secureTextEntry={true}
                  />
                  <Pressable onPress={onClear(key)} style={styles.cancelBtn}>
                    <Icon name={'close-circle'} size={'xl'} color="SECONDARY" />
                  </Pressable>
                </View>
              ))
            : (input = (
                <TextInput
                  editable={!isEditable}
                  autoCorrect={false}
                  key={key}
                  placeholder={placeholder}
                  defaultValue={defaultValue}
                  onChangeText={e => {
                    onChange(e, key);
                  }}
                  selectTextOnFocus
                  style={styles.textInput}
                  secureTextEntry={true}
                />
              ));
          break;
        default:
        case 'text':
          input = (
            <TextInput
              autoCorrect={false}
              key={key}
              placeholder={placeholder}
              defaultValue={defaultValue}
              onChangeText={e => {
                onChange(e, key);
              }}
              style={styles.textInput}
            />
          );
          break;
      }

      return (
        <View style={styles.textInputWrapper} key={key}>
          <Text style={styles.textInputLabel}> {editorInput.label}</Text>
          {input}
        </View>
      );
    });
    return formUI;
  };

  const helpDocsLinkRenderer = () => {
    return helpDockLinks?.map((link: IHelpDockLink) => (
      <Pressable onPress={() => window.open(link.url)} style={styles.tilesAlert}>
        <Text style={styles.subHeading}>{link.title}</Text>
        <MaterialCommunityIcons color="#005BE4" name={'open-in-new'} size={12} />
      </Pressable>
    ));
  };

  const handleRemove = () => {
    dispatch(toggleDeleteIntegrationModal(true));
  };

  return (
    <View style={styles.wrapper}>
      <TextElement color="SECONDARY" fontSize="md" fontWeight="500">
        Your Details
      </TextElement>
      <TextElement color="EDITOR_LIGHT_BLACK" fontSize="sm" lineHeight="2xl">
        Enter the following details to enable your integration
      </TextElement>
      <View style={styles.formWrapper}>
        {renderForm()}
        {/* Ghost element for keeping things even */}
        <View style={styles.textInputWrapper}>
          <TextInput disabled={true} style={[styles.textInput, styles.ghostElement]} />
        </View>
      </View>
      {helpDocsLinkRenderer()}
      <View style={[styles.buttonInputWrapper, !isActive ? {justifyContent: 'flex-end'} : {}]}>
        <Button
          size="MEDIUM"
          color="CTA"
          loading={loading}
          disabled={isDisabled}
          onPress={onSubmit}
          containerStyles={styles.btnStyle}
          textStyles={styles.btnText}>
          {isNewIntegration ? 'CONNECT' : 'UPDATE'}
        </Button>
        {isActive && (
          <Button
            onPress={handleRemove}
            variant={'TEXT'}
            textStyles={styles.btnPlainText}
            containerStyles={styles.btnContainer}>
            DISCONNECT
          </Button>
        )}
      </View>
    </View>
  );
};

export default IntegrationFormBuilder;

const styles = StyleSheet.create({
  textInput: {
    backgroundColor: '#F3F3F3F3',
    paddingHorizontal: 10,
    paddingVertical: 10,
    borderRadius: 5,
    width: '100%',
  },
  wrapper: {
    marginVertical: 30,
  },
  textInputLabel: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 30,
  },
  textInputWrapper: {
    borderRadius: 5,
    minWidth: '30vw',
    flexGrow: 1,
    alignItems: 'flex-start',
  },
  buttonInputWrapper: {
    width: '100%',
    borderRadius: 5,
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 20,
    alignItems: 'center',
  },
  btnPlainText: {
    color: '#FF0000',
    fontWeight: '500',
    fontSize: 14,
  },
  btnStyle: {
    width: 120,
  },
  btnText: {
    fontSize: 14,
    fontWeight: '500',
  },
  btnContainer: {
    marginLeft: -14,
  },
  secretContainer: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
  },
  cancelBtn: {
    position: 'absolute',
    right: 10,
    bottom: 0,
    top: 8,
  },
  formWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    flexGrow: 1,
    gap: 10,
    marginBottom: 15,
  },
  tilesAlert: {
    padding: 2,
    width: '100%',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  subHeading: {fontSize: 12, lineHeight: 14, fontWeight: '400', color: '#005BE4'},
  pressIcon: {marginLeft: 8},
  ghostElement: {
    backgroundColor: '#FFFFFF',
  },
});
