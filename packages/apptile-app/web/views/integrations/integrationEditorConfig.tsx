import React from 'react';
import {IntegrationPlatformType} from '@/root/app/plugins/datasource/datasourceTypes';
import {AppOnlyDiscount} from '../appOnlyDiscount';
import {ApptileCartUpsell} from '../ApptileCartUpsell';
import {Waitlist} from '../waitlist';
import {CartAssist} from '../cartAssist';
import StoreCredit from '../storeCredit';
import {InstallBanner} from '../installBanner';

export type IntegrationPlatformWithoutDatasource =
  | 'nativeBlogs'
  | 'whatsAppLauncher'
  | 'appOnlyDiscounts'
  | 'storeCredit'
  | 'metaAds'
  | 'klaviyo'
  | 'appInstallBanner';

type EditorInputType = 'text' | 'secret';

export interface IIntegrationFormConfig {
  //The docks to enable this integration
  helpDockLinks?: IHelpDockLink[];
  //The integration form config
  inputConfig?: IIntegrationInputConfig[];
  //Any one page that contains this integration tile!
  customConfig?: any;
  pageConfig?: any;
  screenName?: string;
}

export interface IHelpDockLink {
  title: string;
  url: string;
}
export interface IIntegrationInputConfig {
  key: string;
  label: string;
  type: EditorInputType;
  defaultValue?: any;
  placeholder?: string;
}

type IIntegrationEditorConfig =
  | Record<IntegrationPlatformType, IIntegrationFormConfig>
  | Record<IntegrationPlatformWithoutDatasource, IIntegrationFormConfig>;

const IntegrationEditorConfig: IIntegrationEditorConfig = {
  shopify: {
    inputConfig: [
      {key: 'storeName', label: 'Store Name', type: 'text'},
      {key: 'shopifyShopId', label: 'Shop Id', type: 'text'},
      {key: 'offlineAccessToken', label: 'Offline Access Token', type: 'secret'},
      {key: 'storefrontAccessToken', label: 'Storefront Token', type: 'secret'},
    ],
    helpDockLinks: [],
  },

  flits: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'Flit API Base URL', type: 'text', defaultValue: 'https://app.getflits.com'},
      {key: 'userId', label: 'Flit User Id', type: 'text'},
      {key: 'accessToken', label: 'Flit Access Token', type: 'secret'},
    ],
    helpDockLinks: [],
  },
  productFilterSearch: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'Product Filter & Search API Base URL',
        type: 'text',
        defaultValue: 'https://services.mybcapps.com',
      },
      {key: 'shopUrl', label: 'Product Filter & Search Store URL', type: 'text'},
    ],
    helpDockLinks: [],
  },
  firebaseAnalytics: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'Firebase API Base URL', type: 'text', defaultValue: ''},
      {key: 'GA4Secret', label: 'Firebase GA4 Secret', type: 'secret'},
      {key: 'instanceId', label: 'GA4 Instance Id', type: 'text'},
    ],
    helpDockLinks: [],
  },
  rechargePayments: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'Recharge Payments API Base URL',
        type: 'text',
        defaultValue: 'https://api.rechargeapps.com',
      },
      {key: 'accessToken', label: 'Recharge Payments Access Token', type: 'secret'},
      {key: 'apiVersion', label: 'Recharge Payments API Version', type: 'secret', defaultValue: '2021-11'},
    ],
    helpDockLinks: [],
  },
  gorgias: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'Gorgias account url',
        type: 'text',
        defaultValue: 'https://your-shopname.gorgias.com',
      },
      {key: 'apiKey', label: 'Password for gorgias helpdesk', type: 'secret'},
      {key: 'username', label: 'Username for gorgias helpdesk', type: 'text'},
    ],
    helpDockLinks: [],
    screenName: 'Menu',
  },
  apptileMFAuthentication: {
    inputConfig: [
      {
        key: 'apiBasePath',
        label: 'OTP Login API Base Path',
        type: 'text',
        defaultValue: 'https://dev-api.apptile.io/auth-manager',
      },
      {key: 'sourceEntityId', label: 'Source Entity Id', type: 'text'},
    ],
    helpDockLinks: [],
  },
  stampedReviews: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'Stamped API Base URL', type: 'text', defaultValue: 'https://stamped.io/api'},
      {key: 'storeHash', label: 'Stamped Store Hash', type: 'text', placeholder: '123456'},
      {key: 'storeUrl', label: 'Stamped Store URL', type: 'text', placeholder: 'storename.myshopify.com'},
      {key: 'publicKey', label: 'Stamped Public Key', type: 'secret', placeholder: 'public-someRandomString'},
      {key: 'privateKey', label: 'Stamped Private Key', type: 'secret', placeholder: 'key-someRandomString'},
    ],
    helpDockLinks: [{title: 'How to get store name? ', url: 'test.com'}],
    screenName: 'Product',
  },
  stampedRewards: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'Stamped API Base URL', type: 'text', defaultValue: 'https://stamped.io/api'},
      {key: 'storeHash', label: 'Stamped Store Hash', type: 'text'},
      {key: 'storeUrl', label: 'Stamped Store URL', type: 'text'},
      {key: 'publicKey', label: 'Stamped Public Key', type: 'secret'},
      {key: 'privateKey', label: 'Stamped Private Key', type: 'secret'},
    ],
    helpDockLinks: [],
  },
  localWishlist: {
    inputConfig: [],
    screenName: 'Wishlist',
  },
  appOnlyDiscounts: {
    inputConfig: [],
    customConfig: <AppOnlyDiscount />,
  },
  apptilePreOrder: {
    inputConfig: [],
    customConfig: <Waitlist />,
  },
  appInstallBanner: {
    inputConfig: [],
    pageConfig: <InstallBanner />,
  },
  nativeBlogs: {
    inputConfig: [],
    screenName: 'Home',
  },
  storeCredit: {
    inputConfig: [],
    customConfig: <StoreCredit />,
  },
  whatsAppLauncher: {},
  apptileSkinCare: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'Apptile Skin Care API Base URL', type: 'text', defaultValue: 'http://localhost:3018'},
      {key: 'apiAccessKey', label: 'Apptile Skin Care API Key', type: 'secret'},
    ],
    helpDockLinks: [],
  },
  storifyMe: {
    inputConfig: [
      {key: 'accountId', label: 'Account Id', type: 'text'},
      {key: 'apiKey', label: 'API Key', type: 'secret'},
      {key: 'env', label: 'Storify Environment (EU/US)', type: 'text', defaultValue: 'EU'},
    ],
    helpDockLinks: [],
  },
  orderLimiter: {
    inputConfig: [
      {key: 'maximumOrderValue', label: 'Maximum Total Order Amount', type: 'text'},
      {key: 'maximumOrderItems', label: 'Maximum Items Per Order', type: 'text'},
      {key: 'maximumItemQuantity', label: 'Maximum Quantity For Same Product', type: 'text'},
    ],
    helpDockLinks: [],
  },
  judgeMe: {
    inputConfig: [
      {key: 'shopDomain', label: 'Shop domain', type: 'text'},
      {key: 'apiToken', label: 'Private Api Token', type: 'secret'},
    ],
    helpDockLinks: [],
    screenName: 'Product',
  },
  yagiOrderCancellable: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'Yagi API Base URL', type: 'text', defaultValue: 'https://app.cancellable.app'},
      {key: 'shopDomain', label: 'Shop domain', type: 'text'},
    ],
    helpDockLinks: [],
    screenName: 'Product',
  },
  shopifyMultipass: {
    inputConfig: [{key: 'multipassSecret', label: 'Multipass secret value', type: 'text'}],
    helpDockLinks: [],
  },
  msg91: {
    inputConfig: [
      {key: 'authKey', label: 'Auth Key', type: 'secret'},
      {key: 'templateId', label: 'Template Id', type: 'text'},
    ],
    helpDockLinks: [],
  },
  moEngage: {
    inputConfig: [
      {key: 'apiId', label: 'API ID', type: 'text'},
      {key: 'apiKey', label: 'API Key', type: 'text'},
      {key: 'region', label: 'Region (IND | US | EU)', type: 'text'},
    ],
    helpDockLinks: [],
  },
  ApptileMFAuth: {
    inputConfig: [
      {key: 'multipassSecret', label: 'Multipass secret value', type: 'text'},
      {key: 'sourceEntityId', label: 'Store Url', type: 'text'},
    ],
    helpDockLinks: [],
  },
  shopFlo: {
    inputConfig: [{key: 'merchantId', label: 'Merchant ID', type: 'secret'}],
    helpDockLinks: [],
  },
  instagramMedia: {
    inputConfig: [
      {key: 'appId', label: 'App ID', type: 'text'},
      {key: 'orgId', label: 'Org ID', type: 'text'},
      {key: 'apptileServerBaseUrl', label: 'Apptile Server Base Url', type: 'text'},
    ],
    helpDockLinks: [],
  },
  livelyShoppable: {
    inputConfig: [{key: 'brandId', label: 'Company Id', type: 'text'}],
  },
  okendoReviews: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'API base url', type: 'text', defaultValue: 'https://api.okendo.io/v1'},
      {key: 'okendoUserId', label: 'Okendo User Id', type: 'secret'},
    ],
  },
  yotpoReviews: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'API base url', type: 'text', defaultValue: 'https://api-cdn.yotpo.com/v1'},
      {key: 'appKey', label: 'Yopto App Key', type: 'secret'},
    ],
  },
  searchanize: {
    inputConfig: [{key: 'apiKey', label: 'Searchanize API Key', type: 'secret'}],
  },
  reviewsIoReviews: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'API base url', type: 'text', defaultValue: 'https://api.reviews.io'},
      {key: 'appKey', label: 'Reviews App Key', type: 'secret'},
    ],
  },
  cloudsearch: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'API base url', type: 'text', defaultValue: 'https://app.cloudsearchapp.com/api/v1'},
      {key: 'shopDomain', label: 'Shop Domain', type: 'text'},
      {key: 'platformType', label: 'Platform Type', type: 'text'},
    ],
  },
  nectorRewards: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'API base url',
        type: 'text',
        defaultValue: 'https://platform.nector.io/api/v2',
      },
      {
        key: 'apikey',
        label: 'Api key',
        type: 'secret',
      },
      {
        key: 'source',
        label: 'Source',
        type: 'text',
        defaultValue: 'mobile',
      },
      {
        key: 'workspaceid',
        label: 'Workspace Id',
        type: 'text',
      },
    ],
  },
  discourse: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'API base url',
        type: 'text',
        placeholder: 'https://uhrmarket.apptile.io/',
      },
      {key: 'communityHelperEndpoint', label: 'Community Helper Endpoint', type: 'text'},
    ],
  },
  zapiet: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'API base url', type: 'text', defaultValue: 'https://api-us.zapiet.com/v1.0'},
      {key: 'appKey', label: 'Reviews App Key', type: 'secret'},
      {key: 'shop', label: 'Shop Domain', type: 'text'},
    ],
  },
  nosto: {
    inputConfig: [
      {key: 'apiBaseUrl', label: 'API base url', type: 'text', defaultValue: 'https://api.nosto.com/v1/graphql'},
      {
        key: 'searchBaseUrl',
        label: 'Saerch API base url',
        type: 'text',
        defaultValue: 'https://search.nosto.com/v1/graphql',
      },
      {key: 'apiToken', label: 'API Token', type: 'secret'},
      {key: 'accountId', label: 'Account Id', type: 'text'},
    ],
  },
  loyaltyLion: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'API base url',
        type: 'text',
        defaultValue: 'https://api.loyaltylion.com/v2/',
      },
      {key: 'username', label: 'Token', type: 'secret'},
      {key: 'password', label: 'Secret', type: 'secret'},
    ],
  },
  wishlistPlus: {
    inputConfig: [
      {
        key: 'apiEndpoint',
        label: 'API Endpoint',
        type: 'text',
        defaultValue: 'https://swymstore-v3premium-01.swymrelay.com',
      },
      {key: 'apiKey', label: 'apiKey', type: 'secret'},
      {key: 'pid', label: 'pid', type: 'secret'},
    ],
  },
  joyLoyalty: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'API base url',
        type: 'text',
        defaultValue: 'https://dev-api.joy.so/rest_api/v1',
      },
      {key: 'joyLoyaltyAppId', label: 'Joy Loyalty AppId', type: 'secret'},
      {key: 'secretKey', label: 'secretKey', type: 'secret'},
    ],
  },
  appstleSubs: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'API base url',
        type: 'text',
        defaultValue: 'https://subscription-admin.appstle.com',
      },
      {
        key: 'apikey',
        label: 'Api key',
        type: 'secret',
      },
    ],
  },
  riseAI: {
    inputConfig: [
      {key: 'apiKey', label: 'API Key', type: 'secret'},
      {key: 'shopDomain', label: 'Shop Domain', type: 'text'},
    ],
  },
  aitrillion: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'API base url',
        type: 'text',
        defaultValue: 'https://app.aitrillion.com/api-front-v1',
      },
      {
        key: 'storeApiAuthenticationKey',
        label: 'Store Api Authentication Key',
        type: 'secret',
      },
    ],
  },
  simplyOtp: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'API Url',
        type: 'text',
        defaultValue: 'https://omqkhavcch.execute-api.ap-south-1.amazonaws.com',
      },
      {
        key: 'apiVersion',
        label: 'API Version',
        type: 'text',
      },
      {
        key: 'apiKey',
        label: 'SimplyOtp API Key',
        type: 'secret',
      },
      {
        key: 'apiSecret',
        label: 'SimplyOtp Secret Key',
        type: 'secret',
      },
    ],
  },
  apptileCartDiscounts: {
    inputConfig: [],
  },
  preOrderWod: {
    inputConfig: [],
  },
  apptileCartUpsell: {
    inputConfig: [],
    customConfig: <ApptileCartUpsell />,
  },
  smile: {
    inputConfig: [
      {
        key: 'smilePrivateApiKey',
        label: 'Smile Private API Key',
        type: 'secret',
      },
    ],
  },
  cartAssist: {
    inputConfig: [],
    customConfig: <CartAssist />,
  },
  metaAds: {
    inputConfig: [
      {
        key: 'fb_appId',
        label: 'Facebook App Id',
        type: 'text',
        defaultValue: '',
      },
      {
        key: 'fb_clientToken',
        label: 'Facebook Client Token',
        type: 'secret',
        defaultValue: '',
      },
    ],
  },
  klaviyo: {
    inputConfig: [
      {
        key: 'klaviyo_company_id',
        label: 'Klaviyo Company Id',
        type: 'secret',
        defaultValue: '',
      },
    ],
  },
  cleverTap: {
    inputConfig: [
      {
        key: 'cleverTap_id',
        label: 'CleverTap Id',
        type: 'text',
        defaultValue: '',
      },
      {
        key: 'cleverTap_token',
        label: 'CleverTap Token',
        type: 'secret',
        defaultValue: '',
      },
      {
        key: 'cleverTap_region',
        label: 'CleverTap Region',
        type: 'text',
        defaultValue: '',
      },
    ],
  },
  fera: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'Fera API Base URL',
        type: 'text',
        defaultValue: 'https://api.fera.ai',
      },
      {key: 'secretKey', label: 'Fera Secret key', type: 'secret'},
      {key: 'publicKey', label: 'Fera Public Key', type: 'text', defaultValue: ''},
      {key: 'apiVersion', label: 'Fera API Version', type: 'text', defaultValue: 'v3'},
    ],
    helpDockLinks: [],
  },
  rivo: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'Rivo API Base URL',
        type: 'text',
        defaultValue: 'https://developer-api.rivo.io',
      },
      {key: 'authToken', label: 'Rivo Auth Token', type: 'secret'},
      {key: 'apiVersion', label: 'Rivo API Version', type: 'text', defaultValue: 'v1'},
    ],
    helpDockLinks: [],
  },
  recurpay: {
    inputConfig: [
      {
        key: 'apiBaseUrl',
        label: 'Recurpay API Base URL',
        type: 'text',
        defaultValue: 'https://{shopDomain}.recurpay.com',
      },
      {key: 'accessToken', label: 'Recurpay Access Token', type: 'secret'},
    ],
  },
};

export default IntegrationEditorConfig;
