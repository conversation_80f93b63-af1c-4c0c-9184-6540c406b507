import _, {isEmpty} from 'lodash';
import React, {useEffect} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {DELETE_APP, fetchAppIntegration, saveAppIntegrationCredentials} from '../../actions/editorActions';
import TextElement from '../../components-v2/base/TextElement';
import {EditorRootState} from '../../store/EditorRootState';
import {EditorFooterComponent, EditorHeaderComponent} from './components';
import IntegrationFormBuilder from './components/IntegrationFormBuilder';
import integrationEditorConfig from './integrationEditorConfig';
import {useParams} from 'react-router';
import Button from '../../components-v2/base/Button';
import {useNavigate} from '../../routing.web';
import {EmptySuccessScreen} from './components/EmptySuccessScreen';
import {NavigateToEditorScreen} from './components/NavigateToEditorScreen';
import {PendingConnectionScreen} from './components/PendingConnectionScreen';

const currentIntegrationSelector = (state: EditorRootState) => state.integration.currentIntegration;

const EditIntegration = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const integrationState = useSelector(currentIntegrationSelector);
  const {appIntegration, isLoading, integration, integrationCredentials} = integrationState;

  const {appIntegrationId, id: appId} = useParams<{appIntegrationId: string; id: string}>();

  const integrationCode = appIntegration?.platformType;
  const [modelValue, setModelValue]: any = React.useState({});
  const [protectedFields, setProtectedFields]: any = React.useState({});

  const getIntegrationEditorConfig = () => {
    return integrationCode ? _.get(integrationEditorConfig, integrationCode, null) : null;
  };

  const {
    inputConfig: formConfig,
    helpDockLinks,
    screenName,
    customConfig,
    pageConfig,
  } = getIntegrationEditorConfig() ?? {};

  useEffect(() => {
    (formConfig || []).forEach((editorInput: any) => {
      if (editorInput?.type === 'secret') {
        setProtectedFields({...protectedFields, [editorInput.key]: true});
      }
    });
  }, [formConfig]);

  useEffect(() => {
    if (appIntegrationId && appId) {
      dispatch(fetchAppIntegration(appId, appIntegrationId));
    }
  }, [dispatch, appIntegrationId, appId]);

  const updateCredentials = () => {
    if (appIntegrationId && appId && integrationCode) {
      dispatch(saveAppIntegrationCredentials(appId, appIntegrationId, modelValue, integrationCode));
    }
  };

  const onChange = (e: any, key: string) => {
    setModelValue({...modelValue, [key]: e});
  };

  const handleClear = (key: string) => {
    return () => {
      setModelValue({...modelValue, [key]: ''});
      setProtectedFields({...protectedFields, [key]: false});
    };
  };
  const isDisabled = isEmpty(modelValue) || Object.keys(modelValue).some(key => !modelValue[key]);

  const hasFormConfig = !_.isEmpty(formConfig);

  if (pageConfig) return pageConfig;
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={{width: '100%', alignItems: 'flex-start'}}>
        <Button
          onPress={() => (!customConfig ? navigate(`../integrations`) : navigate(-1))}
          containerStyles={{width: 65}}
          icon="arrow-left"
          variant="TEXT"
          color="SECONDARY"
          size="MEDIUM">
          Back
        </Button>
      </View>
      <View style={styles.formStyle}>
        {/* {!formConfig && !appIntegration && (
          <TextElement fontWeight="400" fontSize="xs" lineHeight="xl" color="SECONDARY">
            {`${integrationCode} Integration is not available at the moment`}
          </TextElement>
        )} */}
        {customConfig && (
          <>
            <EditorHeaderComponent headingText=" " title={integration?.title || ''} iconUrl={integration?.icon} />
            {customConfig}
            <EditorFooterComponent integration={integration} />
          </>
        )}
        {hasFormConfig && (
          <>
            <EditorHeaderComponent title={integration?.title || ''} iconUrl={integration?.icon} />
            <NavigateToEditorScreen integration={integration} screenName={screenName} />
            <IntegrationFormBuilder
              key={appIntegration?.id ?? null}
              defaultValues={appIntegration?.credentials}
              loading={isLoading || false}
              formConfig={formConfig}
              onSubmit={updateCredentials}
              integration={integration}
              onChange={onChange}
              onClear={handleClear}
              helpDockLinks={helpDockLinks}
              isEditMode
              updatedValues={modelValue}
              protectedFields={protectedFields}
              isDisabled={isDisabled}
            />
          </>
        )}
        {!customConfig && !hasFormConfig && appIntegration && (
          <>
            <EditorHeaderComponent headingText=" " title={integration?.title || ''} iconUrl={integration?.icon} />
            <EmptySuccessScreen integration={integration} screenName={screenName} />
          </>
        )}
        {!customConfig && !hasFormConfig && !appIntegration && (
          <>
            <EditorHeaderComponent
              isConnected={false}
              headingText=" "
              title={integration?.title || ''}
              iconUrl={integration?.icon}
            />
            <PendingConnectionScreen />
          </>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  formStyle: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    paddingHorizontal: 25,
    width: '100%',
    paddingVertical: 30,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
  },
  container: {
    flex: 1,
    paddingHorizontal: 35,
    paddingVertical: 20,
    alignItems: 'center',
  },
});

export default EditIntegration;
