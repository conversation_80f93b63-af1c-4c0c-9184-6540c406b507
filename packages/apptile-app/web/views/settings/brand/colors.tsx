import React from 'react';
import {StyleSheet, View, Pressable} from 'react-native';

import {useSelector, useDispatch} from 'react-redux';
import {selectAppConfig} from 'apptile-core';
import {baseGlobalThemeConfig} from 'apptile-core';
import {ApptileThemeConfigParams} from 'apptile-core';
import {updateColor} from '@/root/web/actions/themeActions';

import TextElement from '@/root/web/components-v2/base/TextElement';
import _ from 'lodash';

import tinycolor from 'tinycolor2';
import CollapsiblePanel from '@/root/web/components/CollapsiblePanel';
import ApptileColorPicker from '@/root/web/components/controls/themeEditor/components/apptileColorPicker';

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 32,
    borderTopWidth: 1,
    borderColor: '#DADADA',
  },
  headingText: {fontSize: 14},
  bodyText: {fontSize: 12},
  bodySubText: {fontSize: 12, color: 'grey', marginBottom: 10, marginTop: 5, marginLeft: 5},
  section: {marginTop: 24},
  colorPopContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  secondaryColor: {
    color: '#858585',
  },
  popTextColor: {
    color: '#535353',
  },
  pop: {
    width: 24,
    height: 24,
    borderRadius: 4,
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(134, 134, 134, 0.5)',
  },
  sketchPickerLayout: {
    flex: 1,
    marginVertical: 8,
  },
  //popRow: {marginTop: 8, flexDirection: 'row', width: '100%', justifyContent: 'space-between'},
  spacer: {
    width: 50,
    height: 1,
  },
  colorBoxContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#DADADA',
    borderRadius: 6,
    backgroundColor: '#F3F3F3',
    padding: 4,
    width: '49%',
  },
  popRowWrapper: {flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%'},
});

const colorGroup = {
  // default: {
  //   text: 'onBackground',
  //   background: 'background',
  // },
  'Primary Color': {
    text: 'onPrimary',
    background: 'primary',
  },
  'Accent Color': {
    text: 'onSecondary',
    background: 'secondary',
  },
};

const colorGroupMetaData = {
  'Primary Color': {
    description: "Used in buttons like 'Add to cart' or 'Checkout'",
  },
  'Accent Color': {
    description: 'Used sparingly to enhance the brand.',
  },
};

type ColorsProps = {};
const Color: React.FC<ColorsProps> = () => {
  const dispatch = useDispatch();

  const appConfig = useSelector(selectAppConfig);
  let theme = (appConfig?.get('theme')?.toJS() ?? baseGlobalThemeConfig) as ApptileThemeConfigParams;

  const {colors} = theme;

  const [selectedMode] = React.useState<'light' | 'dark'>('light');
  const reservedColors: Record<string, string> = {};

  // colorKeys same for both light and dark
  const reservedGlobalColorKeys = Object.keys(baseGlobalThemeConfig.colors.dark);

  // separating reserved and custom colors
  Object.entries(colors[selectedMode]).map(entry => {
    const colorName = entry[0];
    const colorCode = entry[1];

    if (reservedGlobalColorKeys.includes(colorName)) {
      reservedColors[colorName] = colorCode as string;
    }
  });

  const onChangeHandler = (colorName: string, colorCode: string) => {
    dispatch(
      updateColor({
        colorName,
        colorCode,
        mode: 'light',
      }),
    );
  };

  const debouncedOnChangeHandler = _.debounce(onChangeHandler, 250);

  return (
    <View style={styles.container}>
      <TextElement style={styles.headingText} fontWeight="600" lineHeight="md" color="SECONDARY">
        Brand Colors
      </TextElement>

      <View style={styles.section}>
        {Object.keys(colorGroup).map((entry, idx) => (
          <ColorGroup
            groupName={entry}
            reservedColors={reservedColors}
            onChange={debouncedOnChangeHandler}
            key={`reserved-${idx}}`}
          />
        ))}
      </View>
    </View>
  );
};

type ColorGroupProps = {
  reservedColors: Record<string, string>;
  onChange: (colorName: string, colorCode: string) => void;
  groupName: keyof typeof colorGroup;
};

const ColorGroup: React.FC<ColorGroupProps> = props => {
  const {reservedColors, onChange, groupName} = props;

  const [isPopActive, setPopAsActive] = React.useState(false);
  const [draftColor, setDraftColor] = React.useState(tinycolor());
  const [activeColor, setActiveColor] = React.useState('');
  const [activeKey, setActiveKey] = React.useState('');

  React.useEffect(() => {
    if (activeColor) {
      setDraftColor(tinycolor(activeColor));
    } else {
      setDraftColor(tinycolor());
    }
  }, [activeColor]);

  const onSwatchColorChange = (selectedColor: any) => {
    const color = tinycolor(selectedColor.rgb);

    setDraftColor(color);
    onChange(activeKey, color.getAlpha() === 1 ? color.toHexString() : color.toHex8String());
  };

  const onInputColorChange = (selectedColor: string) => {
    setDraftColor(tinycolor(selectedColor));
    onChange(activeKey, selectedColor);
  };

  const togglePop = (keyValue: string, colorValue: string) => {
    if (!isPopActive) {
      setActiveKey(keyValue);
      setActiveColor(colorValue);
    } else {
      setActiveKey('');
      setActiveColor('');
    }
    setPopAsActive(prev => !prev);
  };

  const generateLabel = `Editing ${groupName}`;

  return (
    <View>
      <View style={styles.popRowWrapper}>
        <TextElement style={[styles.bodyText]} lineHeight="md" color="SECONDARY">
          {_.capitalize(groupName)}
        </TextElement>
        <ColorBox
          name={colorGroup[groupName].background}
          label={reservedColors[colorGroup[groupName].background]}
          isActive={colorGroup[groupName].background === activeKey && isPopActive}
          colorCode={reservedColors[colorGroup[groupName].background]}
          onPress={togglePop}
        />

        {/* <View style={styles.popRow}> */}

        {/* <ColorBox
          name={colorGroup[groupName].text}
          label="Text"
          isActive={colorGroup[groupName].text === activeKey && isPopActive}
          colorCode={reservedColors[colorGroup[groupName].text]}
          onPress={togglePop}
        /> */}
        {/* </View> */}
      </View>
      <TextElement style={[styles.bodySubText]} lineHeight="md" color="TERTIARY">
        {colorGroupMetaData[groupName].description}
      </TextElement>
      <CollapsiblePanel title="" isOpen={isPopActive} backgroundStyle={{flex: 1, borderWidth: 0}} isHeaderHidden={true}>
        <View style={styles.sketchPickerLayout}>
          <ApptileColorPicker
            inTheme
            setPopAsActive={setPopAsActive}
            heading={generateLabel}
            color={draftColor.toHex8String()}
            value={draftColor.toHex8String()}
            onSwatchColorChange={onSwatchColorChange}
            onThemeColorChange={onInputColorChange}
          />
        </View>
      </CollapsiblePanel>
    </View>
  );
};

type ColorBoxProps = {
  onPress: (activeKey: string, activeColor: string) => void;
  name: string;
  label: string;
  isActive: boolean;
  colorCode: string;
};

const ColorBox: React.FC<ColorBoxProps> = props => {
  const {name, colorCode, onPress, label, isActive} = props;

  const onChange = () => {
    onPress(name, colorCode);
  };

  return (
    <Pressable style={[styles.colorBoxContainer, isActive && {borderColor: '#000'}]} onPress={onChange}>
      <View style={styles.colorPopContainer}>
        <View style={[styles.pop, {backgroundColor: colorCode}]} />
        <TextElement style={[styles.bodyText, styles.popTextColor]} lineHeight="md" color="SECONDARY">
          {_.capitalize(label)}
        </TextElement>
      </View>
    </Pressable>
  );
};

export default Color;
