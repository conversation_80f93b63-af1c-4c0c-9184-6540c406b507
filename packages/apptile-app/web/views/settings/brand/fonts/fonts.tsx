import React from 'react';
import {StyleSheet, View, Pressable, Image} from 'react-native';

import {IFontRecord} from 'apptile-core';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {MaterialCommunityIcons, selectAppConfig} from 'apptile-core';
import {useSelector} from 'react-redux';

import Button from '@/root/web/components-v2/base/Button';
import TextInput from '@/root/web/components-v2/base/TextInput';
import loadedFontList from '@/root/web/fonts/googleFonts.json';
import {addFont, deleteFont, updateFont} from '@/root/web/actions/themeActions';
import {useDispatch} from 'react-redux';
import CollapsiblePanel from '@/root/web/components/CollapsiblePanel';

import DropDownControl from '@/root/web/components/controls/DropDownControl';
import {pickWeightAndStyleType, alterFontFamily} from 'apptile-core';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon';
import {getMappedFontsInTheme} from '@/root/web/common/configProcessor/fonts/fonts';
import {makeToast} from '@/root/web/actions/toastActions';

import _ from 'lodash';
import theme from '@/root/web/styles-v2/theme';
import {currentPlanFeaturesSelector} from '@/root/web/selectors/FeatureGatingSelector';
import {allAvailablePlans} from 'apptile-core';
import {setOpenPremiumModal} from '@/root/web/actions/editorActions';
import Analytics from '@/root/web/lib/segment';
import imagePlanMapping from '@/root/web/common/featureGatingConstants';

type fontsProps = {};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 32,
    borderTopWidth: 1,
    borderColor: '#DADADA',
  },
  headingFont: {fontSize: 14, alignItems: 'center'},
  bodyFont: {fontSize: 12},
  subHeading: {
    marginTop: 8,
  },
  secondaryColor: {
    color: '#858585',
  },
  fontItemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    alignItems: 'center',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  popContainer: {
    position: 'relative',
  },
  popMenu: {
    position: 'absolute',
    width: 125,
    top: 30,
    right: 10,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#00000025',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.8,
    shadowRadius: 2,
    backgroundColor: '#fff',
    zIndex: 1,
  },
  fontSearchItem: {
    marginBottom: 16,
  },
  searchContainer: {
    borderColor: '#DADADA',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    paddingTop: 16,
    paddingBottom: 0,
    marginTop: 8,
  },
  searchFontHeadingWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  fontSelectContainer: {
    borderWidth: 1,
    borderColor: '#DADADA',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 12,
    marginTop: 12,
  },
  checkboxContainer: {
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  uploadFontContainer: {
    marginTop: 24,
  },
  uploadDropdown: {
    marginBottom: 8,
  },
  uploadTextInput: {
    marginBottom: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  uploadFormWrapper: {
    borderWidth: 1,
    borderColor: '#DADADA',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingBottom: 20,
    paddingTop: 4,
    marginTop: 12,
  },
  iconContainerStyle: {
    backgroundColor: 'transparent',
  },
  removeFonts: {
    color: '#FF3838',
    marginTop: 12,
    textAlign: 'center',
  },
  checkBox: {
    width: 24,
    height: 24,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBox: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabled: {
    color: theme.DISABLED_COLOR,
  },
  premiumPressable: {width: '100%', height: '100%', position: 'absolute', top: 0, right: 0},
});

type IFontScreenContext = 'GOOGLE_FONTS' | 'APPTILE_FONTS' | 'FONT_LIST';

const DefaultFontScreenContext = {
  screen: 'FONT_LIST',
  setScreen: (_val: IFontScreenContext) => {},
};

const FontScreenContext = React.createContext<typeof DefaultFontScreenContext>(DefaultFontScreenContext);
const useFontScreenContext = () => {
  return React.useContext(FontScreenContext);
};

const fonts: React.FC<fontsProps> = props => {
  const {} = props;
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isFeatureDisabled = !currentPlanFeatures.includes(allAvailablePlans.PRO);
  const dispatch = useDispatch();

  const [currSec, setCurrSec] = React.useState<'GOOGLE_FONTS' | 'APPTILE_FONTS' | 'FONT_LIST'>('FONT_LIST');

  const onMenuClick = (val: 'GOOGLE_FONTS' | 'APPTILE_FONTS') => {
    setCurrSec(val);
  };

  return (
    <FontScreenContext.Provider value={{screen: currSec, setScreen: setCurrSec}}>
      <View style={styles.container}>
        <View style={styles.rowLayout}>
          <View style={{flex: 1}}>
            <View style={{alignItems: 'center', flexDirection: 'row'}}>
              <TextElement style={styles.headingFont} fontWeight="600" lineHeight="md" color="SECONDARY">
                Custom Fonts
              </TextElement>
              {isFeatureDisabled && (
                <View style={{padding: 4, borderRadius: 3}}>
                  <Image
                    source={imagePlanMapping[allAvailablePlans.PRO]}
                    resizeMode="contain"
                    style={{width: 72, height: 22}}
                  />
                </View>
              )}
            </View>
            <TextElement
              style={[styles.bodyFont, styles.subHeading, styles.secondaryColor]}
              lineHeight="md"
              color="SECONDARY">
              Upload your own fonts!
            </TextElement>
          </View>
          {currSec === 'FONT_LIST' && (
            <View style={{alignItems: 'center', flexDirection: 'row'}}>
              <Button
                variant="TEXT"
                color="PRIMARY"
                size="MEDIUM"
                disabled={isFeatureDisabled}
                onPress={() => {
                  onMenuClick('APPTILE_FONTS');
                }}>
                + Add
              </Button>
            </View>
          )}
        </View>
        <View style={{zIndex: -1}}>
          {currSec === 'FONT_LIST' && <FontList />}
          {currSec === 'APPTILE_FONTS' && <ApptileFonts />}
        </View>
        {isFeatureDisabled && (
          <Pressable
            style={styles.premiumPressable}
            onPress={() => {
              Analytics.track('editor:fonts_editCustomFontPremiumClicked');
              dispatch(setOpenPremiumModal(true, allAvailablePlans.PRO));
            }}
          />
        )}
      </View>
    </FontScreenContext.Provider>
  );
};

type ApptileFontsProps = {
  fontKey?: string;
  mode?: 'EDIT' | 'ADD';
};
const ApptileFonts: React.FC<ApptileFontsProps> = props => {
  const {fontKey, mode} = props;
  const {setScreen} = useFontScreenContext();

  return (
    <View style={styles.uploadFontContainer}>
      <TextElement style={[styles.headingFont, {marginBottom: 12}]} fontWeight="600" color="SECONDARY">
        Upload custom font
      </TextElement>
      <ApptileFontForm fontKey={fontKey} mode={mode} onCancel={() => setScreen('FONT_LIST')} />
    </View>
  );
};

type ApptileFontFormProps = {
  onCancel?: () => void;
  isLoaded?: boolean;
} & ApptileFontsProps;
const ApptileFontForm: React.FC<ApptileFontFormProps> = props => {
  const {fontKey, mode, onCancel, isLoaded} = props;

  const appconfig = useSelector(selectAppConfig);
  const fontsMappedInTheme: string[] = getMappedFontsInTheme(appconfig);

  const fontObj = React.useMemo(() => {
    const appConfigFonts = (appconfig?.getFonts().toJS() ?? {}) as Record<string, IFontRecord>;
    return (fontKey ? appConfigFonts[fontKey] : {}) as IFontRecord;
  }, [appconfig, fontKey]);

  const [category, setCategory] = React.useState('sans-serif');
  const [family, setFamily] = React.useState('');
  const [variant, setVariant] = React.useState('400');
  const [style, setStyle] = React.useState<'regular' | 'italic'>('regular');
  const [fileUrl, setFileUrl] = React.useState('');
  const dispatch = useDispatch();

  React.useEffect(() => {
    if (!_.isEmpty(fontObj)) {
      setFamily(fontObj.family);
      setCategory(fontObj.category);
      setFileUrl(fontObj.fileUrl);

      const {fontWeight, fontStyle} = pickWeightAndStyleType(fontObj.variant);
      setVariant(fontWeight);
      setStyle(fontStyle === 'normal' ? 'regular' : 'italic');
    }
  }, [fontObj]);

  const onPressHandler = () => {
    if (mode !== 'EDIT') {
      if (_.isEmpty(category) || _.isEmpty(family) || _.isEmpty(variant) || _.isEmpty(fileUrl) || _.isEmpty(style)) {
        return;
      }

      dispatch(
        addFont({
          category,
          family,
          variant: generateVariant(style, variant),
          fileUrl,
          provider: 'apptileFonts',
        }),
      );

      setCategory('sans-serif');
      setFamily('');
      setVariant('400');
      setFileUrl('');
      setStyle('regular');
    }

    if (mode === 'EDIT' && fontKey) {
      dispatch(
        updateFont({
          id: fontKey,
          category,
          family,
          variant: generateVariant(style, variant),
          fileUrl,
          provider: 'apptileFonts',
        }),
      );
    }

    onCancel && onCancel();
  };

  const categoryOption = ['serif', 'sans-serif', 'display', 'handwriting', 'monospace'];
  const variantOption = ['100', '200', '300', '400', '500', '600', '700', '800', '900'];
  const styleOption = ['regular', 'italic'];

  const generatedFont = alterFontFamily({
    fontFamily: family,
    ...pickWeightAndStyleType(generateVariant(style, variant)),
  });
  const isFontRemovable = !fontsMappedInTheme.includes(generatedFont);

  const removeFontsFromConfig = () => {
    if (!isFontRemovable) {
      dispatch(
        makeToast({
          content: "Can't remove mapped font in theme",
          appearances: 'info',
        }),
      );
      return;
    }

    // if (fontKey && isFontRemovable) {
    //   dispatch(deleteFont(fontKey));
    // }
  };

  return (
    <View>
      <View style={{marginVertical: 8}}>
        <View style={styles.uploadDropdown}>
          <DropDownControl
            value={category}
            options={categoryOption}
            onChange={val => setCategory(val)}
            defaultValue=""
            disableBinding
          />
        </View>
        <TextInput
          onChangeText={setFamily}
          value={family}
          placeholder="family"
          containerStyle={styles.uploadTextInput}
        />
        <View style={styles.uploadDropdown}>
          <DropDownControl
            value={variant}
            options={variantOption}
            onChange={val => setVariant(val)}
            defaultValue=""
            disableBinding
          />
        </View>
        <View style={styles.uploadDropdown}>
          <DropDownControl
            value={style}
            options={styleOption}
            onChange={val => setStyle(val)}
            defaultValue=""
            disableBinding
          />
        </View>
        <TextInput
          onChangeText={setFileUrl}
          value={fileUrl}
          placeholder="https://fileurl.ttf"
          containerStyle={styles.uploadTextInput}
        />
      </View>
      <View style={styles.buttonContainer}>
        <Button
          variant="FILLED-PILL"
          color="SECONDARY"
          onPress={onPressHandler}
          containerStyles={{width: '48%', backgroundColor: '#000'}}
          textStyles={{color: '#fff'}}>
          Save
        </Button>
        <Button variant="FILLED-PILL" color="SECONDARY" containerStyles={{width: '48%'}} onPress={onCancel}>
          Cancel
        </Button>
      </View>

      {isLoaded && (
        <Pressable onPress={removeFontsFromConfig}>
          <TextElement style={[styles.bodyFont, styles.removeFonts, !isFontRemovable ? styles.disabled : {}]}>
            Remove from fonts
          </TextElement>
        </Pressable>
      )}
    </View>
  );
};

const generateVariant = (style: 'regular' | 'italic', variant: string) => {
  if (variant === '400' && style === 'italic') {
    return 'italic';
  }

  if (variant === '400' && style === 'regular') {
    return 'regular';
  }

  if (style === 'italic') {
    return `${variant}${style}`;
  }

  return variant;
};

type FontListProps = {};
const FontList: React.FC<FontListProps> = props => {
  const {} = props;

  const appconfig = useSelector(selectAppConfig);
  const appConfigFonts = (appconfig?.getFonts().toJS() ?? {}) as Record<string, IFontRecord>;

  const appConfigFontsWithId = Object.entries(appConfigFonts).map(entry => {
    return {
      ...entry[1],
      id: entry[0],
    };
  });

  const apptileFonts = appConfigFontsWithId.filter(entry => entry.provider === 'apptileFonts');

  return (
    <View style={{marginTop: 16}}>
      {apptileFonts.map((entry, idx) => {
        return (
          <FontItem
            name={`${entry.family}, ${entry.variant}`}
            key={`Apptile-${idx}`}
            provider="apptileFonts"
            fontId={entry.id}
          />
        );
      })}
    </View>
  );
};

type FontItemProps = {
  name: string;
  provider: 'apptileFonts' | 'googleFonts';
  fontId?: string;
};
const FontItem: React.FC<FontItemProps> = props => {
  const {name, provider, fontId} = props;
  const [isPopActive, setPopAsActive] = React.useState(false);

  return (
    <View>
      <View style={styles.fontItemContainer}>
        <TextElement color="SECONDARY" style={[styles.bodyFont]}>
          {_.capitalize(name)}
        </TextElement>
        <Pressable onPress={() => setPopAsActive(prev => !prev)} style={styles.iconBox}>
          {!isPopActive ? (
            <ApptileWebIcon name="edit-icon" size={28} color="#E7E7E7" />
          ) : (
            <MaterialCommunityIcons size={20} name="window-close" color="#E7E7E7" />
          )}
        </Pressable>
      </View>
      <CollapsiblePanel
        title="Text Colors"
        isOpen={isPopActive}
        backgroundStyle={{flex: 1, borderWidth: 0, backgroundColor: 'transparent'}}
        isHeaderHidden={true}>
        {provider === 'googleFonts' && (
          <FontVariantSelect name={name} onCancel={() => setPopAsActive(false)} isLoaded />
        )}
        {provider === 'apptileFonts' && (
          <View style={styles.uploadFormWrapper}>
            <ApptileFontForm fontKey={fontId} mode="EDIT" onCancel={() => setPopAsActive(false)} isLoaded />
          </View>
        )}
      </CollapsiblePanel>
    </View>
  );
};

type FontVariantSelectProps = {name: string; onCancel: () => void; isLoaded?: boolean};

const FontVariantSelect: React.FC<FontVariantSelectProps> = props => {
  const {name, onCancel, isLoaded} = props;

  const fontObj = _.find(loadedFontList, entry => entry.family === name);

  const regularMap: Record<string, string> = {};
  const italicMap: Record<string, string> = {};

  const dispatch = useDispatch();
  const appconfig = useSelector(selectAppConfig);
  const appConfigFonts = (appconfig?.getFonts().toJS() ?? {}) as Record<string, IFontRecord>;

  const appConfigFontsWithId = Object.entries(appConfigFonts).map(entry => {
    return {
      ...entry[1],
      id: entry[0],
    };
  });

  const groupedAppConfigFonts = _.groupBy(appConfigFontsWithId, entry => entry.family);
  const keyByVariant = groupedAppConfigFonts[name] ? _.keyBy(groupedAppConfigFonts[name], entry => entry.variant) : {};

  fontObj?.variants.forEach(variant => {
    if (variant === 'italic') {
      italicMap[variant] = '400';
    } else if (variant === 'regular') {
      regularMap[variant] = '400';
    } else if (_.endsWith(variant, 'italic')) {
      italicMap[variant] = variant.split('italic')[0];
    } else {
      regularMap[variant] = variant;
    }
  });

  const [selectedFonts, setSelectedFonts] = React.useState<
    {category: string; family: string; variant: string; fileUrl: string; provider: string}[]
  >(Object.values(keyByVariant).map(entry => _.omit(entry, 'id')));

  const selectedFontsKeyByVariant = _.keyBy(selectedFonts, 'variant');

  const variantHandler = (variantKey: string, status: 'ADD' | 'REMOVE') => {
    if (fontObj && status === 'ADD') {
      setSelectedFonts(prev => [
        ...prev,
        {
          category: fontObj.category,
          family: fontObj.family,
          variant: variantKey,
          fileUrl: _.get(fontObj, `files.${variantKey}`) as any,
          provider: 'googleFonts',
        },
      ]);
    }

    if (status === 'REMOVE') {
      setSelectedFonts(prev => prev.filter(entry => entry.variant !== variantKey));
    }
  };

  const regularArr = Object.entries(regularMap);
  const italicArr = Object.entries(italicMap);

  const removeFontsFromConfig = () => {
    Object.values(keyByVariant).forEach(entry => {
      dispatch(deleteFont(entry.id));
    });
  };

  const onSaveClick = () => {
    // removing variant in font family
    Object.values(keyByVariant).forEach(entry => {
      if (!selectedFontsKeyByVariant[entry.variant]) {
        dispatch(deleteFont(entry.id));
      }
    });

    // adding new variant in font family
    selectedFonts.forEach(entry => {
      if (!keyByVariant[entry.variant]) {
        dispatch(addFont(entry));
      }
    });

    onCancel && onCancel();
  };

  return (
    <View style={[styles.fontSelectContainer]}>
      <View style={styles.rowLayout}>
        {regularArr.length > 0 && (
          <View style={{width: '50%'}}>
            <TextElement style={[styles.bodyFont]} color="SECONDARY" fontWeight="500">
              Regular
            </TextElement>
            {regularArr
              .sort((a, b) => parseInt(a[1], 10) - parseInt(b[1], 10))
              .map((entry, idx) => {
                const [variantKey, variantValue] = entry;
                return (
                  <Checkbox
                    variantKey={variantKey}
                    name={variantValue}
                    key={idx}
                    onCheck={variantHandler}
                    checked={!!selectedFontsKeyByVariant[variantKey]}
                  />
                );
              })}
          </View>
        )}
        {italicArr.length > 0 && (
          <View style={{width: '50%'}}>
            <TextElement style={[styles.bodyFont]} color="SECONDARY" fontWeight="500">
              Italic
            </TextElement>
            {italicArr
              .sort((a, b) => parseInt(a[1], 10) - parseInt(b[1], 10))
              .map((entry, idx) => {
                const [variantKey, variantValue] = entry;
                return (
                  <Checkbox
                    variantKey={variantKey}
                    name={variantValue}
                    key={idx}
                    onCheck={variantHandler}
                    checked={!!selectedFontsKeyByVariant[variantKey]}
                  />
                );
              })}
          </View>
        )}
      </View>
      <View style={[styles.buttonContainer, {marginTop: 12}]}>
        <Button
          variant="FILLED-PILL"
          color="SECONDARY"
          onPress={onSaveClick}
          containerStyles={{width: '48%', backgroundColor: '#000'}}
          textStyles={{color: '#fff'}}>
          Save
        </Button>
        <Button variant="FILLED-PILL" color="SECONDARY" containerStyles={{width: '48%'}} onPress={onCancel}>
          Cancel
        </Button>
      </View>

      {isLoaded && (
        <Pressable onPress={removeFontsFromConfig}>
          <TextElement style={[styles.bodyFont, styles.removeFonts]}>Remove from fonts</TextElement>
        </Pressable>
      )}
    </View>
  );
};

type CheckboxProps = {
  variantKey: string;
  name: string;
  checked: boolean;
  onCheck: (val: string, status: 'ADD' | 'REMOVE') => void;
};

const Checkbox: React.FC<CheckboxProps> = props => {
  const {name, onCheck, variantKey, checked} = props;

  return (
    <View style={styles.checkboxContainer}>
      <Pressable
        onPress={() => {
          onCheck(variantKey, !checked ? 'ADD' : 'REMOVE');
        }}>
        <View style={[styles.checkBox, checked && {backgroundColor: '#000000'}]}>
          {checked && <MaterialCommunityIcons size={14} name="check" color="#fff" />}
        </View>
      </Pressable>
      <TextElement style={[styles.bodyFont, {marginLeft: 8}]} color="SECONDARY">
        {name}
      </TextElement>
    </View>
  );
};

export default fonts;
