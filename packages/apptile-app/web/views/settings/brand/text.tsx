import React from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import _ from 'lodash';

import {
  fontWeightMap, 
  selectAppConfig, 
  baseGlobalThemeConfig, 
  MaterialCommunityIcons
} from 'apptile-core';

import TextElement from '@/root/web/components-v2/base/TextElement';
import {FontChooserControl} from '@/root/web/components/controls/typographyControl';
import CollapsiblePanel from '@/root/web/components/CollapsiblePanel';

import {ITypographyItem} from 'apptile-core';
import {ApptileThemeConfigParams} from 'apptile-core';
import {updateUniversalTypography} from '@/root/web/actions/themeActions';
import {debounce} from 'lodash';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon';

import {setOpenPremiumModal} from '@/root/web/actions/editorActions';
import {allAvailablePlans} from 'apptile-core';
import {currentPlanFeaturesSelector} from '@/root/web/selectors/FeatureGatingSelector';
import Analytics from '@/root/web/lib/segment';

type TextProps = {};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 32,
    borderTopWidth: 1,
    borderColor: '#DADADA',
  },
  headingFont: {fontSize: 14},
  bodyFont: {fontSize: 12, width: 220},
  subHeading: {
    marginTop: 8,
  },
  rowLayout: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textShowcase: {
    fontSize: 22,
    marginRight: 8,
    minWidth: 25,
  },
  textName: {
    marginRight: 8,
  },
  secondaryColor: {
    color: '#858585',
  },
  textItemContainer: {
    marginVertical: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  iconBox: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  premiumIcon: {backgroundColor: '#f4e4fa', padding: 4, borderRadius: 3, marginLeft: 10},
  premiumPressable: {width: '100%', height: '100%', position: 'absolute', top: 0, right: 0},
});

const Text = React.forwardRef<View, TextProps>(() => {
  const appConfig = useSelector(selectAppConfig);
  let theme = (appConfig?.get('theme')?.toJS() ?? baseGlobalThemeConfig) as ApptileThemeConfigParams;

  const {typography} = theme;
  const dispatch = useDispatch();

  const [selectedPlatform] = React.useState<'ios' | 'android' | 'web'>('web');
  const reservedTypography: Record<string, ITypographyItem> = {};
  const customTypography: Record<string, ITypographyItem> = {};
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isFeatureDisabled = !currentPlanFeatures.includes(allAvailablePlans.PRO);

  // typographyKeys same for all platforms like iOS and Android and web
  const reservedGlobalTypographyKeys = Object.keys(baseGlobalThemeConfig.typography.android);

  // separating reserved and custom colors
  Object.entries(typography[selectedPlatform]).map(entry => {
    const typographyName = entry[0];
    const typographyItem = entry[1];

    if (reservedGlobalTypographyKeys.includes(typographyName)) {
      reservedTypography[typographyName] = typographyItem;
    } else {
      customTypography[typographyName] = typographyItem;
    }
  });

  const updateTypographyHandler = (typographyName: string, updatedTypographyItem: ITypographyItem) => {
    dispatch(
      updateUniversalTypography({
        typographyName: typographyName,
        typographyItem: updatedTypographyItem,
      }),
    );
  };

  const debouncedUpdateTypographyHandler = debounce(updateTypographyHandler, 250);
  return (
    <View style={styles.container}>
      <TextElement style={styles.headingFont} fontWeight="600" lineHeight="md" color="SECONDARY">
        Text Styles
        {/* {isFeatureDisabled && (
          <View style={styles.premiumIcon}>
            <MaterialCommunityIcons name="crown-outline" size={16} color="#BD00FF" />
          </View>
        )} */}
      </TextElement>

      <TextElement
        style={[styles.bodyFont, styles.subHeading, styles.secondaryColor]}
        lineHeight="md"
        color="SECONDARY">
        Choose text variations for your app.
      </TextElement>

      <View style={{marginTop: 16}}>
        <TextItem
          name="heading"
          value={reservedTypography.heading}
          onChange={val => debouncedUpdateTypographyHandler('heading', val)}
        />
        <TextItem
          name="subHeading"
          value={reservedTypography.subHeading}
          onChange={val => debouncedUpdateTypographyHandler('subHeading', val)}
        />
        <TextItem
          name="body"
          value={reservedTypography.body}
          onChange={val => debouncedUpdateTypographyHandler('body', val)}
        />

        {Object.entries(customTypography).map(entry => {
          const typographyName = entry[0];
          const typographyItem = entry[1];
          return (
            <TextItem
              name={typographyName}
              value={typographyItem}
              onChange={val => debouncedUpdateTypographyHandler(typographyName, val)}
            />
          );
        })}
      </View>
      {/* {isFeatureDisabled && (
        <Pressable
          style={styles.premiumPressable}
          onPress={() => {
            Analytics.track('editor:fonts_editFontPremiumClicked');
            dispatch(setOpenPremiumModal(true));
          }}
        />
      )} */}
    </View>
  );
});

type TextItemProps = {
  name: string;
  value: ITypographyItem;
  onChange: (val: any) => void;
};
const TextItem: React.FC<TextItemProps> = props => {
  const {name, onChange, value} = props;
  const [isPopActive, setPopAsActive] = React.useState(false);

  const generateStyle = {
    fontFamily: value.fontFamily,
    fontWeight: value.fontWeight,
    fontStyle: value.fontStyle,
    fontSize: value.fontSize,
  };

  return (
    <View>
      <View style={styles.textItemContainer}>
        <View style={styles.rowLayout}>
          <View>
            <TextElement color="SECONDARY" style={[styles.bodyFont, styles.textName]}>
              {_.capitalize(name)}
            </TextElement>
            <TextElement style={[styles.bodyFont, styles.secondaryColor]} color="SECONDARY">
              {value.fontFamily ?? ''} {_.get(fontWeightMap, value.fontWeight)}, {value.fontSize}
            </TextElement>
          </View>
        </View>
        <View style={{flexDirection: 'row'}}>
          <TextElement color="SECONDARY" style={[styles.textShowcase, value ? (generateStyle as any) : {}]}>
            Ag
          </TextElement>
          <Pressable onPress={() => setPopAsActive(prev => !prev)} style={styles.iconBox}>
            {!isPopActive ? (
              <ApptileWebIcon name="edit-icon" size={28} color="#E7E7E7" />
            ) : (
              <MaterialCommunityIcons size={20} name="window-close" color="#E7E7E7" />
            )}
          </Pressable>
        </View>
      </View>
      <CollapsiblePanel
        title="Text Colors"
        isOpen={isPopActive}
        backgroundStyle={{flex: 1, borderWidth: 0, backgroundColor: 'transparent'}}
        isHeaderHidden={true}>
        <FontChooserControl setPopAsActive={setPopAsActive} value={value} onChange={onChange} />
      </CollapsiblePanel>
    </View>
  );
};

export default Text;
