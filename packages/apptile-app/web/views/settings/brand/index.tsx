import React from 'react';
import {StyleSheet, ScrollView, View} from 'react-native';

import TextElement from '@/root/web/components-v2/base/TextElement';
import Colors from './colors';
import Text from './text';
import Font from './fonts';
import Logo from './logo';

type indexProps = {};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  heading: {
    marginVertical: 32,
    marginHorizontal: 20,
  },
  headingText: {
    fontSize: 16,
    fontWeight: '600',
    paddingHorizontal: 8,
    paddingTop: 16,
    paddingBottom: 16,
    color: 'Black',
  },
  divider: {
    backgroundColor: '#E5E5E5',
    height: 1,
    width: '100%',
  },
});

const index: React.FC<indexProps> = () => {
  return (
    <ScrollView showsVerticalScrollIndicator={false} style={styles.container}>
      <TextElement style={styles.headingText}>Brand</TextElement>
      <View style={styles.divider} />
      <Logo />
      <Colors />
      <Text />
      <Font />
    </ScrollView>
  );
};

export default index;
