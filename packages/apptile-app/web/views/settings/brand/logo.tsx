import React from 'react';
import {StyleSheet, View, Pressable, Image, ImageSourcePropType} from 'react-native';

import TextElement from '@/root/web/components-v2/base/TextElement';
import AssetChooseDialog from '@/root/web/components/controls/assetEditor/assetChooseDialog';
import Analytics from '@/root/web/lib/segment';
import {useDispatch, useSelector} from 'react-redux';

import {updateSettingsValue, deleteAppSettings} from 'apptile-core';
import {selectAppSettingsForKey} from 'apptile-core';
import {SettingsConfig} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';

import {BrandSettingsTypes} from 'apptile-core';
const BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY,
  BRAND_LOGO_ASSET_ID = BrandSettingsTypes.BRAND_LOGO_ASSET_ID,
  BRAND_LOGO_WIDTH = BrandSettingsTypes.BRAND_LOGO_WIDTH;
import {MaterialCommunityIcons} from 'apptile-core';

import {modelUpdateAction} from 'apptile-core';
import RadioGroupControl from '@/root/web/components/controls/RadioGroupControl';
import RangeSliderControl from '@/root/web/components/controls/RangeSliderControl';

type logoProps = {};

const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 32,
    borderTopWidth: 1,
    borderColor: '#DADADA',
  },
  headingFont: {fontSize: 14},
  bodyFont: {fontSize: 12},
  subHeading: {
    marginTop: 8,
  },
  secondaryColor: {
    color: '#858585',
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
  imageContainer: {
    position: 'relative',
    width: 100,
    height: 100,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
    marginTop: 32,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageWrapper: {width: 100, height: 100, overflow: 'hidden'},
  removeAsset: {
    backgroundColor: '#262626',
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    top: -7.5,
    right: -7.5,
    zIndex: 1,
  },
});

const Logo = React.forwardRef<View, logoProps>((props, ref) => {
  const {} = props;

  const dispatch = useDispatch();
  const [showPopover, setShowPopover] = React.useState(false);

  const appConfig = useSelector(selectAppConfig);
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const logoAssetId = brandSettings.getSettingValue(BRAND_LOGO_ASSET_ID) as string | undefined;
  const logoSize = brandSettings.getSettingValue(BRAND_LOGO_WIDTH) as string | undefined;

  const imageRecord = appConfig?.getImageId(logoAssetId);
  const assetSourceValue = imageRecord?.fileUrl ?? null;

  const triggerModalUpdate = React.useCallback(
    (val, key) => {
      const modelUpdate = [
        {
          selector: ['Apptile', key],
          newValue: val,
        },
      ];
      dispatch(modelUpdateAction(modelUpdate, undefined, true));
    },
    [dispatch],
  );

  const onLogoUpdate = React.useCallback(
    newPrefixes => {
      Analytics.track('editor:dashboard_brandLogoAdded');
      dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_LOGO_ASSET_ID, newPrefixes));
      triggerModalUpdate(newPrefixes, 'brandLogoAssetId');
    },
    [dispatch],
  );

  const onLogoSizeUpdate = React.useCallback(
    value => {
      dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_LOGO_WIDTH, value));
      triggerModalUpdate(value, 'brandLogoSize');
    },
    [dispatch],
  );

  const onLogoDelete = React.useCallback(() => {
    Analytics.track('editor:dashboard_brandLogoRemoved');
    dispatch(deleteAppSettings(BRAND_SETTINGS_KEY));
    triggerModalUpdate('', 'brandLogoAssetId');
  }, [dispatch]);

  return (
    <View style={styles.container}>
      <TextElement style={styles.headingFont} fontWeight="600" lineHeight="md" color="SECONDARY">
        Logo
      </TextElement>
      <TextElement
        style={[styles.bodyFont, styles.subHeading, styles.secondaryColor]}
        lineHeight="md"
        color="SECONDARY">
        This logo will be used on your app header.
      </TextElement>

      {!assetSourceValue ? (
        <Pressable
          style={[styles.imageContainer, styles.upload]}
          onPress={() => setShowPopover(true)}
          nativeID="uploadLogoSection">
          <TextElement style={styles.bodyFont} fontSize="sm" lineHeight="md" color="PRIMARY">
            + UPLOAD
          </TextElement>
        </Pressable>
      ) : (
        <View style={styles.imageContainer}>
          <Pressable onPress={onLogoDelete} style={styles.removeAsset}>
            <MaterialCommunityIcons name="close" size={12} color="#FFF" />
          </Pressable>
          <Image resizeMode="contain" source={assetSourceValue as ImageSourcePropType} style={styles.image} />
        </View>
      )}

      <AssetChooseDialog
        askURL={false}
        currentAssetId={logoAssetId ?? ''}
        onSelectAsset={assetId => {
          onLogoUpdate(assetId);
        }}
        onCloseDialog={val => {
          setShowPopover(val);
        }}
        showDialog={showPopover}
      />
      <RangeSliderControl
        label={'Logo Size'}
        value={logoSize ?? ''}
        onChange={onLogoSizeUpdate}
        minRange="32"
        maxRange="200"
      />
    </View>
  );
});

export default Logo;
