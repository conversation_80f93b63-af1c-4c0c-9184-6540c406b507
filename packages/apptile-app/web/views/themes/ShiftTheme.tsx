import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View, Image} from 'react-native';
import {useNavigate} from 'react-router-dom';
import apptileTheme from '../../styles-v2/theme';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';
import {useParams} from 'react-router';
import {changeAppContextData, replaceAppConfig} from '../../actions/editorActions';
import {EditorRootState} from '../../store/EditorRootState';
import {updateBasicAppInfo} from '../../actions/onboardingActions';
import theme from '../../styles-v2/theme';
import {MaterialCommunityIcons} from 'apptile-core';
import commonStyles from '../../styles-v2/commonStyles';
import Button from '../../components-v2/base/Button';

const ShiftThemeScreen = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [collection, setCollection] = useState({});

  const {mandatoryFields} = useSelector((state: EditorRootState) => state.onboarding);
  const appSaveId = useSelector((state: EditorRootState) => state?.apptile?.appSaveId);

  const {webflowData} = useSelector((state: EditorRootState) => state.onboarding);
  const {themes} = webflowData;

  const {filled, filling} = mandatoryFields;

  const {orgId, id: appId, slug} = useParams();

  const [currentStep, setCurrentStep] = useState(-1);

  const steps = [
    'Installing theme',
    'Adding Products to your app',
    'Adding Collections to your app',
    'Linking Policies',
    'Creating All Screens',
    'Setup Complete',
  ];

  useEffect(() => {
    if (slug != 'onboarded') {
      setCollection(_.find(themes?.items, t => t.slug === slug));
    } else {
      setCurrentStep(steps.length);
    }
    dispatch(changeAppContextData(appId));
  }, [slug, steps.length, themes]);

  useEffect(() => {
    const blueprintId = collection?.['blueprint-id'];
    if (blueprintId && appSaveId && orgId && appId) {
      dispatch(
        replaceAppConfig({
          appId,
          appSaveId,
          blueprintId,
          themeShift: true,
          onboarding: false,
          orgId,
        }),
      );
    }
  }, [appId, appSaveId, collection, orgId]);

  useEffect(() => {
    if (filling && filled) {
      dispatch(
        updateBasicAppInfo({
          appId,
          infoObject: {
            activeBlueprintUUID: collection?.['blueprint-id'],
          },
        }),
      );
    }
  }, [appId, navigate, orgId, filling, filled, dispatch, collection]);

  useEffect(() => {
    if (slug != 'onboarded') {
      setTimeout(() => {
        if (currentStep == steps.length - 2) {
          if (filling && filled) setCurrentStep(steps.length);
        } else {
          if (filling && filled) setCurrentStep(steps.length);
          else setCurrentStep(currentStep + 1);
        }
      }, 2000);
    }
  }, [currentStep, filled, filling, slug, steps.length]);

  useEffect(() => {
    if (currentStep === steps.length) {
      setTimeout(() => {
        navigate(`/dashboard/${orgId}/app/${appId}`);
      }, 1500);
    }
  }, [appId, currentStep, dispatch, navigate, orgId, steps.length]);

  return (
    <View style={styles.rootContainer}>
      {currentStep < steps.length && (
        <View style={[styles.progressBar, {width: `${(currentStep / steps.length) * 100}%`}]} />
      )}
      <View style={styles.subrootContainer}>
        <View style={styles.mainContainer}>
          <View style={styles.leftContainer}>
            <Image
              resizeMode={'contain'}
              source={{uri: collection?.['thumbnail']?.url || require('../../assets/images/placeholder-image.png')}}
              style={{width: '100%', aspectRatio: '459/392'}}
            />
          </View>
          <View style={styles.rightContainer}>
            <View>
              {steps.map((step: string, index: number) => (
                <View style={styles.step}>
                  {index == currentStep ? (
                    <Image
                      style={{width: 20, aspectRatio: 1}}
                      source={require('@/root/web/assets/images/preloader-theme.svg')}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name={index >= currentStep ? 'checkbox-blank-circle-outline' : 'checkbox-marked-circle'}
                      size={24}
                    />
                  )}

                  <Text
                    style={[
                      commonStyles.baseText,
                      {fontWeight: index == currentStep ? '900' : '400', color: index > currentStep ? '#333' : '#000'},
                    ]}>
                    {step}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: {
    height: '100vh',
    fontFamily: apptileTheme.FONT_FAMILY,
    backgroundColor: '#ECE9E1',
    width: '100%',
  },
  progressBar: {
    position: 'absolute',
    top: 0,
    height: 5,
    backgroundColor: theme.CTA_BACKGROUND,
  },
  subrootContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  mainContainer: {
    width: '70%',
    backgroundColor: '#fff',
    flexDirection: 'row',
    borderRadius: 32,
    overflow: 'hidden',
  },
  leftContainer: {
    width: '50%',
  },
  rightContainer: {
    width: '50%',
    paddingLeft: 55,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  step: {
    marginBottom: 25,
    gap: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttomBar: {
    position: 'absolute',
    bottom: 50,
    right: 50,
  },
});

export default ShiftThemeScreen;
