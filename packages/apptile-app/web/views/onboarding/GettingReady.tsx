import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View, Image} from 'react-native';
import {useNavigate} from 'react-router-dom';
import apptileTheme from '../../styles-v2/theme';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';
import {useParams} from 'react-router';
import {replaceAppConfig, softRestartConfig} from '../../actions/editorActions';
import {EditorRootState} from '../../store/EditorRootState';
import {updateBasicAppInfo} from '../../actions/onboardingActions';
import {MaterialCommunityIcons} from 'apptile-core';
import commonStyles from '../../styles-v2/commonStyles';
import theme from '../../styles-v2/theme';

const GettingReadyScreen = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [collection, setCollection] = useState({});

  const {storeData, storeStatus, mandatoryFields} = useSelector((state: EditorRootState) => state.onboarding);

  const {webflowData} = useSelector((state: EditorRootState) => state.onboarding);
  const {themes} = webflowData;

  const {appSaveId, storeName} = storeData;
  let {orgId, appId, slug} = useParams();

  const {isOnboarded, nameChanged} = storeStatus;
  const {filled, filling} = mandatoryFields;
  const [currentStep, setCurrentStep] = useState(-1);

  const steps = [
    'Installing theme',
    'Adding Products to your app',
    'Adding Collections to your app',
    'Linking Policies',
    'Creating All Screens',
    'Setup Complete',
  ];

  useEffect(() => {
    if (slug != 'onboarded') {
      setCollection(_.find(themes?.items, t => t.slug === slug));
    } else {
      setCurrentStep(steps.length);
    }
  }, [slug, steps.length, themes]);

  useEffect(() => {
    collection?.['blueprint-id'] &&
      dispatch(
        replaceAppConfig({
          appId,
          appSaveId,
          blueprintId: collection?.['blueprint-id'],
          onboarding: true,
          orgId,
        }),
      );
  }, [appId, appSaveId, collection, dispatch, orgId]);

  useEffect(() => {
    if (isOnboarded) {
      if (collection && collection['blueprint-id']) {
        dispatch(
          updateBasicAppInfo({
            appId,
            infoObject: {
              name: storeName,
              activeBlueprintUUID: collection?.['blueprint-id'],
            },
          }),
        );
      }
    }
  }, [appId, isOnboarded, navigate, orgId, storeName, nameChanged, dispatch, collection, filling, filled]);

  useEffect(() => {
    if (currentStep === steps.length) {
      setTimeout(() => {
        navigate(`/dashboard/${orgId}/app/${appId}`);
      }, 1500);
    }
  }, [appId, currentStep, dispatch, navigate, orgId, steps.length]);

  useEffect(() => {
    if (slug != 'onboarded') {
      setTimeout(() => {
        if (currentStep == steps.length - 2) {
          if (isOnboarded && nameChanged && filling && filled) setCurrentStep(steps.length);
        } else {
          if (isOnboarded && nameChanged && filling && filled) setCurrentStep(steps.length);
          else setCurrentStep(currentStep + 1);
        }
      }, 2000);
    }
  }, [currentStep, filled, filling, isOnboarded, nameChanged, slug, steps.length]);
  return (
    <View style={styles.rootContainer}>
      {currentStep < steps.length && (
        <View style={[styles.progressBar, {width: `${(currentStep / steps.length) * 100}%`}]} />
      )}
      <View style={styles.subrootContainer}>
        <View style={styles.mainContainer}>
          <View style={styles.leftContainer}>
            <Image
              resizeMode={'contain'}
              source={{uri: collection?.['thumbnail']?.url || require('../../assets/images/placeholder-image.png')}}
              style={{width: '100%', aspectRatio: '459/392'}}
            />
          </View>
          <View style={styles.rightContainer}>
            <View>
              {steps.map((step: string, index: number) => (
                <View style={styles.step}>
                  {index == currentStep ? (
                    <Image
                      style={{width: 20, aspectRatio: 1}}
                      source={require('@/root/web/assets/images/preloader-theme.svg')}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name={index >= currentStep ? 'checkbox-blank-circle-outline' : 'checkbox-marked-circle'}
                      size={24}
                    />
                  )}
                  <Text
                    style={[
                      commonStyles.baseText,
                      {fontWeight: index == currentStep ? '900' : '400', color: index > currentStep ? '#333' : '#000'},
                    ]}>
                    {step}
                  </Text>
                </View>
              ))}
            </View>
            {/* <View style={styles.buttomBar}>
              {currentStep == steps.length && (
                <Button
                  color={'CTA'}
                  onPress={() => {
                    // TODO(manas): A navigate doesn't make the onboarding finish successfully but a reload does. 
                    // Check why this is happening and fix it so we don't need a reload here.
                    // navigate(`/dashboard/${orgId}/app/${appId}/dashboard/store`);
                    logger.info(`Reloading page to: /dashboard/${orgId}/app/${appId}/dashboard/store`)
                    window.location.assign(`/dashboard/${orgId}/app/${appId}/dashboard/store`);
                  }}>
                  Continue
                </Button>
              )}
            </View> */}
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: {
    height: '100vh',
    fontFamily: apptileTheme.FONT_FAMILY,
    backgroundColor: '#ECE9E1',
  },
  progressBar: {
    position: 'absolute',
    top: 0,
    height: 5,
    backgroundColor: theme.CTA_BACKGROUND,
  },
  subrootContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  readyText: {
    color: '#535353',
    fontWeight: apptileTheme.ONBOARDING_FONT_WEIGHT,
    fontSize: 20,
    width: 300,
    fontFamily: apptileTheme.FONT_FAMILY,
    textAlign: 'center',
  },
  loaderImage: {
    width: 150,
    height: 150,
    marginTop: 100,
  },
  mainContainer: {
    width: '70%',
    backgroundColor: '#fff',
    flexDirection: 'row',
    borderRadius: 32,
    overflow: 'hidden',
  },
  leftContainer: {
    width: '50%',
  },
  rightContainer: {
    width: '50%',
    paddingLeft: 55,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  step: {
    marginBottom: 25,
    gap: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttomBar: {
    position: 'absolute',
    bottom: 50,
    right: 50,
  },
});

export default GettingReadyScreen;
