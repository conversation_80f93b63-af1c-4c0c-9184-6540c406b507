import React, {createContext, useContext, useEffect, useState} from 'react';
import {BuildManagerApi} from '../../api/BuildApi';
import {useSelector} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import {is} from 'immutable';

interface PublishContextType {
  step: number;
  totalSteps: number;
  formState: Record<string, any>;
  updateFormState: (key: string, value: any) => void;
  nextStep: () => void;
  prevStep: () => void;
  setStep: (step: number) => void;
  appDetails: Record<string, any>;
  firebaseProjectId: string;
  buildDetails: Record<string, any>;
  setAppDetails: (appDetails: Record<string, any>) => void;
  setFirebaseProjectId: (firebaseDetails: string) => void;
  setBuildDetails: (buildDetails: Record<string, any>) => void;
  isLoading: boolean;
}

// Create the context
export const PublishContext = createContext<PublishContextType | undefined>(undefined);

// Provide context to the app
export const PublishProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  const [step, setStep] = useState(3);
  const [doesMetaDataExist, setMetaDataExist] = useState(false);
  const [totalSteps, setTotalSteps] = useState(4);
  const [formState, setFormState] = useState<Record<string, any>>({});
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const [isLegacyApp, setLegacyApp] = useState(false); // Apps before publish flow
  const [formLoading, setFormLoading] = useState(true);
  const [appDetails, setAppDetails] = useState<Record<string, any>>({
    appName: '',
    subtitle: '',
    privacyPolicy: '',
    supportURL: '',
    description: '',
    iconUri: '',
    splashUri: '',
  });

  const [initialAppDetails, setInitalAppDetails] = useState<Record<string, any>>({
    appName: '',
    subtitle: '',
    privacyPolicy: '',
    supportURL: '',
    description: '',
    iconUri: '',
    splashUri: '',
  });

  const [firebaseProjectId, setFirebaseProjectId] = useState<string>('');
  const [initialFirebaseProjectId, setInitialFirebaseProjectId] = useState<string>('');
  const [isFirebaseInviteChecked, setFirebaseInviteChecked] = useState<boolean>(false);
  const [buildDetails, setBuildDetails] = useState<Record<string, any>>({
    assets: {},
    ios: {
      isAppleSelected: true,
      isDeveloperAccountConnected: false,
      isAppleAccordionOpen: false,
      accountType: 'Apptile Developer Account',
      isSuccessVisible: false,
      hasExistingIOSApp: false,
      isIOSBuildRequested: false,
      accountDetails: ['', '', '', ''],
      appStorePrivateKeyUrl: '',
      initialAccountDetails: ['', '', '', ''],
      iosBundleId: '',
      ipadSupport: false,
      appTrackingPermission: false,
    },
    android: {
      isGoogleSelected: true,
      isGoogleAccordionOpen: false,
      hasExistingGoogleDeveloperAccount: 'No',
      hasExistingGoogleApp: 'No',
      isAndroidBuildRequested: false,
      androidBundleId: '',
      playStoreDevAccountName: '',
    },
  });

  const [alreadyExistingMetaData, setAlreadyExistingMetaData] = useState({});
  const [buildsList, setBuildsList] = useState([]);
  const updateFormState = (key: string, value: any) => {
    setFormState(prev => ({...prev, [key]: value}));
  };

  useEffect(() => {
    if (!appId) return;

    const fetchData = async () => {
      // Fetch App Settings
      try {
        const appSettingsResponse = await BuildManagerApi.getAppSettings(appId);
        var {displayName, firebaseProjectId, androidBundleIdentifier, iosBundleIdentifier} = appSettingsResponse.data;

        setFirebaseProjectId(firebaseProjectId ?? '');
        setInitialFirebaseProjectId(firebaseProjectId ?? '');

        setAppDetails(prev => ({
          ...prev,
          appName: displayName,
        }));

        // setBuildDetails(prev => {
        //   return {
        //     ...prev,
        //     ios: {
        //       ...prev.ios,
        //       iosBundleId: iosBundleIdentifier,
        //     },
        //     android: {
        //       ...prev.android,
        //       androidBundleId: androidBundleIdentifier,
        //     },
        //   };
        // });

        setInitalAppDetails(prev => ({
          ...prev,
          appName: displayName,
        }));
      } catch (error) {
        console.error('Error fetching app settings:', error);
      }

      // Fetch Builds
      let buildsList = [];
      try {
        const buildsListResponse = await BuildManagerApi.getBuildsV2(appId);
        buildsList = buildsListResponse.data;
        setBuildsList(buildsList);
        if (buildsList.length > 0) {
          setStep(4);
        }
      } catch (error) {
        console.error('Error fetching builds:', error);
      }

      // Fetch Metadata
      let appStoreOrgName = '';
      let isLegacyApp = false;

      try {
        const metadataResponse = await BuildManagerApi.getAppMetadata(appId);

        const {
          appSubtitle = '',
          appDescription = '',
          privacyPolicy = '',
          supportURL = '',
          metadata = {},
        } = metadataResponse.data;

        appStoreOrgName = metadata?.ios?.appStoreDevAccountName ?? '';

        setAlreadyExistingMetaData(metadata);

        isLegacyApp = !metadata && buildsList.length > 0;

        setLegacyApp(isLegacyApp);

        setAppDetails(prev => ({
          ...prev,
          subtitle: appSubtitle,
          privacyPolicy,
          supportURL,
          description: appDescription,
        }));
        setInitalAppDetails(prev => ({
          ...prev,
          subtitle: appSubtitle,
          privacyPolicy,
          supportURL,
          description: appDescription,
        }));

        if (!isLegacyApp) {
          setBuildDetails(prev => ({
            ...prev,
            ...metadata,
            ios: {
              ...prev.ios,
              ...(metadata?.ios || {}),
              iosBundleId: metadata?.ios?.isIOSBuildRequested ? iosBundleIdentifier : '',
              ipadSupport: metadata?.ios?.ipadSupport ? true : false,
              appTrackingPermission: metadata?.ios?.appTrackingPermission ? true : false,
            },
            android: {
              ...prev.android,
              ...(metadata?.android || {}),
              androidBundleId: metadata?.android?.isAndroidBuildRequested ? androidBundleIdentifier : '',
            },
          }));

          setMetaDataExist(true);
        } else {
          setBuildDetails(prev => ({
            ...prev,
            ios: {
              ...prev.ios,
              isIOSBuildRequested: true,
              iosBundleId: iosBundleIdentifier,
            },
            android: {
              ...prev.android,
              isAndroidBuildRequested: true,
              androidBundleId: androidBundleIdentifier,
            },
          }));
        }
      } catch (error) {
        console.error('Error fetching metadata:', error);

        if (error?.response?.status === 400) {
          if (error?.response?.data?.message === 'App metadata does not exist') {
            setMetaDataExist(false);
            if (buildsList.length > 0) {
              setLegacyApp(true);
              setBuildDetails(prev => ({
                ...prev,
                ios: {
                  ...prev.ios,
                  isIOSBuildRequested: true,
                  iosBundleId: iosBundleIdentifier,
                },
                android: {
                  ...prev.android,
                  isAndroidBuildRequested: true,
                  androidBundleId: androidBundleIdentifier,
                },
              }));
            }
          }
        }
      }

      // Fetch App Assets
      try {
        const appAssetsResponse = await BuildManagerApi.getActiveAppAsset(appId);
        const assets = appAssetsResponse.data.reduce((acc, asset) => {
          const {assetClass, url} = asset;
          acc[assetClass] = url;
          return acc;
        }, {});
        setAppDetails(prev => ({
          ...prev,
          iconUri: assets.icon,
          splashUri: assets.splash,
        }));
        setInitalAppDetails(prev => ({
          ...prev,
          iconUri: assets.icon,
          splashUri: assets.splash,
        }));
        setBuildDetails(prev => ({
          ...prev,
          assets: {
            ...prev.assets,
            ...assets,
          },
        }));
      } catch (error) {
        console.error('Error fetching app assets:', error);
      }

      // Fetch App Secrets
      try {
        const appSecretsResponse = await BuildManagerApi.getActiveAppSecret(appId);
        const secrets = appSecretsResponse.data.reduce((acc, secret) => {
          const {secretClass, secret: secretValue} = secret;
          acc[secretClass] = secretValue;
          return acc;
        }, {});
        setBuildDetails(prev => ({
          ...prev,
          ios: {
            ...prev.ios,
            accountDetails: [
              appStoreOrgName,
              secrets['teamId'],
              secrets['appStoreIssuerId'],
              secrets['appStoreApiKey'],
            ],
            initialAccountDetails: [
              appStoreOrgName,
              secrets['teamId'],
              secrets['appStoreIssuerId'],
              secrets['appStoreApiKey'],
            ],
          },
        }));
      } catch (error) {
        console.error('Error fetching app secrets:', error);
      } finally {
        setFormLoading(false);
      }
    };

    fetchData();
  }, [appId]);

  return (
    <PublishContext.Provider
      value={{
        formLoading,
        step,
        setStep,
        alreadyExistingMetaData,
        setAlreadyExistingMetaData,
        totalSteps,
        formState,
        appDetails,
        initialAppDetails,
        firebaseProjectId,
        initialFirebaseProjectId,
        buildDetails,
        setAppDetails,
        setFirebaseProjectId,
        setInitialFirebaseProjectId,
        setBuildDetails,
        updateFormState,
        doesMetaDataExist,
        setMetaDataExist,
        setInitalAppDetails,
        isLegacyApp,
        buildsList,
        setBuildsList,
        isFirebaseInviteChecked,
        setFirebaseInviteChecked,
      }}>
      {children}
    </PublishContext.Provider>
  );
};

// Hook to use the context
export const usePublishContext = () => {
  const context = useContext(PublishContext);
  if (!context) {
    throw new Error('usePublishContext must be used within a PublishProvider');
  }
  return context;
};
