import React, {useEffect, useState} from 'react';
import Analytics from '@/root/web/lib/segment';
import {View, ScrollView, TouchableOpacity, Image, Linking, StyleSheet} from 'react-native';
import TextElement from '@/root/web/components-v2/base/TextElement';
import IntegrationsApi from '../../../api/IntegrationsApi';
import {EditorRootState} from '../../../store/EditorRootState';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {usePublishContext} from '../context';
import {Icon} from 'apptile-core';
import Footer from './footer';
import {useDispatch, useSelector} from 'react-redux';
import ConfirmationModal from './confirmationModal';
import {Link} from 'react-router-dom';
import {makeToast} from '@/root/web/actions/toastActions';
import {ActivityIndicator} from 'react-native';
import {truncate} from 'react-native-autolink';
const BuildListPage = () => {
  const {user} = useSelector((state: EditorRootState) => state.user);
  const renderEmptyState = () => (
    <>
      <View style={styles.emptyContainer}>
        <ApptileLoader />
        <TextElement style={styles.emptyTitle}>No builds are available</TextElement>
        <TextElement style={styles.emptySubtitle}>
          We are building your app. Please check back after sometime.
        </TextElement>
      </View>
      <Footer />
    </>
  );

  const [integrations, setIntegrations] = useState([]);
  const dispatch = useDispatch();
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const [isAppNotCreatedModalVisible, setIsAppNotCreatedModalVisible] = useState(false);
  const [isNotificationWarningModalVisible, setNotificationWarningModal] = useState(false);

  const [uploadLoader, setUploadLoader] = useState({
    buildId: null,
    isLoading: false,
  });

  const {buildsList, setBuildsList, buildDetails, alreadyExistingMetaData} = usePublishContext();

  const uploadBuild = async build => {
    Analytics.track('editor:buildpublishflow_uploadToTestflightClicked', { buildId: build.id });
    try {
      const buildId = build.id;
      setUploadLoader({
        buildId,
        isLoading: true,
      });
      try {
        await BuildManagerApi.checkIfAppExists(
          appId,
          buildDetails.ios.iosBundleId || buildDetails.ios.iosBundleIdentifier,
        );
      } catch (err) {
        Analytics.track('editor:buildpublishflow_appNotCreatedOnAppStoreConnect', { buildId });
        throw new Error('App doesnt exist on appstore connect');
      }

      const {data} = await BuildManagerApi.uploadBuild(appId, buildId, 'ios');
      Analytics.track('editor:buildpublishflow_uploadQueued', { buildId });
      dispatch(makeToast({content: data.message || 'upload queued successfully', appearances: 'success'}));
      setBuildsList(prevList =>
        prevList.map(prevBuild => (prevBuild.id === buildId ? {...prevBuild, uploadStatus: 'IN_PROGRESS'} : prevBuild)),
      );
    } catch (err: any) {
      Analytics.track('editor:buildpublishflow_uploadError', { buildId: build?.id, error: String(err) });
      if (err.message.includes('App doesnt exist')) {
        setIsAppNotCreatedModalVisible(true);
      } else {
        dispatch(makeToast({content: err.message, appearances: 'error'}));
      }
    } finally {
      setUploadLoader({
        buildId: null,
        isLoading: false,
      });
    }
  };

  useEffect(() => {
    if (
      user?.email?.includes('apptile') &&
      alreadyExistingMetaData?.ios?.accountType?.includes('Individual') &&
      buildsList.length > 0
    ) {
      setNotificationWarningModal(true);
    }
  }, [user, alreadyExistingMetaData, buildsList]);

  function openCreateAppHelpDoc() {
    Analytics.track('editor:buildpublishflow_openCreateAppHelpDoc', {});
    Linking.openURL('https://help.apptile.com/en/articles/********-creating-an-ios-app-on-app-store-connect');
  }

  function openNotificationHelpDoc() {
    Analytics.track('editor:buildpublishflow_openNotificationHelpDoc', {});
    Linking.openURL(
      'https://coda.io/d/Steps-to-download-P12-File_dmYwa8P3qiS/Steps-to-Generate-CER-file_suxwUfXE#_lu4CEg6k',
    );
  }

  if (buildsList?.length == 0) {
    return renderEmptyState();
  }
  return (
    <>
      <ScrollView style={{padding: 16, backgroundColor: '#fff', flex: 1, borderRadius: 16}}>
        <TextElement style={{fontWeight: 'bold', marginBottom: 16}} fontSize="xl" color="SECONDARY">
          App Builds
        </TextElement>
        <View
          style={{
            borderRadius: 10,
          }}>
          {buildsList.map(build => (
            <View
              key={build.id}
              style={{
                backgroundColor: '#fff',
                flexDirection: 'row',
                padding: 16,
                borderWidth: 1,
                borderRadius: 5,
                flex: 1,
                gap: 10,
                borderColor: '#E2E2E2',

                // marginBottom: 12,

                // shadowColor: '#000',
                // shadowOpacity: 0.1,
                // shadowRadius: 4,
                // elevation: 3,
              }}>
              {build.platformType === 'android' && (
                <Image
                  style={{width: 24, height: 24}}
                  source={require('@/root/web/assets/images/androidicon-publishflow.png')}
                />
              )}

              {build.platformType === 'ios' && (
                <Image
                  style={{width: 24, height: 24}}
                  source={require('@/root/web/assets/images/appstoreicon-publishflow.png')}
                />
              )}

              <View>
                <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 10}}>
                  <TextElement fontSize="md" fontWeight="500" color="SECONDARY">
                    {build.platformType === 'android' ? 'Version Code' : 'Build'} #{build.version}
                  </TextElement>
                  <View
                    style={{
                      marginLeft: 10,
                      backgroundColor: build.status !== 'completed' ? '#FFF3B8' : '#D1FAE5',
                      paddingHorizontal: 12,
                      paddingVertical: 2,
                      borderRadius: 13,
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 5,
                    }}>
                    <TextElement
                      style={{color: build.status === 'completed' ? '#076046' : '#A93E00', fontSize: 12}}
                      fontWeight="500">
                      {build.status === 'completed' ? 'Build generated' : 'Build in progress'}
                    </TextElement>

                    {build.status !== 'completed' && (
                      <Icon iconType="Feather" name={'info'} size={12} color={'#A93E00'} />
                    )}
                  </View>
                  {build.status === 'completed' && build.uploadStatus && build.uploadStatus !== 'NOT_UPLOADED' && (
                    <View
                      style={{
                        marginLeft: 10,
                        backgroundColor:
                          build.uploadStatus === 'IN_PROGRESS' ||
                          build.uploadStatus === 'PENDING' ||
                          build.uploadStatus === 'ERROR'
                            ? '#FFF3B8'
                            : '#D1FAE5',
                        paddingHorizontal: 12,
                        paddingVertical: 2,
                        borderRadius: 13,
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 5,
                      }}>
                      {build.uploadStatus !== 'COMPLETE' && (
                        <Icon iconType="Feather" name={'info'} size={12} color={'#A93E00'} />
                      )}
                      <TextElement
                        style={{
                          fontSize: 12,
                          fontWeight: 500,
                          color:
                            build.uploadStatus === 'IN_PROGRESS' ||
                            build.uploadStatus === 'PENDING' ||
                            build.uploadStatus === 'ERROR'
                              ? '#A93E00'
                              : '#076046',
                        }}>
                        {build.uploadStatus === 'IN_PROGRESS' ||
                        build.uploadStatus === 'PENDING' ||
                        build.uploadStatus === 'ERROR'
                          ? build.uploadStatus === 'ERROR'
                            ? 'Upload error'
                            : 'Upload in progress'
                          : 'Uploaded to appstore'}
                      </TextElement>
                    </View>
                  )}
                </View>
                <View style={{gap: 5}}>
                  <View style={{flexDirection: 'row'}}>
                    <TextElement style={{color: '#5E5D5D'}} fontSize="sm" fontWeight="500">
                      Version Name:{' '}
                    </TextElement>
                    <TextElement style={{color: '#5E5D5D'}} fontSize="sm" fontWeight="400">
                      {build.semVersion}
                    </TextElement>
                  </View>
                  {/* <View style={{flexDirection: 'row'}}>
                  <TextElement style={{color: '#5E5D5D'}} fontSize="sm" fontWeight="500">
                    Integrations:{' '}
                  </TextElement>
                  <TextElement style={{color: '#5E5D5D'}} fontSize="sm" fontWeight="400">
                    {integrations.join(' | ')}
                  </TextElement>
                </View> */}
                </View>
                {(build.triggeredBy || build.createdAt) && (
                  <View
                    style={{
                      marginTop: 8,
                      padding: 8,
                      backgroundColor: '#F1F1F1',
                      borderRadius: 4,
                      justifyContent: 'center',
                      flexDirection: 'row',
                    }}>
                    {build.triggeredBy && (
                      <TextElement style={{color: '#5E5D5D'}} fontSize="sm">
                        Triggered by:{' '}
                        {`${
                          (build.triggeredByEmail && build.triggeredByEmail?.includes('apptile')) ||
                          !build.triggeredByEmail
                            ? 'Apptile'
                            : build.triggeredBy
                        } | `}
                      </TextElement>
                    )}

                    {build.createdAt && (
                      <TextElement style={{color: '#5E5D5D'}} fontSize="sm">
                        Triggered at: {`${new Date(build.createdAt).toLocaleString()}`}
                      </TextElement>
                    )}
                  </View>
                )}
              </View>

              {build.artefactUrl && build.platformType == 'android' && (
                <View
                  style={{
                    position: 'absolute',
                    right: 20,
                    bottom: 10,
                  }}>
                  <TouchableOpacity
                    style={{
                      backgroundColor: '#1060E0',
                      paddingHorizontal: 10,
                      paddingVertical: 6,
                      borderRadius: 17,
                      alignItems: 'center',
                    }}>
                    <a href={build.artefactUrl} style={{textDecoration: 'none'}}>
                      <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
                        <Icon iconType="Feather" name={'download'} size={18} color={'#FFFFFF'} />
                        <TextElement style={{color: '#fff'}} fontSize="sm">
                          Download APK & AAB
                        </TextElement>
                      </View>
                    </a>
                  </TouchableOpacity>
                </View>
              )}
              {build.artefactUrl &&
                build.platformType == 'ios' &&
                build.status === 'completed' &&
                (build.uploadStatus === 'NOT_UPLOADED' || build.uploadStatus === 'ERROR') && (
                  <View
                    style={{
                      position: 'absolute',
                      right: 20,
                      bottom: 10,
                    }}>
                    <TouchableOpacity
                      style={{
                        backgroundColor: '#1060E0',
                        paddingHorizontal: 10,
                        paddingVertical: 6,
                        borderRadius: 17,
                        alignItems: 'center',
                      }}
                      onPress={() => {
                        uploadBuild(build);
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 5,
                          minWidth: 150,
                          justifyContent: 'center',
                        }}>
                        {uploadLoader.buildId === build.id && uploadLoader.isLoading == true ? (
                          <ActivityIndicator size="small" color="#FFFFFF" />
                        ) : (
                          <>
                            <Icon iconType="Feather" name={'upload'} size={18} color={'#FFFFFF'} />
                            <TextElement style={{color: '#fff'}} fontSize="sm">
                              Upload to testflight
                            </TextElement>
                          </>
                        )}
                      </View>
                    </TouchableOpacity>
                  </View>
                )}
            </View>
          ))}
        </View>
      </ScrollView>
      <Footer />
      <ConfirmationModal
        visible={isAppNotCreatedModalVisible}
        onCancel={() => setIsAppNotCreatedModalVisible(false)}
        showConfirmButton={false}
        message={
          <View style={{padding: 16}}>
            {/* Bold Heading */}
            <TextElement style={{fontWeight: 'bold', marginBottom: 12}} color="SECONDARY" fontSize="xl">
              App has not been created on App Store Connect.
            </TextElement>

            {/* Clickable Help Doc Link */}
            <TouchableOpacity onPress={openCreateAppHelpDoc}>
              <TextElement fontSize="md" color="PRIMARY" style={{textDecorationLine: 'underline', marginBottom: 12}}>
                Please create the app by referring to this help doc and try upload.
              </TextElement>
            </TouchableOpacity>

            {/* Bundle ID Display */}
            <TextElement fontSize="sm" color="SECONDARY">
              Your Bundle ID is:{' '}
              <TextElement style={{fontWeight: 'bold'}}>
                {buildDetails.ios.iosBundleId || buildDetails.ios.iosBundleIdentifier}
              </TextElement>
            </TextElement>
          </View>
        }
      />

      <ConfirmationModal
        visible={isNotificationWarningModalVisible}
        onCancel={() => setNotificationWarningModal(false)}
        showConfirmButton={false}
        message={
          <View style={{padding: 16}}>
            {/* Bold Heading */}
            <TextElement style={{fontWeight: 'bold', marginBottom: 12}} color="SECONDARY" fontSize="xl">
              There might be an issue with sending ios notifications.
            </TextElement>

            {/* Clickable Help Doc Link */}
            <TouchableOpacity onPress={openNotificationHelpDoc}>
              <TextElement fontSize="md" color="PRIMARY" style={{textDecorationLine: 'underline', marginBottom: 12}}>
                Since you have an Individual account on appstore connect, you need to upload certificate on appstore
                connect in order to send notifications.
              </TextElement>
            </TouchableOpacity>

            {/* Bundle ID Display */}
            <TextElement fontSize="sm" color="SECONDARY">
              Your Bundle ID is:{' '}
              <TextElement style={{fontWeight: 'bold'}}>
                {buildDetails.ios.iosBundleId || buildDetails.ios.iosBundleIdentifier}
              </TextElement>
            </TextElement>
          </View>
        }
      />
    </>
  );
};

const ApptileLoader = () => {
  return (
    <View style={[styles.apptileLoaderStyles]}>
      <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} />
    </View>
  );
};

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },

  apptileLoaderStyles: {
    backgroundColor: 'transparent',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  loaderImage: {
    width: 100,
    height: 100,
    marginBottom: 20,
  },
  emptySubtitle: {
    fontSize: 15,
    color: '#666',
    textAlign: 'center',
  },
});
export default BuildListPage;
