import React, {useEffect, useState} from 'react';
import Analytics from '@/root/web/lib/segment';
import {View, ScrollView, TouchableOpacity, Image, Linking, StyleSheet} from 'react-native';
import TextElement from '@/root/web/components-v2/base/TextElement';
import IntegrationsApi from '../../../api/IntegrationsApi';
import {EditorRootState} from '../../../store/EditorRootState';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {usePublishContext} from '../context';
import {Icon} from 'apptile-core';
import Footer from './footer';
import {useDispatch, useSelector} from 'react-redux';
import ConfirmationModal from './confirmationModal';
import {Link} from 'react-router-dom';
import {makeToast} from '@/root/web/actions/toastActions';
import {ActivityIndicator} from 'react-native';
import {truncate} from 'react-native-autolink';
interface Build {
  id: string | number;
  platformType: 'ios' | 'android';
  version: string | number;
  status: 'completed' | 'failed' | 'error' | 'in_progress' | 'pending' | string;
  uploadStatus?: 'COMPLETE' | 'IN_PROGRESS' | 'PENDING' | 'ERROR' | 'NOT_UPLOADED' | string;
  createdAt: string | number | Date;
  versionName?: string;
  releaseNotes?: string;
  integrations?: string[];
}

const BuildListPage = () => {
  // Define renderEmptyState outside or as a standalone component if it's used by the new structure
  // For now, assuming the new structure has its own empty state logic or this will be added if needed
  const {user} = useSelector((state: EditorRootState) => state.user);

  const dispatch = useDispatch();
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const [isAppNotCreatedModalVisible, setIsAppNotCreatedModalVisible] = useState(false);
  const [isNotificationWarningModalVisible, setNotificationWarningModal] = useState(false);

  const [uploadLoader, setUploadLoader] = useState({
    buildId: null as string | number | null,
    isLoading: false,
  });

  const {
    buildsList,
    setBuildsList,
    buildDetails,
    alreadyExistingMetaData,
  }: {
    buildsList: Build[];
    setBuildsList: React.Dispatch<React.SetStateAction<Build[]>>;
    buildDetails: any;
    alreadyExistingMetaData: any;
  } = usePublishContext();
  const [activeTab, setActiveTab] = useState<'ios' | 'android'>('ios');

  const uploadBuild = async (build: Build) => {
    Analytics.track('editor:buildpublishflow_uploadToTestflightClicked', {buildId: String(build.id)});
    try {
      if (build.id == null) {
        console.error('Build ID is null, cannot upload for build: ', build);
        Analytics.track('editor:buildpublishflow_uploadError', {buildId: 'null', error: 'Build ID was null'});
        return;
      }
      const buildId = build.id;
      setUploadLoader({
        buildId,
        isLoading: true,
      });
      try {
        await BuildManagerApi.checkIfAppExists(
          appId,
          buildDetails.ios.iosBundleId || buildDetails.ios.iosBundleIdentifier,
        );
      } catch (err) {
        Analytics.track('editor:buildpublishflow_appNotCreatedOnAppStoreConnect', {buildId: String(buildId)});
        throw new Error('App doesnt exist on appstore connect');
      }

      const {data} = await BuildManagerApi.uploadBuild(appId, String(buildId), 'ios');
      Analytics.track('editor:buildpublishflow_uploadQueued', {buildId: String(buildId)});
      dispatch(makeToast({content: data.message || 'upload queued successfully', appearances: 'success'}));
      setBuildsList((prevList: Build[]) =>
        prevList.map((prevBuild: Build) =>
          prevBuild.id === buildId ? {...prevBuild, uploadStatus: 'IN_PROGRESS'} : prevBuild,
        ),
      );
    } catch (err: any) {
      Analytics.track('editor:buildpublishflow_uploadError', {buildId: String(build.id), error: String(err)});
      if (err.message.includes('App doesnt exist')) {
        setIsAppNotCreatedModalVisible(true);
      } else {
        dispatch(makeToast({content: err.message, appearances: 'error'}));
      }
    } finally {
      setUploadLoader({
        buildId: null,
        isLoading: false,
      });
    }
  };

  useEffect(() => {
    if (
      user?.email?.includes('apptile') &&
      alreadyExistingMetaData?.ios?.accountType?.includes('Individual') &&
      (buildsList as Build[]).length > 0 &&
      activeTab === 'ios'
    ) {
      setNotificationWarningModal(true);
    }
  }, [user, alreadyExistingMetaData, buildsList, activeTab]);

  function openCreateAppHelpDoc() {
    Analytics.track('editor:buildpublishflow_openCreateAppHelpDoc', {});
    Linking.openURL('https://help.apptile.com/en/articles/********-creating-an-ios-app-on-app-store-connect');
  }

  function openNotificationHelpDoc() {
    Analytics.track('editor:buildpublishflow_openNotificationHelpDoc', {});
    Linking.openURL(
      'https://coda.io/d/Steps-to-download-P12-File_dmYwa8P3qiS/Steps-to-Generate-CER-file_suxwUfXE#_lu4CEg6k',
    );
  }

  const MOCK_INTEGRATIONS = ['Apptile Wishlist', 'JudgeMe', 'Lively'];

  const typedBuildsList = (buildsList || []) as Build[];
  const androidBuilds = typedBuildsList.filter((build: Build) => build.platformType === 'android');
  const iosBuilds = typedBuildsList.filter((build: Build) => build.platformType === 'ios');
  const displayedBuilds = activeTab === 'ios' ? iosBuilds : androidBuilds;

  if (typedBuildsList.length === 0) {
    return renderEmptyState();
  }

  const renderBuildItem = (build: Build, index: number, isLastItem: boolean) => {
    const buildDate = new Date(build.createdAt);
    const day = buildDate.getDate();
    const month = buildDate.toLocaleString('default', {month: 'short'}).toUpperCase();
    // Default status details, maps to 'In Progress' or 'Pending' visual style
    let statusDetails = {
      text: 'In Progress',
      badgeStyle: styles.inProgressStatusBadge,
      textStyle: styles.inProgressStatusText,
      iconName: 'loader' as 'loader' | 'clock' | 'x-circle' | 'check-circle' | 'upload-cloud',
      iconType: 'Feather' as 'Feather',
      iconColor: styles.inProgressStatusText.color,
    };

    // Determine status text and style based on build status
    if (build.status === 'pending') {
      statusDetails = {
        text: 'Queued', // Assuming 'Queued' uses 'In Progress' visual style for now, can be customized
        badgeStyle: styles.inProgressStatusBadge,
        textStyle: styles.inProgressStatusText,
        iconName: 'clock' as 'loader' | 'clock' | 'x-circle' | 'check-circle' | 'upload-cloud',
        iconType: 'Feather' as 'Feather',
        iconColor: styles.inProgressStatusText.color,
      };
    }

    if (build.status === 'in_progress') {
      statusDetails = {
        text: 'In Progress',
        badgeStyle: styles.inProgressStatusBadge,
        textStyle: styles.inProgressStatusText,
        iconName: 'loader' as 'loader' | 'clock' | 'x-circle' | 'check-circle' | 'upload-cloud',
        iconType: 'Feather' as 'Feather',
        iconColor: styles.inProgressStatusText.color,
      };
    }

    if (build.status === 'failed' || build.status === 'error') {
      statusDetails = {
        text: 'Failed',
        badgeStyle: styles.failedStatusBadge,
        textStyle: styles.failedStatusText,
        iconName: 'x-circle' as 'loader' | 'clock' | 'x-circle' | 'check-circle' | 'upload-cloud',
        iconType: 'Feather' as 'Feather',
        iconColor: styles.failedStatusText.color,
      };
    } else if (build.status === 'completed') {
      statusDetails = {
        text: 'Generated',
        badgeStyle: styles.generatedStatusBadge,
        textStyle: styles.generatedStatusText,
        iconName: 'check-circle' as 'loader' | 'clock' | 'x-circle' | 'check-circle' | 'upload-cloud',
        iconType: 'Feather' as 'Feather',
        iconColor: styles.generatedStatusText.color,
      };
      if (build.platformType === 'ios') {
        if (build.uploadStatus === 'COMPLETE') {
          statusDetails = {
            text: 'Uploaded',
            badgeStyle: styles.completedStatusBadge,
            textStyle: styles.completedStatusText,
            iconName: 'check-circle' as 'loader' | 'clock' | 'x-circle' | 'check-circle' | 'upload-cloud',
            iconType: 'Feather' as 'Feather',
            iconColor: styles.completedStatusText.color,
          };
        } else if (build.uploadStatus === 'IN_PROGRESS' || build.uploadStatus === 'PENDING') {
          statusDetails = {
            text: 'Uploading...',
            badgeStyle: styles.inProgressStatusBadge,
            textStyle: styles.inProgressStatusText,
            iconName: 'upload-cloud' as 'loader' | 'clock' | 'x-circle' | 'check-circle' | 'upload-cloud',
            iconType: 'Feather' as 'Feather',
            iconColor: styles.inProgressStatusText.color,
          };
        } else if (build.uploadStatus === 'ERROR') {
          statusDetails = {
            text: 'Upload Failed',
            badgeStyle: styles.failedStatusBadge,
            textStyle: styles.failedStatusText,
            iconName: 'x-circle' as 'loader' | 'clock' | 'x-circle' | 'check-circle' | 'upload-cloud',
            iconType: 'Feather' as 'Feather',
            iconColor: styles.failedStatusText.color,
          };
        }
      }
    }

    const releaseNote = build.releaseNotes || 'New build';
    const integrationsToDisplay =
      build.integrations && build.integrations.length > 0 ? build.integrations : MOCK_INTEGRATIONS;

    const formattedTriggeredDate = new Date(build.createdAt).toLocaleString('en-US', {
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    return (
      <View key={String(build.id)} style={styles.buildItemOuterContainer}>
        <View style={styles.dateColumn}>
          <View style={styles.dateBox}>
            <TextElement style={styles.dateDay}>{day}</TextElement>
            <TextElement style={styles.dateMonth}>{month}</TextElement>
          </View>
          {!isLastItem && <View style={styles.timelineConnector} />}
        </View>
        <View style={styles.buildItemCard}>
          {/* Row 1: Build Title and Status Badge */}
          <View style={styles.buildHeader}>
            <TextElement style={styles.buildTitle}>Build #{build.version}</TextElement>
            <View style={[styles.statusBadge, statusDetails.badgeStyle]}>
              <Icon
                name={statusDetails.iconName}
                iconType={statusDetails.iconType}
                size={14}
                color={statusDetails.iconColor}
                style={{marginRight: 6}}
              />
              <TextElement style={[styles.statusText, statusDetails.textStyle]}>{statusDetails.text}</TextElement>
            </View>
            {/* Upload button remains associated with the header/first row area */}
            {build.platformType === 'ios' &&
              build.status === 'completed' &&
              build.uploadStatus !== 'COMPLETE' &&
              build.uploadStatus !== 'IN_PROGRESS' &&
              build.uploadStatus !== 'PENDING' &&
              build.uploadStatus !== 'ERROR' && (
                <TouchableOpacity
                  style={styles.uploadButton}
                  onPress={() => uploadBuild(build)}
                  disabled={uploadLoader.isLoading && uploadLoader.buildId === build.id}>
                  {uploadLoader.isLoading && uploadLoader.buildId === build.id ? (
                    <ActivityIndicator size="small" color="#007AFF" />
                  ) : (
                    <>
                      <Icon name="upload-cloud" iconType="Feather" size={14} color="#007AFF" style={{marginRight: 6}} />
                      <TextElement style={styles.uploadButtonText}>Upload to Appstore</TextElement>
                    </>
                  )}
                </TouchableOpacity>
              )}

            {/* Download APK button for Android builds */}
            {build.platformType === 'android' &&
              (build.status === 'completed' || build.status === 'uploaded') &&
              build.artefactUrl && (
                <TouchableOpacity
                  style={styles.uploadButton}
                  onPress={() => {
                    // For web: open in new tab
                    if (typeof window !== 'undefined') {
                      window.open(build.artefactUrl, '_blank');
                    } else {
                      Linking.openURL(build.artefactUrl);
                    }
                  }}>
                  <Icon name="download" iconType="Feather" size={14} color="#007AFF" style={{marginRight: 6}} />
                  <TextElement style={styles.uploadButtonText}>Download APK</TextElement>
                </TouchableOpacity>
              )}
          </View>

          {/* Row 2: Version, Release Note, Integrations, Triggered */}
          <View style={styles.buildInfoRowTwo}>
            <View style={styles.infoColumn}>
              <TextElement style={styles.infoLabel}>Version name</TextElement>
              <TextElement style={styles.infoValue}>{build.versionName || build.version}</TextElement>
            </View>

            <View style={styles.infoColumn}>
              <TextElement style={styles.infoLabel}>Release note</TextElement>
              <TextElement style={styles.infoValue} numberOfLines={1} ellipsizeMode="tail">
                {truncate(releaseNote, 30)}
              </TextElement>
            </View>

            <View style={styles.infoColumnWide}>
              <TextElement style={styles.infoLabel}>Integrations</TextElement>
              <View style={styles.integrationsContainerHorizontal}>
                {integrationsToDisplay.map((integration: string, i: number) => (
                  <View key={i} style={styles.integrationTag}>
                    <TextElement style={styles.integrationText}>{integration}</TextElement>
                  </View>
                ))}
              </View>
            </View>

            <View style={styles.infoColumn}>
              <TextElement style={styles.infoLabel}>Triggered</TextElement>
              <TextElement style={styles.infoValue}>{formattedTriggeredDate}</TextElement>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.pageContainer}>
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tabItem, activeTab === 'android' && styles.activeTabItem]}
          onPress={() => setActiveTab('android')}>
          <TextElement style={[styles.tabText, activeTab === 'android' && styles.activeTabText]}>Android</TextElement>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabItem, activeTab === 'ios' && styles.activeTabItem]}
          onPress={() => setActiveTab('ios')}>
          <TextElement style={[styles.tabText, activeTab === 'ios' && styles.activeTabText]}>iOS</TextElement>
        </TouchableOpacity>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContentContainer}>
        {displayedBuilds.length > 0 ? (
          displayedBuilds.map((build: Build, index: number) =>
            renderBuildItem(build, index, index === displayedBuilds.length - 1),
          )
        ) : (
          <View style={styles.emptyTabContentContainer}>
            <TextElement style={styles.emptyTitle}>No {activeTab} builds yet</TextElement>
            <TextElement style={styles.emptySubtitle}>
              Once you trigger a new build for {activeTab}, it will appear here.
            </TextElement>
          </View>
        )}
      </ScrollView>

      {isAppNotCreatedModalVisible && (
        <ConfirmationModal
          visible={isAppNotCreatedModalVisible}
          message="Before you can upload your build, you need to create an app entry on App Store Connect. Please follow our guide to set this up."
          onCancel={() => {}}
          onConfirm={() => {}}
        />
      )}
      {isNotificationWarningModalVisible && (
        <ConfirmationModal
          visible={isNotificationWarningModalVisible} // Corrected prop name
          message="To ensure push notifications work correctly with your individual developer account, please update your push notification certificate by following our guide."
          onCancel={() => {}}
          onConfirm={() => {}}
        />
      )}
      <Footer />
    </View>
  );
};

const renderEmptyState = () => (
  <>
    <View style={styles.emptyContainer}>
      <ApptileLoader />
      <TextElement style={styles.emptyTitle}>No builds are available</TextElement>
      <TextElement style={styles.emptySubtitle}>
        We are building your app. Please check back after sometime.
      </TextElement>
    </View>
    <Footer />
  </>
);

const ApptileLoader = () => {
  return (
    <View style={[styles.apptileLoaderStyles]}>
      <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} />
    </View>
  );
};

const styles = StyleSheet.create({
  pageContainer: {
    flex: 1,
    backgroundColor: '#F8F9FA', // Light grey background for the whole page
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingTop: 10, // Reduced top padding
    backgroundColor: '#F8F9FA', // Match page background or use #FFFFFF if preferred for tabs area
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  tabItem: {
    paddingBottom: 12, // Increased padding for larger touch area and underline space
    paddingHorizontal: 4, // Minimal horizontal padding, text itself will create space
    marginRight: 24, // Space between tabs
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
  },
  activeTabItem: {
    borderBottomColor: '#007AFF', // Blue underline for active tab
  },
  tabText: {
    fontSize: 16,
    color: '#5F6368', // Google Grey 700 for inactive tab text
    fontWeight: '500',
  },
  activeTabText: {
    color: '#007AFF', // Blue text for active tab
  },
  scrollContentContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 16,
  },
  buildItemOuterContainer: {
    flexDirection: 'row',
    marginBottom: 16, // Space between build items
  },
  dateColumn: {
    alignItems: 'center',
    marginRight: 16,
    width: 46, // Fixed width for date column to match date box
  },
  dateBox: {
    backgroundColor: '#0000000D', // Light grey with opacity as per Figma
    paddingTop: 6,
    paddingRight: 14,
    paddingBottom: 6,
    paddingLeft: 14,
    borderRadius: 8,
    alignItems: 'center',
    width: 46,
    height: 44,
    gap: 2,
  },
  dateDay: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#00000099', // Dark grey with opacity as per Figma
  },
  dateMonth: {
    fontSize: 10,
    color: '#495057', // Medium grey for month
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  timelineConnector: {
    flex: 1,
    width: 1,
    backgroundColor: '#D0D0D0', // Lighter grey for dotted line
    marginTop: 4, // Space from date box
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#D0D0D0',
  },
  buildItemCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    padding: 16,
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  buildHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    flexWrap: 'wrap', // Allow wrapping for smaller screens
  },
  buildTitle: {
    fontSize: 17, // Slightly smaller than image, adjust as needed
    fontWeight: '500', // Medium weight for titled
    color: '#212529',
    marginRight: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 'auto', // Push to left, before upload button if it's on the right
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 20,
    // marginLeft: 'auto', // Removed to allow status badge to push it right if needed
  },
  uploadButtonText: {
    color: '#007AFF',
    fontSize: 12,
    fontWeight: '500',
  },
  // Styles for Row 2
  buildInfoRowTwo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12, // Gap from Row 1
    // gap: 12, // If using React Native 0.71+ for gap property
  },
  infoColumn: {
    flex: 1, // Distribute space, adjust as needed e.g. for Integrations
    paddingHorizontal: 6, // Small horizontal padding for columns
  },
  infoColumnWide: {
    // For wider columns like Integrations
    flex: 1.5, // Give more space to integrations
    paddingHorizontal: 6,
  },
  infoLabel: {
    fontSize: 12,
    color: '#6B7280', // Medium gray for labels
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    color: '#1F2937', // Darker gray for values
    fontWeight: '500',
  },
  integrationsContainerHorizontal: {
    flexDirection: 'row',
    flexWrap: 'wrap', // Allow tags to wrap
    alignItems: 'flex-start',
    gap: 4, // Gap between integration tags
  },
  integrationTag: {
    backgroundColor: '#F3F4F6', // Light gray background for tags
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    // marginRight: 4, // Use gap on parent if available
    // marginBottom: 4, // Use gap on parent if available
  },
  integrationText: {
    fontSize: 12,
    color: '#4B5563', // Slightly darker gray for tag text
  },
  emptyTabContentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    minHeight: 200, // Ensure it takes some space
  },
  emptyContainerOriginal: {
    // Renamed to avoid conflict if old styles weren't fully removed
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingTop: 16,
    backgroundColor: '#F7F7F7', // Light background for the tab area
  },
  tabItem: {
    paddingVertical: 10,
    paddingHorizontal: 20, // Increased padding for better touch target
    marginRight: 8, // Spacing between tabs
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabItem: {
    borderBottomColor: '#007AFF', // Blue underline for active tab
  },
  tabText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#007AFF', // Blue text for active tab
  },
  scrollViewContainer: {
    padding: 16,
    backgroundColor: '#F7F7F7', // Consistent light background
    flex: 1,
  },
  buildItemContainer: {
    backgroundColor: '#FFFFFF', // White cards for builds
    flexDirection: 'row',
    borderRadius: 16, // Increased for more rounded corners
    borderWidth: 1,
    borderColor: '#E2E2E2',
    padding: 12, // Reduced for a more compact card
    // marginBottom: 16, // Removed, handled by marginTop in renderBuildItem
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  dateContainer: {
    alignItems: 'center',
    marginRight: 16,
    paddingTop: 4, // Align with build title
    position: 'relative',
  },
  dateDay: {
    fontSize: 18, // Larger font for day
    fontWeight: 'bold',
    color: '#333',
  },
  dateMonth: {
    fontSize: 12, // Smaller font for month
    color: '#666',
    textTransform: 'uppercase',
  },
  timelineConnector: {
    position: 'absolute',
    top: 40, // Start below the date month
    bottom: -16, // Extend to the next item's top (approx)
    left: '50%',
    width: 1,
    backgroundColor: '#E2E2E2',
    marginLeft: -0.5, // Center the line
    borderStyle: 'dashed',
    borderWidth: 1, // react-native-dashed-line might be better if available
    borderColor: '#D0D0D0',
  },
  buildDetailsContainer: {
    flex: 1,
  },
  buildHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    flexWrap: 'wrap', // Allow items to wrap if space is tight
  },
  buildTitle: {
    fontSize: 18, // Larger build title
    fontWeight: 'bold',
    color: '#111',
    marginRight: 12,
  },
  statusBadge: {
    paddingHorizontal: 12, // Adjusted for pill shape
    paddingVertical: 5, // Adjusted for pill shape
    borderRadius: 20, // Increased for pill shape
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10, // Space before upload button
    marginBottom: 4, // For wrapping
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 20,
    marginLeft: 'auto', // Pushes to the right
    marginBottom: 4, // For wrapping
  },
  uploadButtonText: {
    color: '#007AFF',
    marginLeft: 6,
    fontSize: 12,
    fontWeight: '500',
  },
  // Status-specific styles based on design
  generatedStatusBadge: {
    backgroundColor: '#E6F4EA', // Light Green
  },
  generatedStatusText: {
    color: '#34A853', // Dark Green
  },
  completedStatusBadge: {
    // Assuming 'Uploaded'/'Completed' uses similar styling to 'Generated'
    backgroundColor: '#E6F4EA',
  },
  completedStatusText: {
    color: '#34A853',
  },
  inProgressStatusBadge: {
    backgroundColor: '#FFF3E0', // Light Orange/Yellow
  },
  inProgressStatusText: {
    color: '#FB8C00', // Dark Orange/Yellow
  },
  failedStatusBadge: {
    backgroundColor: '#FCE8E6', // Light Red
  },
  failedStatusText: {
    color: '#D93025', // Dark Red
  },
  buildInfoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 16, // Gap between grid items
  },
  buildInfoItem: {
    // flexBasis: '48%', // Adjust for desired layout, considering gap
    minWidth: '40%', // Ensure items don't get too squished
  },
  buildInfoItemFullWidth: {
    flexBasis: '100%',
  },
  buildInfoItemRightAligned: {
    marginLeft: 'auto', // Pushes this block to the right
    textAlign: 'right',
    alignItems: 'flex-end', // Align text to the right for this block
    minWidth: '40%',
  },
  buildInfoLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  buildInfoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  integrationsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 4,
  },
  integrationTag: {
    backgroundColor: '#EEF2F5', // Light grey background for tags
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  integrationText: {
    fontSize: 12,
    color: '#4B5563', // Darker grey text
  },
  emptyTabContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    marginTop: 50,
  },
  emptyContainer: {
    // Original empty state for no builds at all
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },

  apptileLoaderStyles: {
    backgroundColor: 'transparent',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  loaderImage: {
    width: 100,
    height: 100,
    marginBottom: 20,
  },
});
export default BuildListPage;
