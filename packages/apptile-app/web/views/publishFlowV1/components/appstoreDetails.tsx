import React, {useState, useEffect, useRef} from 'react';
import {useCallbackRef} from 'apptile-core';
import Footer from './footer';
import {View, StyleSheet, ScrollView} from 'react-native';
import CheckBoxWidget from './checkBoxWidget';
import {isEmpty} from 'lodash';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {usePublishContext} from '../context';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../../store/EditorRootState';
import {saveAppState} from '../../../actions/editorActions';
import Helpdocs from './helpDocsPanel';
import {makeToast} from '@/root/web/actions/toastActions';
import TextElement from '@/root/web/components-v2/base/TextElement';
import packageJson from '@/root/package.json';
import {selectCurrentPlanWithDetails} from '@/root/web/selectors/BillingSelector';
import Analytics from '@/root/web/lib/segment';
import IosDetails from './iosDetails';

type LastBuildVersion_ApiResponse = {
  semVersion: string;
  version: string;
};

const AppstoreDetails: React.FC = () => {
  const {buildDetails, appDetails, setBuildDetails, alreadyExistingMetaData, setAlreadyExistingMetaData, isLegacyApp} =
    usePublishContext();
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const dispatch = useDispatch();
  const onPublish = useCallbackRef(() => {
    dispatch(saveAppState(true, true, 'Updated template'));
  });

  const {user} = useSelector((state: EditorRootState) => state.user);
  const currentPlan = useSelector(selectCurrentPlanWithDetails);
  const isEnterpriseOrPlus = currentPlan?.plan?.name === 'Enterprise' || currentPlan?.plan?.name === 'Plus';

  let {
    isAppleSelected,
    isAppleAccordionOpen,
    accountType,
    hasExistingIOSApp,
    accountDetails,
    initialAccountDetails,
    isIOSBuildRequested,
    isDeveloperAccountConnected,
    ipadSupport,
    appTrackingPermission,
    appstoreBundleIds,
  } = buildDetails.ios;

  const {isAndroidBuildRequested} = buildDetails.android;
  const [requestConfirmationLoader, setRequestConfirmationLoader] = useState(false);
  const [tooltipVisible, setTooltipVisible] = useState<number | null>(null);

  const appleDeveloperAccountTypes = [
    {label: 'Yes. Individual Account', value: 'Customer Developer Account,Individual Type'},
    {label: 'Yes. Organization Account', value: 'Customer Developer Account,Organization Type'},
    {label: 'No', value: 'Apptile Developer Account'},
  ];

  // iOS-specific help docs
  const helpDocs = useRef([
    {
      title: 'How to check my Apple Developer Account type',
      url: 'https://help.apptile.com/en/articles/9490585-how-to-check-my-apple-developer-account-type',
    },
    {
      title: 'Invite Apptile to your Apple Developer Account',
      url: 'https://help.apptile.com/en/articles/7192278-invite-apptile-to-your-apple-developer-account',
    },
    {
      title: 'Find Entity Name, Team ID, Issuer ID, Key ID, P8 File',
      url: 'https://help.apptile.com/en/articles/9479809-how-to-find-entity-name-team-id-issuer-id-key-id-p8-file-on-app-store-connect',
    },
  ]);

  const setMainDocRef = useRef(() => {});

  const updateIOSDetails = (key: string, value: any) => {
    setBuildDetails((prev: any) => ({
      ...prev,
      ios: {
        ...prev.ios,
        [key]: value,
      },
    }));
  };

  const autoFetchIOSVersion = async (appId: string, platform: 'android' | 'ios') => {
    try {
      // Call the API and retrieve the response
      const response = await BuildManagerApi.getLatestBuildVersion<LastBuildVersion_ApiResponse>(appId, platform);
      const {semVersion: apiSemVersion, version, ...messages} = response.data ?? {};

      let semVersion = apiSemVersion;

      // If semVersion is not available, generate it from version
      if (!semVersion && version) {
        semVersion = `${version}.0.0`;
      }

      return {semVersion, version};
    } catch (error) {
      console.error('Error fetching latest build version:', error);
      return undefined;
    }
  };

  const handleBuild = async (platform: 'android' | 'ios', version: string, semver: string) => {
    Analytics.track('editor:buildpublishflow_BuildTriggered', {
      appId,
      version,
      semVersion: semver,
      frameworkVersion: packageJson.version,
      platforms: [platform],
      triggeredBy: `${user.firstname} ${user.lastname}`,
      triggeredByEmail: user.email,
    });
    const buildPayload = {
      version,
      semVersion: semver,
      frameworkVersion: packageJson.version,
      platforms: [platform],
      triggeredBy: `${user.firstname} ${user.lastname}`,
      triggeredByEmail: user.email,
    };
    if (platform === 'ios') {
      buildPayload.enableIpadSupport = ipadSupport;
      buildPayload.enableAppTrackingTransparency = appTrackingPermission;

      buildPayload.isIndividualAccount = buildDetails?.ios?.accountType?.includes('Individual') ? true : false;
    }

    //These entire things should be removed when integrations are self serve
    buildPayload.enableApptileAnalytics = isEnterpriseOrPlus;
    buildPayload.enableLivelyPIP = isEnterpriseOrPlus;

    const response = await BuildManagerApi.createBuild(appId, buildPayload);
    let responseData;

    // Parse response data, if it's a string
    if (typeof response.data === 'string') {
      try {
        responseData = JSON.parse(response.data);
      } catch (parseError) {
        console.error('Failed to parse response data:', parseError);
        responseData = response.data;
      }
    } else {
      responseData = response.data;
    }

    const {errors, warnings, info, success} = responseData;

    let hasPendingBuildMessage = false;

    // Handle success messages
    if (success && success.length > 0) {
      success.forEach((message: string) => {
        dispatch(makeToast(message, 'success'));
      });
    }

    // Handle info messages
    if (info && info.length > 0) {
      info.forEach((message: string) => {
        dispatch(makeToast(message, 'info'));
        if (message.includes('pending')) {
          hasPendingBuildMessage = true;
        }
      });
    }

    // Handle warning messages
    if (warnings && warnings.length > 0) {
      warnings.forEach((message: string) => {
        dispatch(makeToast(message, 'warning'));
      });
    }

    // Handle error messages
    if (errors && errors.length > 0) {
      errors.forEach((message: string) => {
        dispatch(makeToast(message, 'error'));
      });
      throw new Error('Build creation failed');
    }

    if (!hasPendingBuildMessage) {
      dispatch(makeToast('Build request submitted successfully!', 'success'));
    }
  };

  const handleRequestBuild = async () => {
    setRequestConfirmationLoader(true);
    try {
      Analytics.track('editor:buildpublishflow_requestBuildClicked', {platform: 'ios'});

      // Update iOS build request status
      updateIOSDetails('isIOSBuildRequested', true);

      // Prepare updated iOS details for metadata
      const updatedIOSDetails = {
        accountType,
        hasExistingIOSApp,
        accountDetails,
        isDeveloperAccountConnected,
        iosBundleId: buildDetails.ios.iosBundleId,
        ipadSupport,
        appTrackingPermission,
      };

      // Call `patchAppMetadata` with the latest details
      if (buildDetails.ios.accountType !== 'Apptile Developer Account' && buildDetails.ios.hasExistingIOSApp) {
        await BuildManagerApi.patchAppSettings(appId, {
          iosBundleIdentifier: updatedIOSDetails.iosBundleId,
        });
      }

      const newMetaData = {
        ...alreadyExistingMetaData,
        ios: {
          accountType,
          hasExistingIOSApp,
          accountDetails,
          isDeveloperAccountConnected,
          iosBundleId: buildDetails.ios.iosBundleId,
          ipadSupport,
          appTrackingPermission,
        },
      };
      BuildManagerApi.sendBuildAlert(appId, {appId, appName: appDetails.appName, ios: newMetaData.ios});

      if (hasExistingIOSApp) {
        const {version, semVersion} = await autoFetchIOSVersion(appId, 'ios');
        await handleBuild('ios', version, semVersion);
      } else {
        await handleBuild('ios', '1', '1.0.0');
      }

      await BuildManagerApi.patchAppMetadata(appId, {
        metadata: newMetaData,
      });

      setAlreadyExistingMetaData(newMetaData);
    } catch (error) {
      console.error('Error requesting iOS build:', error);
      updateIOSDetails('isIOSBuildRequested', false);
    } finally {
      setRequestConfirmationLoader(false);
    }
  };

  return (
    <>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          {/* Left Panel */}
          <View style={styles.leftPanel}>
            <ScrollView style={styles.card} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
              <TextElement style={styles.heading}>Apple Appstore Details</TextElement>
              
              <View style={styles.storeCheckBoxStyles}>
                <CheckBoxWidget
                  label="Apple Appstore"
                  checked={isIOSBuildRequested ? true : isAppleSelected}
                  isDisabled={isIOSBuildRequested}
                  onChange={value => updateIOSDetails('isAppleSelected', value)}
                />
              </View>

              {/* iOS Details */}
              <IosDetails
                onPublish={onPublish}
                isAndroidBuildRequested={isAndroidBuildRequested}
                helpDocs={helpDocs.current}
                setMainDocRef={setMainDocRef}
                tooltipVisible={tooltipVisible}
                setTooltipVisible={setTooltipVisible}
                appleDeveloperAccountTypes={appleDeveloperAccountTypes}
                handleRequestBuild={handleRequestBuild}
                autoFetchIOSVersion={autoFetchIOSVersion}
                handleBuild={handleBuild}
                alreadyExistingMetaData={alreadyExistingMetaData}
                setAlreadyExistingMetaData={setAlreadyExistingMetaData}
                appDetails={appDetails}
                isLegacyApp={isLegacyApp}
              />
            </ScrollView>
          </View>

          {/* Right Panel */}
          <Helpdocs
            helpDocs={helpDocs.current}
            setMainDocFn={fn => {
              setMainDocRef.current = fn;
            }}
          />
        </View>
      </View>
      <Footer canProceed={true} isLoading={requestConfirmationLoader} />
    </>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: '#ECE9E1',
    padding: 20,
    height: '100%',
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    height: '100%',
  },
  leftPanel: {
    width: '60%',
    marginRight: 20,
    height: '100%',
    flexGrow: 1,
  },
  card: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#ECECEC',
    height: '100%',
  },
  heading: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginVertical: 15,
  },
  storeCheckBoxStyles: {
    flexDirection: 'row',
    marginVertical: 10,
    gap: 50,
    justifyContent: 'flex-start',
  },
});

export default AppstoreDetails;
