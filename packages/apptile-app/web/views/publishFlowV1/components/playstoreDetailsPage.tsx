import React, {useState, useEffect, useRef} from 'react';
import {useCallbackRef} from 'apptile-core';
import Footer from './footer';
import {View, StyleSheet, ScrollView} from 'react-native';
import CheckBoxWidget from './checkBoxWidget';
import {isEmpty} from 'lodash';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {usePublishContext} from '../context';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../../store/EditorRootState';
import {saveAppState} from '../../../actions/editorActions';
import Helpdocs from './helpDocsPanel';
import {makeToast} from '@/root/web/actions/toastActions';
import TextElement from '@/root/web/components-v2/base/TextElement';
import packageJson from '@/root/package.json';
import {selectCurrentPlanWithDetails} from '@/root/web/selectors/BillingSelector';
import Analytics from '@/root/web/lib/segment';
import PlaystoreDetailsComponent from './playstoreDetails';

type LastBuildVersion_ApiResponse = {
  semVersion: string;
  version: string;
};

const PlaystoreDetailsPage: React.FC = () => {
  const {buildDetails, appDetails, setBuildDetails, alreadyExistingMetaData, setAlreadyExistingMetaData, isLegacyApp} =
    usePublishContext();
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const dispatch = useDispatch();
  const onPublish = useCallbackRef(() => {
    dispatch(saveAppState(true, true, 'Updated template'));
  });

  const {user} = useSelector((state: EditorRootState) => state.user);
  const currentPlan = useSelector(selectCurrentPlanWithDetails);
  const isEnterpriseOrPlus = currentPlan?.plan?.name === 'Enterprise' || currentPlan?.plan?.name === 'Plus';

  const {isIOSBuildRequested} = buildDetails.ios;
  const {
    isGoogleSelected,
    isGoogleAccordionOpen,
    hasExistingGoogleDeveloperAccount,
    hasExistingGoogleApp,
    isAndroidBuildRequested,
    androidBundleId,
    playStoreDevAccountName,
    androidPreviousVersion,
    androidPreviousSemver,
  } = buildDetails.android;

  const [requestConfirmationLoader, setRequestConfirmationLoader] = useState(false);
  const [tooltipVisible, setTooltipVisible] = useState<number | null>(null);

  // Android-specific help docs (reordered to match expected indices)
  const helpDocs = useRef([
    {
      title: 'How to invite Apptile to your Google Developer Account',
      url: 'https://help.apptile.com/en/articles/7193835-invite-apptile-to-your-google-play-developer-account',
    },
    {
      title: 'How to find your Google Developer Name',
      url: 'https://help.apptile.com/en/articles/9490635-how-to-find-your-google-developer-account-name',
    },
    {
      title: 'How to find your Google Developer Name',
      url: 'https://help.apptile.com/en/articles/9490635-how-to-find-your-google-developer-account-name',
    },
    {
      title: 'How to find the Bundle ID, Version Semver & Version No for Google Developer account',
      url: 'https://help.apptile.com/en/articles/9482240-how-to-find-bundle-id-version-semver-version-no-for-google-play-console',
    },
    {
      title: 'How to invite Apptile to your Google Developer Account',
      url: 'https://help.apptile.com/en/articles/7193835-invite-apptile-to-your-google-play-developer-account',
    },
    {
      title: 'How to find your Google Developer Name',
      url: 'https://help.apptile.com/en/articles/9490635-how-to-find-your-google-developer-account-name',
    },
  ]);

  const setMainDocRef = useRef(() => {});

  const updateAndroidDetails = (key: string, value: any) => {
    setBuildDetails((prev: any) => ({
      ...prev,
      android: {
        ...prev.android,
        [key]: value,
      },
    }));
  };

  const autoFetchAndroidVersion = async (appId: string, platform: 'android' | 'ios') => {
    try {
      // Call the API and retrieve the response
      const response = await BuildManagerApi.getLatestBuildVersion<LastBuildVersion_ApiResponse>(appId, platform);
      const {semVersion: apiSemVersion, version, ...messages} = response.data ?? {};

      let semVersion = apiSemVersion;

      // If semVersion is not available, generate it from version
      if (!semVersion && version) {
        semVersion = `${version}.0.0`;
      }

      return {semVersion, version};
    } catch (error) {
      console.error('Error fetching latest build version:', error);
      return undefined;
    }
  };

  const handleBuild = async (platform: 'android' | 'ios', version: string, semver: string) => {
    Analytics.track('editor:buildpublishflow_BuildTriggered', {
      appId,
      version,
      semVersion: semver,
      frameworkVersion: packageJson.version,
      platforms: [platform],
      triggeredBy: `${user.firstname} ${user.lastname}`,
      triggeredByEmail: user.email,
    });
    const buildPayload = {
      version,
      semVersion: semver,
      frameworkVersion: packageJson.version,
      platforms: [platform],
      triggeredBy: `${user.firstname} ${user.lastname}`,
      triggeredByEmail: user.email,
    };

    //These entire things should be removed when integrations are self serve
    buildPayload.enableApptileAnalytics = isEnterpriseOrPlus;
    buildPayload.enableLivelyPIP = isEnterpriseOrPlus;

    const response = await BuildManagerApi.createBuild(appId, buildPayload);
    let responseData;

    // Parse response data, if it's a string
    if (typeof response.data === 'string') {
      try {
        responseData = JSON.parse(response.data);
      } catch (parseError) {
        console.error('Failed to parse response data:', parseError);
        responseData = response.data;
      }
    } else {
      responseData = response.data;
    }

    const {errors, warnings, info, success} = responseData;

    let hasPendingBuildMessage = false;

    // Handle success messages
    if (success && success.length > 0) {
      success.forEach((message: string) => {
        dispatch(makeToast(message, 'success'));
      });
    }

    // Handle info messages
    if (info && info.length > 0) {
      info.forEach((message: string) => {
        dispatch(makeToast(message, 'info'));
        if (message.includes('pending')) {
          hasPendingBuildMessage = true;
        }
      });
    }

    // Handle warning messages
    if (warnings && warnings.length > 0) {
      warnings.forEach((message: string) => {
        dispatch(makeToast(message, 'warning'));
      });
    }

    // Handle error messages
    if (errors && errors.length > 0) {
      errors.forEach((message: string) => {
        dispatch(makeToast(message, 'error'));
      });
      throw new Error('Build creation failed');
    }

    if (!hasPendingBuildMessage) {
      dispatch(makeToast('Build request submitted successfully!', 'success'));
    }
  };

  const handleRequestAndroidBuild = async () => {
    setRequestConfirmationLoader(true);
    try {
      Analytics.track('editor:buildpublishflow_requestBuildClicked', {platform: 'android'});

      // Update Android build request status
      updateAndroidDetails('isAndroidBuildRequested', true);

      // Prepare updated Android details for metadata
      const updatedAndroidDetails = {
        hasExistingGoogleDeveloperAccount,
        hasExistingGoogleApp,
        androidBundleId,
        playStoreDevAccountName,
        androidPreviousVersion,
        androidPreviousSemver,
      };

      // Call `patchAppMetadata` with the latest details
      if (buildDetails.android.hasExistingGoogleApp === 'Yes') {
        await BuildManagerApi.patchAppSettings(appId, {
          androidBundleIdentifier: updatedAndroidDetails.androidBundleId,
        });
      }

      const newMetaData = {
        ...alreadyExistingMetaData,
        android: updatedAndroidDetails,
      };
      BuildManagerApi.sendBuildAlert(appId, {appId, appName: appDetails.appName, android: newMetaData.android});

      if (hasExistingGoogleApp === 'Yes') {
        await handleBuild('android', androidPreviousVersion, androidPreviousSemver);
      } else {
        await handleBuild('android', '1', '1.0.0');
      }

      await BuildManagerApi.patchAppMetadata(appId, {
        metadata: newMetaData,
      });

      setAlreadyExistingMetaData(newMetaData);
    } catch (error) {
      console.error('Error requesting Android build:', error);
      updateAndroidDetails('isAndroidBuildRequested', false);
    } finally {
      setRequestConfirmationLoader(false);
    }
  };

  return (
    <>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          {/* Left Panel */}
          <View style={styles.leftPanel}>
            <ScrollView style={styles.card} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
              {/* Section Header */}
              <View style={styles.sectionHeader}>
                <TextElement style={styles.sectionTitle}>4. Add Playstore Account Details</TextElement>
              </View>

              <View style={styles.storeCheckBoxStyles}>
                <CheckBoxWidget
                  label="Google Playstore"
                  checked={isAndroidBuildRequested ? true : isGoogleSelected}
                  isDisabled={isAndroidBuildRequested}
                  onChange={value => {
                    Analytics.track('editor:buildpublishflow_selectGoogleStore', {appId, selected: value});
                    updateAndroidDetails('isGoogleSelected', value);
                  }}
                />
              </View>

              {/* Android Details */}
              <PlaystoreDetailsComponent
                onPublish={onPublish}
                isIOSBuildRequested={isIOSBuildRequested}
                helpDocs={helpDocs.current}
                setMainDocRef={setMainDocRef}
                tooltipVisible={tooltipVisible}
                setTooltipVisible={setTooltipVisible}
              />
            </ScrollView>
          </View>

          {/* Right Panel */}
          <Helpdocs
            helpDocs={helpDocs.current}
            setMainDocFn={fn => {
              setMainDocRef.current = fn;
            }}
          />
        </View>
      </View>
      <Footer canProceed={true} isLoading={requestConfirmationLoader} />
    </>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: '#ECE9E1',
    padding: 20,
    height: '100%',
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    height: '100%',
  },
  leftPanel: {
    width: '60%',
    marginRight: 20,
    height: '100%',
    flexGrow: 1,
  },
  card: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#ECECEC',
    height: '100%',
  },
  heading: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginVertical: 15,
  },
  storeCheckBoxStyles: {
    flexDirection: 'row',
    marginVertical: 10,
    gap: 50,
    justifyContent: 'flex-start',
  },
  sectionHeader: {
    width: '100%',
    height: 51,
    gap: 8,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    paddingTop: 16,
    paddingRight: 18,
    paddingBottom: 16,
    paddingLeft: 18,
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
    marginLeft: -20,
    marginRight: -20,
    paddingLeft: 38,
    paddingRight: 38,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    lineHeight: 24,
  },
});

export default PlaystoreDetailsPage;
