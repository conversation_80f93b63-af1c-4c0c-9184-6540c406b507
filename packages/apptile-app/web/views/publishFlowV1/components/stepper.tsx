import React, {useEffect} from 'react';
import {View, Text, StyleSheet, ActivityIndicator, TouchableOpacity} from 'react-native';
import {usePublishContext} from '../context';
import {MaterialCommunityIcons} from 'apptile-core';
import TextElement from '../../../components-v2/base/TextElement';

const steps = ['App Details', 'Firebase Details', 'Appstore Details', 'Playstore Details', 'Your Builds'];

const Stepper: React.FC = () => {
  const {step, setStep, isLoading, buildsList} = usePublishContext();

  return (
    <View style={styles.stepper}>
      {steps.map((label, index) => (
        <React.Fragment key={index}>
          {/* Step Circle and Label */}
          <View style={styles.stepContainer}>
            <TouchableOpacity
              style={styles.stepButton}
              onPress={() => {
                if (buildsList.length > 0) {
                  setStep(index + 1);
                }
              }}>
              <View
                style={[
                  styles.circle,
                  index + 1 < step && styles.completedCircle, // Completed step
                  index + 1 === step && styles.activeCircle, // Active step
                ]}>
                {index + 1 < step ? (
                  <MaterialCommunityIcons name="check" size={12} color="#FFFFFF" style={styles.completedStepIcon} />
                ) : index + 1 === step && isLoading ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <TextElement
                    style={[
                      styles.stepNumber,
                      index + 1 === step && styles.activeStepNumber, // Active step
                    ]}>
                    {index + 1}
                  </TextElement>
                )}
              </View>

              {/* Step Label */}
              <TextElement
                style={[
                  styles.stepLabel,
                  index + 1 < step && styles.completedStepLabel, // Completed label
                  index + 1 === step && styles.activeStepLabel, // Active label
                ]}>
                {label}
              </TextElement>
            </TouchableOpacity>
          </View>

          {/* Connecting Line */}
          {index < steps.length - 1 && <View style={[styles.line, index + 1 < step && styles.completedLine]} />}
        </React.Fragment>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  stepper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    paddingHorizontal: 20,
    paddingVertical: 10,
    maxWidth: 1000,
    overflow: 'visible',
    width: '100%',
    alignSelf: 'center',
    height: 60,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  line: {
    flex: 1,
    height: 2,
    backgroundColor: '#D3D3D3', // Default line color
    marginHorizontal: 8,
    minWidth: 40,
  },
  completedLine: {
    backgroundColor: '#000000', // Black for completed steps
  },
  circle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E0E0E0', // Default grey
  },
  completedCircle: {
    backgroundColor: '#000000', // Black for completed steps
  },
  activeCircle: {
    backgroundColor: '#1060E0', // Blue for active step
  },
  stepNumber: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  completedStepNumber: {
    color: '#FFFFFF',
  },
  activeStepNumber: {
    color: '#FFFFFF',
  },
  stepLabel: {
    marginLeft: 8,
    fontSize: 13,
    color: '#888888', // Default grey for labels
    fontWeight: '500',
  },
  completedStepLabel: {
    color: '#000000', // Black for completed step labels
    fontWeight: '600',
  },
  activeStepLabel: {
    color: '#1060E0', // Blue for active step label
    fontWeight: '600',
  },
  completedStepIcon: {
    fontSize: 24,
    color: '#FFFFFF', // White checkmark color
  },
});

export default Stepper;
