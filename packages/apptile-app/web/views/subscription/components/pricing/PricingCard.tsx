import {IPlanListing} from '@/root/web/api/ApiTypes';
import {getSubscriptionCurrencySymbol, getSubscriptionStrikeoutPrice} from '@/root/web/common/utils';
import Box from '@/root/web/components-v2/base/Box';
import Icon from '@/root/web/components-v2/base/Icon';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {
  selectCurrentPlanWithDetails,
  selectCurrentSubscription,
  selectPaymentStatus,
} from '@/root/web/selectors/BillingSelector';
import React from 'react';
import {StyleProp, StyleSheet, Text, View, ViewStyle} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';
import PricingButton from '../BillingButton';
import {useIntercom} from 'react-use-intercom';
import {BillingIntervalEnum} from '../../PublishPricing';
import PaymentButton from '../subscription/PaymentButton';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import {selectCurrentBrand} from '@/root/web/selectors/OnboardingSelector';

const planNameMapping = {
  ENTERPRISE: 'Everything in Plus, and',
  PLUS: 'Everything in Pro, and',
  PRO: 'Everything in Core, and',
};

const ROIMapping = {
  // CORE: 'Upto 10x ROI',
  // PRO: 'Upto 15x ROI',
  // PLUS: 'Upto 25x ROI',
  // ENTERPRISE: 'Upto 35x ROI',
};

export const PricingCard: React.FC<{
  plan: IPlanListing;
  cardWrapperStyles?: StyleProp<ViewStyle>;
  billingInterval?: BillingIntervalEnum;
  enablePopularBorderRadius?: boolean;
}> = ({
  plan,
  cardWrapperStyles,
  billingInterval = BillingIntervalEnum.MONTHLY,
  enablePopularBorderRadius = false,
  enableActivePlanBorderRadius = true,
}) => {
  const navigate = useNavigate();
  const currentSubscription = useSelector(selectCurrentSubscription);
  const salesData = useSelector(selectCurrentBrand)?.sales;
  const convertedUSDAmount = Number(salesData?.convertedUSDAmount);
  let preferredPlan;
  if (convertedUSDAmount < 5000) {
    preferredPlan = 'CORE';
  } else if (convertedUSDAmount > 5001 && convertedUSDAmount < 50000) {
    preferredPlan = 'PRO';
  } else if (convertedUSDAmount > 50001 && convertedUSDAmount < 100000) {
    preferredPlan = 'PLUS';
  } else if (convertedUSDAmount > 100000) {
    preferredPlan = 'ENTERPRISE';
  } else {
    preferredPlan = plan?.mostPopular && plan?.name;
  }
  const isMostPopular = preferredPlan === plan?.name;
  const currentBillingInterval = currentSubscription?.billingInterval;
  const isCurrentPlan = plan?.isCurrentPlan && currentBillingInterval === billingInterval;
  const requiresPricingConsultation = plan?.requiresPricingConsultation || false;

  const planHierarchy = ['CORE', 'PRO', 'PLUS', 'ENTERPRISE'];

  const isPaidCustomer = useSelector(selectPaymentStatus);
  let currentPlan = useSelector(selectCurrentPlanWithDetails);

  const {show, hide, isOpen} = useIntercom();
  const toggleIntercom = () => {
    if (isOpen) {
      hide();
    } else {
      show();
    }
  };

  const onPlanSelect = () => {
    navigate(`plans/${plan.id}`);
  };

  const renderPaymentButton = () => {
    if (isPaidCustomer) {
      if (!isCurrentPlan) {
        const isUpgradePlan = planHierarchy.indexOf(plan?.name) > planHierarchy.indexOf(currentPlan.name);
        if (plan?.isCurrentPlan && billingInterval !== currentSubscription?.billingInterval) {
          return <PaymentButton billingInterval={billingInterval} title="Select plan" color={'CTA'} planId={plan.id} requiresPricingConsultation={requiresPricingConsultation} />;
        }
        if (currentPlan && currentPlan.name) {
          return (
            <PaymentButton
              billingInterval={billingInterval}
              title={isUpgradePlan ? 'Upgrade' : 'Downgrade'}
              color={isUpgradePlan ? 'CTA' : 'TILE'}
              planId={plan.id}
              requiresPricingConsultation={requiresPricingConsultation}
            />
          );
        } else {
          return (
            <PaymentButton billingInterval={billingInterval} title="Select plan" color={'TILE'} planId={plan.id} requiresPricingConsultation={requiresPricingConsultation} />
          );
        }
      }
    } else if (currentPlan && currentPlan.id) {
      if (isCurrentPlan) {
        return <PricingButton title="Complete Your Payment" key={plan.id} onPress={onPlanSelect} />;
      }
    } else {
      if (isCurrentPlan && currentSubscription) {
        return <></>;
      }
      return (
        <PaymentButton
          billingInterval={billingInterval}
          title="Select plan"
          color={isMostPopular ? 'CTA' : 'TILE'}
          planId={plan.id}
          requiresPricingConsultation={requiresPricingConsultation}
        />
      );
    }
  };

  let requiredPrice;
  let requiredPriceDiscount;
  let requiredPercentageOff;
  let billingText = '';
  if (billingInterval === BillingIntervalEnum.MONTHLY) {
    requiredPrice = plan.monthlyPrice;
    requiredPriceDiscount = plan.monthlyPriceDiscount;
    requiredPercentageOff = Math.round((Number(plan.monthlyPriceDiscount) / Number(plan.monthlyPrice)) * 100);
    billingText = 'monthly';
  } else if (billingInterval === BillingIntervalEnum.ANNUAL) {
    requiredPrice = Math.round(Number(plan.annualPrice) / 12);
    requiredPriceDiscount = Math.round(Number(plan.annualPriceDiscount) / 12);
    requiredPercentageOff = Math.round((Number(plan.annualPriceDiscount) / Number(plan.annualPrice)) * 100);
    billingText = 'yearly';
  }

  const {strikeoutPrice, displayPrice} = getSubscriptionStrikeoutPrice(
    Number(requiredPrice),
    Number(requiredPriceDiscount),
  );
  const currencySymbols = getSubscriptionCurrencySymbol(plan.currencyCode);

  return (
    <View
      style={[
        styles.card,
        cardWrapperStyles,
        !currentPlan && isMostPopular && [styles.popularCard, enablePopularBorderRadius && styles.popularBorderRadius],

        isCurrentPlan && [styles.popularCard, enableActivePlanBorderRadius && styles.popularBorderRadius],
      ]}>
      <View style={[styles.subCardWrapper]}>
        {!currentPlan?.id && isMostPopular && (
          <View style={styles.popularTag}>
            <TextElement fontSize="sm" style={{color: '#FFF'}}>
              Recommended for you
            </TextElement>
          </View>
        )}
        {isCurrentPlan && (
          <View style={styles.popularTag}>
            <TextElement fontSize="sm" style={{color: '#FFF'}}>
              Current Plan
            </TextElement>
          </View>
        )}
        <View>
          <Box space="sm" style={[styles.headerBox]}>
            <Box space="md">
              <Box space="md">
                <TextElement fontSize="lg" color="SECONDARY" fontWeight="600">
                  {plan.name}
                </TextElement>
              </Box>
              <Box space="md">
                <TextElement fontSize="xs" color="SECONDARY">
                  {plan.description}
                </TextElement>
              </Box>
            </Box>

            <Box space="md" style={styles.paddingTop16}>
              {requiresPricingConsultation ? (
                <>
                  <Box space="md" style={styles.priceBox}>
                    <TextElement fontSize="3xl" color="SECONDARY" fontWeight="500">
                      Custom
                    </TextElement>
                  </Box>
                  <TextElement fontSize="xs" color="EDITOR_LIGHT_BLACK">
                    Starts at {currencySymbols}{displayPrice}/mo
                  </TextElement>
                </>
              ) : (
                <>
                  {strikeoutPrice && (
                    <Box space="md" style={[styles.priceBox, styles.strikeoutPrice]}>
                      <TextElement fontSize="xs" color="EDITOR_LIGHT_BLACK">
                        {currencySymbols}
                        {strikeoutPrice}
                      </TextElement>
                      <TextElement fontSize="xs" color="EDITOR_LIGHT_BLACK">
                        /mo
                      </TextElement>
                    </Box>
                  )}
                  <Box space="md" style={styles.priceBox}>
                    <TextElement fontSize="3xl" color="SECONDARY" fontWeight="500">
                      {displayPrice > 0 ? `${currencySymbols}${displayPrice}` : `FREE`}
                    </TextElement>
                    <TextElement fontSize="xs" color="EDITOR_LIGHT_BLACK">
                      /mo
                    </TextElement>
                  </Box>
                  <TextElement fontSize="xs" color="EDITOR_LIGHT_BLACK">
                    Billed {billingText}
                  </TextElement>
                </>
              )}

              {plan.name in ROIMapping && (
                <View style={[styles.roiWrapper]}>
                  <Text style={[commonStyles.baseText, styles.roiText]}>{ROIMapping[plan.name]}</Text>
                </View>
              )}

              {plan.trialPeriodDays > 0 && (
                <Box space="md">
                  <TextElement fontSize="sm" color="EDITOR_LIGHT_BLACK">
                    {plan.trialPeriodDays} Days Free Trial
                  </TextElement>
                </Box>
              )}
              {/* {plan.featureDescription && (
              <Box space="md">
                <TextElement fontSize="xs" color="EDITOR_LIGHT_BLACK">
                  {plan.featureDescription}
                </TextElement>
              </Box>
            )} */}
            </Box>
          </Box>

          {plan.name in planNameMapping && (
            <View style={styles.paddingTop16}>
              <TextElement fontSize="sm" color="SECONDARY" fontWeight="600">
                {planNameMapping[plan.name]}
              </TextElement>
            </View>
          )}

          <View>
            <Box style={styles.featuresBox}>
              {plan.features &&
                plan.features.map((feature, index) => {
                  return (
                    <View style={styles.featureItem}>
                      <Icon name="check" size="xs" color="TERTIARY" />
                      <Box style={styles.featureText}>
                        <TextElement fontSize="xs" color="EDITOR_LIGHT_BLACK" key={index}>
                          {feature.name}
                        </TextElement>
                      </Box>
                    </View>
                  );
                })}
            </Box>
          </View>
        </View>
        <View style={{width: '100%', paddingRight: 16}}>{renderPaymentButton()}</View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  centeredBox: {alignItems: 'center', width: '100%'},
  headerBox: {},
  priceBox: {flexDirection: 'row', alignItems: 'center'},
  featuresBox: {justifyContent: 'flex-start', flex: 1, height: 200},
  featureItem: {flexDirection: 'row', alignItems: 'center'},
  featureText: {padding: 2, flex: 1},
  card: {
    width: 300,
    alignSelf: 'stretch',
    backgroundColor: '#FFF',
    paddingLeft: 16,
  },
  roiWrapper: {
    backgroundColor: '#F3F3F3',
    paddingHorizontal: 9,
    borderRadius: 42,
    marginTop: 16,
    paddingVertical: 5,
  },
  roiText: {
    color: '#414141',
    fontSize: 12,
    fontWeight: '500',
  },
  subCardWrapper: {justifyContent: 'space-between', flex: 1, flexDirection: 'column', paddingVertical: 24},
  paddingLeft: {paddingLeft: 12},
  popularCard: {
    borderColor: '#1060E0',
    borderWidth: 3,
    overflow: 'hidden',
  },
  popularBorderRadius: {
    borderRadius: 24,
  },
  activePlanBadge: {
    textAlign: 'center',
    padding: 4,
    backgroundColor: '#eddf5e',
    width: '100%',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    height: '20px',
  },
  activePlanBadgeText: {
    textAlign: 'center',
    color: '#ffffff',
  },
  cardBlock: {
    padding: 12,
  },
  strikeoutPrice: {
    textDecorationLine: 'line-through',
  },
  paddingTop16: {
    paddingTop: 16,
  },
  popularTag: {
    backgroundColor: '#1060E0',
    width: 200,
    position: 'absolute',
    top: 0,
    right: 0,
    borderBottomLeftRadius: 16,
    padding: 4,
    alignItems: 'center',
  },
});
