import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Text, Pressable} from 'react-native';

import theme from '@/root/web/styles-v2/theme';
import {IntegrationRouter} from '@/root/web/views/integrations';
import {useDispatch, useSelector} from 'react-redux';
import {Route, Routes, useLocation, useParams} from 'react-router';
import {changeAppContextData, fetchAppForks, setOrgId} from '../../actions/editorActions';
import LeftSidebar from '../../layout-v2/LeftSidebar';
import MainBlock from '../../layout-v2/MainBlock';
import RightSidebar from '../../layout-v2/RightSidebar';
import {BuildManagerRouter} from '../buildManager';
import {NotificationRouter} from '../notificationAdmin';
import {ThemeContainer, initApptileIsPreview} from 'apptile-core';

import {StoreRouter} from '@/root/web/views/myStore';
import {ThemeRouter} from '@/root/web/views/themes';
import {initApptileIsEditable} from 'apptile-core';
import {SettingsRouter} from '../settingsSection';
import {CustomDragLayer} from '../../components/CustomDragLayer';
import {AnalyticsRouter} from '../analytics';
import {EditorRootState} from '../../store/EditorRootState';
import {LiveRouter} from '../live';

import EditorContext from '@/root/web/context/editorContext';
import ShiftThemeScreen from '../themes/ShiftTheme';
import {TopBar} from '../../layout-v2/TopBar';
import {AiTileTopBar} from '../../layout-v2/AITile/AiTileTopBar';
import {PublishPricing} from '../subscription/PublishPricing';
import BetaBuildTag from '../../components/BetaBuildTag';
import {PublishFlowRouter} from '../publishFlowV1';
import Button from '../../components-v2/base/Button';
import {useNavigate} from '../../routing.web';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    height: '100vh',
    flexBasis: 'auto',
    backgroundColor: theme.PRIMARY_BACKGROUND,
  },
  fullscreenHeight: {
    height: 'calc(100vh - 45px)',
  },
  row: {
    flexDirection: 'row',
  },
});

function EditorContainerV2() {
  const {isFullscreen} = useSelector((state: EditorRootState) => state.shopify);
  const appId = useSelector(state => state.apptile?.appId);
  const orgId = useSelector(state => state.apptile?.orgId);
  const dispatch = useDispatch();
  const location = useLocation();

  const queryParams: URLSearchParams = new URLSearchParams(location.search);
  const isPreview = queryParams.get('preview') === 'true';

  React.useEffect(() => {
    dispatch(initApptileIsEditable(true));
    if (appId) {
      dispatch(changeAppContextData(appId));
      dispatch(fetchAppForks(appId));

      if(isPreview) {
        dispatch(initApptileIsPreview(isPreview));
        dispatch(initApptileIsEditable(!isPreview));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appId]);

  //Set Platform state org id
  React.useEffect(() => {
    orgId && dispatch(setOrgId(orgId));
  }, [orgId, dispatch]);

  return (
    <EditorContext.Provider value={{layout: 'editorV2'}}>
      <View style={[styles.root, isFullscreen && styles.fullscreenHeight]}>
        <EditorRouter />
        <DashboardRouter />
        <PricingRouter />
        <BetaBuildTag />
      </View>
    </EditorContext.Provider>
  );
}

const EditorRouter = () => {
  return (
    <Routes>
      <Route path=":panelName/*" element={<AppEditor />} />
      <Route path="*" element={<AppEditor />} />
    </Routes>
  );
};

const AppEditor = () => {
  const params = useParams();
  const {panelName} = params;
  return (
    <>
      {panelName == 'AI-Tile-Creation' ? <AiTileTopBar /> : <TopBar />}
      <ThemeContainer>
        <View style={{flexDirection: 'row', flex: 1, flexBasis: 'auto'}}>
          <LeftSidebar mainBar="APP_EDITOR" />
          <MainBlock />
          <RightSidebar />
          <CustomDragLayer />
        </View>
      </ThemeContainer>
    </>
  );
};
const DashboardRouter = () => {
  return (
    <Routes>
      <Route path="dashboard/*" element={<Dashboards />} />
      <Route path="overlay/*" element={<Overlays />} />
    </Routes>
  );
};

const PricingRouter = () => {
  return (
    <Routes>
      <Route path="pricing/*" element={<PublishPricing />} />
    </Routes>
  );
};

const Dashboards = () => {
  const {isFullscreen} = useSelector((state: EditorRootState) => state.shopify);
  const [showAnnoucment, setShowAnnoucement] = useState(null);
  const GetAnnoucement: React.FC = ({showAnnoucment}) => {
    const navigate = useNavigate();
    useEffect(() => {
      if (showAnnoucment != false && new Date().getTime() < new Date('2025-04-05T05:38:24.127Z').getTime())
        setShowAnnoucement(true);
      return () => {
        setShowAnnoucement(false);
      };
    }, []);
    if (!showAnnoucment) return null;
    return (
      <View
        style={{
          backgroundColor: '#222',
          padding: 9,
          marginBottom: 10,
          width: '100%',
          position: 'fixed',
          top: 0,
          zIndex: 5,
          justifyContent: 'center',
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <Text style={{color: '#fff'}}>Boost app downloads with the new App Install Banner - </Text>
        <Text style={{color: '#fff', marginRight: 8}}>seamlessly prompt users to install the app! </Text>
        <Button
          size="SMALL"
          color="SECONDARY"
          onPress={() => navigate('../integrations/e5905ad4-72c3-489d-b0b1-49241feee32c')}>
          Try Now
        </Button>
        <Pressable
          onPress={() => {
            setShowAnnoucement(false);
          }}
          style={{position: 'absolute', zIndex: 2, right: 20, fontSize: 11, color: '#fff'}}>
          X
        </Pressable>
      </View>
    );
  };
  return (
    <View
      style={[
        styles.root,
        {paddingTop: showAnnoucment ? 48 : 0},
        styles.row,
        isFullscreen && styles.fullscreenHeight,
        StyleSheet.absoluteFill,
      ]}>
      <Routes>
        <Route path="store" element={<GetAnnoucement key={showAnnoucment} showAnnoucment={showAnnoucment} />} />
      </Routes>
      <LeftSidebar mainBar="DASHBOARD" />
      <StoreRouter />
      <ThemeRouter />
      <AnalyticsRouter />
      <LiveRouter />
      <NotificationRouter />
      <BuildManagerRouter />
      <IntegrationRouter />
      <SettingsRouter />
      <PublishFlowRouter />
    </View>
  );
};

const Overlays = () => {
  const {isFullscreen} = useSelector((state: EditorRootState) => state.shopify);
  return (
    <View style={[styles.root, styles.row, isFullscreen && styles.fullscreenHeight, StyleSheet.absoluteFill]}>
      <Routes>
        <Route path="themes/shift-theme/:slug" element={<ShiftThemeScreen />} />
      </Routes>
    </View>
  );
};

export default EditorContainerV2;
