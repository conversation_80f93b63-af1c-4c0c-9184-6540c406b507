import ApptileApp from '@/root/app/ApptileApp';
import {DispatchActions, getAppConstants} from 'apptile-core';
import React, {useEffect, useState} from 'react';
import {Dimensions, View, StyleSheet, ActivityIndicator} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {verifyAppForks} from '../../actions/editorActions';
import {selectCurrentApptileForkId} from '../../selectors/AppSelectors';
import {EditorRootState} from '../../store/EditorRootState';

const PREVIEW_HEIGHT = 1028;

const AppClip: React.FC<any> = (props: any) => {
  const [viewHeight, setViewHeight] = useState(Dimensions.get('window').height);
  const dispatch = useDispatch();
  const appId = getAppConstants().APPTILE_APP_ID;
  const currentApptileForkId = useSelector(selectCurrentApptileForkId);
  const [loader, setLoader] = useState(!!props?.screenName);
  useEffect(() => {
    dispatch(verifyAppForks(appId));
  }, [appId, dispatch]);
  const activePageKey = useSelector((state: EditorRootState) => state?.activeNavigation?.activePageKey);
  console.log('apptileNavigation', activePageKey);

  useEffect(() => {
    if (activePageKey && props?.screenName && loader) {
      dispatch({
        type: DispatchActions.APPNAV_NAVIGATE,
        payload: {
          screenName: props.screenName,
          params: {...props.params},
        },
      });
      setTimeout(() => setLoader(false), 70);
    }
  }, [activePageKey]);
  return (
    <>
      {currentApptileForkId ? (
        <View style={[styles.previewContainer, {height: viewHeight, flexBasis: viewHeight, maxHeight: viewHeight}]}>
          <View style={[styles.appCanvas, {height: viewHeight - 1}]}>
            <ApptileApp />
          </View>
        </View>
      ) : (
        <></>
      )}
      {loader && (
        <View
          style={{
            flex: 1,
            position: 'fixed',
            height: '100vh',
            width: '100vw',
            zIndex: 999999999999,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: '#fff',
          }}>
          <ActivityIndicator size="large" />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  previewContainer: {
    height: '100%',
    maxHeight: '100%',
    flex: 1,
    position: 'relative',
  },
  deviceBezel: {
    backgroundColor: 'transparent',
    // shadowColor: 'black',
    // shadowOffset: {width: 10, height: 10},
    // shadowRadius: 50,
    // shadowOpacity: 0.4,
    overflow: 'visible',
  },
  appContainer: {
    width: 950,
    height: PREVIEW_HEIGHT,
    flexGrow: 0,
    flexShrink: 0,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    // alignSelf: 'center',
    overflow: 'visible',
    backgroundColor: 'rgba(0,0,0,0)',
  },
  appCanvas: {
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    position: 'absolute',
    overflow: 'hidden',
    flexGrow: 0,
    flexShrink: 0,
  },
});

export default AppClip;
