import React from 'react';
import {Route, Routes} from 'react-router';
import PaymentSummary from '../subscription/PaymentSummary';
import Pricing from '../subscription/Pricing';
import {ListingView} from './listing';
import {Settings} from './settings';
import {YourAccountSection} from './yourAccount';

export const SettingsRouter: React.FC = () => {
  return (
    <Routes>
      <Route path="/settings" element={<Settings />} />
      <Route path="/settings/your-account" element={<YourAccountSection />} />
      <Route path="/settings/billing" element={<Pricing />} />
      <Route path="/settings/billing/plans/:planId" element={<PaymentSummary />} />
      <Route path="/settings/app-listing" element={<ListingView />} />
    </Routes>
  );
};
