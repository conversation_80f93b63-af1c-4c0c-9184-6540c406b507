import React from 'react';
import {Button, Pressable, StyleSheet, Text, View} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import {Route, Routes, useNavigate, useParams} from 'react-router';

import theme from '@/root/web/styles-v2/theme';
import _ from 'lodash';

type LeftPaneProps = {slug: string; stepMapping: {number: string}; logoAssetId?: string};

const LeftPane = ({slug, stepMapping, logoAssetId}: LeftPaneProps) => {
  const location = Number(_.findKey(stepMapping, o => o.split(' ').join('-').toLowerCase() === slug)) ?? 1;
  //checking for brand logo

  const navigate = useNavigate();
  const isStepSelected = stepNumber => location === stepNumber;
  const isStepCompleted = stepNumber => {
    if (stepMapping[stepNumber] === 'Customize logo') {
      if (logoAssetId) return true;
      return false;
    }
    return location > stepNumber;
  };

  const renderStepNumber = stepNumber => {
    let activeComponent;

    if (isStepCompleted(stepNumber)) {
      activeComponent = (
        <View style={[styles.numberBackground, styles.iconBackgroundColor]}>
          <MaterialCommunityIcons name="check" size={17} color={'#FFFFFF'} />
        </View>
      );
    } else {
      activeComponent = (
        <View
          style={[
            styles.numberBackground,
            isStepSelected(stepNumber) ? styles.selectedNumberBackgroundColor : styles.numberBackgroundColor,
          ]}>
          <Text
            style={[styles.numberText, isStepSelected(stepNumber) ? styles.selectedNumberColor : styles.numberColor]}>
            {stepNumber}
          </Text>
        </View>
      );
    }
    return (
      <>
        {activeComponent}
        <Pressable onPress={() => navigate(`../${stepMapping[stepNumber].split(' ').join('-').toLowerCase()}`)}>
          <Text style={[isStepSelected(stepNumber) ? styles.stepText : styles.selectedStepText]}>
            {stepMapping[stepNumber]}
          </Text>
        </Pressable>
      </>
    );
  };

  return (
    <View style={styles.rootContainer}>
      <View style={styles.stepContainer}>{renderStepNumber(1)}</View>

      <View style={styles.dashLine} />

      <View style={styles.stepContainer}>{renderStepNumber(2)}</View>

      <View style={styles.dashLine} />

      <View style={styles.stepContainer}>{renderStepNumber(3)}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: {
    alignItems: 'flex-start',
    marginTop: 20,
    marginBottom: 20,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepText: {
    marginLeft: 5,
    color: '#535353',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: theme.FONT_FAMILY,
  },
  selectedStepText: {
    marginLeft: 5,
    color: '#535353',
    fontSize: 12,
    fontWeight: '400',
    opacity: 0.6,
    fontFamily: theme.FONT_FAMILY,
  },
  dashLine: {
    height: 44,
    borderLeftWidth: 2,
    borderColor: '#E4E4E4',
    marginLeft: 15,
    marginVertical: 10,
  },
  numberBackground: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
    height: 32,
    width: 32,
    marginRight: 7,
  },
  iconBackgroundColor: {
    backgroundColor: '#1D1D1C',
  },
  selectedNumberBackgroundColor: {
    backgroundColor: '#1060E0',
  },
  numberBackgroundColor: {
    borderWidth: 1,
    borderColor: '#D9D9D9',
  },
  numberText: {
    color: '#535353',
    fontSize: 17,
    fontWeight: '400',
    fontFamily: theme.FONT_FAMILY,
  },
  selectedNumberColor: {
    color: 'white',
  },
  numberColor: {
    color: '#1D1D1C',
  },
});

export default LeftPane;
