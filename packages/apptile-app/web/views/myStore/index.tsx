import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Text, Image, Pressable} from 'react-native';

import {Route, Routes} from 'react-router';
import Button from '@/root/web/components-v2/base/Button';
import theme from '@/root/web/styles-v2/theme';

import {useNavigate, useParams} from 'react-router';
import _ from 'lodash';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import {ScrollView} from 'react-native';
import {MaterialCommunityIcons, selectAppSettingsForKey} from 'apptile-core';
import {initApptileIsEditable, initApptileIsPreview} from 'apptile-core';
import Analytics from '@/root/web/lib/segment';
import {APP_PREVIEW_CLICKED, TILE_DROPPED} from '@/root/web/common/onboardingConstants';
import {callWebflowApis, changeOnboardingMetadata} from '../../actions/onboardingActions';
import {selectMandatoryCheck} from '../../selectors/EditorModuleSelectors';
import {getOnboardingMetadataWithKey} from '../../selectors/OnboardingSelector';
import {useCallbackRef} from 'apptile-core';
import {saveAppState} from '../../actions/editorActions';
import {PublishModal} from '../../layout-v2/SaveAndPublish';
import {GrowYourApp} from './GrowYourApp';
import {RecommendedApps} from './RecommendedApps';
import {
  selectCurrentPlanWithDetails,
  selectCurrentSubscription,
  selectPaymentStatus,
} from '../../selectors/BillingSelector';
import commonStyles from '../../styles-v2/commonStyles';
import ProgressBar from '../../components-v2/base/ProgressBar';
import {BrandSettingsTypes} from 'apptile-core';
import {SettingsConfig} from 'apptile-core';
import {NewCustomerAccountAlert} from './NewCustomerAccountAlert';
import {AlertBar} from './AlertBar';
const BRAND_LOGO_ASSET_ID = BrandSettingsTypes.BRAND_LOGO_ASSET_ID;
const BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY;

// const getShopPrimaryDomainReducer = (state: EditorRootState) => {
//   return state.appModel?.getModelValue(['shopify', 'shop', 'primaryDomain', 'host']);
// };
const styles = StyleSheet.create({
  storeContainer: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    flex: 1,
    alignItems: 'center',
  },
  storeSubContainer: {
    width: '95%',
    maxWidth: 1230,
    justifyContent: 'center',
    alignItems: 'center',
  },
  storeCard: {
    height: 350,
    width: '100%',
    borderWidth: 1,
    borderColor: '#262626',
    padding: 10,
    gap: 30,
    position: 'relative',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  alignCenter: {
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  imageContainer: {
    overflow: 'hidden',
    flex: 1,
    borderRadius: 27,
    justifyContent: 'center',
  },
  image: {
    height: 320,
    aspectRatio: '327/710',
  },
  indicator: {
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '500',
    fontSize: 14,
    color: theme.PRIMARY_COLOR,
  },
  button: {
    width: 135,
    marginTop: 20,
  },
  buttonOnboarding: {
    width: 175,
    paddingVertical: 15,
  },
  editButton: {
    backgroundColor: '#1060E0',
    border: 'none',
    paddingHorizontal: 24,
    paddingVertical: 6,
  },
  demoButton: {
    width: 125,
    backgroundColor: '#1060E0',
  },
  demoButtonText: {
    color: '#ffffff',
    fontFamily: theme.FONT_FAMILY,
  },
  // greetingText: {
  //   fontFamily: theme.FONT_FAMILY,
  //   fontSize: 40,
  //   color: theme.TEXT_COLOR,
  //   lineHeight: 60,
  // },
  wrapper: {
    marginHorizontal: '8vh',
    marginTop: 20,
    width: '100%',
    alignContent: 'center',
    backgroundColor: '#393939',
    borderRadius: 27,
    overflow: 'hidden',
  },
  header: {
    marginVertical: 30,
    width: '100%',
    marginBottom: 24,
  },
  headerTopText: {
    fontWeight: '600',
    fontFamily: theme.FONT_FAMILY,
    fontSize: 28,
  },
  shopNameText: {
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '600',
    fontSize: 30,
    marginTop: 10,
  },
  userNameText: {
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '600',
    color: '#1060E0',
    fontSize: 28,
    marginTop: 10,
    textTransform: 'capitalize',
  },
  headerBottomText: {
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '400',
    fontSize: 16,
    color: '#535353',
    marginTop: 6,
  },
  headerImage: {
    width: 200,
    height: 100,
    resizeMode: 'contain',
  },
  demoWrapper: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#000000',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginTop: 20,
  },
  demoText: {},
  demoMainText: {
    fontWeight: '500',
    fontSize: 16,
    fontFamily: theme.FONT_FAMILY,
  },
  demoSubText: {
    fontWeight: '400',
    fontSize: 12,
    fontFamily: theme.FONT_FAMILY,
    maxWidth: 340,
    marginTop: 5,
    color: '#858585',
  },
  stepsWrapper: {
    width: '100%',
    marginTop: 20,
    marginBottom: 20,
    backgroundColor: '#FFF',
    paddingHorizontal: 30,
    paddingVertical: 31,
    borderRadius: 10,
    overflow: 'hidden',
  },
  stepsTopText: {
    marginLeft: 20,
    fontWeight: '500',
    fontSize: 16,
    marginBottom: 0,
    fontFamily: theme.FONT_FAMILY,
  },
  stepsCardWrapper: {
    flexDirection: 'row',
    marginTop: 10,
    flexWrap: 'wrap',
    gap: 21,
  },
  stepsCard: {
    borderWidth: 1,
    position: 'relative',
    borderRadius: 15,
    overflow: 'hidden',
    borderColor: '#CDCDCD',
    height: 200,
  },
  stepCounterText: {
    color: '#858585',
    fontFamily: theme.FONT_FAMILY,
    fontSize: 14,
  },
  stepDone: {
    backgroundColor: 'rgba(155, 155, 155, 0.10)',
  },
  stepPending: {
    backgroundColor: '#FFFFFF',
  },
  stepName: {
    fontWeight: '500',
    fontSize: 16,
    marginTop: 5,
  },
  stepIcons: {
    // position: 'absolute',
    // bottom: 5,
    // right: 5,
  },
  stepsCardHeader: {
    paddingVertical: 17,
    paddingLeft: 18,
  },
  contentText: {
    color: '#FFFFFF',
    fontFamily: theme.FONT_FAMILY,
    fontSize: 15,
    fontWeight: '400',
    marginBottom: 20,
    justifyContent: 'center',
  },
  verticalLine: {
    color: '#ADADAD',
    marginHorizontal: 5,
  },
  headerKey: {
    fontSize: 16,
    color: '#535353',
  },
  headerValue: {
    color: '#000000',
    fontSize: 16,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  headerFlexDirection: {flexDirection: 'row', alignItems: 'center'},
  backGroundCircle: {width: 250, height: 250, borderRadius: '50%', backgroundColor: 'grey'},
  announcementBanner: {
    width: '100%',
    marginBottom: 17,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 10,
    paddingVertical: 14,
    paddingHorizontal: 14,
  },
});

let steps = [
  {
    id: 1,
    name: 'Theme selected',
    done: true,
    disabled: false,
    image: '',
    enableEdit: false,
  },
  {
    id: 2,
    name: 'Setup your brand',
    done: false,
    customRedirect: (orgId: string, appId: string) => `/editor-onboarding/${orgId}/app/${appId}`,
    //Redirect url after step is done
    postCompletionRedirect: '../../brand-settings',
    enableEdit: true,
    dispatchAction: () => {
      Analytics.track('editor:dashboard_setupYourBrandClicked');
    },
    disabled: false,
    image: '',
  },
  {
    id: 3,
    name: 'Design your app',
    done: false,
    redirect: '../../tiles',
    postCompletionRedirect: '../../tiles',
    disabled: false,
    enableEdit: true,
    image: require('@/root/web/assets/images/design-your-app.png'),
  },
  {
    id: 4,
    name: 'Preview your app',
    done: false,
    redirect: '../../',
    postCompletionRedirect: '../../',
    enableEdit: true,
    dispatchAction: (dispatch, previewClicked, setShowPublishFlow, isPaidCustomer) => {
      dispatch(initApptileIsPreview(true));
      dispatch(initApptileIsEditable(false));
      Analytics.track('editor:dashboard_previewClicked');

      if (typeof previewClicked !== undefined && !previewClicked) {
        dispatch(changeOnboardingMetadata({[APP_PREVIEW_CLICKED]: true}));
      }
    },
    disabled: false,
    image: require('@/root/web/assets/images/preview-your-app.png'),
  },
  {
    id: 5,
    name: 'Publish',
    done: false,
    enableEdit: false,
    dispatchAction: (dispatch, previewClicked, setShowPublishFlow, isPaidCustomer, navigate) => {
      if (isPaidCustomer) {
        setShowPublishFlow(true);
      } else {
        navigate('../../pricing');
      }
      Analytics.track('editor:dashboard_publishClicked');
    },
    disabled: false,
    image: require('@/root/web/assets/images/publish-your-app.png'),
  },
];

const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);

const getBrandSelector = (state: EditorRootState) => {
  return state.appModel?.getModelValue(['shopify', 'shop']);
};

const MyStore: React.FC = () => {
  const {orgId} = useParams();
  let appId = useSelector((state: EditorRootState) => state.apptile.appId as string);
  const userState = useSelector((state: EditorRootState) => state.user);
  const orgsState = useSelector((state: EditorRootState) => state.orgs);
  const isPaidCustomer = useSelector(selectPaymentStatus);
  const onPublish = useCallbackRef(() => {
    dispatch(saveAppState(true, true, 'Updated template'));
  });
  const [showPublishFlow, setShowPublishFlow] = useState(false);
  const navigate = useNavigate();
  const param = useParams();
  const dispatch = useDispatch();
  const [collection, setCollection] = useState({});
  //Calling all the webflow apis
  useEffect(() => {
    dispatch(callWebflowApis());
  }, [dispatch]);

  //Fetching brand
  // const primaryDomain = useSelector(getShopPrimaryDomainReducer);
  // useEffect(() => {
  //   if (!_.isEmpty(primaryDomain)) dispatch(fetchBrand(primaryDomain));
  // }, [dispatch, primaryDomain]);

  const isEditorOnboarded = _.get(orgsState, ['appsById', appId, 'isEditorOnboarded'], '');
  const activeBlueprintUUID = _.get(orgsState, ['appsById', appId, 'activeBlueprintUUID'], '');
  let isPublished = useSelector((state: EditorRootState) => state.apptile.isPublished);
  const previewClicked = useSelector((state: EditorRootState) =>
    getOnboardingMetadataWithKey(state, APP_PREVIEW_CLICKED),
  );
  const mandatoryFields = useSelector(selectMandatoryCheck());
  const [stepState, setStepState] = useState(steps);
  const {current: currentAppConfig} = useSelector((state: EditorRootState) => state.appConfig);
  const currentShopBrand = useSelector(getBrandSelector)?.brand;

  //Getting brand logo
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const logoAssetId = brandSettings.getSettingValue(BRAND_LOGO_ASSET_ID) as string | undefined;
  const imageRecord = currentAppConfig?.getImageId(logoAssetId);
  const assetSourceValue = imageRecord?.fileUrl ?? null;

  //Getting current plan
  const currentActiveSubscription = useSelector(selectCurrentSubscription);
  const currentPlan = useSelector(selectCurrentPlanWithDetails)?.name;

  const {appSaveUpdatedAt} = useSelector((state: EditorRootState) => state.apptile);
  const {themes, loading} = useSelector((state: EditorRootState) => state.onboarding.webflowData);

  //Checking mandetory fields
  const isAppConfigFetched = useSelector((state: EditorRootState) => state.appConfig.isFetched);
  const tileDropped = useSelector((state: EditorRootState) => getOnboardingMetadataWithKey(state, TILE_DROPPED));

  if (isAppConfigFetched) {
    if (mandatoryFields && steps[2].done !== !!tileDropped) {
      //For firing analytics. For now when the "mandatory.check" is true it will not enter into this condition when the page reloads.
      if (mandatoryFields.check) {
        Analytics.track('editor:dashboard_mandatoryFieldsIncomplete');
      } else {
        Analytics.track('editor:dashboard_mandatoryFieldsComplete');
      }
      steps[2] = {
        ...steps[2],
        done: !!tileDropped,
      };
      setStepState([...steps]);
    }
  }

  //Updating the image url in the first step
  useEffect(() => {
    let image;
    if (!loading && !collection?.thumbnail?.url) {
      image = require('@/root/web/assets/images/theme-selected-placeholder.png');
    } else {
      image = collection?.thumbnail?.url;
    }
    if (loading) {
      image = require('@/root/web/assets/images/preloader.svg');
    }
    steps[0] = {
      ...steps[0],
      image: image,
    };
    setStepState([...steps]);
  }, [collection, loading]);

  //Checking editor onboarding status
  useEffect(() => {
    steps[1] = {
      ...steps[1],
      done: !!isEditorOnboarded,
      image: assetSourceValue ?? require('@/root/web/assets/images/setup-your-brand.png'),
    };
    setStepState([...steps]);
  }, [assetSourceValue, isEditorOnboarded]);

  useEffect(() => {
    steps[1] = {
      ...steps[1],
      disabled: !currentAppConfig || !currentShopBrand,
    };
    setStepState([...steps]);
  }, [currentAppConfig, currentShopBrand]);

  //Checking and setting if the preview button is already clicked
  useEffect(() => {
    steps[3] = {
      ...steps[3],
      done: !!previewClicked,
    };
    setStepState([...steps]);
  }, [previewClicked]);

  //Checking and setting is the app is live
  useEffect(() => {
    steps[4] = {
      ...steps[4],
      done: !!isPublished,
    };
    setStepState([...steps]);
  }, [isPublished]);

  const appName = _.get(orgsState, ['appsById', param.id, 'name']);

  useEffect(() => {
    setCollection(_.find(themes?.items, t => t?.['blueprint-id'] === activeBlueprintUUID));
  }, [activeBlueprintUUID, themes]);

  const totalStepsCompleted = steps.filter(step => step.done).length;
  const totalStepsCompletedPercentage = (totalStepsCompleted / steps.length) * 100;
  const [hover, setHover] = useState();
  const isNewCustomerAccountEnabled =
    userState?.user?.shop?.customerAccountsVersion == 'NEW_CUSTOMER_ACCOUNTS' ? true : false;

  return (
    <ScrollView contentContainerStyle={styles.storeContainer}>
      <View style={styles.storeSubContainer}>
        {/* <AlertBar
          alertIcon="alert-circle-outline"
          alertColor="ERROR"
          alertIconSize="4xl"
          alertMessage="Alert: Due to Shopify's updated security measures, please ensure all users update their app to the latest secure version by 14th January. After this date, older versions will no longer function for security reasons."
        /> */}
        {!!isNewCustomerAccountEnabled && (
          <AlertBar
            alertIcon="alert-circle-outline"
            alertMessage='Attention: You’ve enabled Shopify’s "New Customer Account" feature on your website. Please be aware it will affect the login and signup process on your app. We are actively working on full integration support and appreciate your patience!.'
          />
        )}
        <View style={styles.header}>
          <Text style={styles.headerTopText}>
            Welcome to your Dashboard, <Text style={styles.userNameText}>{userState?.user?.firstname}!</Text>
          </Text>
          <Text style={styles.headerBottomText}>Let’s build your branded app!</Text>
        </View>
        {/* <View style={styles.announcementBanner}>
          <View
            style={{
              flexDirection: 'row',
              gap: 10,
              alignItems: 'center',
            }}>
            <MaterialCommunityIcons color={'#FFF'} name="bullhorn-variant-outline" size={20} />
            <Text
              style={[
                commonStyles.baseText,
                {
                  color: '#FFF',
                  fontSize: 16,
                  fontWeight: '500',
                },
              ]}>
              Register for Our Latest Feature Enhancement Webinar
            </Text>
          </View>
          <Button
            textStyles={[commonStyles.baseText, {color: '#000', fontWeight: '500', fontSize: 14}]}
            innerContainerStyles={{
              minHeight: 0,
            }}
            containerStyles={{backgroundColor: '#FFF', alignItems: 'center', justifyContent: 'center'}}
            onPress={() => {
              window.open(
                'https://app.livestorm.co/apptile/holiday-sales-masterclass-apptiles-latest-live-selling-release?type=detailed',
              );
            }}>
            Register now
          </Button>
        </View> */}
        <View
          style={{
            backgroundColor: '#FFF',
            width: '100%',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingVertical: 11,
            borderRadius: 10,
            paddingHorizontal: 20,
          }}>
          <View style={[styles.headerFlexDirection, {gap: 11}]}>
            <View style={styles.headerFlexDirection}>
              <Text style={[commonStyles.baseText, styles.headerKey]}>App name</Text>
              <MaterialCommunityIcons name="circle-small" size={20} />
              <Text style={[commonStyles.baseText, styles.headerValue]}>{appName}</Text>
            </View>
            <Text style={[commonStyles.baseText, styles.headerKey]}>|</Text>
            <View style={styles.headerFlexDirection}>
              <Text style={[commonStyles.baseText, styles.headerKey]}>Plan</Text>
              <MaterialCommunityIcons name="circle-small" size={20} />
              <Text style={[commonStyles.baseText, styles.headerValue]}>
                {currentActiveSubscription ? currentPlan : 'Free trial'}
              </Text>
            </View>
            <Text style={[commonStyles.baseText, styles.headerKey]}>|</Text>
            <View style={styles.headerFlexDirection}>
              <Text style={[commonStyles.baseText, styles.headerKey]}>Theme</Text>
              <MaterialCommunityIcons name="circle-small" size={20} />
              <Text style={[commonStyles.baseText, styles.headerValue]}>{collection?.name ?? 'No theme selected'}</Text>
            </View>
          </View>
          <View style={{flexDirection: 'row', gap: 10}}>
            {!isEditorOnboarded && (
              <Button
                id="editAppButton"
                variant="FILLED-PILL"
                color="DEFAULT"
                disabled={!currentAppConfig || !currentShopBrand}
                containerStyles={[styles.editButton]}
                onPress={() => {
                  Analytics.track('editor:dashboard_setupYourBrandClicked');
                  navigate(`/editor-onboarding/${orgId}/app/${appId}`);
                }}>
                Setup your brand
              </Button>
            )}
            {isEditorOnboarded && (
              <Button
                id="editAppButton"
                variant="FILLED-PILL"
                color="DEFAULT"
                containerStyles={[styles.editButton]}
                onPress={() => {
                  navigate(`../../app-editor`);
                }}>
                Edit app
              </Button>
            )}
          </View>
        </View>
        <View style={styles.stepsWrapper}>
          <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 32}}>
            <Text style={[commonStyles.baseText, {fontSize: 18, fontWeight: '600', color: '#000'}]}>
              Publish your app in 5 easy steps!
            </Text>
            <View style={{flexDirection: 'row', gap: 20, alignItems: 'center'}}>
              <Text style={[commonStyles.baseText, {fontSize: 14, fontWeight: '500', color: '#000'}]}>
                {totalStepsCompleted}/{steps.length} completed
              </Text>
              <ProgressBar progress={totalStepsCompletedPercentage} color={'#005BE4'} height={6} width={220} />
            </View>
          </View>
          <View style={styles.stepsCardWrapper}>
            {stepState.map((step, index) => (
              <Pressable
                style={[
                  step.done ? (step.enableEdit ? {cursor: 'pointer'} : {cursor: 'null'}) : {cursor: 'pointer'},
                  {width: '18%', minWidth: 185},
                ]}
                onMouseEnter={() => setHover(step.id)}
                onMouseLeave={() => setHover(null)}
                disabled={!!step.disabled}
                onPress={
                  !step.done
                    ? () => {
                        navigate(step.customRedirect ? step.customRedirect(orgId, appId) : step.redirect);
                        step.dispatchAction &&
                          step.dispatchAction(dispatch, previewClicked, setShowPublishFlow, isPaidCustomer, navigate);
                      }
                    : step.enableEdit
                    ? () => {
                        navigate(step.postCompletionRedirect);
                        step.dispatchAction &&
                          step.dispatchAction(dispatch, previewClicked, setShowPublishFlow, isPaidCustomer, navigate);
                      }
                    : null
                }>
                <View
                  key={index}
                  style={[
                    styles.stepsCard,
                    step.done ? styles.stepDone : styles.stepPending,
                    (step.enableEdit || !step.done) &&
                      hover === step.id && {
                        outlineWidth: 1,
                        outlineStyle: 'solid',
                        outlineColor: '#005BE4',
                      },
                  ]}>
                  <View style={styles.stepsCardHeader}>
                    <View style={{flexDirection: 'row', gap: 10}}>
                      <Text style={styles.stepCounterText}>STEP {step.id}</Text>
                      <View style={styles.stepIcons}>
                        {step.done && <MaterialCommunityIcons name={'check-circle'} size={17} color={'#1060E0'} />}
                      </View>
                    </View>
                    <Text style={styles.stepName}>{step.name}</Text>
                  </View>

                  <View style={[{flex: 1, backgroundColor: '#FFF'}, step.id === 2 && {paddingHorizontal: 10}]}>
                    <Image
                      style={[{width: '100%', height: '100%'}]}
                      resizeMode={'contain'}
                      source={{uri: step.image}}
                    />
                  </View>
                  {/* <View
                    style={{
                      position: 'absolute',
                      bottom: 10,
                      right: 10,
                      backgroundColor: 'rgba(155, 155, 155, 0.10)',
                      padding: 5,
                      borderRadius: 20,
                    }}>
                    <MaterialCommunityIcons name={'pencil'} size={20} color={'#333'} />
                  </View> */}
                </View>
              </Pressable>
            ))}
          </View>
        </View>
        <GrowYourApp />
        <RecommendedApps />
      </View>
      {showPublishFlow && <PublishModal onCancel={() => setShowPublishFlow(false)} onPublish={onPublish} />}
    </ScrollView>
  );
};

export const StoreRouter = () => {
  return (
    <Routes>
      <Route path="store" element={<MyStore />} />
    </Routes>
  );
};
