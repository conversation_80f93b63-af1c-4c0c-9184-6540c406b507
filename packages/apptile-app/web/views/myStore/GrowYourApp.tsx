import React from 'react';
import {FlatList, StyleSheet, View} from 'react-native';
import TextElement from '../../components-v2/base/TextElement';
import {IntegrationCard} from '../integrations/components/IntegrationCard';
import {useSelector} from 'react-redux';
import {integrationsById} from '../../selectors/BillingSelector';
import {IFetchIntegrationResponse} from '../../api/ApiTypes';
import {EditorRootState} from '../../store/EditorRootState';

const integrationsToDisplay = ['appInstallBanner', 'appOnlyDiscounts', 'metaAds'];

export const GrowYourApp = () => {
  const appId = useSelector((state: EditorRootState) => state.apptile.appId) as string;
  const allIntegrations = Object.values(useSelector(integrationsById) ?? {});
  const renderListData = allIntegrations.filter((i: IFetchIntegrationResponse) =>
    integrationsToDisplay.includes(i.integrationCode),
  );

  return (
    <View style={styles.wrapper}>
      <TextElement color="SECONDARY" fontWeight="500" fontSize="2xl">
        Grow your app
      </TextElement>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={renderListData}
        style={{
          overflowX: 'auto',
        }}
        columnWrapperStyle={{gap: 20}}
        renderItem={({item}) => (
          <IntegrationCard
            {...{
              integration: item,
              appId,
              isNewIntegration: true,
              boxStyles: {minHeight: 100, padding: 0, paddingHorizontal: 10},
              cardStyles: {marginTop: 10},
            }}
          />
        )}
        keyExtractor={item => item.integrationCode}
        numColumns={3}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    justifyContent: 'flex-start',
    width: '100%',
    marginBottom: 10,
  },
});
