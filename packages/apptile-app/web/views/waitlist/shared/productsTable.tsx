import Button from '@/root/web/components-v2/base/Button';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import theme from '@/root/web/styles-v2/theme';
import {Icon} from 'apptile-core';
import React, {useEffect, useState} from 'react';
import {View, Text, Image, FlatList, StyleSheet, Pressable} from 'react-native';

export const ProductTable: React.FC<{
  item: {
    id: string;
    title: string;
    price: number;
    currencyCode: string;
    totalInventory: number;
    collections: string;
    image: string;
    variants: any;
  }[];
  onProductDel: (product: any) => void;
  onProductBulkDel: (product: any) => void;
  operationStatus?: string;
  selectProducts?: any;
}> = ({item, onProductDel, selectProducts, onProductBulkDel, operationStatus}) => {
  const [checkedProducts, setCheckedProducts] = useState<string[]>([]);
  const [productStockFilter, setProductStockFilter] = useState([0, 1]);
  const [filteredProducts, setFilteredProducts] = useState(item);
  const [editIndex, setEditIndex] = useState(-1);

  useEffect(() => {
    setFilteredProducts(item);
  }, [item]);

  const resetFilters = () => {
    setProductStockFilter([0, 1]);
    setCheckedProducts([]);
  };

  const checkedIcon = () => (
    <Icon iconType="MaterialCommunityIcons" name="checkbox-outline" size={20} color="#1060E0" />
  );
  const uncheckedIcon = () => (
    <Icon iconType="MaterialCommunityIcons" name="checkbox-blank-outline" size={20} color="#D8D8D8" />
  );

  const renderItem = ({item, index}) => (
    <View style={styles.row} onMouseEnter={() => setEditIndex(index)} onMouseLeave={() => setEditIndex(-1)}>
      <View style={styles.cellSmall}>
        {checkedProducts.includes(item.id) ? (
          <Pressable
            style={styles.checkBox}
            onPress={() => {
              setCheckedProducts(checkedProducts.filter(product => product !== item.id));
            }}>
            {checkedIcon()}
          </Pressable>
        ) : (
          <Pressable
            style={styles.checkBox}
            onPress={() => {
              setCheckedProducts([...checkedProducts, item.id]);
            }}>
            {uncheckedIcon()}
          </Pressable>
        )}
      </View>
      <View style={styles.cellSmall}>
        <Image source={{uri: item.image}} style={styles.image} />
      </View>
      <Text style={styles.cell} numberOfLines={2}>
        {item.title}
      </Text>
      <Text style={styles.cell}>{item.displayPrice}</Text>
      <View style={[styles.cell, {flexDirection: 'row', flexWrap: 'wrap'}]}>
        {item?.variants?.map(e => (
          <View
            style={{
              paddingVertical: 4,
              paddingHorizontal: 10,
              borderRadius: 15,
              borderWidth: 0.5,
              borderColor: '#595959',
              marginRight: 3,
            }}>
            <Text style={commonStyles.baseText}>{e.title}</Text>
          </View>
        ))}
      </View>
      <Text style={styles.cell}>$0 for pre-auth</Text>
      <Text style={styles.cellSmall}>
        {editIndex == index && (
          <Pressable onPress={selectProducts}>
            <ApptileWebIcon name="edit-icon" size={21} />
          </Pressable>
        )}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {checkedProducts.length === 0 && (
        <View style={styles.headerRow}>
          <Text style={[styles.headerCellSmall, styles.headerCellText]} />
          <Text style={[styles.headerCellSmall, styles.headerCellText]} />
          <Text style={[styles.headerCell, styles.headerCellText]}>Product</Text>
          <Text style={[styles.headerCell, styles.headerCellText]}>Price</Text>
          <Text style={[styles.headerCell, styles.headerCellText]}>Variants</Text>
          <Text style={[styles.headerCell, styles.headerCellText]}>Payment</Text>
          <Text style={[styles.headerCellSmall, styles.headerCellText]} />
        </View>
      )}
      {checkedProducts.length > 0 && (
        <View style={styles.headerRow}>
          <View style={{flex: '1/8'}}>
            {filteredProducts.length === checkedProducts.length ? (
              <View style={styles.headerDeleteWrapper}>
                <Pressable
                  onPress={value => {
                    setCheckedProducts([]);
                  }}
                  style={{flex: 1}}>
                  {checkedIcon()}
                </Pressable>
                <Button
                  color="PRIMARY"
                  variant="PILL"
                  size="SMALL"
                  onPress={async () => {
                    await onProductBulkDel(checkedProducts);
                    setCheckedProducts([]);
                    resetFilters();
                  }}>
                  Delete all
                </Button>
              </View>
            ) : (
              <View style={styles.headerDeleteWrapper}>
                <Pressable
                  onPress={value => {
                    setCheckedProducts(filteredProducts.map(product => product.id));
                  }}
                  style={{flex: 1}}>
                  {uncheckedIcon()}
                </Pressable>
                <Button
                  color="PRIMARY"
                  variant="PILL"
                  size="SMALL"
                  onPress={() => {
                    onProductBulkDel(checkedProducts);
                    setCheckedProducts([]);
                    resetFilters();
                  }}>
                  Delete selected
                </Button>
              </View>
            )}
          </View>
          <View style={{flex: 1}}>
            <Text style={commonStyles.baseText}>{operationStatus}</Text>
          </View>
        </View>
      )}
      <FlatList
        contentContainerStyle={{maxHeight: 500, overflow: 'scroll'}}
        data={filteredProducts}
        renderItem={renderItem}
        keyExtractor={item => item.id}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E3E3E3',
    overflow: 'auto',
  },
  stockFilterWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  stockRadiogroupWrapper: {
    padding: 16,
    width: 200,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E3E3E3',
    paddingVertical: 16,
    backgroundColor: '#F7F7F7',
    marginBottom: 8,
    gap: 8,
    paddingHorizontal: 16,
    height: 48,
    alignItems: 'center',
  },
  checkBox: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
  },
  headerCell: {
    width: '21%',
  },
  headerCellSortWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  deleteHeaderWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerCellText: {
    fontWeight: '500',
    textAlign: 'left',
    fontFamily: theme.FONT_FAMILY,
    color: '#616161',
  },
  headerCellSmall: {
    width: '4%',
    alignSelf: 'flex-start',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    gap: 8,
    paddingHorizontal: 16,
  },
  cell: {
    width: '21%',
    textAlign: 'left',
    fontSize: 12,
    fontFamily: theme.FONT_FAMILY,
    color: '#595959',
  },
  cellSmall: {
    width: '4%',
    textAlign: 'left',
  },
  image: {
    width: 30,
    height: 40,
  },
  headerDeleteWrapper: {flexDirection: 'row', alignItems: 'center', gap: 24},
});

export default ProductTable;
