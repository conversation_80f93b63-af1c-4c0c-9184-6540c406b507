import React, {useEffect, useState} from 'react';
import {Pressable, Image, Text, View} from 'react-native';
import axios from 'axios';
import RadioGroupControlV2 from '../../components/controls-v2/RadioGroupControl';
import commonStyles from '../../styles-v2/commonStyles';
import Icon from '../../components-v2/base/Icon';
import {
  TransformGetProductsPaginatedQuery,
  TransformSearchCollections,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {datasourceTypeModelSel} from 'apptile-core';
import {ApptileWebIcon} from '../../icons/ApptileWebIcon';
import theme from '../../styles-v2/theme';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import {debounce} from 'lodash';
import Button from '../../components-v2/base/Button';
import PreOrderApi from '../../api/PreOrderApi';
import { EditorRootState } from '../../store/EditorRootState';
import { useSelector } from 'react-redux';

export const ProductAddition: React.FC = ({closeModal, existingProducts, existingVariants, planId}) => {
  const [activeCommentTab, setActiveCommentTab] = useState('product');
  const [products, setProducts] = useState([]);
  const [collections, setCollections] = useState([]);
  const [query, setQuery] = useState('');
  const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const ShopifyDSModel = shopifyModelSel ? shopifyModelSel(store.getState()) : null;
  const queryRunner = ShopifyDSModel?.get('queryRunner');
  const debouncedSetQuery = debounce(setQuery, 150);
  const [disableAddition, setDisableAddition] = useState(false);
  const [additionStatus, setAdditionStatus] = useState('');
  const [openItem, setOpenItem] = useState('');
  const [productIdsMap, setProductIdsMap] = useState<any>({});
  const [variantIdsMap, setVariantIdsMap] = useState<any>({});
  const [existingProductIds, setExistingProductIds] = useState<String[]>([]);
  const [existingVariantsIds, setExistingVariantsIds] = useState<String[]>([]);
  const [selectedProductIds, setSelectedProductIds] = useState<String[]>([]);
  const [selectedVariantsIds, setSelectedVariantsIds] = useState<String[]>([]);
  const [partialProductIds, setPartialProductIds] = useState<String[]>([]);
  const [selectedCollectionIds, setSelectedCollectionIds] = useState<String[]>([]);
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);

  useEffect(() => {
    const countryCode = ShopifyDSModel?.getIn(['shop', 'paymentSettings', 'countryCode']) ?? 'US';
    if (queryRunner) {
      if (activeCommentTab == 'product') {
        queryRunner
          .runQuery('query', ProductCollectionGqls.SEARCH_PRODUCTS, {
            first: 50,
            query: query.trim(),
            countryCode,
            variantMetafields: [],
            productMetafields: [],
          })
          .then(queryResponse => {
            const {transformedData} = processShopifyGraphqlQueryResponse(
              queryResponse,
              {transformer: TransformGetProductsPaginatedQuery},
              ShopifyDSModel?.get('shop') ?? {},
              ShopifyDSModel,
            );
            setProducts(transformedData);
            setProductIdsMap(oldMap => {
              transformedData.map(e=>{
                oldMap[e?.id]=e?.variants?.map(e => e?.id);
              })
              return {...oldMap};
            });
            setVariantIdsMap(oldMap => {
              transformedData.map(e => {
                e?.variants?.map(f => {oldMap[f?.id]=e?.id});
              })
              return {...oldMap};
            });
          });
      } else {
        queryRunner
          .runQuery('query', ProductCollectionGqls.SEARCH_COLLECTIONS_FOR_DROPDOWN, {
            first: 50,
            query: query.trim(),
            countryCode,
          })
          .then(queryResponse => {
            const {transformedData} = processShopifyGraphqlQueryResponse(
              queryResponse,
              {transformer: TransformSearchCollections},
              ShopifyDSModel?.get('shop') ?? {},
              ShopifyDSModel,
            );
            setCollections(transformedData);
          });
      }
    }
  }, [ShopifyDSModel, activeCommentTab, query, queryRunner]);

  useEffect(() => {
    setSelectedProductIds(existingProducts.map(e => e?.id));
    setPartialProductIds(existingVariants.map(e => e?.product?.id));
    setSelectedVariantsIds(existingVariants.map(e => e?.id));
    setExistingProductIds(existingProducts.map(e => e?.id));
    setExistingVariantsIds(existingVariants.map(e => e?.id));
  }, [existingProducts, existingVariants]);
  return (
    <View style={{height: 413, width: 592, padding: 20, justifyContent: 'flex-start'}}>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <Text style={[commonStyles.heading]}>Select items</Text>
        <Pressable onPress={()=>{closeModal()}}>
          <Icon iconType={'Entypo'} color={'SECONDARY'} name="cross" />
        </Pressable>
      </View>
      <View>
        {/* <RadioGroupControlV2
          value={activeCommentTab}
          options={[
            {text: 'Product', value: 'product'},
            {text: 'Collection', value: 'collection'},
          ]}
          onChange={setActiveCommentTab}
        /> */}
        <View>
          <ApptileWebIcon
            style={{position: 'absolute', marginTop: 16, marginLeft: 10, zIndex: 1}}
            name={'magnify'}
            size={12}
            color={theme.CONTROL_INPUT_COLOR}
          />
          <CodeInputControlV2
            placeholder={'Search ' + activeCommentTab + 's'}
            singleLine={true}
            defaultValue={query}
            inputStyles={{paddingLeft: 23}}
            onChange={function (value: string): void {
              debouncedSetQuery(value);
            }}
          />
        </View>
      </View>
      <View style={{marginTop: 20, paddingHorizontal: 2, flex: 1, overflow: 'scroll'}}>
        {activeCommentTab == 'product'
          ? products?.map(product => (
              <View>
                <View
                  style={{
                    flexDirection: 'row',
                    marginBottom: 14,
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    gap: 8,
                  }}>
                  <View style={{flexDirection: 'row', alignItems: 'center', gap: 8, flex: 1, flexBasis: 'auto'}}>
                    <Pressable
                      style={{
                        borderWidth: 1,
                        borderColor:
                          selectedProductIds.includes(product?.id) || partialProductIds.includes(product?.id)
                            ? theme.CTA_BORDER
                            : '#CECECE',
                        borderRadius: 2,
                        height: 14,
                        width: 14,
                      }}
                      onPress={() => {
                        if (partialProductIds.includes(product?.id)) {
                          setPartialProductIds(pIds => pIds.filter(id => id != product?.id));
                          setSelectedProductIds(pIds => [...pIds, product?.id]);
                          const variantIds = product?.variants?.map((e: any) => e.id);
                          setSelectedVariantsIds(vIds => vIds.filter(id => !variantIds.includes(id)));
                        } else if (selectedProductIds.includes(product?.id)) {
                          setSelectedProductIds(pIds => pIds.filter(id => id != product?.id));
                        } else if (!selectedProductIds.includes(product?.id)) {
                          setSelectedProductIds(pIds => [...pIds, product?.id]);
                        }
                      }}>
                      {selectedProductIds.includes(product?.id) && <Icon size={'xs'} name={'check'} />}
                      {partialProductIds.includes(product?.id) && <Icon size={'xs'} name={'minus'} />}
                    </Pressable>
                    <Pressable
                      style={{flexDirection: 'row', alignItems: 'center', gap: 4, flex: 1, flexBasis: 'auto'}}
                      onPress={() => setOpenItem(old => (old == product?.id ? '' : product?.id))}>
                      <Image source={{uri: product.featuredImage}} style={{height: 30, width: 22, borderRadius: 3}} />
                      <Text>{product.title}</Text>
                    </Pressable>
                  </View>
                  <Pressable style={{flex: 1, flexGrow: 0, flexBasis: 'auto'}} onPress={() => setOpenItem(old => (old == product?.id ? '' : product?.id))}>
                    <Icon
                      iconType={'MaterialIcons'}
                      size={'lg'}
                      color="SECONDARY"
                      name={openItem == product?.id ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
                    />
                  </Pressable>
                </View>
                {openItem == product?.id && (
                  <View style={{paddingLeft: 25}}>
                    {product?.variants?.map(variant => (
                      <View style={{flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 4}}>
                        <Pressable
                          style={{
                            borderWidth: 1,
                            borderColor:
                              selectedProductIds.includes(product?.id) || selectedVariantsIds.includes(variant?.id)
                                ? theme.CTA_BORDER
                                : '#CECECE',
                            borderRadius: 2,
                            height: 14,
                            width: 14,
                          }}
                          onPress={() => {
                            const variantIds = product?.variants?.map((e: any) => e.id);
                            const notSelectedVariants = variantIds.filter(id => !selectedVariantsIds.includes(id));
                            if (selectedProductIds.includes(product?.id)) {
                              setSelectedVariantsIds(vIds => [...vIds, ...variantIds.filter(e => e != variant?.id)]);
                              setSelectedProductIds(pIds => pIds.filter(id => id != product?.id));
                              if (variantIds.length != 1) setPartialProductIds(pIds => [...pIds, product?.id]);
                            } else if (notSelectedVariants.length > 1) {
                              if (notSelectedVariants.includes(variant?.id)) {
                                setSelectedVariantsIds(vIds => [...vIds, variant?.id]);
                                if (variantIds.length == notSelectedVariants.length) {
                                  setPartialProductIds(pIds => [...pIds, product?.id]);
                                }
                              } else {
                                setSelectedVariantsIds(vIds => vIds.filter(id => id != variant?.id));
                                if (variantIds.length - notSelectedVariants.length == 1) {
                                  setPartialProductIds(pIds => pIds.filter(id => id != product?.id));
                                }
                              }
                            } else if (notSelectedVariants.length == 1) {
                              if (notSelectedVariants[0] == variant?.id) {
                                setSelectedVariantsIds(vIds => vIds.filter(id => !variantIds.includes(id)));
                                setPartialProductIds(pIds => pIds.filter(id => id != product?.id));
                                setSelectedProductIds(pIds => [...pIds, product?.id]);
                              } else {
                                setSelectedVariantsIds(vIds => vIds.filter(id => id != variant?.id));
                                if (variantIds.length == 2) {
                                  setPartialProductIds(pIds => pIds.filter(id => id != product?.id));
                                }
                              }
                            }
                          }}>
                          {(selectedProductIds.includes(product?.id) || selectedVariantsIds.includes(variant?.id)) && (
                            <Icon size={'xs'} name={'check'} />
                          )}
                        </Pressable>
                        <Text>{variant.title}</Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>
            ))
          : collections?.map(collection => {
              return (
                <View>
                  <View
                    style={{
                      flexDirection: 'row',
                      marginBottom: 14,
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      gap: 8,
                    }}>
                    <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
                      <Pressable
                        style={{
                          borderWidth: 1,
                          borderColor: selectedCollectionIds.includes(collection?.id) ? theme.CTA_BORDER : '#CECECE',
                          borderRadius: 2,
                          height: 14,
                          width: 14,
                        }}
                        onPress={() => {
                          if (selectedCollectionIds.includes(collection?.id)) {
                            setSelectedCollectionIds(cIds => cIds.filter(id => id != collection?.id));
                          } else {
                            setSelectedCollectionIds(cIds => [...cIds, collection?.id]);
                          }
                        }}>
                        {selectedCollectionIds.includes(collection?.id) && <Icon size={'xs'} name={'check'} />}
                      </Pressable>
                      <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
                        <Image
                          source={
                            collection?.featuredImage || collection?.products?.[0]?.featuredImage
                              ? {uri: collection?.featuredImage || collection?.products?.[0]?.featuredImage}
                              : require('../../assets/images/placeholder-image.png')
                          }
                          resizeMode="contain"
                          style={{height: 30, width: 22, borderRadius: 3}}
                        />
                        <Text>{collection?.title}</Text>
                      </View>
                    </View>
                  </View>
                </View>
              );
            })}
      </View>
      <View style={{justifyContent: 'space-between', alignItems: 'center', flexDirection: 'row'}}>
        <Text style={[commonStyles.baseText, {flex: 1}]}>{additionStatus}</Text>
        <Button
          disabled={disableAddition}
          onPress={async () => {
            setDisableAddition(true);
            setAdditionStatus("Don't refresh the window until products are updated - 0%");
            const deletedProductIds = [];
            const deletedVariantIds = [];
            const addedProductIds = [];
            const addedVariantIds = [];
            selectedProductIds.map(id => {
              if (!existingProductIds.includes(id)) addedProductIds.push(id);
            });
            existingProductIds.map(id => {
              if (!selectedProductIds.includes(id)) deletedProductIds.push(id);
            });
            selectedVariantsIds.map(id => {
              if (!existingVariantsIds.includes(id)) addedVariantIds.push(id);
            });
            existingVariantsIds.map(id => {
              if (!selectedVariantsIds.includes(id)) deletedVariantIds.push(id);
            });
            const totalOperations = addedProductIds.length + deletedProductIds.length + addedVariantIds.length + deletedVariantIds.length;
            let operationsDone = 0;
            if(deletedProductIds.length > 0){
              if(deletedProductIds.length>35){
                for(let i=0;i<deletedProductIds.length;i+=35) {
                  const pIdMap = {};
                  deletedProductIds.slice(i, Math.min(i+35, deletedProductIds.length)).map(e=>{pIdMap[e]=productIdsMap[e]});
                  await PreOrderApi.removeProductsFromSellingPlan(appId, planId, deletedProductIds.slice(i, Math.min(i+35, deletedProductIds.length)),pIdMap);
                  operationsDone += Math.min(35, deletedProductIds.length-i);
                  setAdditionStatus(`Don't refresh the window until products are updated - ${parseInt((operationsDone/totalOperations)*100)}%`);
                }
              } else {
                const pIdMap = {};
                deletedProductIds?.map(e=>{pIdMap[e]=productIdsMap[e]});
                await PreOrderApi.removeProductsFromSellingPlan(appId, planId, deletedProductIds, pIdMap);
                operationsDone += deletedProductIds.length;
                setAdditionStatus(`Don't refresh the window until products are updated - ${parseInt((operationsDone/totalOperations)*100)}%`);
              }
            }
            if(addedProductIds.length > 0){              
              if(addedProductIds.length>35){
                for(let i=0;i<addedProductIds.length;i+=35) {
                  const pIdMap = {};
                  addedProductIds.slice(i, Math.min(i+35, addedProductIds.length))?.map(e=>{pIdMap[e]=productIdsMap[e]});
                  await PreOrderApi.addProductsToSellingPlan(appId, planId, addedProductIds.slice(i, Math.min(i+35, addedProductIds.length)), pIdMap);
                  operationsDone += Math.min(35, addedProductIds.length-i);
                  setAdditionStatus(`Don't refresh the window until products are updated - ${parseInt((operationsDone/totalOperations)*100)}%`);
                }
              } else {
                const pIdMap = {};
                addedProductIds?.map(e=>{pIdMap[e]=productIdsMap[e]});
                await PreOrderApi.addProductsToSellingPlan(appId, planId, addedProductIds, pIdMap);
                operationsDone += addedProductIds.length;
                setAdditionStatus(`Don't refresh the window until products are updated - ${parseInt((operationsDone/totalOperations)*100)}%`);
              }
            }
            if(deletedVariantIds.length > 0){
              const products = [];
              const productMap = {};
              deletedVariantIds.map(variantId => {
                if(!productMap[variantIdsMap[variantId]]) productMap[variantIdsMap[variantId]]=[];
                productMap[variantIdsMap[variantId]].push(variantId);
                products.push(variantIdsMap[variantId]);
              });
              if(products.length>35){
                for(let i=0;i<products.length;i+=35) {
                  const currentProducts = products.slice(i, Math.min(i+35, deletedVariantIds.length));
                  const currentMap = {};
                  let currentVariants = []
                  currentProducts.map(pId => {
                    currentMap[pId] = productMap[pId];
                    currentVariants = [...currentVariants, ...productMap[pId]];
                  });
                  await PreOrderApi.removeVariantsFromSellingPlan(appId, planId, currentVariants, currentMap);
                  operationsDone += Math.min(35, deletedVariantIds.length-i);
                  setAdditionStatus(`Don't refresh the window until products are updated - ${parseInt((operationsDone/totalOperations)*100)}%`);
                }
              } else {
                await PreOrderApi.removeVariantsFromSellingPlan(appId, planId, deletedVariantIds, productMap);
                operationsDone += deletedVariantIds.length;
                setAdditionStatus(`Don't refresh the window until products are updated - ${parseInt((operationsDone/totalOperations)*100)}%`);
              }
            }
            if(addedVariantIds.length > 0){
              const products = [];
              const productMap = {};
              addedVariantIds.map(variantId => {
                if(!productMap[variantIdsMap[variantId]]) productMap[variantIdsMap[variantId]]=[];
                productMap[variantIdsMap[variantId]].push(variantId);
                products.push(variantIdsMap[variantId]);
              });
              if(products.length>35){
                for(let i=0;i<products.length;i+=35) {
                  const currentProducts = products.slice(i, Math.min(i+35, addedVariantIds.length));
                  const currentMap = {};
                  let currentVariants = []
                  currentProducts.map(pId => {
                    currentMap[pId] = productMap[pId];
                    currentVariants = [...currentVariants, ...productMap[pId]];
                  });
                  await PreOrderApi.addVariantsToSellingPlan(appId, planId, currentVariants, currentMap);
                  operationsDone += Math.min(35, addedVariantIds.length-i);
                  setAdditionStatus(`Don't refresh the window until products are updated - ${parseInt((operationsDone/totalOperations)*100)}%`);
                }
              } else {
                await PreOrderApi.addVariantsToSellingPlan(appId, planId, addedVariantIds, productMap);
                operationsDone += addedVariantIds.length;
                setAdditionStatus(`Don't refresh the window until products are updated - ${parseInt((operationsDone/totalOperations)*100)}%`);
              }
            }
            closeModal(true);
          }}>
          Done
        </Button>
      </View>
    </View>
  );
};
