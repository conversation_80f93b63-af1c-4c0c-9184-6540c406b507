import TextElement from '@/root/web/components-v2/base/TextElement';
import React, {useCallback, useEffect, useState} from 'react';
import {ImageSourcePropType, Pressable, StyleSheet, View, Image, Text} from 'react-native';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import AssetChooseDialog from '../../components/controls/assetEditor/assetChooseDialog';
import {MaterialCommunityIcons, datasourceTypeModelSel, selectAppConfig} from 'apptile-core';
import {DefaultRootState, useDispatch, useSelector} from 'react-redux';
import commonStyles from '../../styles-v2/commonStyles';
import Button from '../../components-v2/base/Button';
import * as ProductCollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/product';
import * as CollectionGqls from '@/root/app/plugins/datasource/ShopifyV_22_10/queries/productCollection';
import {makeToast} from '../../actions/toastActions';
import {shopifyProductTransformer} from './shared/transformers';
import {
  TransformGetCollectionProductsQuery,
  TransformGetProductsQuery,
  TransformGetProductVariantsQuery,
} from '@/root/app/plugins/datasource/ShopifyV_22_10/transformers/productTransformer';
import {processShopifyGraphqlQueryResponse} from '@/root/app/plugins/datasource/utils';
import _ from 'lodash';
import axios from 'axios';
import {ShopifyProductCollectionPicker} from '../../integrations/shopify/components/ShopifyProductCollectionPicker';
import ProductTable from './shared/productsTable';
import Icon from '../../components-v2/base/Icon';
import ModalComponent from '../../components-v2/base/Modal';
import {ProductAddition} from './productAddition';
import PreOrderApi from '../../api/PreOrderApi';
import {EditorRootState} from '../../store/EditorRootState';
import {toggleAppIntegration} from '../../actions/editorActions';
import {useNavigate} from '../../routing.web';
import {currentIntegrationSelector} from '../integrations/components/deleteIntegration/DeleteIntegration';
import {DeleteConfirmationModal} from './shared/deleteConfirmationModal';

export const Dashboard = ({checkConfig}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [operationStatus, setOperationStatus] = useState('');
  const [disableAddition, setDisableAddition] = useState(false);
  const [newProductModal, setNewProductModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [productIdsMap, setProductIdsMap] = useState<any>({});
  const [variantsProductIdsMap, setVariantsProductIdsMap] = useState<any>({});
  const [existingProducts, setExistingProducts] = useState<any[]>([]);
  const [existingProductsIds, setExistingProductsIds] = useState<any[]>([]);
  const [existingVariants, setExistingVariants] = useState<any[]>([]);
  const [productList, setProductList] = useState<any[]>([]);
  const [sellingPlan, setSellingPlan] = useState<any>(null);
  const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const ShopifyDSModel = shopifyModelSel ? shopifyModelSel(store.getState()) : null;
  const queryRunner = ShopifyDSModel?.get('queryRunner');
  const shop = ShopifyDSModel?.get('shop');
  const formatCurrency = ShopifyDSModel?.get('formatCurrency');
  const storefrontApiUrl = ShopifyDSModel?.get('storefrontApiUrl');
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const fetchSellingPlanProducts = useCallback(
    (cursor?: string, cursorVariant?: string) => {
      PreOrderApi.getAllSellingPlans(appId, cursor, cursorVariant).then(response => {
        const sellingPlans = response?.data?.sellingPlanGroups?.edges;
        const preOrderSellingPlan = sellingPlans.find(e => e.node.merchantCode == 'apptile-pre-order');
        setSellingPlan(preOrderSellingPlan.node);
        const productIds = preOrderSellingPlan?.node?.products?.edges?.map(e => e.node.id) || [];
        const variantIds = preOrderSellingPlan?.node?.productVariants?.edges?.map(e => e.node.id) || [];
        if (productIds.length) {
          queryRunner
            .runQuery('query', ProductCollectionGqls.GET_PRODUCT_BY_IDS, {
              productIds,
            })
            .then(queryResponse => {
              const {transformedData} = processShopifyGraphqlQueryResponse(
                queryResponse,
                {transformer: TransformGetProductsQuery},
                {},
                null,
              );
              console.log(existingProductsIds);
              const newProducts = transformedData
                ?.filter(e => !!e.id)
                ?.filter(e => !existingProductsIds.includes(e.id))
                ?.map((obj: any) => ({
                  id: obj.id,
                  url: obj.onlineStoreUrl,
                  image: obj.featuredImage,
                  title: obj.title,
                  price: obj.minPrice,
                  displayPrice: formatCurrency(obj.minSalePrice),
                  currencyCode: shop.paymentSettings.currencyCode || 'USD',
                  totalInventory: obj.totalInventory,
                  variants: [{title: 'All Variants'}],
                }));
              setExistingProducts(newProducts);
              setExistingProductsIds(ids => [...ids, ...newProducts.map(e => e.id)]);
              setProductList(oldProducts => [...oldProducts, ...newProducts]);
              setProductIdsMap((oldMap: any) => {
                transformedData
                  ?.filter(e => !!e.id)
                  ?.map((e: any) => {
                    oldMap[e.id] = e?.variants?.map(e => e.id);
                  });
                return {...oldMap};
              });
            });
        }
        if (variantIds.length) {
          queryRunner
            .runQuery('query', ProductCollectionGqls.GET_VARIANT_BY_IDS, {
              variantIds,
            })
            .then(queryResponse => {
              const {transformedData} = processShopifyGraphqlQueryResponse(
                queryResponse,
                {transformer: TransformGetProductVariantsQuery},
                {},
                null,
              );
              const newProducts = [];
              const newProductsKeys = {};
              const newProductVariants = transformedData?.map((obj: any) => {
                if (!newProductsKeys[obj?._raw?.product.id]) newProductsKeys[obj?._raw?.product.id] = [];
                newProductsKeys[obj?._raw?.product.id].push({
                  id: obj.id,
                  url: obj.onlineStoreUrl,
                  image: obj.featuredImage,
                  title: obj.title,
                  price: obj.salePrice,
                  currencyCode: shop?.paymentSettings?.currencyCode || 'USD',
                  totalInventory: obj.totalInventory,
                  product: obj?._raw?.product,
                });
                return {
                  id: obj.id,
                  url: obj.onlineStoreUrl,
                  image: obj.featuredImage,
                  title: obj.title,
                  price: obj.salePrice,
                  currencyCode: shop?.paymentSettings?.currencyCode || 'USD',
                  totalInventory: obj.totalInventory,
                  product: obj?._raw?.product,
                };
              });
              setExistingVariants(oldVariants => [...oldVariants, ...newProductVariants]);
              Object.keys(newProductsKeys).map(e => {
                newProducts.push({
                  id: newProductsKeys[e][0]?.product?.id,
                  image: newProductsKeys[e][0]?.product?.images?.[0]?.url || newProductsKeys[e][0]?.featuredImage,
                  title: newProductsKeys[e][0]?.product?.title,
                  price: newProductsKeys[e][0]?.product?.priceRange.minVariantPrice.amount,
                  displayPrice: formatCurrency(newProductsKeys[e][0]?.product?.priceRange.minVariantPrice.amount),
                  currencyCode: shop.paymentSettings.currencyCode || 'USD',
                  totalInventory: newProductsKeys[e][0]?.product?.totalInventory,
                  variants: newProductsKeys[e],
                });
              });
              setVariantsProductIdsMap((oldMap: any) => {
                newProducts.map((e: any) => {
                  oldMap[e.id] = e?.variants?.map(e => e.id);
                });
                return {...oldMap};
              });
              setProductList(oldProducts => [...oldProducts, ...newProducts]);
            });
        }
        let productsCursor,
          variantsCursor = '';
        if (preOrderSellingPlan?.node?.products?.pageInfo?.hasNextPage) {
          productsCursor = preOrderSellingPlan?.node?.products?.pageInfo?.endCursor;
        }
        if (preOrderSellingPlan?.node?.productVariants?.pageInfo?.hasNextPage) {
          variantsCursor = preOrderSellingPlan?.node?.productVariants?.pageInfo?.endCursor;
        }
        if (productsCursor || variantsCursor) fetchSellingPlanProducts(productsCursor, variantsCursor);
        else setDisableAddition(false);
      });
    },
    [existingProducts, existingProductsIds, existingVariants],
  );
  const integrationState = useSelector(currentIntegrationSelector);
  const {integration} = integrationState;
  const id = _.get(integration, ['appIntegrations', 0, 'id'], null);
  const platformType = _.get(integration, ['appIntegrations', 0, 'platformType'], null);
  const deleteSellingPlan = async () => {
    await onProductBulkDel(existingProductsIds);
    await PreOrderApi.deleteSellingPlan(appId, sellingPlan.id);
    dispatch(toggleAppIntegration(appId, id, false, platformType, []));
    navigate(-1);
  };
  useEffect(() => {
    if (queryRunner) {
      setDisableAddition(true);
      setExistingProducts([]);
      setExistingProductsIds([]);
      setProductList([]);
      setExistingVariants([]);
      fetchSellingPlanProducts();
    }
  }, [queryRunner]);

  const onProductDel = (obj: any) => {};
  const onProductBulkDel = async products => {
    const deletedProductIds = [];
    const variantsDeletedProductIds = [];
    products.map((e: string) => {
      if (variantsProductIdsMap[e]) {
        variantsDeletedProductIds.push(e);
      } else {
        deletedProductIds.push(e);
      }
    });
    setOperationStatus(`Don't refresh the window until products are deleted from waitlist`);
    if (deletedProductIds.length > 0) {
      if (deletedProductIds.length > 35) {
        for (let i = 0; i < deletedProductIds.length; i += 35) {
          const pIdMap = {};
          deletedProductIds.slice(i, Math.min(i + 35, deletedProductIds.length)).map(e => {
            pIdMap[e] = productIdsMap[e];
          });
          await PreOrderApi.removeProductsFromSellingPlan(
            appId,
            sellingPlan?.id,
            deletedProductIds.slice(i, Math.min(i + 35, deletedProductIds.length)),
            pIdMap,
          );
          setOperationStatus(`Don't refresh the window until products are deleted from waitlist`);
        }
        setOperationStatus(``);
      } else {
        const pIdMap = {};
        deletedProductIds?.map(e => {
          pIdMap[e] = productIdsMap[e];
        });
        await PreOrderApi.removeProductsFromSellingPlan(appId, sellingPlan?.id, deletedProductIds, pIdMap);
        setOperationStatus(``);
      }
    }
    if (variantsDeletedProductIds.length > 0) {
      if (variantsDeletedProductIds.length > 35) {
        for (let i = 0; i < variantsDeletedProductIds.length; i += 35) {
          const pIdMap = {};
          let variantIds = [];
          variantsDeletedProductIds.slice(i, Math.min(i + 35, variantsDeletedProductIds.length)).map(e => {
            pIdMap[e] = variantsProductIdsMap[e];
            variantIds = [...variantIds, ...variantsProductIdsMap[e]];
          });
          await PreOrderApi.removeVariantsFromSellingPlan(appId, sellingPlan?.id, variantIds, pIdMap);
          setOperationStatus(`Don't refresh the window until products are deleted from waitlist`);
        }
      } else {
        const pIdMap = {};
        let variantIds = [];
        variantsDeletedProductIds?.map(e => {
          pIdMap[e] = variantsProductIdsMap[e];
          variantIds = [...variantIds, ...variantsProductIdsMap[e]];
        });
        await PreOrderApi.removeVariantsFromSellingPlan(appId, sellingPlan?.id, variantIds, pIdMap);
        setOperationStatus(``);
      }
    }
    checkConfig();
  };

  return (
    <View style={styles.wrapper}>
      <View style={{gap: 32}}>
        <View style={styles.infoArea}>
          <Text style={[commonStyles.heading, {marginBottom: 12, fontSize: 15, fontWeight: '500'}]}>
            Pre-authorisation Details
          </Text>
          <View>
            <View style={[styles.rowLayout, {gap: 5, alignItems: 'center', marginBottom: 2}]}>
              <Icon iconType="Octicons" size={'custom'} customSize={5} color={'SECONDARY'} name="dot-fill" />
              <Text style={[commonStyles.baseText]}>Users will not be charged anything for pre-authorisation.</Text>
            </View>
            <View style={[styles.rowLayout, {gap: 5, alignItems: 'center', marginBottom: 2}]}>
              <Icon iconType="Octicons" size={'custom'} customSize={5} color={'SECONDARY'} name="dot-fill" />
              <Text style={[commonStyles.baseText]}>Payment will have to be collected using shopify dashboard.</Text>
            </View>
            <View style={[styles.rowLayout, {gap: 5, alignItems: 'center', marginBottom: 2}]}>
              <Icon iconType="Octicons" size={'custom'} customSize={5} color={'SECONDARY'} name="dot-fill" />
              <Text style={[commonStyles.baseText]}>
                Inventory for items will be reserved as soon as pre-authorisation occurs.
              </Text>
            </View>
            <View style={[styles.rowLayout, {gap: 5, alignItems: 'center', marginBottom: 2}]}>
              <Icon iconType="Octicons" size={'custom'} customSize={5} color={'SECONDARY'} name="dot-fill" />
              <Text style={[commonStyles.baseText]}>
                Pre-authorisation expiry depends on the credit card carrier regulations of the user.
              </Text>
            </View>
            <View style={[styles.rowLayout, {gap: 5, alignItems: 'center', marginBottom: 2}]}>
              <Icon iconType="Octicons" size={'custom'} customSize={5} color={'SECONDARY'} name="dot-fill" />
              <Text style={[commonStyles.baseText]}>
                <Pressable
                  onPress={() => {
                    window.open(`https://help.shopify.com/en/manual/payments/payment-authorization`);
                  }}>
                  <Text style={[commonStyles.baseText, {color: '#005BE4', textDecorationLine: 'underline'}]}>
                    Learn more
                  </Text>
                </Pressable>{' '}
                about payment authorization and capture.
              </Text>
            </View>
          </View>
        </View>
        <View>
          <View style={[styles.rowLayout, {justifyContent: 'space-between'}]}>
            <View style={{gap: 8}}>
              <Text style={[commonStyles.baseText, styles.sectionHeaderText]}>Products</Text>
              <Text style={[commonStyles.baseText]}>Add products to be available to pre-order</Text>
            </View>
            <View style={styles.rowLayout}>
              <Button
                color="PRIMARY"
                variant="PILL"
                onPress={() => {
                  let store = storefrontApiUrl.replace('https://', '');
                  store = store.slice(0, store.indexOf('.myshopify.com'));
                  window.open(
                    `https://admin.shopify.com/store/${store}/orders?financial_status=pending%2Coverdue%2Cpartially_paid%2Cauthorized`,
                  );
                }}>
                View Orders
              </Button>
              <Button disabled={disableAddition} onPress={() => setNewProductModal(true)} color="CTA">
                Select Products
              </Button>
            </View>
          </View>
          <View style={[styles.sectionBody]}>
            <View style={{flex: 1}}>
              {productList.length > 0 ? (
                <ProductTable
                  operationStatus={operationStatus}
                  item={productList}
                  onProductDel={onProductDel}
                  onProductBulkDel={onProductBulkDel}
                  selectProducts={() => {
                    setNewProductModal(true);
                  }}
                />
              ) : (
                <View style={styles.noProducts}>
                  <Image style={styles.emptyImage} source={require('../../assets/images/snapshot-no-result.png')} />
                  <Text style={[commonStyles.baseText, {fontSize: 12, fontWeight: '500', color: '#000'}]}>
                    Search or browse to add products to your waitlist
                  </Text>
                </View>
              )}
            </View>
            <View style={{alignItems: 'flex-end'}}>
              <Pressable
                onPress={() => {
                  setDeleteModal(true);
                }}>
                <Text style={[commonStyles.baseText, {color: '#E93B3B'}]}>DISCONNECT</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </View>
      <ModalComponent
        onVisibleChange={setNewProductModal}
        visible={newProductModal}
        content={
          <ProductAddition
            planId={sellingPlan?.id}
            existingProducts={existingProducts}
            existingVariants={existingVariants}
            closeModal={reset => {
              if (reset) checkConfig();
              setNewProductModal(false);
            }}
          />
        }
      />
      <ModalComponent
        onVisibleChange={setDeleteModal}
        visible={deleteModal}
        content={<DeleteConfirmationModal operationStatus={operationStatus} onConfirmPress={deleteSellingPlan} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
  },
  emptyImage: {width: 150, height: 150},
  createStreamHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
  },
  sectionHeaderText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  sectionBody: {
    flex: 1,
    marginTop: 18,
  },
  rowLayout: {
    flexDirection: 'row',
    gap: 20,
  },
  alignCenter: {
    alignItems: 'center',
  },
  wrapper: {
    gap: 20,
    width: '100%',
    flex: 1,
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageWrapper: {width: 100, height: 100, overflow: 'hidden'},
  removeAsset: {
    backgroundColor: '#262626',
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    top: -7.5,
    right: -7.5,
    zIndex: 1,
  },
  infoArea: {
    padding: 24,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: '#F3F3F3',
  },
  noProducts: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: '#E3E3E3',
    borderWidth: 1,
    borderRadius: 10,
    paddingBottom: 45,
  },
});
