import {connect} from 'react-redux';
import {bindActionCreators} from 'redux';
import {AppDispatch} from '../../../../app/store';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {saveAppState} from '../../../actions/editorActions';
import EditorRightPaneTabs from '../components/EditorRightPaneTabs';

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return bindActionCreators({saveAppState}, dispatch);
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    editor: state.editor,
    appIntegrationIds: state.integration.appIntegrationIds,
    integrationsById: state.integration.integrationsById,
    appId: state.apptile.appId,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(EditorRightPaneTabs);
