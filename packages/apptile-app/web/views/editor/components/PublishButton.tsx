import React, {useRef, useState} from 'react';
import {Button, Pressable, StyleSheet, Text, View} from 'react-native';
import {TextInput} from 'react-native-gesture-handler';
import Popover, {PopoverMode, PopoverPlacement} from 'react-native-popover-view';

import {MaterialCommunityIcons} from 'apptile-core';

type PublishButtonProps = {onPress: (message: string) => void};
const PublishButton: React.FC<PublishButtonProps> = ({onPress}) => {
  const touchable = useRef() as React.LegacyRef<MaterialCommunityIcons>;
  const [showPopover, setShowPopover] = useState(false);

  const [message, setMessage] = useState('');
  const [showForm, setShowForm] = useState(false);

  return (
    <>
      <Pressable style={styles.button} onPress={() => setShowPopover(true)}>
        <MaterialCommunityIcons ref={touchable} style={styles.icon} name="cloud-upload-outline" size={20} />
      </Pressable>
      <Popover
        mode={PopoverMode.RN_MODAL}
        from={touchable}
        isVisible={showPopover}
        placement={PopoverPlacement.RIGHT}
        onRequestClose={() => setShowPopover(false)}>
        <View style={styles.dialogStyle}>
          <View style={styles.dialogHeader}>
            <Text>Publish Alert!</Text>
          </View>
          <View style={styles.dialogBody}>
            <Text style={styles.heading}>Publishing will send your changes to app users.</Text>
            <Text style={styles.subheading}>Make sure you have tested changes thoroughly in preview app</Text>
            {!showForm ? (
              <View style={[styles.actionButtons, styles.mt24]}>
                <View style={{marginRight: 4}}>
                  <Button title="Cancel" onPress={() => setShowPopover(false)} color="red" />
                </View>
                <Button title="Yes! Publish" onPress={() => setShowForm(true)} />
              </View>
            ) : (
              <>
                <TextInput
                  style={[styles.textInput, styles.mt24]}
                  placeholder="Enter publish message"
                  value={message}
                  onChange={e => setMessage(e.target.value)}
                />
                <View style={styles.actionButtons}>
                  <View style={{marginRight: 4}}>
                    <Button
                      title="Cancel"
                      onPress={() => {
                        setShowForm(false);
                        setShowPopover(false);
                      }}
                      color="red"
                    />
                  </View>
                  <Button
                    title="Publish"
                    onPress={() => {
                      onPress(message);
                      setShowForm(false);
                      setShowPopover(false);
                    }}
                    disabled={!message}
                  />
                </View>
              </>
            )}
          </View>
        </View>
      </Popover>
    </>
  );
};

const styles = StyleSheet.create({
  flex1: {flex: 1},
  button: {
    position: 'relative',
    borderColor: 'rgb(112 110 110)',
    borderWidth: 1,
    borderRadius: 50,
    width: 32,
    height: 32,
  },
  icon: {
    width: 20,
    height: 20,
    margin: 5,
    textAlign: 'center',
  },
  dialogStyle: {
    flex: 1,
    flexDirection: 'column',
    width: 340,
    backgroundColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  dialogHeader: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    padding: 10,
    backgroundColor: '#eee',
  },
  closeButton: {
    alignSelf: 'flex-end',
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    marginLeft: 'auto',
  },
  dialogBody: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'column',
    backgroundColor: '#fff',
    padding: 10,
  },
  rowContainer: {
    flex: 0,
    flexDirection: 'row',
    flexBasis: 'auto',
    alignItems: 'center',
    padding: 8,
  },
  heading: {
    fontSize: 14,
    color: '#black',
  },
  subheading: {
    fontSize: 12,
    color: '#999999',
    marginTop: 12,
  },
  actionButtons: {
    display: 'flex',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  mt24: {
    marginTop: 24,
  },
  textInput: {
    height: '40px',
    borderWidth: 1,
    borderColor: '#0091bc',
    paddingHorizontal: 12,
    borderRadius: 4,
  },
});

export default PublishButton;
