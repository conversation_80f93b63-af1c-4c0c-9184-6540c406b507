import React from 'react';
import {StyleSheet, View, Text, Pressable} from 'react-native';

import theme from '@/root/web/styles-v2/theme';
import {MaterialCommunityIcons} from 'apptile-core';

import moment from 'moment';
import {DesignContent, DropDown, SetupAudience} from '../shared';
import {NotificationPaneContext} from '../context';
import {useNotificationPlaygroundContext} from './context';
import Button from '@/root/web/components-v2/base/Button';
import {PushNotificationApi} from '@/root/web/api/PushNotificationApi';
import {useDispatch, useSelector} from 'react-redux';
import {makeToast} from '@/root/web/actions/toastActions';
import {useParams} from '@/root/web/routing.web';
import {flatMap, isEmpty} from 'lodash';
import {useNavigate} from 'react-router';
import {useSearchParams} from 'react-router-dom';
import {TimePickerModal, DatePickerModalContent} from 'rn-dates';
import {SafeAreaProvider} from 'react-native-safe-area-context';

import {currentPlanFeaturesSelector} from '@/root/web/selectors/FeatureGatingSelector';
import {allAvailablePlans} from 'apptile-core';
import {setOpenPremiumModal} from '@/root/web/actions/editorActions';

import Modal from '@/root/web/components-v2/base/Modal';
import TextElement from '@/root/web/components-v2/base/TextElement';
import cronUtils from '../../../common/cronUtils';
import commonStyles from '../../../styles-v2/commonStyles';
import DropDownControl from '../../../components/controls/DropDownControl';
import RadioGroupControl from '../../../components/controls/RadioGroupControl';
import DateAndTimeControl from '../../../components/controls/DateAndTimeControl';
import Tooltip from '../../../components-v2/base/Tooltip/Index';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {OneSignalPushNotificationApi} from '@/root/web/api/OneSignalPushNotificationApi';
import {oneSignalCreateRecordTransformer} from '../shared/oneSignalTransformer';

const styles = StyleSheet.create({
  rightPaneContainer: {
    height: '100%',
    backgroundColor: '#FFFFFF',
  },
  rightPaneWrapper: {
    marginHorizontal: 16,
    marginTop: 89,
    flex: 1,
  },
  rightPaneHeading: {
    fontSize: 17,
    color: theme.TEXT_COLOR,
  },
  contentLabel: {
    color: '#1D1DC',
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    bottom: 25,
  },
  buttonWidth: {
    width: '45%',
    minHeight: 40,
  },
  solidButton: {
    backgroundColor: '#262626',
    borderColor: '#262626',
  },
  outlineButton: {borderColor: '#262626'},
  outlineButtonText: {
    color: '#262626',
  },
  rightPaneContent: {
    flex: 1,
    marginTop: 45,
  },
  rowCenter: {flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'},
  actionButton: {borderRadius: 20, alignItems: 'center', flexDirection: 'row', justifyContent: 'center', width: '100%'},
  actionButtonLabel: {
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 16,
    width: 100,
    textAlign: 'center',
    color: '#FFF',
  },
  overLayButton: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  overLayBottomButton: {
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  dateTimePickerContainer: {
    height: 450,
    width: 350,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  pickerContainer: {
    borderWidth: 1,
    color: '#000',
    padding: 5,
    borderRadius: 26,
    flexDirection: 'row-reverse',
    width: 125,
  },
  toggleContainer: {
    flexDirection: 'row',
  },
  spacer: {
    height: '100%',
    width: 8,
  },
  separator: {
    height: 0.75,
    width: '100%',
    backgroundColor: '#BFBFBF',
    marginTop: 16,
    marginBottom: 32,
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: '#FFFFFF',
  },
});

const RightPane: React.FC = () => {
  const {pane: activePane} = React.useContext(NotificationPaneContext);
  const {record, updateRecord} = useNotificationPlaygroundContext();

  return (
    <View style={[styles.rightPaneContainer, commonStyles.rightSideBar]}>
      {activePane === 'Set Up & Audience' && <SetupAudience record={record} updateRecord={updateRecord} />}
      {activePane === 'Design & Content' && <DesignContent record={record} updateRecord={updateRecord} />}
      {activePane === 'Scheduling' && <Scheduling />}
    </View>
  );
};

type SchedulingProps = {};
const Scheduling: React.FC<SchedulingProps> = () => {
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isGated = !currentPlanFeatures.includes(allAvailablePlans.PRO);
  const openToggle = () => {
    dispatch(setOpenPremiumModal(true, allAvailablePlans.PRO));
  };

  const param = useParams();
  const hasOneSignal = useSelector((state: EditorRootState) => state.platform.hasOneSignal);
  const oneSignalAppId = useSelector((state: EditorRootState) => state.platform.oneSignalAppId);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const {record, updateRecord, isValid} = useNotificationPlaygroundContext();
  const [isRequestTrigger, setRequestTriggered] = React.useState(false);

  const {setPane: setValue} = React.useContext(NotificationPaneContext);
  const [isDraft, setDraft] = React.useState(false);
  const [toggleActionButton, setActionButton] = React.useState(false);
  const [isEndDatePickerVisible, setEndDatePickerVisible] = React.useState(false);
  const [isTriggerTimePickerVisible, setTriggerTimePickerVisible] = React.useState(false);

  const [isScheduledDatePickerVisible, setScheduledDatePickerVisible] = React.useState(false);
  const [isScheduledTimePickerVisible, setScheduledTimePickerVisible] = React.useState(false);

  const [publishStatus, setPublishStatus] = React.useState<'NOW' | 'LATER'>('NOW');
  const isCampaignLive = record.status === 'LIVE';
  const isRecurringCampaign = record.deliveryFrequency === 'RECURRING';

  React.useEffect(() => {
    if (record.isScheduled) {
      setPublishStatus('LATER');
    } else {
      setPublishStatus('NOW');
    }
  }, [record.isScheduled]);

  // create or update
  const sendPushHandler = (status: 'PENDING' | 'DRAFT') => {
    if (!isValid) return;

    if (searchParams.get('action') === 'create') {
      createPush(status);
    }
    if (searchParams.get('action') === 'edit') {
      updatePush(status);
    }
  };

  let frequencyOptions = [
    {name: 'Daily', value: '1'},
    {name: 'Weekly', value: '7'},
    {name: 'Fortnightly', value: '14'},
    {name: 'Monthly', value: '30'},
  ];

  const getDeliveryTimeAndMethod = function () {
    if (isCampaignLive) {
      return {
        deliveryType: null,
        scheduledAt: null,
      };
    } else if (isRecurringCampaign) {
      if (record.scheduledAt) {
        var todaysDate = new Date();
        if (todaysDate.setHours(0, 0, 0, 0) === record.scheduledAt.setHours(0, 0, 0, 0)) {
          return {
            deliveryType: 'INSTANT',
            scheduledAt: null,
          };
        } else {
          const scheduledDate = new Date(record.scheduledAt.setHours(0, 0, 0, 0));
          return {
            deliveryType: 'SCHEDULED',
            scheduledAt: scheduledDate,
          };
        }
      }
    }
    return {
      deliveryType: record.isScheduled ? 'SCHEDULED' : 'INSTANT',
      scheduledAt: record.isScheduled ? record.scheduledAt : null,
    };
  };

  const createPush = async (status: 'PENDING' | 'DRAFT') => {
    try {
      if (isRequestTrigger) return;

      const appID = param.id as string;
      if (isEmpty(appID)) return;

      let cronRepeatPattern = null;

      if (isRecurringCampaign) {
        cronRepeatPattern = cronUtils.getCronPatternFromTimeAndRepeatDayFrequency(
          record.triggerTime,
          parseInt(record.frequencyIntervalDays),
        );
      }

      const createRecord = {
        appId: appID,
        title: record.title,
        body: record.body,
        targetAudienceType: record.targetAudienceType,
        userSegmentName: record.userSegmentName,
        userSegmentId: record.userSegmentId,
        imageObject: {
          url: record.imageUrl,
        },
        deeplinkUrl: record.deepLinkUrl,
        status: status,
        deeplinkMetadata: record.deepLinkMetaData,
        deliveryFrequency: record.deliveryFrequency,
        endDate: record.endDate,
        repeatPattern: cronRepeatPattern,
      };
      const {deliveryType, scheduledAt} = getDeliveryTimeAndMethod();
      if (deliveryType) {
        createRecord.deliveryType = deliveryType;
      }
      if (scheduledAt) {
        createRecord.scheduledAt = scheduledAt;
      }

      setRequestTriggered(true);
      let response;
      if (hasOneSignal) {
        const transformedRecord = oneSignalCreateRecordTransformer(createRecord, oneSignalAppId, status);
        if (status === 'PENDING') {
          response = await OneSignalPushNotificationApi.sendPushNotification(appID, transformedRecord);
        }
        if (status === 'DRAFT') {
          response = await OneSignalPushNotificationApi.createTemplate(appID, transformedRecord);
        }
      } else {
        response = await PushNotificationApi.createManualRecord(appID, createRecord);
      }

      if (response.status >= 200 && response.status <= 299) {
        dispatch(
          makeToast({
            content: 'Push created successfully',
            appearances: 'success',
          }),
        );
        setRequestTriggered(false);
        navigate(-1);
      } else {
        throw new Error();
      }
    } catch (err) {
      logger.error(err);
      const errorMessage = err?.response?.data?.errors?.[0] || 'Unable to create push notification';
      setRequestTriggered(false);
      dispatch(
        makeToast({
          content: errorMessage,
          appearances: 'error',
          duration: 3000,
        }),
      );
    }
  };

  const updatePush = async (status: 'PENDING' | 'DRAFT') => {
    try {
      if (isRequestTrigger) return;

      const appID = param.id as string;
      const notificationID = searchParams.get('notificationId') as string;

      // validate all required fields
      if (isEmpty(appID) || isEmpty(notificationID)) return;
      let cronRepeatPattern = null;

      if (isRecurringCampaign) {
        cronRepeatPattern = cronUtils.getCronPatternFromTimeAndRepeatDayFrequency(
          record.triggerTime,
          parseInt(record.frequencyIntervalDays),
        );
      }

      const updateManualRecord = {
        appId: appID,
        title: record.title,
        body: record.body,
        targetAudienceType: record.targetAudienceType,
        userSegmentName: record.userSegmentName,
        userSegmentId: record.userSegmentId,
        imageObject: {
          url: record.imageUrl,
        },
        status: status,
        deeplinkUrl: record.deepLinkUrl,
        deeplinkMetadata: record.deepLinkMetaData,
        deliveryFrequency: record.deliveryFrequency,
        endDate: record.endDate,
        repeatPattern: cronRepeatPattern,
      };

      const {deliveryType, scheduledAt} = getDeliveryTimeAndMethod();
      if (deliveryType) {
        updateManualRecord.deliveryType = deliveryType;
      }
      if (scheduledAt) {
        updateManualRecord.scheduledAt = scheduledAt;
      }

      setRequestTriggered(true);
      let response: any = {};
      if (hasOneSignal) {
        const transformedRecord = oneSignalCreateRecordTransformer(updateManualRecord, oneSignalAppId, status);
        if (status === 'PENDING') {
          response = await OneSignalPushNotificationApi.sendPushNotification(appID, transformedRecord);
          await OneSignalPushNotificationApi.stopPushNotification(appID, notificationID, oneSignalAppId);
        }
        if (status === 'DRAFT') {
          response = await OneSignalPushNotificationApi.createTemplate(appID, transformedRecord);
        }
      } else {
        response = await PushNotificationApi.createManualRecord(appID, updateRecord);
      }

      if (response.status >= 200 && response.status <= 299) {
        dispatch(
          makeToast({
            content: 'Push updated successfully',
            appearances: 'success',
          }),
        );
        setRequestTriggered(false);
        navigate(-1);
      } else {
        throw new Error();
      }
    } catch (err) {
      setRequestTriggered(false);
      console.error(err);
      dispatch(
        makeToast({
          content: 'Unable to update push notification',
          appearances: 'error',
        }),
      );
    }
  };

  return (
    <SafeAreaProvider>
      <View style={styles.rightPaneWrapper}>
        <TextElement fontWeight="600" style={styles.rightPaneHeading} lineHeight="md">
          Scheduling
        </TextElement>

        <View style={styles.rightPaneContent}>
          <View>
            {/* <DropDownControl
              label={'Frequency'}
              value={record.deliveryFrequency || ''}
              disabled={isCampaignLive}
              defaultValue={record.deliveryFrequency || ''}
              options={[{name: 'One Time', value: 'ONCE'}]}
              onChange={val => {
                updateRecord('deliveryFrequency', val);
              }}
              nameKey={'name'}
              valueKey={'value'}
              disableBinding={true}
            /> */}
            {isRecurringCampaign && (
              <DropDownControl
                label={'Repeat Interval'}
                value={record.frequencyIntervalDays || ''}
                disabled={isCampaignLive}
                defaultValue={record.frequencyIntervalDays || ''}
                options={frequencyOptions}
                onChange={val => {
                  updateRecord('frequencyIntervalDays', val);
                }}
                nameKey={'name'}
                valueKey={'value'}
                disableBinding={true}
              />
            )}

            {!isCampaignLive && (
              <>
                {!isRecurringCampaign && record?.deliveryFrequency == 'ONCE' && (
                  <RadioGroupControl
                    disableBinding={true}
                    value={record.isScheduled}
                    label={'Publish'}
                    options={[
                      {text: 'Now', value: false},
                      {text: 'Later', value: true},
                    ]}
                    onChange={val => updateRecord('isScheduled', val)}
                  />
                )}
                {(record.isScheduled || isRecurringCampaign) && (
                  <>
                    {isRecurringCampaign && <Separator />}
                    <DateAndTimeControl
                      value={((!isRecurringCampaign && record.scheduledAt) ||
                      (isRecurringCampaign && record.triggerTime)
                        ? isRecurringCampaign
                          ? record.triggerTime
                          : record.scheduledAt
                        : new Date()
                      )?.toISOString()}
                      label={isRecurringCampaign ? 'Start Date' : 'Date'}
                      name={isRecurringCampaign ? 'Start Date' : 'Date'}
                      onChange={function (value: string): void {
                        updateRecord('scheduledAt', new Date(value));
                        if (isRecurringCampaign) updateRecord('triggerTime', new Date(value));
                      }}
                    />
                  </>
                )}
              </>
            )}

            {isRecurringCampaign && (
              <DateAndTimeControl
                value={(record.endDate ? new Date(record.endDate) : new Date()).toISOString()}
                label={'End Date'}
                name={'End Date'}
                showTime={false}
                onChange={function (value: string): void {
                  updateRecord('endDate', value);
                }}
              />
            )}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            variant="PILL"
            onPress={() => setValue('Design & Content')}
            containerStyles={[styles.buttonWidth, styles.outlineButton]}
            textStyles={styles.outlineButtonText}>
            BACK
          </Button>
          {record.isScheduled ? (
            <View style={[styles.buttonWidth]}>
              {toggleActionButton && (
                <Pressable
                  onPress={() => {
                    setActionButton(false);
                    setDraft(prev => !prev);
                  }}
                  style={[styles.buttonWidth, styles.solidButton, styles.actionButton, styles.overLayButton]}>
                  <Text style={styles.actionButtonLabel}>{!isDraft ? 'SAVE' : 'GO LIVE'}</Text>
                </Pressable>
              )}
              <View
                style={[
                  styles.buttonWidth,
                  styles.actionButton,
                  toggleActionButton && styles.overLayBottomButton,
                  isValid ? styles.solidButton : {backgroundColor: theme.DISABLED_BACKGROUND},
                ]}>
                <Pressable
                  onPress={() => {
                    sendPushHandler(isDraft ? 'DRAFT' : 'PENDING');
                  }}>
                  <Text style={styles.actionButtonLabel}>{isDraft ? 'SAVE' : 'GO LIVE'}</Text>
                </Pressable>
                {!hasOneSignal && (
                  <Pressable
                    onPress={() => {
                      setActionButton(true);
                    }}>
                    <MaterialCommunityIcons name="chevron-up" size={20} color={'#fff'} />
                  </Pressable>
                )}
              </View>
            </View>
          ) : (
            <View style={styles.buttonWidth}>
              <Tooltip
                visible={!isValid}
                tooltipPosition={'Top'}
                tooltip={
                  <View style={[styles.tooltip]}>
                    <Text style={[commonStyles.baseText, commonStyles.errorText]}>Fill valid values in all fields</Text>
                  </View>
                }>
                <Button
                  disabled={!isValid}
                  onPress={() => sendPushHandler('PENDING')}
                  containerStyles={[
                    isValid
                      ? {...styles.outlineButton, ...styles.solidButton}
                      : {backgroundColor: theme.DISABLED_BACKGROUND},
                  ]}>
                  GO LIVE
                </Button>
              </Tooltip>
            </View>
          )}
        </View>
      </View>
    </SafeAreaProvider>
  );
};

export default RightPane;

const Separator: React.FC = () => {
  return <View style={styles.separator} />;
};
