import {UserState} from '../../web/store/UserReducer';
import {ToastState} from '../../web/store/ToastReducer';
import {OrgsReduxState} from '../../web/store/OrgReducer';
import {AssetReduxState} from '../../web/store/AssetReducer';
import type {PlatformState} from '@/root/web/store/PlatformReducer';
import type {RootState} from 'apptile-core';
import {BillingReduxState} from './BillingReducer';
import {IntegrationReduxState} from './IntegrationReducer';
import {TilesCacheType} from '../common/webDatatypes';
import {IBlueprintState} from './BlueprintReducer';
import {IPageState} from './PageReducer';
import {OnboardingState} from './onboardingReducer';
import {ShopifyState} from './ShopifyReducer';
import {AppBranchesReduxState} from './BranchReducer';
import {AppForksReduxState} from './AppForkReducer';
import {ISnapshotState} from './SnapshotReducer';
import {LiveSellingData} from './liveSellingReducer';
import { CartAssistData } from './CartAssistReducer';

export type EditorRootState = RootState & {
  user: UserState;
  orgs: OrgsReduxState;
  asset: AssetReduxState;
  platform: PlatformState;
  toast: ToastState;
  billing: BillingReduxState;
  integration: IntegrationReduxState;
  tiles: TilesCacheType;
  blueprint: IBlueprintState;
  pages: IPageState;
  branches: AppBranchesReduxState;
  forks: AppForksReduxState;
  onboarding: OnboardingState;
  shopify: ShopifyState;
  snapshots: ISnapshotState;
  liveSelling: LiveSellingData;
  cartAssist: CartAssistData
};
