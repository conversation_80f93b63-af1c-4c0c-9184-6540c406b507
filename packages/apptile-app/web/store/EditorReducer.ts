import {DispatchAction, DispatchActions, DispatchEmptyAction} from 'apptile-core';
import {BindingError, ModuleCreationParams, PluginSubType} from 'apptile-core';
import {createReducer} from 'apptile-core';
import {
  EDITOR_OPEN_MODULE_DIALOG,
  EDITOR_CLOSE_MODULE_DIALOG,
  EDITOR_OPEN_PLUGIN_LISTING,
  EDITOR_OPEN_THEME_EDITOR,
  EDITOR_OPEN_PROPERTY_INSPECTOR,
  EDITOR_SELECT_NAV_COMPONENT,
  EDITOR_SELECT_PAGE,
  REGISTER_PLUGINS,
  SET_MODULE_CREATION_PARAMS,
  EDITOR_SELECTED_PAGE_TYPE,
  EDITOR_SET_ACTIVE_ATTACHMENT_ID,
  EDITOR_SET_ACTIVE_ATTACHMENT_KEY,
  EditorActiveAttachmenIdPayload,
  EditorActiveAttachmentKeyPayload,
  EDITOR_OPEN_TILES_BROWSER,
  EDITOR_RECORD_BINDING_ERROR,
  EDITOR_RESOLVE_BINDING_ERROR,
  EDITOR_TOGGLE_BINDING_ERRORS,
  EDITOR_SELECT_SETTING_COMPONENT,
} from '../actions/editorActions';
import {EditorState} from '../common/webDatatypes';

const initialState = new EditorState();

const handleOpenPI = (state: EditorState, _) => {
  return state.openPropertyInspector();
};
const handleOpenListing = (state: EditorState, _) => {
  return state.openPluginListing();
};

const handleOpenThemeEditor = (state: EditorState, _) => {
  return state.openThemeEditor();
};

const handleOpenTilesBrowser = (state: EditorState, _) => {
  return state.openTilesBrowser();
};

const handleRegisterPlugins = (state: EditorState, action: DispatchAction<PluginSubType[]>) => {
  return state.registerPlugins(action.payload);
};

const handleSetModuleCreationParams = (state: EditorState, action: DispatchAction<ModuleCreationParams>) => {
  return state.setModuleCreationParams(action.payload);
};
const handleOpenModuleDialog = (state: EditorState, action: DispatchEmptyAction) => {
  return state.openModuleCreationDialog();
};
const handleCloseModuleDialog = (state: EditorState, action: DispatchEmptyAction) => {
  return state.closeModuleCreationDialog();
};

export const EditorReducer = createReducer<EditorState>(
  {
    [DispatchActions.SELECT_PLUGIN]: (state: EditorState, action: DispatchAction<string[]>) => {
      const sel = action.payload;
      return state.selectPlugin(sel);
    },
    [EDITOR_SELECT_NAV_COMPONENT]: (state: EditorState, action: DispatchAction<string[]>) => {
      const sel = action.payload;
      return state.selectNavComponent(sel);
    },
    [EDITOR_SELECT_SETTING_COMPONENT]: (state: EditorState, action: DispatchAction<string>) => {
      const sel = action.payload;
      return state.selectSettings(sel);
    },
    [EDITOR_SELECTED_PAGE_TYPE]: (state: EditorState, action: DispatchAction<string>) => {
      const pageType = action.payload;
      return state.selectPageType(pageType);
    },
    [EDITOR_SET_ACTIVE_ATTACHMENT_ID]: (state: EditorState, action: DispatchAction<EditorActiveAttachmenIdPayload>) => {
      const {activeAttachmentId} = action.payload;
      return state.setActiveAttachmentId(activeAttachmentId);
    },
    [EDITOR_SET_ACTIVE_ATTACHMENT_KEY]: (
      state: EditorState,
      action: DispatchAction<EditorActiveAttachmentKeyPayload>,
    ) => {
      const {activeAttachmentKey} = action.payload;
      return state.setActiveAttachmentKey(activeAttachmentKey);
    },
    [EDITOR_SELECT_PAGE]: (state: EditorState, action: DispatchAction<string>) => {
      const pageId = action.payload;
      return state.selectPage(pageId);
    },
    [EDITOR_OPEN_PROPERTY_INSPECTOR]: handleOpenPI,
    [EDITOR_OPEN_PLUGIN_LISTING]: handleOpenListing,
    [EDITOR_OPEN_THEME_EDITOR]: handleOpenThemeEditor,
    [EDITOR_OPEN_TILES_BROWSER]: handleOpenTilesBrowser,
    [REGISTER_PLUGINS]: handleRegisterPlugins,
    [SET_MODULE_CREATION_PARAMS]: handleSetModuleCreationParams,
    [EDITOR_OPEN_MODULE_DIALOG]: handleOpenModuleDialog,
    [EDITOR_CLOSE_MODULE_DIALOG]: handleCloseModuleDialog,
    [EDITOR_RECORD_BINDING_ERROR]: (
      state: EditorState,
      action: DispatchAction<{
        binding: string;
        error: BindingError;
      }>,
    ) => {
      return state.recordBindingError(action.payload.binding, action.payload.error);
    },
    [EDITOR_RESOLVE_BINDING_ERROR]: (state: EditorState, action: DispatchAction<string>) => {
      return state.deleteBindingError(action.payload);
    },
    [EDITOR_TOGGLE_BINDING_ERRORS]: (state: EditorState) => {
      return state.toggleBindingError();
    },
  },
  initialState,
);
