import {createReducer} from 'apptile-core';
import { 
  CART_ASSIST_ADD_LINE_ITEM,
  CART_ASSIST_ADD_LINE_ITEMS,
  CART_ASSIST_CLEAR_ALL_LINE_ITEMS,
  CART_ASSIST_CLEAR_ALL_REMOVED_LINE_ITEMS,
  CART_ASSIST_REMOVE_LINE_ITEM,
  CART_ASSIST_SET_CART,
  CART_ASSIST_SET_CHANGES_EXIST,
  CART_ASSIST_SET_CUSTOMER,
  CART_ASSIST_UPDATE_LINE_ITEM_QUANTITY,
  CART_ASSIST_UPDATE_LINE_ITEM_VARIANT,
  LINE_ITEM_TYPE,
  QUANTITY_UPDATE_TYPE
} from '../actions/cartAssistActions'
import _ from 'lodash';
import { IProductVariant } from '@/root/app/plugins/datasource/ShopifyV_22_10/types';
import { I_CART_ASSIST_CART, I_CART_ASSIST_CUSTOMER, I_CART_ASSIST_LINE_ITEM } from '../views/cartAssist/types';

export type CartAssistData = {
  customer: I_CART_ASSIST_CUSTOMER,
  cart: I_CART_ASSIST_CART,
  existingLineItems: I_CART_ASSIST_LINE_ITEM[]
  newLineItems: I_CART_ASSIST_LINE_ITEM[]
  removedFromExistingLineItem: I_CART_ASSIST_LINE_ITEM[],
  changesExists: boolean
};

const initialWebflowDataState: CartAssistData = {
  customer: {},
  cart: {},
  existingLineItems: [],
  newLineItems: [],
  removedFromExistingLineItem: [],
  changesExists: false
};

export const CartAssistReducer = createReducer<CartAssistData>(
  {
    [CART_ASSIST_SET_CUSTOMER]: (state: CartAssistData, action: {payload: any}) => {
      if (!_.eq(action.payload, state.customer)) {
        return {
          ...state,
          customer: action.payload
        };
      } else {
        return state;
      }
    },
    [CART_ASSIST_SET_CART]: (state: CartAssistData, action: { payload: any }) => {
      return {
        ...state,
        cart: action.payload
      }
    },
    [CART_ASSIST_ADD_LINE_ITEM]: (state: CartAssistData, action: { payload: { lineItem: I_CART_ASSIST_LINE_ITEM, type: LINE_ITEM_TYPE, variant: IProductVariant} }) => {
      const type = action.payload.type
      const field = type === LINE_ITEM_TYPE.EXISTING ? 'existingLineItems' : 'newLineItems'

      const newVariant = action.payload?.variant
      let lineItem= action.payload.lineItem
      if(newVariant) {
        lineItem = {
          ...lineItem, 
          quantity: 1,
          variant: { 
            ...newVariant,
            product: lineItem.variant.product
          }
        }
      }

      return {
        ...state,
        [field]: [
          ...state[field],
          lineItem
        ]
      }
    },
    [CART_ASSIST_ADD_LINE_ITEMS]: (state: CartAssistData, action: { payload: { lineItems: I_CART_ASSIST_LINE_ITEM[], type: LINE_ITEM_TYPE} }) => {
      const type = action.payload.type
      const field = type === LINE_ITEM_TYPE.EXISTING ? 'existingLineItems' : 'newLineItems'

      return {
        ...state,
        [field]: [...state[field], ...action.payload.lineItems]
      } 
    },
    [CART_ASSIST_UPDATE_LINE_ITEM_QUANTITY]: (state: CartAssistData, action: { payload: { lineItem: I_CART_ASSIST_LINE_ITEM, quantityType: QUANTITY_UPDATE_TYPE, lineType: LINE_ITEM_TYPE} }) => {
        const type = action.payload.lineType
        const field = type === LINE_ITEM_TYPE.EXISTING ? 'existingLineItems' : 'newLineItems'
        const operationType = action.payload.quantityType
  
        return {
          ...state,
          [field]: state[field].map((line: I_CART_ASSIST_LINE_ITEM) => {
            if(line.variant.id == action.payload.lineItem.variant.id) {
              if(operationType == QUANTITY_UPDATE_TYPE.INCREASE) {
                return { ...line, quantity: line.quantity + 1 }
              } else {
                return { ...line, quantity: line.quantity - 1 }
              }
            } 
            return line;
          })
        } 
    },
    [CART_ASSIST_UPDATE_LINE_ITEM_VARIANT]: (state: CartAssistData, action: { payload: { lineItem: I_CART_ASSIST_LINE_ITEM, variantTitle: string, lineType: LINE_ITEM_TYPE}}) => {
      const type = action.payload.lineType
      const field = type === LINE_ITEM_TYPE.EXISTING ? 'existingLineItems' : 'newLineItems'
      return {
        ...state,
        [field]: state[field].map(lineItem => {
          if (lineItem.variant.id === action.payload.lineItem.variant.id) {
            const newVariant = lineItem.variant.product.variants.find(
              v => v.title === action.payload.variantTitle
            );
            // ? Updating a products's variant will make its quantity to 1
            if (newVariant) {
              return {
                ...lineItem,
                quantity: 1,
                variant: {
                  ...newVariant,
                  product: lineItem.variant.product
                }
              };
            }
          }
          return lineItem;
        })
      };
    },
    [CART_ASSIST_REMOVE_LINE_ITEM]: (state: CartAssistData, action: { payload: { lineItem: I_CART_ASSIST_LINE_ITEM, type: LINE_ITEM_TYPE} }) => {
      const type = action.payload.type
      const field = type === LINE_ITEM_TYPE.EXISTING ? 'existingLineItems' : 'newLineItems'
      const removedFromExistingLineItem = (type === LINE_ITEM_TYPE.EXISTING) ? [...state.removedFromExistingLineItem, action.payload.lineItem] : [...state.removedFromExistingLineItem]

      return {
        ...state,
        [field]: state[field].filter((value) => value.variant.id != action.payload.lineItem.variant.id),
        removedFromExistingLineItem
      }
    },
    [CART_ASSIST_SET_CHANGES_EXIST]: (state: CartAssistData, action: { payload: boolean }) => {
      return {
        ...state,
        changesExists: action.payload
      }
    },
    [CART_ASSIST_CLEAR_ALL_LINE_ITEMS]: (state: CartAssistData) => {
      return {
        ...state,
        newLineItems: [],
        existingLineItems: [],
        removedFromExistingLineItem: []
      }
    },
    [CART_ASSIST_CLEAR_ALL_REMOVED_LINE_ITEMS]: (state: CartAssistData) => {
      return {
        ...state,
        removedFromExistingLineItem: []
      }
    },
  },
  initialWebflowDataState,
);
