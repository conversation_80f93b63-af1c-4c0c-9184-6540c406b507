import {createReducer} from 'apptile-core';
import {
  CREATE_STREAM,
  CREATE_STREAM_SUCCESS,
  Comment<PERSON>rop,
  FETCHED_LIVELY_CREDS,
  FETCH_FACEBOOK_PAGES,
  <PERSON>ETCH_FACEBOOK_PAGES_FAILED,
  <PERSON><PERSON><PERSON>_FACEBOOK_PAGES_SUCCESS,
  <PERSON><PERSON><PERSON>_FACEBOOK_TOKEN_FAILED,
  <PERSON>ET<PERSON>_FACEBOOK_TOKEN_SUCCESS,
  LIVELY_LOGIN_FAILED,
  LIVELY_LOGIN_SUCCESS,
  LIVELY_LOGOUT_SUCCESS,
  LIVELY_REGISTER,
  LIVELY_REGISTER_FAILED,
  LIVELY_REGISTER_STATUS,
  LIVELY_REGISTER_SUCCESS,
  ADD_PINNED_COMMENT,
  REMOVE_PINNED_COMMENT,
  LIVELY_LOGIN,
  UPDATE_CREATE_STREAM_META,
  DISPLAY_STREAM_DELETE_CONFIRM,
  CLOSE_STREAM_DELETE_CONFIRM,
  IDisplayStreamDeleteConfirmPayload,
  <PERSON>LETE_STREAM_SUCCESS,
  DELETE_STREAM_FAILED,
  <PERSON>LETE_STREAM,
  LIVELY_FETCH_PAST_STREAMS_SUCCESS,
  LIVELY_FETCH_PAST_STREAMS_FAILED,
  IPastStreamsResponse,
  LIVELY_FETCH_PAST_STREAMS,
  LIVELY_FETCH_UPCOMING_STREAMS,
  LIVELY_FETCH_UPCOMING_STREAMS_SUCCESS,
  LIVELY_FETCH_UPCOMING_STREAMS_FAILED,
  IActiveStreamsResponse,
  LIVELY_FETCH_IN_PROGRESS_STREAMS,
  LIVELY_FETCH_IN_PROGRESS_STREAMS_SUCCESS,
  LIVELY_FETCH_IN_PROGRESS_STREAMS_FAILED,
  LIVELY_LOGIN_TRIGGERED,
  CREATE_STREAM_FAILED,
  DELETE_UPCOMING_STREAM,
  errorPayload,
  FETCH_INSTAGRAM_PAGES_SUCCESS,
  FETCH_INSTAGRAM_PAGES_FAILED,
  FETCH_INSTAGRAM_TOKEN_SUCCESS,
  FETCH_INSTAGRAM_TOKEN_FAILED,
  FETCH_INSTAGRAM_PAGES,
} from '../actions/liveSellingActions';
import _ from 'lodash';
import {ILiveStream} from '../api/LivelyApiTypes';

export type LiveSellingData = {
  auth: {
    status: 'PENDING' | 'SUCCESS' | 'FAILED' | 'LOADING';
    registrationStatus: 'PENDING' | 'SUCCESS' | 'FAILED' | 'REGISTERING';
    registerError: string;
    loginError: string;
    authToken: string;
    livelyUser: any;
    companyInfo: any;
  };
  facebookTokens: {
    [key: string]: {
      error: null | string;
      token: string;
    };
  };
  instagramTokens: {
    [key: string]: {
      error: null | string;
      token: string;
    };
  };
  livelyUserEmail: string;
  livelyAppId: string;
  facebookChannelLoading: boolean;
  facebookChannelAuths: string[];
  facebookChannelFound: boolean | null;
  streamCreationInProgress: boolean;
  streamCreationMeta: any;
  streamCreationStatus: 'PENDING' | 'SUCCESS' | 'FAILED' | 'LOADING';
  streamCreationError: string;
  isFacebookConnectionNetworkError: boolean;
  pinnedComments: {
    [key: string]: CommentProp[];
  };
  streamCreated: boolean;
  deleteStream: {
    displayDeleteStreamConfirm: boolean;
    loading: boolean;
  };
  inProgressStreams: {
    loading: boolean;
    data: ILiveStream[];
    errorMessage: string;
    isNetworkError: boolean;
  };
  upcomingStreams: {
    loading: boolean;
    data: ILiveStream[];
    errorMessage: string;
    isNetworkError: boolean;
  };
  pastStreams: {
    loading: boolean;
    data: ILiveStream[];
    recordings: any;
    errorMessage: string;
    isNetworkError: boolean;
  };
};

const initialWebflowDataState: LiveSellingData = {
  auth: {
    status: 'PENDING',
    registrationStatus: 'PENDING',
    registerError: '',
    loginError: '',
    authToken: '',
    livelyUser: null,
  },
  livelyUserEmail: '',
  facebookTokens: {},
  instagramTokens: {},
  facebookChannelLoading: true,
  facebookChannelAuths: [],
  facebookChannelFound: null,
  streamCreationInProgress: false,
  streamCreationError: '',
  streamCreationMeta: {},
  streamCreationStatus: 'PENDING',
  pinnedComments: {},
  livelyAppId: '',
  streamCreated: false,
  isFacebookConnectionNetworkError: false,
  deleteStream: {
    displayDeleteStreamConfirm: false,
    loading: false,
  },
  upcomingStreams: {
    loading: false,
    data: [],
    errorMessage: '',
    isNetworkError: false,
  },
  inProgressStreams: {
    loading: false,
    data: [],
    errorMessage: '',
    isNetworkError: false,
  },
  pastStreams: {
    loading: false,
    recordings: [],
    data: [],
    errorMessage: '',
    isNetworkError: false,
  },
};

export const LiveSellingReducer = createReducer<LiveSellingData>(
  {
    [FETCHED_LIVELY_CREDS]: (state: LiveSellingData, action: {payload: {email: string}}) => {
      return {
        ...state,
        livelyUserEmail: action.payload?.email,
      };
    },
    [LIVELY_REGISTER]: (state: LiveSellingData) => {
      return {
        ...state,
        auth: {
          ...state.auth,
          registrationStatus: 'REGISTERING',
          registrationState: 'Generating credentials for broadcaster app',
        },
      };
    },
    [LIVELY_REGISTER_STATUS]: (state: LiveSellingData, action: {payload: {registrationState: string}}) => {
      return {
        ...state,
        auth: {
          ...state.auth,
          registrationStatus: 'REGISTERING',
          registrationState: action.payload.registrationState,
        },
      };
    },
    [LIVELY_REGISTER_SUCCESS]: (state: LiveSellingData) => {
      return {
        ...state,
        auth: {
          ...state.auth,
          registrationStatus: 'SUCCESS',
          registerError: '',
        },
      };
    },
    [LIVELY_REGISTER_FAILED]: (state: LiveSellingData, action: {payload: {registerError: string}}) => {
      return {
        ...state,
        auth: {
          ...state.auth,
          registrationStatus: 'FAILED',
          registerError: action.payload?.registerError,
        },
      };
    },
    [LIVELY_LOGIN]: (state: LiveSellingData, action: {payload: {email: string}}) => {
      return {
        ...state,
        livelyUserEmail: action.payload?.email,
      };
    },

    [LIVELY_LOGIN_FAILED]: (state: LiveSellingData, action: {payload: {loginError: string}}) => {
      return {
        ...state,
        auth: {
          ...state.auth,
          status: 'FAILED',
          loginError: action.payload?.loginError,
        },
      };
    },
    [LIVELY_LOGIN_SUCCESS]: (
      state: LiveSellingData,
      action: {payload: {authToken: string; livelyAppId: string; livelyUser: any}},
    ) => {
      return {
        ...state,
        livelyAppId: action.payload?.livelyAppId,
        auth: {
          status: 'SUCCESS',
          loginError: '',
          authToken: action.payload?.authToken,
          livelyUser: action.payload?.livelyUser,
        },
      };
    },
    [LIVELY_LOGIN_TRIGGERED]: (state: LiveSellingData) => {
      return {
        ...state,
        auth: {
          status: 'LOADING',
          loginError: '',
        },
      };
    },
    [LIVELY_LOGOUT_SUCCESS]: (state: LiveSellingData) => {
      return {
        ...state,
        auth: {
          ...state.auth,
          status: 'PENDING',
          authToken: '',
          livelyUser: '',
        },
      };
    },

    [FETCH_FACEBOOK_PAGES]: (state: LiveSellingData) => {
      return {
        ...state,
        facebookChannelLoading: true,
        isFacebookConnectionNetworkError: false,
      };
    },
    [FETCH_FACEBOOK_PAGES_SUCCESS]: (state: LiveSellingData, action: {payload: any}) => {
      return {
        ...state,
        facebookChannelLoading: false,
        facebookChannelAuths: action.payload,
        facebookChannelFound: true,
        isFacebookConnectionNetworkError: false,
      };
    },
    [FETCH_FACEBOOK_PAGES_FAILED]: (
      state: LiveSellingData,
      action: {payload: {noFacebookLogin: boolean; isNetworkError: boolean}},
    ) => {
      return {
        ...state,
        facebookChannelLoading: false,
        facebookChannelFound: _.isNil(action.payload?.noFacebookLogin) ? null : !action.payload?.noFacebookLogin,
        isFacebookConnectionNetworkError: action.payload.isNetworkError,
      };
    },
    [FETCH_FACEBOOK_TOKEN_SUCCESS]: (
      state: LiveSellingData,
      action: {payload: {token: string; streamingId: string}},
    ) => {
      return {
        ...state,
        facebookTokens: {
          ...state.facebookTokens,
          [action.payload.streamingId]: {
            ...action.payload,
            error: null,
          },
        },
      };
    },
    [FETCH_FACEBOOK_TOKEN_FAILED]: (
      state: LiveSellingData,
      action: {payload: {facebookTokenError: string; streamingId: string}},
    ) => {
      return {
        ...state,
        facebookTokens: {
          ...state.facebookTokens,
          [action.payload.streamingId]: {
            token: null,
            error: action.payload.facebookTokenError,
          },
        },
      };
    },

    [FETCH_INSTAGRAM_PAGES]: (state: LiveSellingData) => {
      return {
        ...state,
        instagramChannelLoading: true,
        isInstagramConnectionNetworkError: false,
      };
    },
    [FETCH_INSTAGRAM_PAGES_SUCCESS]: (state: LiveSellingData, action: {payload: any}) => {
      return {
        ...state,
        instagramChannelLoading: false,
        instagramChannelAuths: action.payload,
        instagramChannelFound: true,
        isInstagramConnectionNetworkError: false,
      };
    },
    [FETCH_INSTAGRAM_PAGES_FAILED]: (
      state: LiveSellingData,
      action: {payload: {noInstagramLogin: boolean; isNetworkError: boolean}},
    ) => {
      return {
        ...state,
        instagramChannelLoading: false,
        instagramChannelFound: _.isNil(action.payload?.noInstagramLogin) ? null : !action.payload?.noInstagramLogin,
        isInstagramConnectionNetworkError: action.payload.isNetworkError,
      };
    },
    [FETCH_INSTAGRAM_TOKEN_SUCCESS]: (
      state: LiveSellingData,
      action: {payload: {token: string; streamingId: string}},
    ) => {
      return {
        ...state,
        instagramTokens: {
          ...state.instagramTokens,
          [action.payload.streamingId]: {
            ...action.payload,
            error: null,
          },
        },
      };
    },
    [FETCH_INSTAGRAM_TOKEN_FAILED]: (
      state: LiveSellingData,
      action: {payload: {instagramTokenError: string; streamingId: string}},
    ) => {
      return {
        ...state,
        instagramTokens: {
          ...state.instagramTokens,
          [action.payload.streamingId]: {
            token: null,
            error: action.payload.instagramTokenError,
          },
        },
      };
    },

    [CREATE_STREAM]: (state: LiveSellingData) => {
      return {
        ...state,
        streamCreationInProgress: true,
        streamCreationError: '',
      };
    },
    [CREATE_STREAM_SUCCESS]: (state: LiveSellingData, action: any) => {
      return {
        ...state,
        streamCreationInProgress: false,
        streamCreationMeta: action.payload,
        streamCreated: true,
      };
    },
    [CREATE_STREAM_FAILED]: (state: LiveSellingData, streamCreationError: string) => {
      return {
        ...state,
        streamCreationInProgress: false,
        streamCreationError: streamCreationError,
      };
    },

    [ADD_PINNED_COMMENT]: (state: LiveSellingData, action: {payload: {comment: CommentProp; streamingId: string}}) => {
      return {
        ...state,
        pinnedComments: {
          ...state.pinnedComments,
          [action.payload.streamingId]: action.payload.comment,
        },
      };
    },
    [REMOVE_PINNED_COMMENT]: (state: LiveSellingData, action: {payload: {streamingId: string}}) => {
      return {
        ...state,
        pinnedComments: {
          [action.payload.streamingId]: null,
        },
      };
    },

    [UPDATE_CREATE_STREAM_META]: (state: LiveSellingData, action: {payload: any}) => {
      return {
        ...state,
        streamCreated: false,
        streamCreationMeta: {
          ...state.streamCreationMeta,
          ...action.payload,
        },
      };
    },
    [DISPLAY_STREAM_DELETE_CONFIRM]: (
      state: LiveSellingData,
      action: {payload: IDisplayStreamDeleteConfirmPayload},
    ) => {
      return {
        ...state,
        deleteStream: {
          streamId: action.payload?.streamId,
          feedFileId: action.payload?.feedFileId,
          displayDeleteStreamConfirm: true,
          onCreateClose: action.payload?.onCreateClose
        },
      };
    },
    [CLOSE_STREAM_DELETE_CONFIRM]: (state: LiveSellingData) => {
      return {
        ...state,
        deleteStream: {
          displayDeleteStreamConfirm: false,
        },
      };
    },

    [DELETE_STREAM]: (state: LiveSellingData) => {
      return {
        ...state,
        deleteStream: {
          displayDeleteStreamConfirm: true,
          loading: true,
        },
      };
    },
    [DELETE_UPCOMING_STREAM]: (state: LiveSellingData) => {
      return {
        ...state,
        deleteStream: {
          displayDeleteStreamConfirm: true,
          loading: true,
        },
      };
    },
    [DELETE_STREAM_SUCCESS]: (state: LiveSellingData) => {
      return {
        ...state,
        deleteStream: {
          displayDeleteStreamConfirm: false,
          loading: false,
        },
      };
    },
    [DELETE_STREAM_FAILED]: (state: LiveSellingData) => {
      return {
        ...state,
        deleteStream: {
          displayDeleteStreamConfirm: false,
          loading: false,
        },
      };
    },

    [LIVELY_FETCH_PAST_STREAMS]: (state: LiveSellingData) => {
      return {
        ...state,
        streamCreated: false,
        pastStreams: {
          ...state.pastStreams,
          loading: true,
          errorMessage: '',
          isNetworkError: false,
        },
      };
    },
    [LIVELY_FETCH_PAST_STREAMS_SUCCESS]: (state: LiveSellingData, action: {payload: IPastStreamsResponse}) => {
      return {
        ...state,
        streamCreated: false,
        pastStreams: {
          ...state.pastStreams,
          data: action.payload?.data,
          recordings: action.payload?.recordings,
          loading: false,
          errorMessage: '',
          isNetworkError: false,
        },
      };
    },
    [LIVELY_FETCH_PAST_STREAMS_FAILED]: (state: LiveSellingData, action: {payload: errorPayload}) => {
      return {
        ...state,
        streamCreated: false,
        pastStreams: {
          ...state.pastStreams,
          loading: false,
          errorMessage: action.payload.errorMessage,
          isNetworkError: action.payload.isNetworkError,
        },
      };
    },

    [LIVELY_FETCH_UPCOMING_STREAMS]: (state: LiveSellingData) => {
      return {
        ...state,
        streamCreated: false,
        upcomingStreams: {
          ...state.upcomingStreams,
          loading: true,
          errorMessage: '',
          isNetworkError: false,
        },
      };
    },
    [LIVELY_FETCH_UPCOMING_STREAMS_SUCCESS]: (state: LiveSellingData, action: {payload: IActiveStreamsResponse}) => {
      return {
        ...state,
        streamCreated: false,
        upcomingStreams: {
          ...state.upcomingStreams,
          data: action.payload?.data,
          loading: false,
          errorMessage: '',
          isNetworkError: false,
        },
      };
    },
    [LIVELY_FETCH_UPCOMING_STREAMS_FAILED]: (state: LiveSellingData, action: {payload: errorPayload}) => {
      return {
        ...state,
        streamCreated: false,
        upcomingStreams: {
          ...state.upcomingStreams,
          loading: false,
          errorMessage: action.payload.errorMessage,
          isNetworkError: action.payload.isNetworkError,
        },
      };
    },

    [LIVELY_FETCH_IN_PROGRESS_STREAMS]: (state: LiveSellingData) => {
      return {
        ...state,
        streamCreated: false,
        inProgressStreams: {
          ...state.inProgressStreams,
          loading: true,
          errorMessage: '',
          isNetworkError: false,
        },
      };
    },
    [LIVELY_FETCH_IN_PROGRESS_STREAMS_SUCCESS]: (state: LiveSellingData, action: {payload: IActiveStreamsResponse}) => {
      return {
        ...state,
        streamCreated: false,
        inProgressStreams: {
          ...state.inProgressStreams,
          data: action.payload?.data,
          loading: false,
          errorMessage: '',
          isNetworkError: false,
        },
      };
    },
    [LIVELY_FETCH_IN_PROGRESS_STREAMS_FAILED]: (state: LiveSellingData, action: {payload: errorPayload}) => {
      return {
        ...state,
        streamCreated: false,
        inProgressStreams: {
          ...state.inProgressStreams,
          loading: false,
          errorMessage: action.payload.errorMessage,
          isNetworkError: action.payload.isNetworkError,
        },
      };
    },
  },
  initialWebflowDataState,
);
