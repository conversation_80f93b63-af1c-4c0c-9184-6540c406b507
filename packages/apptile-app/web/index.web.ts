import { shouldInitWebLogrocket } from './globalvariables';
console.log('glvarcheck', shouldInitWebLogrocket);
import {main} from './reactLauncher';

import livelyScript from './lively.txt';
import shoppableFeedScript from './lively-shoppableFeed.txt';

main();

const _reloadLively = new Function(livelyScript);
window.reloadLively = () => {
  try {
    _reloadLively();
  } catch(err) {
    logger.error("Failed when reloading lively: ", err);
  }
}

const _reloadShoppableFeeds = new Function(shoppableFeedScript);
window.reloadShoppableFeeds = () => {
  try {
    _reloadShoppableFeeds();
  } catch (err) {
    logger.error("Failed when reloading shoppablefeeds: ", err);
  }
}
