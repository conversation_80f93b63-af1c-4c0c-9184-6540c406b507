import React, {useEffect, useState} from 'react';
import {Image, Pressable, StyleSheet, Text, View} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';

import theme from '../styles-v2/theme';
import {selectSelectedPluginConfig, selectSelectedPluginPageId} from '../selectors/EditorSelectors';
import {moduleVariantUpdate, saveModuleVariant, sendTileAnalytics, setOpenPremiumModal} from '../actions/editorActions';
import {useCallbackRef} from 'apptile-core';
import commonStyles from '../styles-v2/commonStyles';
import PopoverComponent from '../components-v2/base/Popover';
import TilesApi from '../api/TilesApi';
import Button from './base/Button';
import {currentPlanFeaturesSelector} from '../selectors/FeatureGatingSelector';
import {allAvailablePlans} from 'apptile-core';
import {ITileVariantSaveInterface} from '../api/ApiTypes';
import {Feather, MaterialCommunityIcons} from 'apptile-core';
import Tooltip from './base/SimpleTooltip';
import RedDot from './base/RedDot';
import {EditorRootState} from '../store/EditorRootState';
import {selectModuleByUUID, RecordSerializer} from 'apptile-core';
import imagePlanMapping from '../common/featureGatingConstants';

type Variant = (Omit<ITileVariantSaveInterface, 'data'> & {customised: boolean}) | null;

type VariantsProps = {
  hidden?: boolean;
};

const VariantSelection: React.FC<VariantsProps> = ({hidden}) => {
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const pluginId = moduleConfig?.get('id');
  const pageId = useSelector(state => selectSelectedPluginPageId(state));

  const dispatch = useDispatch();

  const onVariantSelect = useCallbackRef((variant: any) => {
    dispatch(moduleVariantUpdate(pluginId, pageId, variant));
  });

  const onVariantSave = useCallbackRef(() => {
    dispatch(saveModuleVariant(pluginId, pageId, moduleUUID));
    setConfirmModal(false);
  });

  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  const moduleVariantSelected = moduleInstanceConfig?.get('variantSelected');

  const [showPopover, setShowPopover] = useState(false);

  const [confirmModal, setConfirmModal] = useState(false);

  const [tileVariants, setTileVariants] = useState(null);
  const [variantLoading, setVariantLoading] = useState(true);
  const [changesDone, setChangesDone] = useState(moduleInstanceConfig?.get('changesDone') ?? true);
  const [variantGating, setVariantGating] = useState(
    moduleInstanceConfig?.get('variantGating') ?? allAvailablePlans.CORE,
  );
  const [selectedVariant, setSelectedVariant] = useState<Variant>(null);
  const [isLoading, setLoading] = useState(true);
  const [isDeleted, setIsDeleted] = useState(false);
  const [isApptileUser, setIsApptileUser] = useState(false);
  const {userFetched, user} = useSelector((state: EditorRootState) => state.user);
  const [tileDetails, setTileDetails] = useState(null);
  const moduleRecord = useSelector(state => selectModuleByUUID(state, moduleUUID));

  useEffect(() => {
    if (userFetched && user?.email && typeof user?.email === 'string') {
      if (user.email.endsWith('@apptile.io') || user.email.endsWith('@apptile.com')) setIsApptileUser(true);
    }
  }, [user.email, userFetched]);

  useEffect(() => {
    if (moduleUUID) {
      TilesApi.getTileStatus(moduleUUID)
        .then(resp => {
          setLoading(false);
          if (resp.data.status === 'deleted') {
            setIsDeleted(true);
          } else {
            setIsDeleted(false);
          }
          setTileDetails(resp?.data?.tile);
        })
        .catch(() => {
          setIsDeleted(false);
          setLoading(false);
        });
    }
  }, [moduleUUID]);
  // console.log('moduleInstanceConfig', moduleInstanceConfig, selectedVariant);

  useEffect(() => {
    if (moduleUUID) {
      if (!tileVariants || !Array.isArray(tileVariants)) {
        setVariantLoading(true);
        TilesApi.getTileVariants(moduleUUID, hidden)
          .then(resp => {
            setTileVariants(resp.data.items);
            setVariantLoading(false);
            setSelectedVariant(
              resp?.data?.items?.find((e: any) => e.id === moduleInstanceConfig?.get('variantSelected')),
            );
          })
          .catch(() => {
            setTileVariants(null);
            setVariantLoading(false);
          });
      } else {
        setSelectedVariant(tileVariants?.find((e: any) => e.id === moduleInstanceConfig?.get('variantSelected')));
      }
      setChangesDone(moduleInstanceConfig?.get('changesDone'));
      setVariantGating(moduleInstanceConfig?.get('variantGating'));
    }
  }, [hidden, moduleInstanceConfig, moduleUUID, moduleVariantSelected, tileVariants]);
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);

  useEffect(() => {
    if (showPopover) {
      dispatch(sendTileAnalytics(moduleUUID, 'editor:tile_variantsListViewed', {}));
    }
  }, [dispatch, moduleUUID, showPopover]);

  return (
    <>
      {!isLoading && isDeleted && isApptileUser && (
        <View style={styles.errorStrip}>
          <View style={[styles.rowContainer, {justifyContent: 'center', alignItems: 'center'}]}>
            <Feather name={'alert-triangle'} color={'#D20000'} size={26} />
            <Text style={{fontSize: 14, lineHeight: 16, fontWeight: '500', marginLeft: 8}}>Tile updated</Text>
          </View>
          <Text style={[commonStyles.baseText, {textAlign: 'center', color: '#000', fontSize: 12}]}>
            An updated Tile with new features is now available. If you prefer to stick with your current Tile, it will
            continue to function as usual. To access the new features, simply drag and drop the Tile again.
          </Text>
        </View>
      )}
      {!isLoading && !variantLoading && !!tileVariants?.length && (
        <View style={styles.variantsStrip}>
          {changesDone && confirmModal && (
            <View style={styles.confirmMessage}>
              <Text style={[commonStyles.baseText, {lineHeight: 21}]}>
                This overrides your current variant. Do you wish to continue?
              </Text>
              <View style={styles.confirmMessageButtons}>
                <Button
                  size={'EXTRA-SMALL'}
                  variant="PILL"
                  color="SECONDARY"
                  onPress={() => {
                    setConfirmModal(false);
                  }}>
                  Discard
                </Button>
                <Button size={'EXTRA-SMALL'} color="CTA" variant={'FILLED-PILL'} onPress={onVariantSave}>
                  {' Agree '}
                </Button>
              </View>
            </View>
          )}
          {!confirmModal && (
            <>
              <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
                  <Text style={commonStyles.heading}>Layout</Text>
                </View>
                {!!tileVariants?.length && tileVariants?.length > 2 && (
                  <Pressable onPress={() => setShowPopover(true)}>
                    <Text style={[commonStyles.baseText, {color: theme.CONTROL_ACTIVE_COLOR}]}>See All</Text>
                  </Pressable>
                )}
              </View>
              <PopoverComponent
                visible={showPopover}
                onVisibleChange={setShowPopover}
                trigger={
                  <Pressable
                    style={[
                      styles.variantsContainer,
                      {
                        borderRadius: 0,
                        gap: 5,
                        justifyContent:
                          !!tileVariants?.length && tileVariants?.length > 2 ? 'space-between' : 'space-around',
                      },
                    ]}>
                    <Tooltip
                      containerStyles={[styles.variant, {width: '24%'}]}
                      tooltip="Try me for free"
                      visible={false}>
                      <Pressable
                        style={[styles.rowContainer, styles.variantItem, {borderRadius: 0}]}
                        onPress={() => {
                          dispatch(
                            sendTileAnalytics(moduleUUID, 'editor:tile_variantSelected', {
                              variantName: 'DEFAULT',
                              variantPlan: 'CORE',
                            }),
                          );
                          onVariantSelect({id: 'default', data: RecordSerializer.stringify(moduleRecord)});
                        }}>
                        <View
                          style={[
                            styles.popoverImageCont,
                            {
                              width: 64,
                              borderColor:
                                moduleInstanceConfig?.get('variantSelected') == 'default'
                                  ? theme.CONTROL_ACTIVE_COLOR
                                  : '#DDD',
                              borderWidth: moduleInstanceConfig?.get('variantSelected') == 'default' ? 2 : 1,
                              backgroundColor:
                                moduleInstanceConfig?.get('variantSelected') == 'default' ? '#f0f6ff' : null,
                              borderRadius: 6,
                              overflow: 'hidden',
                            },
                          ]}>
                          <Image
                            resizeMode="contain"
                            source={{
                              uri:
                                tileDetails?.coverImage ??
                                'https://cdn.apptile.io/ce0249d7-4cae-4e5e-828c-226f982bb0e7/84b99d55-ef9a-48b3-9bf3-e94649ff792c/original.png',
                            }}
                            style={styles.popoverImage}
                          />
                        </View>
                        <View style={[styles.popoverCardText, {padding: 0, paddingTop: 4, justifyContent: 'center'}]}>
                          <Text style={[commonStyles.baseText, styles.variantName]}>Default</Text>
                        </View>
                      </Pressable>
                    </Tooltip>
                    {tileVariants?.slice(0, 3)?.map((e: ITileVariantSaveInterface, index: number) => {
                      const plan = allAvailablePlans[e.gating ?? 'CORE'];
                      const isFeatureDisabled = !currentPlanFeatures.includes(plan ?? allAvailablePlans.CORE);
                      return (
                        <View style={[styles.variant, {width: '24%'}]}>
                          <Pressable
                            style={[styles.rowContainer, styles.variantItem, {borderRadius: 0}]}
                            onPress={() => {
                              dispatch(
                                sendTileAnalytics(moduleUUID, 'editor:tile_variantSelected', {
                                  variantName: e?.name,
                                  variantPlan: e?.gating,
                                }),
                              );
                              onVariantSelect(e);
                            }}>
                            <View
                              style={[
                                styles.popoverImageCont,
                                {
                                  width: 64,
                                  borderColor: e.id == selectedVariant?.id ? theme.CONTROL_ACTIVE_COLOR : '#DDD',
                                  borderWidth: e.id == selectedVariant?.id ? 2 : 1,
                                  backgroundColor: e.id == selectedVariant?.id ? '#f0f6ff' : null,
                                  borderRadius: 6,
                                  overflow: 'hidden',
                                },
                              ]}>
                              <Image resizeMode="contain" source={{uri: e.coverImage}} style={styles.popoverImage} />
                            </View>
                            <View
                              style={[styles.popoverCardText, {padding: 0, paddingTop: 4, justifyContent: 'center'}]}>
                              <Text style={[commonStyles.baseText, styles.variantName]}>{e.name}</Text>
                            </View>
                          </Pressable>
                        </View>
                      );
                    })}
                  </Pressable>
                }
                positions={['left']}>
                <View
                  style={[
                    styles.variantsContainer,
                    styles.variantsPopover,
                    {
                      height: Math.ceil((tileVariants?.length ?? 2) / 2) * 125 - 12 + 30 + 2,
                    },
                  ]}>
                  <View style={{width: '100%', justifyContent: 'center'}}>
                    <Text style={commonStyles.heading}>Select a Tile Design</Text>
                  </View>
                  {tileVariants?.map((e: ITileVariantSaveInterface) => {
                    const plan = allAvailablePlans[e.gating ?? 'CORE'];
                    const isFeatureDisabled = !currentPlanFeatures.includes(plan ?? allAvailablePlans.CORE);
                    return (
                      <Tooltip containerStyles={styles.variant} tooltip="Try me for free" visible={isFeatureDisabled}>
                        <Pressable
                          style={[
                            styles.rowContainer,
                            styles.variantItem,
                            {
                              borderColor: e.id == selectedVariant?.id ? theme.CONTROL_ACTIVE_COLOR : '#F5F4F0',
                              borderWidth: e.id == selectedVariant?.id ? 2 : 1,
                            },
                          ]}
                          onPress={() => {
                            setShowPopover(false);
                            dispatch(
                              sendTileAnalytics(moduleUUID, 'editor:tile_variantSelected', {
                                variantName: e?.name,
                                variantPlan: e?.gating,
                              }),
                            );
                            onVariantSelect(e);
                          }}>
                          <View
                            style={[
                              styles.popoverImageCont,
                              {backgroundColor: e.id == selectedVariant?.id ? '#f0f6ff' : null},
                            ]}>
                            <Image resizeMode="contain" source={{uri: e.coverImage}} style={styles.popoverImage} />
                            {isFeatureDisabled && (
                              <Image
                                source={imagePlanMapping['SMALL']}
                                resizeMode="contain"
                                style={{width: 22, height: 22, position: 'absolute', top: 5, right: 5}}
                              />
                            )}
                          </View>
                          <View style={styles.popoverCardText}>
                            <Text style={[commonStyles.baseText, styles.variantName]}>{e.name}</Text>
                          </View>
                        </Pressable>
                      </Tooltip>
                    );
                  })}
                </View>
              </PopoverComponent>
            </>
          )}
          {changesDone && !confirmModal && (
            <View>
              {!selectedVariant?.customised && (
                <Button
                  color="PRIMARY"
                  variant="OUTLINED"
                  onPress={() => {
                    if (!currentPlanFeatures.includes(variantGating ?? allAvailablePlans.CORE)) {
                      dispatch(setOpenPremiumModal(true, variantGating));
                    } else onVariantSave();
                    dispatch(
                      sendTileAnalytics(moduleUUID, 'editor:tile_saveTileVariantClick', {
                        variantName: selectedVariant?.name,
                        variantPlan: selectedVariant?.gating,
                      }),
                    );
                  }}>
                  <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
                    {!currentPlanFeatures.includes(variantGating ?? allAvailablePlans.CORE) && (
                      <Image source={imagePlanMapping['SMALL']} resizeMode="contain" style={{width: 20, height: 20}} />
                    )}
                    <Text>Save My Style</Text>
                  </View>
                </Button>
              )}
              {selectedVariant?.customised && (
                <Button
                  color="PRIMARY"
                  variant="OUTLINED"
                  onPress={() => {
                    setConfirmModal(true);
                  }}>
                  Update My Style
                </Button>
              )}
            </View>
          )}
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  root: {
    width: '100vw',
    height: '100vh',
    flexDirection: 'row',
    backgroundColor: theme.PRIMARY_BACKGROUND,
  },
  platformIconsContainer: {
    flexDirection: 'row',
    width: 185,
    justifyContent: 'center',
    marginTop: 20,
    gap: 20,
  },
  platformIcon: {
    width: 32,
    height: 32,
  },
  platformIconWrapper: {
    borderWidth: 1,
    borderColor: '#000000',
    padding: 14,
    borderRadius: 8,
  },
  rowContainer: {
    flexDirection: 'row',
  },
  sidebarHeader: {
    paddingTop: '1vh',
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  sidebarHeaderRight: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'center',
    zIndex: 1,
  },
  previewButton: {
    marginRight: 4,
    alignItems: 'center',
  },
  topButtons: {
    flex: 1,
  },
  bottomButtons: {
    width: '40%',
  },
  sidebarRight: {
    width: '100%',
    height: '100%',
    paddingLeft: 24,
    paddingRight: 12,
  },
  QRContainer: {
    alignItems: 'flex-end',
    paddingVertical: '1vh',
  },
  QRCaption: {
    fontSize: 14,
    width: 184,
    textAlign: 'center',
    marginTop: 20,
    lineHeight: 20,
  },
  propertyEditorContainer: {
    backgroundColor: '#FFFFFF',
  },
  bottomButtonsWrapper: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    paddingLeft: 20,
    paddingRight: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    boxShadow: '0px -1px 8px rgba(0, 0, 0, 0.09)',
  },
  contentSettingsContainer: {
    flex: 1,
    paddingBottom: 5,
    paddingTop: '1vh',
  },
  contentContainer: {
    flexGrow: 0,
    flexShrink: 0,
    paddingBottom: 20,
    paddingTop: '1vh',
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
  },
  errorStrip: {
    backgroundColor: theme.INPUT_BACKGROUND,
    minHeight: 114,
    marginTop: 10,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    gap: 10,
  },
  variantsStrip: {
    marginTop: 12,
    marginBottom: 15,
    gap: 10,
  },
  variantsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    gap: 5,
    borderColor: theme.INPUT_BORDER,
    // borderWidth: 1,
    padding: 5,
    borderRadius: 4,
    alignItems: 'center',
  },
  variantsPopover: {
    width: 362,
    maxHeight: 642,
    marginTop: 40,
    overflowY: 'scroll',
    padding: 15,
    paddingRight: 15,
    flexWrap: 'wrap',
    gap: 12,
    backgroundColor: theme.TILE_BACKGROUND,
    borderRadius: 22,
    borderColor: '#EBE9E1',
    borderWidth: 1,
    alignItems: 'flex-start',
    justifyContent: 'flex-end',
  },
  variant: {
    width: '30%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  variantItem: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    flexDirection: 'column',
    borderRadius: 12,
  },
  image: {
    width: 64,
    height: 64,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#DDD',
    backgroundColor: '#fff',
  },
  variantName: {
    fontWeight: '500',
    fontSize: 12,
    color: theme.SECONDARY_COLOR,
  },
  premiumText: {
    paddingHorizontal: 8,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: theme.PREMIUM_BORDER,
    backgroundColor: theme.PREMIUM_BACKGROUND,
    color: theme.PREMIUM_COLOR,
    fontWeight: '500',
    fontSize: 10,
    lineHeight: 14,
    marginLeft: 10,
  },
  smallPremiumText: {
    fontWeight: '400',
    fontSize: 8,
    lineHeight: 10,
    paddingHorizontal: 4,
  },
  popoverImageCont: {
    width: '100%',
    aspectRatio: 1,
  },
  popoverImage: {
    width: '100%',
    height: '100%',
  },
  popoverCardText: {
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  confirmMessage: {
    padding: 10,
    width: '100%',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#BFBFBF',
  },
  confirmMessageButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    width: '100%',
    paddingTop: 14,
    gap: 10,
  },
});
export default VariantSelection;
