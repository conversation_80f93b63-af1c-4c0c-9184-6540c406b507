import React, {useCallback, useEffect, useRef, useState} from 'react';
import {GestureResponderEvent, StyleSheet, ScrollView, View, StyleProp, ViewStyle} from 'react-native';

import {VARIANTS, COLORS, SIZES} from '@/root/web/styles-v2/types';
import Button from '../base/Button';
import Menu from './Menu';

type TabPressableProps = {
  index?: number;
  activeVariant?: VARIANTS;
  inactiveVariant?: VARIANTS;
  color?: COLORS;
  size?: SIZES;
  text: string;
  isActive: boolean;
  id?: string;
  onPress: (event: GestureResponderEvent) => void;
  scrollViewRef?: React.RefObject<ScrollView>;
  activeOpaque?: boolean;
  inactiveOpaque?: boolean;
  activeBackgroundColor?: COLORS;
  inactiveBackgroundColor?: COLORS;
};
type TabDetails = {
  title: string;
  component: React.ReactElement;
  disableScroll?: boolean;
  id?: string;
};

type TabsProps = {
  activeVariant?: VARIANTS;
  inactiveVariant?: VARIANTS;
  activeColor?: COLORS;
  inactiveColor?: COLORS;
  size?: SIZES;
  tabs: TabDetails[];
  noOfLines?: number;
  wrapperStyles?: StyleProp<ViewStyle>;
  rootStyles?: StyleProp<ViewStyle>;
  activeOpaque?: boolean;
  inactiveOpaque?: boolean;
  activeBackgroundColor?: COLORS;
  inactiveBackgroundColor?: COLORS;
  currentTab?: string;
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  headerWrapper: {
    flexGrow: 0,
    flexShrink: 1,
    flexBasis: 'auto',
    padding: 4,
    zIndex: 1,
    gap: 5,
    borderRadius: 6,
  },
  nonScrollHeaderWrapper: {
    flexWrap: 'wrap',
  },
  headerContentContainer: {
    flexDirection: 'row',
  },
  tab: {
    flexGrow: 1,
    flexShrink: 0,
    flexBasis: 'auto',
  },
  firstTab: {
    marginLeft: 0,
  },
  trigger: {},
  contentWrapper: {
    flex: 1,
  },
  contentContainer: {
    // padding: 8,
  },
  opaque: {
    opacity: 0,
  },
});

const TabPressable: React.FC<TabPressableProps> = ({
  index,
  activeVariant = 'TAB',
  inactiveVariant = 'TEXT',
  color,
  size,
  text,
  isActive,
  onPress,
  scrollViewRef,
  id,
  activeOpaque = false,
  inactiveOpaque = false,
  activeBackgroundColor,
  inactiveBackgroundColor,
}) => {
  const ref = useRef<View>(null);

  const onPressHOF = useCallback(
    (e: GestureResponderEvent) => {
      onPress?.(e);
      const scrollView = scrollViewRef?.current;
      if (scrollView && scrollView.scrollWidth > scrollView.clientWidth)
        ref.current?.scrollIntoView({behavior: 'smooth', block: 'center', inline: 'center'});
    },
    [onPress, scrollViewRef],
  );

  return (
    <Button
      id={id}
      ref={ref}
      variant={isActive ? activeVariant : inactiveVariant}
      color={color}
      size={size}
      containerStyles={[styles.tab, index === 0 && styles.firstTab]}
      opaque={isActive ? activeOpaque : inactiveOpaque}
      backgroundColor={isActive ? activeBackgroundColor : inactiveBackgroundColor}
      onPress={onPressHOF}>
      {text}
    </Button>
  );
};

const Tabs: React.FC<TabsProps> = ({
  activeVariant,
  inactiveVariant,
  activeColor,
  inactiveColor,
  size,
  tabs,
  noOfLines,
  wrapperStyles,
  rootStyles,
  activeOpaque = false,
  inactiveOpaque = false,
  activeBackgroundColor,
  inactiveBackgroundColor,
  currentTab,
}) => {
  const [activeTab, setActiveTab] = useState(currentTab || tabs[0]?.title);
  useEffect(() => {
    if (tabs.filter(t => t.title === activeTab).length === 0) {
      setActiveTab(tabs[0]?.title);
    }
  }, [activeTab, tabs]);

  const headerWrapperRef = useRef<ScrollView>(null);
  const headerViewRef = useRef<View>(null);
  const [showHeaderWrapper, setShowHeaderWrapper] = useState(false);
  const [maxItems, setMaxItems] = useState(0);
  const calculateMaxItems = useCallback(() => {
    const headerElement = headerWrapperRef.current as unknown as HTMLElement;
    const headerView = headerViewRef.current as unknown as HTMLElement;
    let availableSpace = headerElement?.clientWidth;
    if (headerView) {
      availableSpace = headerView?.clientWidth;
    }
    let _maxItems = 0;
    let children = headerElement?.children[0]?.children;
    if (headerView) {
      children = headerView?.children;
    }
    if (children) {
      for (let line = 0; line < (noOfLines || 1); line++) {
        let widthOccupied = line === (noOfLines || 1) - 1 ? 0 : 0;
        for (let i = _maxItems; i < children.length; i++) {
          const d = children[i] as HTMLElement;
          const cs = getComputedStyle(d);
          const headerStyle = getComputedStyle(headerElement || headerView);
          const itemWidth =
            d.getBoundingClientRect().width +
            parseFloat(cs.getPropertyValue('margin-left')) +
            parseFloat(cs.getPropertyValue('margin-right')) +
            parseFloat(headerStyle.getPropertyValue('gap'));
          if (widthOccupied + itemWidth < availableSpace) {
            widthOccupied += itemWidth;
            _maxItems++;
          } else break;
        }
      }
      if (_maxItems >= children.length || (noOfLines && noOfLines > 1)) setMaxItems(_maxItems);
      setShowHeaderWrapper(true);
    }
  }, [noOfLines]);
  useEffect(() => {
    setMaxItems(50);
    setTimeout(() => calculateMaxItems(), 120);
  }, [calculateMaxItems, tabs.length]);

  return (
    <View style={rootStyles}>
      {!maxItems ? (
        <ScrollView
          ref={headerWrapperRef}
          style={[styles.headerWrapper, !showHeaderWrapper && styles.opaque, wrapperStyles]}
          contentContainerStyle={styles.headerContentContainer}
          horizontal
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}>
          {tabs.map((tab, index) => (
            <TabPressable
              key={tab.title}
              id={tab.id}
              index={index}
              activeVariant={activeVariant}
              inactiveVariant={inactiveVariant}
              color={activeTab === tab.title ? activeColor : inactiveColor}
              size={size}
              text={tab.title}
              isActive={activeTab === tab.title}
              onPress={() => setActiveTab(tab.title)}
              scrollViewRef={headerWrapperRef}
              activeOpaque={activeOpaque}
              inactiveOpaque={inactiveOpaque}
              activeBackgroundColor={activeBackgroundColor}
              inactiveBackgroundColor={inactiveBackgroundColor}
            />
          ))}
        </ScrollView>
      ) : (
        <View
          ref={headerViewRef}
          style={[styles.headerWrapper, styles.headerContentContainer, styles.nonScrollHeaderWrapper, wrapperStyles]}>
          {tabs.slice(0, maxItems).map((tab, index) => (
            <TabPressable
              id={tab.id}
              key={tab.title}
              index={index}
              activeVariant={activeVariant}
              inactiveVariant={inactiveVariant}
              color={activeTab === tab.title ? activeColor : inactiveColor}
              size={size}
              text={tab.title}
              isActive={activeTab === tab.title}
              onPress={() => setActiveTab(tab.title)}
              activeOpaque={activeOpaque}
              inactiveOpaque={inactiveOpaque}
              activeBackgroundColor={activeBackgroundColor}
              inactiveBackgroundColor={inactiveBackgroundColor}
            />
          ))}
          {!!tabs.slice(maxItems).length && (
            <Menu
              activeVariant={activeVariant}
              inactiveVariant={inactiveVariant}
              activeColor={activeColor}
              inactiveColor={inactiveColor}
              activeBackgroundColor={activeBackgroundColor}
              inactiveBackgroundColor={inactiveBackgroundColor}
              size={size}
              items={tabs.slice(maxItems).map(tab => ({
                title: tab.title,
                isActive: activeTab === tab.title,
                onPress: () => setActiveTab(tab.title),
              }))}
              trigger={({isMenuOpen, onPress}) => (
                <Button
                  containerStyles={styles.trigger}
                  variant={tabs.slice(maxItems).find(i => i.title === activeTab) ? activeVariant : inactiveVariant}
                  color={tabs.slice(maxItems).find(i => i.title === activeTab) ? activeColor : inactiveColor}
                  size={size}
                  backgroundColor={
                    tabs.slice(maxItems).find(i => i.title === activeTab)
                      ? activeBackgroundColor
                      : inactiveBackgroundColor
                  }
                  icon={isMenuOpen ? 'chevron-up' : 'chevron-down'}
                  onPress={onPress}
                />
              )}
            />
          )}
        </View>
      )}

      {tabs.map(tab =>
        activeTab === tab.title ? (
          tab.disableScroll ? (
            <View
              key={tab.title}
              style={[styles.contentWrapper, styles.contentContainer, !showHeaderWrapper && styles.opaque]}>
              {tab.component}
            </View>
          ) : (
            <ScrollView
              key={tab.title}
              style={styles.contentWrapper}
              contentContainerStyle={[styles.contentContainer, !showHeaderWrapper && styles.opaque]}>
              {tab.component}
            </ScrollView>
          )
        ) : null,
      )}
    </View>
  );
};

export default Tabs;
