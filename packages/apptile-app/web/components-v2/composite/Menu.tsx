import React, {useState} from 'react';
import {StyleSheet, View} from 'react-native';

import theme from '../../styles-v2/theme';
import {getShadowObj} from '../../styles-v2/utils';
import Button from '../base/Button';
import {VARIANTS, COLORS, SIZES} from '@/root/web/styles-v2/types';
type Item = {
  title: string;
  isActive: boolean;
  onPress: () => void;
};

type MenuProps = {
  activeVariant?: VARIANTS;
  inactiveVariant?: VARIANTS;
  activeColor?: COLORS;
  inactiveColor?: COLORS;
  size?: SIZES;
  items: Item[];
  trigger?: React.FC<{isMenuOpen: boolean; onPress: () => void}>;
  activeBackgroundColor?: COLORS;
  inactiveBackgroundColor?: COLORS;
};

const styles = StyleSheet.create({
  root: {
    zIndex: 1,
  },
  item: {
    flexGrow: 1,
    flexShrink: 0,
    flexBasis: 'auto',
    marginVertical: 4,
  },
  float: {
    position: 'absolute',
    top: '105%',
    right: '-5%',
    minWidth: 200,
    backgroundColor: theme.SECONDARY_BACKGROUND,
    paddingVertical: 12,
    paddingHorizontal: 8,
    ...getShadowObj(),
  },
});

const Menu: React.FC<MenuProps> = ({
  activeVariant = 'FILLED-PILL',
  inactiveVariant = 'TEXT',
  activeColor,
  inactiveColor,
  size,
  items,
  trigger: Trigger,
  activeBackgroundColor,
  inactiveBackgroundColor,
}) => {
  const [showMenu, setShowMenu] = useState(false);
  return (
    <View style={styles.root}>
      {!!Trigger && <Trigger isMenuOpen={showMenu} onPress={() => setShowMenu(_showMenu => !_showMenu)} />}
      <View style={[!!Trigger && showMenu && styles.float]}>
        {(!Trigger || (!!Trigger && showMenu)) &&
          items.map(item => (
            <Button
              key={item.title}
              containerStyles={styles.item}
              variant={item.isActive ? activeVariant : inactiveVariant}
              color={item.isActive ? activeColor : inactiveColor}
              size={size}
              backgroundColor={item.isActive ? activeBackgroundColor : inactiveBackgroundColor}
              onPress={() => {
                setShowMenu(false);
                item.onPress?.();
              }}>
              {item.title}
            </Button>
          ))}
      </View>
    </View>
  );
};

export default Menu;
