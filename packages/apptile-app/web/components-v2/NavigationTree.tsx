import React, {useState} from 'react';
import {StyleSheet, Text, View, TouchableOpacity, Pressable} from 'react-native';
import {useDispatch, useSelector, batch} from 'react-redux';

import theme from '../styles-v2/theme';
import {Icon, MaterialCommunityIcons} from 'apptile-core';
import {getNavigationContext} from 'apptile-core';
import {
  editorSetActiveAttachmentId,
  editorSetActiveAttachmentKey,
  EDITOR_SELECTED_PAGE_TYPE,
  EDITOR_SELECT_NAV_COMPONENT,
  setOpenPremiumModal,
} from '@/root/web/actions/editorActions';
import Button from './base/Button';
import AddCustomPage from '../views/editor/components/AddCustomPage';
import standardScreens, {editableScreens, premiumScreens} from '../common/screenConstants';
import _ from 'lodash';
import {selectScreensInNavWithPath} from '../selectors/EditorSelectors';
import {ScreenConfig} from 'apptile-core';
import DeletePageDialog from '../views/editor/components/DeletePageDialog';
import {currentPlanFeaturesSelector} from '../selectors/FeatureGatingSelector';
import {allAvailablePlans} from 'apptile-core';
import {selectMandatoryCheck} from '../selectors/EditorModuleSelectors';
import RedDot from './base/RedDot';
import {Image} from 'react-native';
import imagePlanMapping from '../common/featureGatingConstants';

const styles = StyleSheet.create({
  heading: {
    width: '100%',
    paddingLeft: 24,
    paddingVertical: 16,
  },
  menuItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingVertical: 16,
  },
  menuItemIcon: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 24,
  },
  menuItemText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 12,
    fontWeight: '400',
    color: '#262626',
    paddingLeft: 8,
  },
  menuItemTextActive: {
    color: theme.PRIMARY_COLOR,
  },
  menuItemActive: {
    backgroundColor: '#005be40f',
  },
  treeWrapper: {
    paddingLeft: 14,
    borderLeftWidth: 2,
    borderColor: '#f1f1f1',
  },
  pagesWrapper: {},
  pagesTitleWrapper: {
    width: '100%',
    borderTopColor: '#DADADA',
    borderTopWidth: 1,
    paddingLeft: 24,
    paddingTop: 20,
    paddingBottom: 16,
    justifyContent: 'center',
  },
  defaulPageTitle: {
    fontWeight: '600',
    fontFamily: theme.FONT_FAMILY,
    fontSize: 11,
    color: '#000000',
  },
  defaulPageSubTitle: {
    fontWeight: '400',
    fontSize: 12,
    color: '#858585',
    fontFamily: theme.FONT_FAMILY,
    marginTop: 8,
  },
  rootWrapper: {
    width: '100%',
    flex: 1,
  },
  customPageTitleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuTextWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
  },
});

export type NavigationConfig = {
  config: ScreenConfig;
  path: [string];
};

const NavigationTree = () => {
  const screens: any = useSelector(selectScreensInNavWithPath(['/']));
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isFeatureDisabled = !currentPlanFeatures.includes(allAvailablePlans.PRO);
  const dispatch = useDispatch();
  const [closeCustomizablePages, setCloseCustomizablePages] = useState(false);

  const mandatoryFields = useSelector(selectMandatoryCheck());

  const [showAddPageDialog, setShowAddPageDialog] = useState(false);

  return (
    <View style={styles.rootWrapper}>
      <View>
        <View style={[styles.pagesTitleWrapper, {flexDirection: 'row', justifyContent: 'space-between'}]}>
          <Text style={styles.defaulPageTitle}>CUSTOMIZABLE PAGES</Text>
          <Pressable onPress={() => setCloseCustomizablePages(!closeCustomizablePages)} style={{paddingRight: 10}}>
            <MaterialCommunityIcons
              name={!closeCustomizablePages ? 'chevron-down' : 'chevron-up'}
              size={16}
              color="#000"
            />
          </Pressable>
          {/* <Text style={styles.defaulPageSubTitle}>You can edit these pages but cannot delete them</Text> */}
        </View>
        {!closeCustomizablePages && (
          <>
            {screens?.map((s: NavigationConfig, index: number) => {
              const {path, config} = s;
              return (
                editableScreens.includes(config?.name) && (
                  <ScreenItem
                    key={config.name + '-' + path.join('-')}
                    screen={config}
                    path={path}
                    isEditable={true}
                    isDeletable={false}
                    redDot={mandatoryFields?.pages[config?.name] ? true : false}
                  />
                )
              );
            })}
          </>
        )}
      </View>
      <View>
        <View style={styles.pagesTitleWrapper}>
          <View style={styles.customPageTitleWrapper}>
            <Text style={styles.defaulPageTitle}>PREMIUM PAGES</Text>
            {isFeatureDisabled && (
              <Image source={imagePlanMapping['PLUS']} resizeMode="contain" style={{width: 50, height: 20}} />
            )}
            <Button
              variant="TEXT"
              color="PRIMARY"
              size="MEDIUM"
              disabled={isFeatureDisabled}
              containerStyles={{paddingVertical: 0}}
              onPress={() => {
                setShowAddPageDialog(!showAddPageDialog);
              }}>
              + Add
            </Button>
          </View>
          {/* <Text style={styles.defaulPageSubTitle}>You can fully customise these pages and add new ones</Text> */}
        </View>
        <AddCustomPage showAddPageDialog={showAddPageDialog} />
        {screens?.map((s: NavigationConfig, index: number) => {
          const {path, config} = s;
          return (
            !standardScreens.includes(config.name) && (
              <ScreenItem
                key={config.name}
                screen={config}
                path={path}
                isEditable={true}
                isDeletable={!premiumScreens.includes(config.name)}
                redDot={mandatoryFields?.pages[config?.name] ? true : false}
              />
            )
          );
        })}
        {isFeatureDisabled && (
          <Pressable
            onPress={() => dispatch(setOpenPremiumModal(true, allAvailablePlans.PLUS))}
            style={{position: 'absolute', top: 0, bottom: 0, left: 0, right: 0}}
          />
        )}
      </View>
      {/* HIDING NON EDIATBLE PAGES */}
      {/* <View>
        <View style={styles.pagesTitleWrapper}>
          <Text style={styles.defaulPageTitle}>Non-editable Pages</Text>
          <Text style={styles.defaulPageSubTitle}>You cannot edit or delete any of these pages.</Text>
        </View>
        {screens?.map((s: NavigationConfig, index: number) => {
          const {path, config} = s;
          return (
            nonEditableScreens.includes(config.name) && (
              <ScreenItem
                key={config.name}
                screen={config}
                path={path}
                isEditable={false}
                isDeletable={false}
                redDot={mandatoryFields?.pages[config?.name] ? true : false}
              />
            )
          );
        })}
      </View> */}
    </View>
  );
};

const ScreenItem = ({screen: s, path, isEditable, isDeletable, redDot}) => {
  const [showDeletePageDialog, setShowDeletePageDialog] = useState(false);
  const dispatch = useDispatch();

  const context = getNavigationContext();
  const activeNavigation = useSelector(state => state.activeNavigation);
  const active = activeNavigation?.activePageId === s.screen;

  return (
    <>
      <TouchableOpacity
        style={[styles.menuItem, active ? styles.menuItemActive : null]}
        onPress={() => {
          batch(() => {
            dispatch(editorSetActiveAttachmentId(''));
            dispatch(editorSetActiveAttachmentKey(''));
            dispatch({type: EDITOR_SELECT_NAV_COMPONENT, payload: path});
            dispatch({type: EDITOR_SELECTED_PAGE_TYPE, payload: s.type});
          });
          context.navigate(s.name);
        }}>
        <View style={styles.menuItemIcon}>
          {/* <MaterialCommunityIcons name={s.iconName} size={16} {...(active ? {color: '#1060E0'} : {})} /> */}
          <Icon iconType={s.iconType} name={s.iconName} size={16} {...(active ? {color: '#1060E0'} : {})} />
        </View>
        <View style={styles.menuTextWrapper}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text style={[styles.menuItemText, active ? styles.menuItemTextActive : null]}>
              {!_.isEmpty(s.title) ? s.title : s.name}{' '}
            </Text>
            {redDot && <RedDot tooltip={'Tile setup incomplete in this page'} />}
          </View>
          {isDeletable && (
            <Pressable
              onPress={() => {
                setShowDeletePageDialog(!showDeletePageDialog);
              }}
              style={{paddingRight: 10}}>
              <MaterialCommunityIcons name="trash-can-outline" size={16} color="#BFBFBF" />
            </Pressable>
          )}
        </View>
      </TouchableOpacity>
      <DeletePageDialog showDeletePageDialog={showDeletePageDialog} screen={s} selector={path} />
    </>
  );
};

export default NavigationTree;
