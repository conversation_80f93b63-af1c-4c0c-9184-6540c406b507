import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {selectNavComponentSelector, selectSelectedNavComponent, selectSelectedPluginConfig} from '@/root/web/selectors/EditorSelectors';
import {Ionicons, MaterialCommunityIcons} from 'apptile-core';
import {sendTileAnalytics} from '../actions/editorActions';
import { ApptileWebIcon } from '../icons/ApptileWebIcon';
import theme from '../styles-v2/theme';

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    marginBottom: 8,
    width: '100%',
    justifyContent: 'space-between',
  },
  tile: {
    fontSize: 12,
    marginBottom: 4,
    color: '#B0B0B0',
  },
  tileName: {
    fontWeight: '600',
    wordBreak: 'break-word',
    width: '100%',
    fontSize: theme.FONT_SIZE,
    fontFamily: theme.FONT_FAMILY,
  },
  delContainer: {
    width: '20%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  delIcon: {
    width: 36,
    height: 36,
    paddingLeft: 3,
    // backgroundColor: '#fce5e6',
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    // borderColor: '#D80707',
    // borderWidth: 1,
  },
  nameContainer: {
    width: '80%',
    justifyContent: 'center',
  },
});

const TileHeader = (props: any) => {
  const {isDeletable, onDelete} = props;
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const navConfig = useSelector(state => selectNavComponentSelector(state));
  const dispatch = useDispatch();
  if (!moduleConfig && navConfig !== 'Main' && navConfig !== 'TopTab') return null;
  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  return (
    <View style={styles.header}>
      <View style={styles.nameContainer}>
        {/* <Text style={styles.tile}>Tile Name</Text> */}
        <Text style={styles.tileName}>
          {moduleConfig?.config?.get('moduleName')
            ? moduleConfig?.config?.get('moduleName')
            : navConfig === 'Main'
            ? 'Bottom Navigation'
            : navConfig === 'TopTab'
            ? 'Top Navigation'
            : null}
        </Text>
      </View>
      <View style={styles.delContainer}>
        {isDeletable && (
          <View style={styles.delIcon}>
            <ApptileWebIcon
              name={'delete'}
              color="#D80707"
              size={16}
              onPress={() => {
                dispatch(sendTileAnalytics(moduleUUID, 'editor:tile_deleted', {}));
                onDelete();
              }} />
          </View>
        )}
      </View>
    </View>
  );
};

export default TileHeader;
