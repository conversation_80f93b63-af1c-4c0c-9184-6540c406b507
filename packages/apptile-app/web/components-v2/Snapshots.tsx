import {ActivityIndicator, Image, Pressable, StyleSheet, Text, View} from 'react-native';
import RadioGroupControlV2 from '../components/controls-v2/RadioGroupControl';
import React, {useEffect, useState} from 'react';
import Button from './base/Button';
import {useDispatch, useSelector} from 'react-redux';
import {MaterialCommunityIcons} from 'apptile-core';
import commonStyles from '../styles-v2/commonStyles';
import TextInput from './base/TextInput';
import {createAppBranch, deleteAppBranch, fetchAppBranchesWithScheduledOta} from '../actions/editorActions';
import {IAppBranchesWithScheduledOta, ScheduledOta} from '../api/ApiTypes';
import CollapsiblePanel from '../components/CollapsiblePanel';
import {EditorRootState} from '../store/EditorRootState';
import {apptileStateSelector} from 'apptile-core';
import {useParams} from 'react-router';
import _ from 'lodash';
import {defaultBranchNameSelector} from '../selectors/EditorSelectors';

enum SnapshotType {
  UPCOMING = 'upcoming',
  HISTORY = 'history',
  LIVE = 'live',
  DRAFT = 'draft',
}

export const Snapshots = () => {
  const [snapshotType, setSnapshotType] = React.useState(SnapshotType.UPCOMING);
  const [createSnapshot, setCreateSnapshot] = React.useState(false);
  const defaultBranchName = useSelector(defaultBranchNameSelector);

  const apptileState = useSelector(apptileStateSelector);
  const currentBranch = useParams()?.branchName;
  const dispatch = useDispatch();
  const fetchBranchesWithScheduledOta = () =>
    dispatch(fetchAppBranchesWithScheduledOta(apptileState.appId as string, apptileState.forkId));
  useEffect(() => {
    fetchBranchesWithScheduledOta();
  }, [apptileState.forkId]);
  const branchesByNameWithOta = {...useSelector((state: EditorRootState) => state.branches.branchesByNameWithOta)};
  delete branchesByNameWithOta[defaultBranchName];
  const branches = Object.values(branchesByNameWithOta);
  //Remove main branch from branches object
  const loading = false;
  //   - If app branch does not have any scheduled oats then it is draft
  // - If app branch has scheduled oats with both revertSnapshot status pending then it is upcoming
  const draftBranches: Array<IAppBranchesWithScheduledOta> = [];
  const upcomingBranches: Array<IAppBranchesWithScheduledOta> = [];
  const historyBranches: Array<IAppBranchesWithScheduledOta> = [];
  let liveBranch: [IAppBranchesWithScheduledOta] | null = null;
  // if (_.isEmpty(branches)) {
  //   return <></>;
  // }
  branches?.forEach(branch => {
    if (branch.scheduledOtas.length === 0) {
      draftBranches.push(branch);
    } else {
      const revertSnapshots = branch.scheduledOtas.filter(scheduledOta => scheduledOta.revertSnapshot);
      const nonRevertSnapshots = branch.scheduledOtas.filter(scheduledOta => !scheduledOta.revertSnapshot);
      const hasPendingRevertSnapshot =
        revertSnapshots.length > 0 && revertSnapshots.find(scheduledOta => scheduledOta.status === 'PENDING');
      const hasPendingNonRevertSnapshot =
        nonRevertSnapshots.length > 0 && nonRevertSnapshots.find(scheduledOta => scheduledOta.status === 'PENDING');
      if (hasPendingRevertSnapshot && hasPendingNonRevertSnapshot) {
        upcomingBranches.push(branch);
      }
      if (hasPendingRevertSnapshot && !hasPendingNonRevertSnapshot) {
        liveBranch = [branch];
      }
      if (!hasPendingRevertSnapshot && !hasPendingNonRevertSnapshot) {
        historyBranches.push(branch);
      }
    }
  });

  const createBranchMeta = useSelector((state: EditorRootState) => state.branches.createBranchMeta);

  return (
    <View style={{flex: 1, justifyContent: 'space-between'}}>
      <View style={styles.wrapper}>
        <View style={{paddingHorizontal: 12}}>
          <RadioGroupControlV2
            label=""
            options={[
              {text: 'Upcoming', value: SnapshotType.UPCOMING},
              {text: 'History', value: SnapshotType.HISTORY},
            ]}
            value={snapshotType}
            onChange={value => setSnapshotType(value as SnapshotType)}
          />
          {createSnapshot && <CreateSnapshotForm setCreateSnapshot={setCreateSnapshot} />}
          {!createSnapshot && (
            <View>
              <View style={{width: '100%', alignItems: 'flex-end', marginTop: 10}}>
                <Button
                  onPress={() => fetchBranchesWithScheduledOta()}
                  size="EXTRA-SMALL"
                  containerStyles={{borderRadius: 30}}
                  icon="reload"
                  variant="OUTLINED"
                  color="SECONDARY">
                  Refresh
                </Button>
              </View>
              {createBranchMeta.isCreating && (
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 10,
                  }}>
                  <ActivityIndicator />
                  <Text style={[commonStyles.baseText, {marginTop: 10}]}>Creating a version</Text>
                </View>
              )}
              {snapshotType === SnapshotType.UPCOMING && (
                <>
                  <SnapshotList branches={upcomingBranches} snapshotType={SnapshotType.UPCOMING} loading={loading} />
                  <SnapshotList snapshotType={SnapshotType.LIVE} branches={liveBranch} loading={loading} />
                  <SnapshotList snapshotType={SnapshotType.DRAFT} branches={draftBranches} loading={loading} />
                </>
              )}
              {snapshotType === SnapshotType.HISTORY && (
                <SnapshotList branches={historyBranches} snapshotType={SnapshotType.HISTORY} loading={loading} />
              )}
            </View>
          )}
        </View>
        {!createSnapshot && (
          <View style={styles.antivityIndicatorWrapper}>
            {loading ? (
              <ActivityIndicator />
            ) : (
              branches?.length === 0 && (
                <View style={styles.emptyStateWrapper}>
                  <Image style={styles.emptyImage} source={require('../assets/images/snapshot-no-result.png')} />
                  <Text style={commonStyles.baseText}>No upcoming versions</Text>
                </View>
              )
            )}
          </View>
        )}
      </View>
      <View style={styles.footer}>
        {currentBranch !== defaultBranchName ? (
          <Button
            onPress={() => {
              window.location.href = `/dashboard/${apptileState.orgId}/app/${apptileState.appId}/f/${apptileState.forkId}/b/${defaultBranchName}/tiles`;
            }}
            containerStyles={[styles.buttonContainerStyles, {marginTop: 12}]}
            variant="PILL"
            color="SECONDARY">
            Back to Primary App
          </Button>
        ) : (
          <Button onPress={() => setCreateSnapshot(true)} containerStyles={styles.buttonContainerStyles} color="CTA">
            + Create version
          </Button>
        )}
      </View>
    </View>
  );
};

const CreateSnapshotForm = ({setCreateSnapshot}) => {
  const [snapshotName, setSnapshotName] = React.useState('');
  const dispatch = useDispatch();
  const onAdd = () => {
    dispatch(createAppBranch(snapshotName));
    setCreateSnapshot(false);
  };

  return (
    <View style={styles.createWrapper}>
      <TextInput onChangeText={(value: string) => setSnapshotName(value)} placeholder="Version name" />
      <View style={styles.createSnapshotFooter}>
        <Button onPress={onAdd}>Add</Button>
        <Button onPress={() => setCreateSnapshot(false)} color="SECONDARY">
          Cancel
        </Button>
      </View>
    </View>
  );
};

const SnapshotList: React.FC<{
  branches: IAppBranchesWithScheduledOta[] | IAppBranchesWithScheduledOta | null;
  loading: boolean;
  snapshotType?: SnapshotType;
}> = ({branches, loading, snapshotType}) => {
  //States with branch id
  const currentActiveBranch = useParams()?.branchName;
  const apptileState = useSelector(apptileStateSelector);
  const [expanded, setExpanded] = useState<number>(0);
  const [deleteDialog, setDeleteDialog] = useState<number>(0);
  if (loading) {
    return <ActivityIndicator />;
  }
  if (!branches) {
    return <></>;
  }
  //Calculating the start date. It is the the publishDate of the scheduled ota whose revertSnapshot is false
  const renderStartDate = (branch: IAppBranchesWithScheduledOta, snapshotType?: SnapshotType) => {
    if (snapshotType === SnapshotType.UPCOMING) {
      return branch.scheduledOtas.find((ota: ScheduledOta) => !ota.revertSnapshot && ota.status === 'PENDING')
        ?.publishDate;
    }
    if (snapshotType === SnapshotType.HISTORY) {
      return branch.scheduledOtas.find((ota: ScheduledOta) => !ota.revertSnapshot && ota.status === 'EXECUTED')
        ?.publishDate;
    }
    if (snapshotType === SnapshotType.LIVE) {
      return branch.scheduledOtas.find((ota: ScheduledOta) => !ota.revertSnapshot && ota.status === 'EXECUTED')
        ?.publishDate;
    }
    return '';
  };
  const renderEndDate = (branch: IAppBranchesWithScheduledOta, snapshotType?: SnapshotType) => {
    if (snapshotType === SnapshotType.UPCOMING) {
      return branch.scheduledOtas.find((ota: ScheduledOta) => ota.revertSnapshot && ota.status === 'PENDING')
        ?.publishDate;
    }
    if (snapshotType === SnapshotType.HISTORY) {
      return branch.scheduledOtas.find(
        (ota: ScheduledOta) => ota.revertSnapshot && (ota.status === 'EXECUTED' || ota.status === 'CANCELLED'),
      )?.publishDate;
    }
    if (snapshotType === SnapshotType.LIVE) {
      return branch.scheduledOtas.find((ota: ScheduledOta) => ota.revertSnapshot && ota.status === 'PENDING')
        ?.publishDate;
    }
    return '';
  };
  const renderSnapshotHeader = (snapshotType: SnapshotType | undefined) => {
    if (snapshotType === SnapshotType.UPCOMING) {
      return <Text style={[commonStyles.baseText, {color: '#EAA92A', fontSize: 12}]}>Scheduled</Text>;
    }
    if (snapshotType === SnapshotType.HISTORY) {
      return '';
    }
    if (snapshotType === SnapshotType.LIVE) {
      return (
        <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
          <MaterialCommunityIcons name="circle" size={12} color="#00C853" />
          <Text>Live</Text>
        </View>
      );
    }
    if (snapshotType === SnapshotType.DRAFT) {
      return <Text style={[commonStyles.baseText, {color: '#6B6B6B', fontSize: 12}]}>Draft</Text>;
    }
    return '';
  };

  return (
    <View>
      {branches?.map((branch: IAppBranchesWithScheduledOta) => (
        <CollapsiblePanel
          title={branch.branchName}
          key={branch.id}
          isOpen={expanded === branch.id}
          backgroundStyle={[
            styles.collapsibleBackground,
            {borderColor: currentActiveBranch === branch.branchName ? '#1060E0' : '#E5E5E5'},
          ]}
          customHeader={
            <View style={styles.collapsibleHeader}>
              <View style={{flex: 1}} key={branch.id}>
                {renderSnapshotHeader(snapshotType)}
                <Text style={[commonStyles.baseText, {fontSize: 14}]}>{branch.title}</Text>
              </View>
              <Pressable onPress={() => setExpanded(expanded === branch.id ? 0 : branch.id)}>
                <MaterialCommunityIcons size={10} name={expanded ? 'chevron-up' : 'chevron-down'} />
              </Pressable>
            </View>
          }>
          <View style={styles.collapsibleBody}>
            <View style={styles.dateWrapper}>
              {renderStartDate(branch, snapshotType) && (
                <Text style={[commonStyles.baseText, styles.dateText]}>
                  Start: {new Date(renderStartDate(branch, snapshotType))?.toLocaleString('en-US', {
                    month: 'long',
                    day: 'numeric',
                    year: 'numeric',
                    hour: 'numeric',
                    minute: 'numeric',
                  })}
                </Text>
              )}
              {renderEndDate(branch, snapshotType) && (
                <Text style={[commonStyles.baseText, styles.dateText]}>
                  End: {new Date(renderEndDate(branch, snapshotType))?.toLocaleString('en-US', {
                    month: 'long',
                    day: 'numeric',
                    year: 'numeric',
                    hour: 'numeric',
                    minute: 'numeric',
                  })}
                </Text>
              )}
            </View>
            {deleteDialog === branch.id && (
              <DeleteDialog setDeleteDialog={setDeleteDialog} branchName={branch.branchName} />
            )}
            {!(deleteDialog === branch.id) && (
              <View style={styles.collapsibleFooter}>
                <Button
                  size="SMALL"
                  color="TAB"
                  textStyles={styles.editButtonText}
                  onPress={() => {
                    //navigate to the editor
                    window.location.href = `/dashboard/${apptileState.orgId}/app/${apptileState.appId}/f/${apptileState.forkId}/b/${branch.branchName}/tiles`;
                  }}
                  innerContainerStyles={{minHeight: 0}}
                  containerStyles={styles.editButton}>
                  Edit design
                </Button>
                <Pressable onPress={() => setDeleteDialog(branch.id)}>
                  <MaterialCommunityIcons color={'#A1A1A1'} size={21} name="delete-outline" />
                </Pressable>
              </View>
            )}
          </View>
        </CollapsiblePanel>
      ))}
    </View>
  );
};

export const DeleteDialog: React.FC<{
  setDeleteDialog: React.Dispatch<React.SetStateAction<number>>;
  branchName: string;
}> = ({setDeleteDialog, branchName}) => {
  const dispatch = useDispatch();
  const apptileState = useSelector(apptileStateSelector);
  const onDelete = () => {
    //dispatch delete action
    dispatch(deleteAppBranch(apptileState.appId as string, apptileState.forkId, branchName));
    setDeleteDialog(0);
  };
  return (
    <View>
      <Text style={[commonStyles.baseText, {color: '#484848'}]}>Are you sure you want to delete this version?</Text>
      <View style={{flexDirection: 'row', gap: 20, marginTop: 10}}>
        <Button
          size={'SMALL'}
          onPress={() => setDeleteDialog(0)}
          variant="PILL"
          color="SECONDARY"
          containerStyles={styles.deleteButtons}>
          Cancel
        </Button>
        <Button size={'SMALL'} onPress={onDelete} color="ERROR" containerStyles={styles.deleteButtons}>
          Delete
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    paddingTop: 16,
    flex: 1,
    overflow: 'scroll',
  },
  createWrapper: {
    marginVertical: 16,
    borderWidth: 1,
    borderColor: '#DFE5EB',
    borderRadius: 12,
    paddingVertical: 13,
    paddingHorizontal: 12,
  },
  createSnapshotFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
  },
  collapsibleFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  editButtonText: {
    fontSize: 11,
  },
  collapsibleBackground: {borderRadius: 12, borderWidth: 1, overflow: 'hidden', marginTop: 16},
  dateText: {
    color: '#5E5D5D',
    fontSize: 12,
    lineHeight: 18,
  },
  editButton: {
    width: 90,
    height: 29,
  },
  collapsibleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 14,
    // paddingBottom: 10,
  },
  collapsibleBody: {
    padding: 15,
    paddingTop: 0,
  },
  dateWrapper: {
    marginBottom: 10,
  },
  deleteButtons: {
    width: 80,
  },
  buttonContainerStyles: {
    width: 236,
  },
  emptyImage: {width: 150, height: 150},
  emptyStateWrapper: {
    paddingVertical: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
  },
  antivityIndicatorWrapper: {
    marginVertical: 'auto',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    paddingVertical: 26,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#DFE5EB',
    width: '100%',
  },
});
