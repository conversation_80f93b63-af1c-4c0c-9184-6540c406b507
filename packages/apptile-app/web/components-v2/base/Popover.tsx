import _ from 'lodash';
import {default as React, DOMElement, useEffect, useRef, useState} from 'react';
import {Pressable, StyleProp, StyleSheet, View, ViewStyle} from 'react-native';
import {Popover, PopoverPosition} from 'react-tiny-popover';

type PopoverProps = {
  trigger: React.ReactNode;
  children: React.ReactNode;
  onVisibleChange?: (visible: boolean) => void;
  visible?: boolean;
  positions?: PopoverPosition[];
  propOnly?: boolean;
  containerStyle?: any;
  disableClickOutside?: boolean;
};

type ComputedStyles = {
  popover: StyleProp<ViewStyle>;
};

const PopoverComponent: React.FC<PopoverProps> = (props: PopoverProps) => {
  const {children, trigger, visible = false, positions = ['top', 'bottom', 'left', 'right'], onVisibleChange, propOnly= false, containerStyle, disableClickOutside = false} = props;
  const [showPopover, setShowPopover] = useState(visible);

  const reference = useRef(null);
  useEffect(() => {
    if (_.isBoolean(visible)) {
      setShowPopover(visible);
    }
  }, [visible]);

  return (
    <>
      <View style={styles.container}>
        <Popover
          ref={reference}
          clickOutsideCapture={!disableClickOutside}
          onClickOutside={disableClickOutside ? undefined : () => {
            setShowPopover(false);
            if (onVisibleChange) onVisibleChange(false);
          }}
          {...(containerStyle ? {containerStyle} : {})}
          isOpen={propOnly ? visible : showPopover}
          positions={positions} // preferred positions by priority
          content={children}>
          <Pressable
            onPress={() => {
              setShowPopover(!showPopover);
              if (onVisibleChange) onVisibleChange(!showPopover);
            }}>
            {trigger}
          </Pressable>
        </Popover>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  popoverView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 0,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default PopoverComponent;
