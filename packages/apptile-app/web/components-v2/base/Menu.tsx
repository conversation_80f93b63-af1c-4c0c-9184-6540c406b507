import React from 'react';
import {
  GestureResponderEvent,
  Image,
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';

import {Icon, MaterialCommunityIcons, Plan} from 'apptile-core';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon';
import theme from '@/root/web/styles-v2/theme';
import Analytics from '@/root/web/lib/segment';
import {IconType} from '../../common/webDatatypes';
import RedDot from './RedDot';
import {useDispatch, useSelector} from 'react-redux';
import {allAvailablePlans} from 'apptile-core';
import {currentPlanFeaturesSelector} from '../../selectors/FeatureGatingSelector';
import {setOpenPremiumModal} from '../../actions/editorActions';
import imagePlanMapping from '../../common/featureGatingConstants';

type MenuItemP = {
  id: string;
  text: string;
  icon?: string;
  iconType?: IconType;
  isActive?: boolean;
  redDot?: boolean;
  onPress?: (event: GestureResponderEvent, id: string) => void;
  premiumBase?: Plan;
  vertical?: boolean;
  textStyles?: StyleProp<TextStyle>;
  wrapperStyles?: StyleProp<ViewStyle>;
};
type MenuProps = {
  items: MenuItem[];
};

const styles = StyleSheet.create({
  menuItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    paddingVertical: 4,
    paddingRight: 4,
    borderRadius: 6,
  },
  menuItemActive: {
    backgroundColor: theme.PRIMARY_OPAQUE_BACKGROUND,
  },
  menuItemIcon: {
    width: 42,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuItemText: {
    fontFamily: theme.FONT_FAMILY,
    marginLeft: 18,
    fontSize: theme.LeftSideBar.FONT_SIZE,
    lineHeight: theme.LeftSideBar.LINE_HEIGHT,
  },
  menuItemTextActive: {
    color: theme.PRIMARY_COLOR,
  },
  premiumButton: {
    borderRadius: 6,
    position: 'absolute',
    width: '100%',
    height: '80%',
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginBottom: 12,
    paddingVertical: 4,
    paddingRight: 12,
  },
  wrapper: {
    justifyContent: 'center',
  },
});

type MenuItemProps = MenuItemP;
const MenuItem: React.FC<MenuItemProps> = props => {
  const {
    id,
    text,
    icon,
    iconType,
    isActive,
    onPress,
    redDot,
    premiumBase,
    vertical = false,
    textStyles,
    wrapperStyles,
  } = props;
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isFeatureDisabled = !currentPlanFeatures.includes(allAvailablePlans.PRO);
  const dispatch = useDispatch();

  const isPremium = premiumBase && !currentPlanFeatures?.includes(premiumBase);

  return (
    <View style={[styles.wrapper, wrapperStyles]}>
      <TouchableOpacity
        style={[
          styles.menuItem,
          vertical && {flexDirection: 'column'},
          vertical && isActive ? styles.menuItemActive : null,
        ]}
        onPress={e => onPress?.(e, id)}>
        <View style={styles.menuItemIcon}>
          {iconType === 'ApptileWebIcons' ? (
            <ApptileWebIcon name={icon} size={21} {...(isActive ? {color: '#1060E0'} : {})} />
          ) : (
            <Icon iconType={iconType} size={21} name={icon} {...(isActive ? {color: '#1060E0'} : {})} />
          )}
        </View>
        <Text
          nativeID={id ? id : ''}
          style={[
            styles.menuItemText,
            vertical && {marginLeft: 0},
            isActive ? styles.menuItemTextActive : null,
            textStyles,
          ]}>
          {text}
        </Text>
        <View style={{top: 0, right: 0, position: 'absolute'}}>{redDot && <RedDot size={15} />}</View>
      </TouchableOpacity>
      {isPremium && isFeatureDisabled && (
        <Pressable
          onPress={() => {
            dispatch(setOpenPremiumModal(true, premiumBase));
            Analytics.track('purchase:upgradePopup_viewed', {
              pricingFlow: 'Analytics',
            });
          }}
          style={styles.premiumButton}>
          <Image
            source={imagePlanMapping[premiumBase]}
            resizeMode="contain"
            style={{width: 78, height: 20, right: 20}}
          />
        </Pressable>
      )}
    </View>
  );
};

// const Menu = React.forwardRef<View, MenuProps>((props, ref) => {
//   const {} = props;
//   return null;
// });

export default MenuItem;
