import React, {useEffect, useState} from 'react';
import {
  GestureResponderEvent,
  StyleSheet,
  Text,
  Pressable,
  StyleProp,
  ViewStyle,
  TextStyle,
  View,
  ActivityIndicator,
} from 'react-native';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {mergeDeep} from '@apollo/client/utilities';

import theme from '@/root/web/styles-v2/theme';
import {getAbsoluteFill} from '../../styles-v2/utils';
import {VARIANTS, COLORS, SIZES} from '@/root/web/styles-v2/types';

type ButtonProps = {
  containerStyles?: StyleProp<ViewStyle>;
  innerContainerStyles?: StyleProp<ViewStyle>;
  textStyles?: StyleProp<TextStyle>;
  variant?: VARIANTS;
  color?: COLORS; // Exclude<COLORS, 'DISABLED'>;
  size?: SIZES; // Exclude<SIZES, 'MEDIUM'>;
  opaque?: boolean;
  icon?: string;
  iconPosition?: 'LEFT' | 'RIGHT';
  id?: string;
  loading?: boolean;
  disabled?: boolean;
  backgroundColor?: string;
  inversed?: boolean;
  border?: boolean;
  onPress?: (event: GestureResponderEvent) => void;
};
type ComputedStyles = {
  container: StyleProp<ViewStyle>;
  wrapper: StyleProp<ViewStyle>;
  text: StyleProp<TextStyle>;
  icon: StyleProp<TextStyle>;
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    overflow: 'hidden',
  },
  text: {
    fontFamily: theme.FONT_FAMILY,
  },
  loader: {
    ...getAbsoluteFill({T: 0, L: 0, B: 0, R: 0}),
    justifyContent: 'center',
    alignItems: 'center',
  },
});

const Button = React.forwardRef<View, ButtonProps & {children?: any}>((props, ref) => {
  const {containerStyles, textStyles, innerContainerStyles} = props;
  let {variant = 'FILLED-PILL', color = 'DEFAULT', size = 'MEDIUM', backgroundColor} = props;
  const {
    icon,
    iconPosition = 'LEFT',
    loading,
    disabled,
    children,
    opaque = false,
    inversed = false,
    border = true,
  } = props;
  const customId = props.id;

  // prepare styles based on variant and color
  const SPACING = {'EXTRA-SMALL': 2.5, SMALL: 3.5, MEDIUM: 4, LARGE: 5.5}[size];
  const FONT_SIZE = {'EXTRA-SMALL': 11, SMALL: 12, MEDIUM: 13, LARGE: 15}[size];
  if (disabled) color = 'DISABLED';
  const TEXT_COLOR = theme[`${color}_COLOR`];
  const SELECTED_COLOR = theme[`${color}${opaque ? '_OPAQUE' : ''}_BACKGROUND`];
  let DERIVED_TEXT_COLOR = TEXT_COLOR;
  let DERIVED_BACKGROUND_COLOR = SELECTED_COLOR;
  let DERIVED_BORDER_COLOR = backgroundColor ? theme[`${color}_BORDER`] ?? TEXT_COLOR : TEXT_COLOR;
  if (inversed) {
    DERIVED_TEXT_COLOR = SELECTED_COLOR;
    DERIVED_BACKGROUND_COLOR = TEXT_COLOR;
    DERIVED_BORDER_COLOR = backgroundColor ? theme[`${color}_BORDER`] ?? SELECTED_COLOR : SELECTED_COLOR;
  }
  let computedStyles: ComputedStyles = {
    container: {
      backgroundColor: DERIVED_BACKGROUND_COLOR,
      borderColor: DERIVED_BORDER_COLOR,
      borderRadius: SPACING * 7,
      ...(!children ? {padding: SPACING * 2} : {paddingVertical: SPACING * 2, paddingHorizontal: SPACING * 3}),
    },
    wrapper: {flexDirection: iconPosition === 'LEFT' ? 'row' : 'row-reverse', minHeight: FONT_SIZE + 0.25 * FONT_SIZE},
    text: {color: DERIVED_TEXT_COLOR, fontFamily: theme.FONT_FAMILY, fontSize: FONT_SIZE},
    icon: !children ? {} : {[iconPosition === 'LEFT' ? 'marginRight' : 'marginLeft']: SPACING * 2},
  };
  if (variant === 'FILLED') {
    computedStyles = mergeDeep(computedStyles, {
      container: {backgroundColor: DERIVED_BACKGROUND_COLOR, borderRadius: 8},
      text: {color: DERIVED_TEXT_COLOR},
    });
  }
  if (variant === 'PILL')
    computedStyles = mergeDeep(computedStyles, {
      container: {backgroundColor: DERIVED_BACKGROUND_COLOR + '00'},
    });
  if (variant === 'OUTLINED')
    computedStyles = mergeDeep(computedStyles, {
      container: {
        backgroundColor: DERIVED_BACKGROUND_COLOR + '00',
        borderRadius: 6,
      },
    });
  if (variant === 'TEXT')
    computedStyles = mergeDeep(computedStyles, {
      container: {
        backgroundColor: DERIVED_BACKGROUND_COLOR + '00',
        borderColor: DERIVED_BORDER_COLOR + '00',
      },
    });
  if (variant === 'TAB')
    computedStyles = mergeDeep(computedStyles, {
      container: {
        borderRadius: 0,
        borderWidth: 0,
        borderBottomWidth: 2,
        backgroundColor: DERIVED_BACKGROUND_COLOR,
        borderColor: theme[`${color}_BORDER`] ?? DERIVED_BORDER_COLOR,
      },
    });

  if (!border && computedStyles?.container?.borderColor) computedStyles.container.borderColor = '#0000';
  // onPress animation
  const scale = useSharedValue(1);
  const [isPressed, setIsPressed] = useState(false);
  useEffect(() => {
    scale.value = withTiming(isPressed ? 1.05 : 1, {
      duration: 120,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPressed, scale]);
  const animatedStyles = useAnimatedStyle(() => ({transform: [{scale: scale.value}]}));

  return (
    <Pressable
      ref={ref}
      style={[styles.container, computedStyles.container, containerStyles]}
      onPress={props.onPress}
      onPressIn={() => setIsPressed(true)}
      onPressOut={() => setIsPressed(false)}
      disabled={disabled}
      nativeID={customId ? customId : ''}>
      <Animated.View style={[computedStyles.wrapper, animatedStyles, innerContainerStyles]}>
        {!!icon && (
          <Icon
            style={[computedStyles.icon]}
            name={icon}
            size={FONT_SIZE + 0.25 * FONT_SIZE}
            color={DERIVED_TEXT_COLOR}
          />
        )}
        {!!children && <Text style={[styles.text, computedStyles.text, textStyles]}>{children}</Text>}
      </Animated.View>
      {loading && (
        <View style={[styles.loader, {backgroundColor: `${DERIVED_BACKGROUND_COLOR}e6`}]}>
          <ActivityIndicator size={FONT_SIZE} color={DERIVED_TEXT_COLOR} />
        </View>
      )}
    </Pressable>
  );
});

export default Button;
