import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import theme from '@/root/web/styles-v2/theme';
import {MaterialCommunityIcons} from 'apptile-core';

type PillProps = {
  isActive?: boolean;
  text: string;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 28,
    margin: 4,
    borderWidth: 1,
    borderRadius: 28,
    padding: 2,
    borderColor: theme.DISABLED_COLOR,
    backgroundColor: theme.DISABLED_COLOR,
  },
  container_ACTIVE: {
    borderColor: theme.PRIMARY_COLOR,
    backgroundColor: theme.PRIMARY_COLOR,
  },
  textWrapper: {
    flex: 1,
    paddingHorizontal: 8,
  },
  text: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 12,
  },
  text_ACTIVE: {
    color: 'white',
  },
  iconWrapper: {
    width: 24,
    height: 24,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.PRIMARY_BACKGROUND,
  },
  iconWrapper_ACTIVE: {
    backgroundColor: theme.DISABLED_COLOR,
  },
});

const Pill: React.FC<PillProps> = ({isActive, text}) => {
  return (
    <TouchableOpacity style={[styles.container, isActive && styles.container_ACTIVE]}>
      <View style={styles.textWrapper}>
        <Text style={[styles.text, isActive && styles.text_ACTIVE]}>{text}</Text>
      </View>
      <View style={[styles.iconWrapper, isActive && styles.iconWrapper_ACTIVE]}>
        <MaterialCommunityIcons name="close" size={18} />
      </View>
    </TouchableOpacity>
  );
};

export default Pill;
