import React, {useCallback, useEffect, useState} from 'react';
import {FlatList, Image, Text, View, StyleSheet, Pressable} from 'react-native';
import {ITileItem} from '../api/ApiTypes';
import TilesApi from '../api/TilesApi';
import TileItem from './TileItem';
import theme from '../styles-v2/theme';
import Fuse from 'fuse.js';

import {NonDataSourceTileIntegrations} from '../common/tileConstants';
import {MaterialCommunityIcons, selectAppSettingsForKey} from 'apptile-core';
import {BrandSettingsTypes} from 'apptile-core';
const BRAND_EXPOSED_TILES = BrandSettingsTypes.BRAND_EXPOSED_TILES,
  BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY;
import {useSelector} from 'react-redux';
import {SettingsConfig} from 'apptile-core';
import {selectModulesCache} from 'apptile-core';
import {IntegrationCodesMappingWithDataSource} from '@/root/app/plugins/datasource/constants';
import TextInput from '@/root/web/components-v2/base/TextInput';
import {EditorRootState} from '../store/EditorRootState';
import commonStyles from '../styles-v2/commonStyles';
import Button from './base/Button';

export interface TilesExplorerProps {
  tags: string[];
  appId: string;
  appIntegrations: string[];
  aiPlugins: ModuleRecord[];
  appIntegrationObj: any;
  legacyMode: boolean;
  tileAnimation: TileAnimation;
  themeSlug: string;
  screenBasedTiles: any;
  searchQuery?: string;
}

export type TileHoverStatus = {
  status: boolean;
  tileIndex: number;
};
export type TileAnimation = {
  isTileAnimationHappening: boolean;
  hasTileAnimationEverHappened: boolean;
  setTileAnimationStatus: React.Dispatch<React.SetStateAction<boolean>>;
  setTileAnimationEverHappend: React.Dispatch<React.SetStateAction<boolean>>;
};

const TILE_EXP_PAGE_SIZE = 50;

const TILES_FUSE_OPTIONS = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  minMatchCharLength: 2,
  ignoreLocation: true,
  threshold: 0.1,
  keys: ['name'],
};

const TilesExplorer: React.FC<TilesExplorerProps> = props => {
  const {
    tags,
    appId,
    aiPlugins,
    appIntegrations,
    appIntegrationObj,
    tileAnimation,
    legacyMode = false,
    themeSlug,
    screenBasedTiles,
    searchQuery,
  } = props;
  const [isTileHovered, setTileHover] = useState<TileHoverStatus>({status: false, tileIndex: 0});
  const [tilesLoading, setTilesLoading] = useState<boolean>(false);
  const {isTileAnimationHappening} = tileAnimation;
  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const brandExposedTiles = brandSettings.getSettingValue(BRAND_EXPOSED_TILES) ?? '';
  const modulesRecords = useSelector(state => selectModulesCache(state));

  const [exposedModules, setExposedModules] = useState<string[]>(brandExposedTiles?.split(',')?.filter(e => e) ?? []);
  useEffect(() => {
    setExposedModules(brandExposedTiles?.split(',')?.filter(e => e) ?? []);
  }, [brandExposedTiles]);

  const [tiles, setTiles] = useState<ITileItem[]>([]);
  const [hasNextPage, setHasNextPage] = useState(false);
  const screenTags = Object.keys(screenBasedTiles || {});
  const screenTagsString = screenTags.join(',');
  useEffect(() => {
    if (tags[0] != 'aiTiles' && tags[0] != 'exposedTiles' && tags[0] != 'themeTiles' && themeSlug) {
      setTilesLoading(true);
      TilesApi.getTiles([themeSlug, ...tags], 0, TILE_EXP_PAGE_SIZE).then(resp => {
        setTilesLoading(false);
        setTiles(resp.data?.items);
        setHasNextPage(resp.data?.nextPageAvailable);
      });
    } else if (tags[0] == 'exposedTiles') {
      const exposedTiles = [];
      exposedModules.map(e => {
        exposedTiles.push({
          id: e,
          name: modulesRecords?.get(e)?.moduleName ?? '',
          description: null,
          currentTileSaveId: 1,
          coverImage:
            'https://cdn.apptile.io/ce0249d7-4cae-4e5e-828c-226f982bb0e7/84b99d55-ef9a-48b3-9bf3-e94649ff792c/original.png',
          assets: null,
          tags: [],
          weight: 0,
        });
      });
      setTiles(exposedTiles);
    } else if (tags[0] == 'aiTiles') {
      const aiTiles = [];
      aiPlugins.map(e => {
        aiTiles.push({
          id: e.moduleUUID,
          name: e.moduleName ?? '',
          description: null,
          currentTileSaveId: 1,
          coverImage:
            'https://cdn.apptile.io/ce0249d7-4cae-4e5e-828c-226f982bb0e7/84b99d55-ef9a-48b3-9bf3-e94649ff792c/original.png',
          assets: null,
          tags: [],
          weight: 0,
          moduleRecord: e,
        });
      });
      setTiles(aiTiles);
    } else if (tags[0] == 'themeTiles') {
      setTilesLoading(true);
      TilesApi.getTiles([themeSlug], 0, TILE_EXP_PAGE_SIZE * screenTags.length).then(resp => {
        setTilesLoading(false);
        setTiles(
          resp.data?.items.filter((item: ITileItem) => item.tags.find(e => screenTags.includes(e.toLowerCase()))),
        );
        setHasNextPage(resp.data?.nextPageAvailable);
      });
    }
  }, [themeSlug, screenTagsString]);
  const onEndReached = useCallback(() => {
    if (hasNextPage) {
      setTilesLoading(true);
      if (tags[0] != 'aiTiles' && tags[0] != 'exposedTiles' && tags[0] != 'themeTiles' && themeSlug) {
        TilesApi.getTiles([themeSlug, ...tags], tiles.length, TILE_EXP_PAGE_SIZE).then(resp => {
          setTilesLoading(false);
          setTiles(tiles.concat(resp.data?.items));
          setHasNextPage(resp.data?.nextPageAvailable);
        });
      } else if (tags[0] == 'themeTiles' && themeSlug) {
        setTilesLoading(true);
        TilesApi.getTiles([themeSlug], tiles.length, TILE_EXP_PAGE_SIZE * screenTags.length).then(resp => {
          setTilesLoading(false);
          setTiles(
            tiles.concat(
              resp.data?.items.filter((item: ITileItem) => item.tags.find(e => screenTags.includes(e.toLowerCase()))),
            ),
          );
          setHasNextPage(resp.data?.nextPageAvailable);
        });
      }
    }
  }, [hasNextPage, tags, tiles, themeSlug]);
  const renderItem = ({item, index}) => {
    if (_.isEmpty(item)) {
      return null;
    }
    const isOpenTile = item.tags.some((tag: string) => appIntegrations.includes(tag));
    const integrationCode = item.tags.find((tag: string) =>
      [...NonDataSourceTileIntegrations, ...Object.keys(IntegrationCodesMappingWithDataSource)].includes(tag),
    );
    const isTileUnLocked = integrationCode ? isOpenTile : true;
    const integrationInfo = Object.values(appIntegrationObj).find(
      (item: any) => item.integrationCode === integrationCode,
    );

    return (
      <>
        {isTileUnLocked && (
          <TileItem
            tileNo={index}
            key={index}
            tile={item}
            isTileUnLocked={isTileUnLocked}
            integrationInfo={integrationInfo}
            appId={appId}
            integrationCode={integrationCode}
            tileAnimation={tileAnimation}
            tileHover={{isTileHovered, setTileHover}}
            legacyMode={legacyMode}
            localDefinition={tags[0] == 'exposedTiles'}
            aiTile={tags[0] == 'aiTiles'}
          />
        )}
      </>
    );
  };
  const [fuseSearch, setFuseSearch] = React.useState(new Fuse([], TILES_FUSE_OPTIONS));
  // const [filterText, setFilterText] = useState('');
  const [filteredItems, setFilteredItems] = React.useState(fuseSearch?.search(searchQuery || ''));
  const [displaySearch, setDisplaySearch] = useState(false);
  const onSearchClose = () => {
    setDisplaySearch(!displaySearch);
    // setFilterText('');
    setFilteredItems([]);
  };

  useEffect(() => {
    setFuseSearch(new Fuse(tiles, TILES_FUSE_OPTIONS));
  }, [tiles]);
  useEffect(() => {
    if (searchQuery) {
      const fi = fuseSearch.search(searchQuery);
      setFilteredItems(fi.map(e => e.item));
    }
  }, [fuseSearch, searchQuery]);

  return (
    <View
      style={{
        flex: 1,
        flexBasis: 'auto',
        overflowX: isTileAnimationHappening ? 'visible' : 'hidden',
        overflowY:
          isTileAnimationHappening || (_.isEmpty(filteredItems) ? tiles?.length : filteredItems?.length) == 0
            ? 'hidden'
            : 'auto',
      }}>
      {isTileAnimationHappening && (
        <View style={styles.tilescontainer}>
          {tiles?.length !== 0 && (
            <Text style={[commonStyles.baseText, {color: '#909090', textAlign: 'center', width: '100%'}]}>
              Drag & drop a tile into your workspace
            </Text>
          )}
        </View>
      )}
      <View style={{flex: 1, flexBasis: 'auto'}}>
        {tilesLoading && (
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} />
          </View>
        )}
        <FlatList
          style={{
            flex: 1,
          }}
          scrollEnabled={true}
          numColumns={1}
          data={_.isEmpty(filteredItems) ? tiles : filteredItems}
          extraData={_.isEmpty(filteredItems) ? tiles?.length : filteredItems?.length}
          renderItem={renderItem}
          showsVerticalScrollIndicator={true}
          showsHorizontalScrollIndicator={false}
          onEndReachedThreshold={0.3}
          onEndReached={onEndReached}
          maxToRenderPerBatch={tags[0] == 'aiTiles' ? 4 : 10}
          initialNumToRender={tags[0] == 'aiTiles' ? 4 : 10}
          windowSize={2}
          scrollEventThrottle={tags[0] == 'aiTiles' ? 4 : 16}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tilescontainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 1,
    paddingVertical: 18,
  },
  tilesContainerText: {
    fontFamily: theme.FONT_FAMILY,
    justifyContent: 'center',
    alignSelf: 'center',
    padding: 10,
  },
  searchbox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  textBox: {
    flex: 1,
    //Remove border on select
    border: 'none',
  },
  loaderImage: {
    width: 40,
    height: 40,
    position: 'absolute',
    zIndex: 5,
    top: 10,
  },
});

export default TilesExplorer;
