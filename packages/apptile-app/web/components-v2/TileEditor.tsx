import React, {useEffect, useState} from 'react';
import {Pressable, StyleSheet, Text, View, Image} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import theme from '@/root/web/styles-v2/theme';
import {selectSelectedPluginConfig, selectSelectedPluginPageId} from '@/root/web/selectors/EditorSelectors';
import {selectModuleByUUID} from 'apptile-core';
import {EventHandlerConfig, ModuleEventHandlerConfig, PluginNamespaceImpl} from 'apptile-core';
import {addNamespace} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import PluginPropertyEditor from '../views/propertyInspector/components/PluginPropertyEditor';
import {ModuleInput} from './ModuleInput';
import Tabs from './composite/Tabs';
import EventsInspector from './controls/events/EventsInspector';
import commonStyles from '../styles-v2/commonStyles';
import CollapsiblePanel from '../components/CollapsiblePanel';
import EditorSectionHeader from '../components/controls/EditorSectionHeader';
import {MaterialCommunityIcons} from 'apptile-core';
import {currentPlanFeaturesSelector} from '../selectors/FeatureGatingSelector';
import {Plan, allAvailablePlans} from 'apptile-core';
import {cleanModuleAutoFill, sendTileAnalytics, setOpenPremiumModal} from '../actions/editorActions';
import TileHeader from './TileHeader';
import VariantSelection from './VariantSelection';
import {pluginConfigUpdatePath} from 'apptile-core';
import TileSetup from './TileSetup';
import TilesApi from '../api/TilesApi';
import Tooltip from './base/SimpleTooltip';
import RadioGroupControlV2 from '../components/controls-v2/RadioGroupControl';
import {EditorRootState} from '../store/EditorRootState';
import {Feather} from 'apptile-core';
import imagePlanMapping from '../common/featureGatingConstants';

const styles = StyleSheet.create({
  heading: {
    marginTop: 24,
    marginBottom: 16,
  },
  separator: {
    backgroundColor: '#BFBFBF',
    height: 1,
    marginTop: 24,
    width: '100%',
  },
  sectionSeparator: {
    backgroundColor: '#E5E5E5',
    height: 1,
    marginTop: 0,
    marginBottom: 4,
    width: '100%',
  },
  tabWrapperStyle: {padding: 0, backgroundColor: theme.INPUT_BACKGROUND, gap: 0},
  upgradeButtonStyle: {
    padding: 6,
    borderWidth: 0.5,
    borderColor: '#5853F040',
    color: theme.CONTROL_ACTIVE_COLOR,
    borderRadius: 6,
    paddingRight: 8,
  },
  header: {
    flexDirection: 'row',
    marginVertical: 15,
    width: '100%',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  tileName: {
    fontSize: 12,
    marginBottom: 4,
    color: '#B0B0B0',
  },
  delContainer: {
    justifyContent: 'flex-end',
    marginLeft: 5,
  },
  overlay: {
    backgroundColor: '#fffb',
    height: '100%',
    width: '100%',
    position: 'absolute',
    zIndex: 1,
  },
  tileSetupCont: {
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tileSetup: {
    width: 229,
    height: 219,
    backgroundColor: theme.PRIMARY_OPAQUE_BACKGROUND,
    paddingVertical: 58,
    paddingHorizontal: 13,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.CONTROL_BORDER,
  },
  tileSetupIcon: {
    borderRadius: '50%',
    height: 54,
    width: 54,
    marginBottom: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.PRIMARY_COLOR,
  },
});

function TileEditor({moduleConfig, pageId, location, disabled}: any) {
  const moduleInstanceConfig = moduleConfig?.get('config');
  const pluginId = moduleConfig?.get('id');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  const moduleRecord = useSelector((state: any) => selectModuleByUUID(state, moduleUUID));
  const inputs = moduleRecord?.get('inputs');
  const editors = moduleRecord?.get('editors')?.toKeyedSeq().toList().toJS();
  const styleEditors = moduleRecord?.get('styleEditors')?.toKeyedSeq().toList().toJS();
  const basicEditors = moduleRecord?.get('basicEditors')?.toKeyedSeq().toList().toJS();
  const editorSections = editors?.reduce((acc: any, editorRecord: any) => {
    if (acc.length === 0) {
      if (editorRecord?.editorType?.type === 'editorSectionHeader') {
        return [{sectionHeader: editorRecord, basicEditors: [], advanceEditors: []}];
      } else if (editorRecord?.advanceProperty) {
        return [{sectionHeader: null, basicEditors: [], advanceEditors: [editorRecord]}];
      } else {
        return [{sectionHeader: null, basicEditors: [editorRecord], advanceEditors: []}];
      }
    } else {
      if (editorRecord?.editorType?.type === 'editorSectionHeader') {
        acc?.push({sectionHeader: editorRecord, basicEditors: [], advanceEditors: []});
      } else if (editorRecord?.advanceProperty) {
        acc[acc.length - 1]?.advanceEditors?.push(editorRecord);
      } else {
        acc[acc.length - 1]?.basicEditors?.push(editorRecord);
      }
      return acc;
    }
  }, []);
  const styleEditorSections = styleEditors?.reduce((acc: any, editorRecord: any) => {
    if (acc.length === 0) {
      if (editorRecord?.editorType?.type === 'editorSectionHeader') {
        return [{sectionHeader: editorRecord, basicEditors: [], advanceEditors: []}];
      } else if (editorRecord?.advanceProperty) {
        return [{sectionHeader: null, basicEditors: [], advanceEditors: [editorRecord]}];
      } else {
        return [{sectionHeader: null, basicEditors: [editorRecord], advanceEditors: []}];
      }
    } else {
      if (editorRecord?.editorType?.type === 'editorSectionHeader') {
        acc?.push({sectionHeader: editorRecord, basicEditors: [], advanceEditors: []});
      } else if (editorRecord?.advanceProperty) {
        acc[acc.length - 1]?.advanceEditors?.push(editorRecord);
      } else {
        acc[acc.length - 1]?.basicEditors?.push(editorRecord);
      }
      return acc;
    }
  }, []);
  const basicEditorSections = basicEditors?.reduce((acc: any, editorRecord: any) => {
    if (acc.length === 0) {
      if (editorRecord?.editorType?.type === 'editorSectionHeader') {
        return [{sectionHeader: editorRecord, basicEditors: [], advanceEditors: []}];
      } else if (editorRecord?.advanceProperty) {
        return [{sectionHeader: null, basicEditors: [], advanceEditors: [editorRecord]}];
      } else {
        return [{sectionHeader: null, basicEditors: [editorRecord], advanceEditors: []}];
      }
    } else {
      if (editorRecord?.editorType?.type === 'editorSectionHeader') {
        acc?.push({sectionHeader: editorRecord, basicEditors: [], advanceEditors: []});
      } else if (editorRecord?.advanceProperty) {
        acc[acc.length - 1]?.advanceEditors?.push(editorRecord);
      } else {
        acc[acc.length - 1]?.basicEditors?.push(editorRecord);
      }
      return acc;
    }
  }, []);
  // console.log(`editorSections`, editorSections);
  const outputs = moduleRecord?.get('outputs');
  let rawEvents = moduleRecord?.get('defaultEventHandlers');
  const events = rawEvents?.filter((event: ModuleEventHandlerConfig) => {
    if (event.get('isExposed') && event.get('eventHandler').method === 'navigate') {
      return true;
    } else if (
      event.get('isExposed') &&
      event.get('eventHandler').method === 'triggerAction' &&
      event.get('eventHandler').pluginId === 'Apptile' &&
      event.get('eventHandler').value === 'openLink'
    ) {
      return true;
    }
    return false;
  });
  rawEvents = rawEvents?.map((event: ModuleEventHandlerConfig, index) =>
    event.set('eventHandler', moduleInstanceConfig?.get('events').get(index)),
  );
  // logger.info('moduleRecord', rawEvents, events, moduleRecord?.toJS(), moduleConfig, moduleInstanceConfig.toJS());
  const [closeSectionIndex, setCloseSectionIndex] = useState<Number[]>([]);
  const [advanceSectionOpen, setAdvanceSectionOpen] = useState<Number[]>([]);
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const dispatch = useDispatch();

  const sectionsRender = (sectionEditors: any) => {
    const onChange = (section: string, property: string, advanceProperty: boolean) => {
      if (!moduleConfig?.config?.get('changesDone')) {
        dispatch(
          pluginConfigUpdatePath(pluginId, pageId, ['config'], {
            changesDone: true,
          }),
        );
      }
      dispatch(
        sendTileAnalytics(moduleUUID, 'editor:tile_tileCustomized', {
          tab: location == 'style' ? 'style' : 'content',
          section,
          property,
          advanceProperty,
        }),
      );
    };

    return (
      <>
        {sectionEditors?.map((currentEditors: any, index: number) =>
          currentEditors.sectionHeader ? (
            <>
              <CollapsiblePanel
                isOpen={!closeSectionIndex.includes(index)}
                setOpen={(open: boolean) => {
                  if (closeSectionIndex.includes(index)) closeSectionIndex.splice(closeSectionIndex.indexOf(index), 1);
                  else closeSectionIndex.push(index);
                  setCloseSectionIndex([...closeSectionIndex]);
                }}
                backgroundStyle={{borderWidth: 0}}
                title="section"
                customHeader={
                  <EditorSectionHeader
                    label={currentEditors.sectionHeader?.editorType?.props?.label}
                    name={''}
                    isPremiumDesabled={
                      !!currentEditors.advanceEditors?.find(
                        (editorRecord: any) =>
                          !currentPlanFeatures.includes(allAvailablePlans[editorRecord?.basePlan as Plan]),
                      )
                    }
                    icon={!closeSectionIndex.includes(index) ? 'chevron-up' : 'chevron-down'}
                    iconSize={18}
                    iconType={'Material Icon'}
                  />
                }>
                {currentEditors.basicEditors?.map((editorRecord: any, key: number) => (
                  <PluginPropertyEditorWrapper
                    key={key}
                    {...{pageId, moduleConfig, editorRecord}}
                    onChange={() => {
                      onChange(currentEditors.sectionHeader?.editorType?.props?.label, editorRecord?.label, false);
                    }}
                  />
                ))}
                {currentEditors.basicEditors.length == 0 &&
                  currentEditors.advanceEditors.length > 0 &&
                  currentEditors.advanceEditors?.slice(0, 2)?.map((editorRecord: any, key: number) => {
                    const isPremiumDisabled = !currentPlanFeatures.includes(
                      allAvailablePlans[editorRecord?.basePlan as Plan],
                    );
                    return (
                      <View>
                        {isPremiumDisabled && (
                          <Pressable
                            style={{
                              position: 'absolute',
                              height: '100%',
                              width: '100%',
                              backgroundColor: '#fff6',
                              zIndex: 10,
                            }}
                            onPress={() => dispatch(setOpenPremiumModal(true, editorRecord?.basePlan))}
                          />
                        )}
                        <PluginPropertyEditorWrapper
                          key={key}
                          {...{pageId, moduleConfig, editorRecord}}
                          onChange={() => {
                            onChange(currentEditors.sectionHeader?.editorType?.props?.label, editorRecord?.label, true);
                          }}
                        />
                      </View>
                    );
                  })}
                {currentEditors.advanceEditors.length > 0 &&
                  (currentEditors.basicEditors.length > 0 || currentEditors.advanceEditors.length > 2) && (
                    <CollapsiblePanel
                      isOpen={advanceSectionOpen.includes(index)}
                      setOpen={(open: boolean) => {
                        if (advanceSectionOpen.includes(index))
                          advanceSectionOpen.splice(advanceSectionOpen.indexOf(index), 1);
                        else advanceSectionOpen.push(index);
                        setAdvanceSectionOpen([...advanceSectionOpen]);
                      }}
                      backgroundStyle={{borderWidth: 0}}
                      overflowStyle={{flexDirection: 'column-reverse'}}
                      title="section"
                      customHeader={
                        <Text
                          style={[
                            commonStyles.baseText,
                            {color: theme.CONTROL_ACTIVE_COLOR, textAlign: 'center', marginTop: 16},
                          ]}>
                          {advanceSectionOpen.includes(index) ? 'See Less' : 'See More'}{' '}
                          <MaterialCommunityIcons
                            style={{textAlignVertical: 'center'}}
                            name={advanceSectionOpen.includes(index) ? 'chevron-up' : 'chevron-down'}
                            size={18}
                            color={theme.CONTROL_ACTIVE_COLOR}
                          />
                        </Text>
                      }>
                      {currentEditors.advanceEditors
                        ?.slice(currentEditors.basicEditors.length == 0 ? 2 : 0)
                        ?.map((editorRecord: any, key: number) => {
                          const isPremiumDisabled = !currentPlanFeatures.includes(
                            allAvailablePlans[editorRecord?.basePlan as Plan],
                          );
                          return (
                            <View>
                              {isPremiumDisabled && (
                                <Pressable
                                  style={{
                                    position: 'absolute',
                                    height: '100%',
                                    width: '100%',
                                    backgroundColor: '#fff6',
                                    zIndex: 10,
                                  }}
                                  onPress={() => dispatch(setOpenPremiumModal(true, editorRecord?.basePlan))}
                                />
                              )}
                              <PluginPropertyEditorWrapper
                                key={key}
                                {...{pageId, moduleConfig, editorRecord}}
                                onChange={() => {
                                  onChange(
                                    currentEditors.sectionHeader?.editorType?.props?.label,
                                    editorRecord?.label,
                                    true,
                                  );
                                }}
                              />
                            </View>
                          );
                        })}
                    </CollapsiblePanel>
                  )}
              </CollapsiblePanel>
              <View style={styles.sectionSeparator} />
            </>
          ) : (
            <>
              {[...currentEditors.basicEditors, ...currentEditors.advanceEditors].map(
                (editorRecord: any, key: number) => (
                  <PluginPropertyEditorWrapper
                    key={key}
                    {...{pageId, moduleConfig, editorRecord}}
                    onChange={() => {
                      onChange('NO SECTION', editorRecord?.label, false);
                    }}
                  />
                ),
              )}
            </>
          ),
        )}
      </>
    );
  };

  return (
    <View style={{marginTop: 4}}>
      {location === 'style' ? (
        sectionsRender(styleEditorSections)
      ) : location === 'settings' ? (
        <>
          {/* {!!inputs?.length && <Text style={styles.heading}>Inputs</Text>}
      {inputs?.map(input => (
        <ModuleInput
          key={`${moduleUUID}_input_${input}`}
          pluginId={moduleUUID}
          pageId={pageId}
          propertyName={input}
          propertyValue={moduleInstanceConfig.get(input)}
        />
      ))} */}

          {/* {editors?.length && <Text style={styles.heading}>Forwarded Props</Text>} */}
          {sectionsRender(editorSections)}
          {/* {!!events?.size && <Text style={[styles.heading, commonStyles.heading]}>Interactions</Text>} */}
        </>
      ) : (
        <>
          <VariantSelection key={`${moduleUUID}_Variants`} />
          {/* <TileSetup key={`${moduleUUID}_TileSetup`} /> */}
          {sectionsRender(basicEditorSections)}
          {!!outputs?.length && <View style={styles.separator} />}
          {!!outputs?.length && <Text style={[styles.heading, commonStyles.heading]}>Outputs</Text>}
          {outputs?.map(input => (
            <ModuleInput
              key={`${moduleUUID}_input_${input}`}
              pluginId={moduleUUID}
              pageId={pageId}
              propertyName={input}
              propertyValue={moduleInstanceConfig.get(input)}
            />
          ))}
          {!!events?.size && (
            <EventsInspector
              key={pluginId}
              rawEvents={rawEvents}
              pluginId={pluginId}
              pageId={pageId}
              moduleUUID={moduleUUID}
            />
          )}
        </>
      )}
    </View>
  );
}

const PluginPropertyEditorWrapper = ({pageId, moduleConfig, editorRecord, onChange}: any) => {
  let {label, selector, editorType: propEditor} = editorRecord;

  const propertyName = _.last(selector);
  if (!propEditor) {
    propEditor = {
      name: propertyName,
      type: 'codeInput',
      props: {
        label: label,
        placeholder: '',
      },
    };
  }
  propEditor.name = propertyName;
  propEditor.props.label = label;

  const moduleInstanceConfig = moduleConfig.get('config');
  const childNamespace = moduleInstanceConfig.get('childNamespace');

  const moduleNamespace = new PluginNamespaceImpl(
    (moduleConfig.namespace?.getNamespace() ?? []).concat([childNamespace]),
    moduleConfig.id,
  );
  const pluginId = addNamespace(moduleNamespace, selector[0]);
  const pluginConfig = useSelector(state => selectPluginConfig(state, pageId, pluginId));

  const propertyUpdatePath = selector.slice(1, -1);

  return (
    <PluginPropertyEditor
      key={pluginConfig?.id + propEditor.name + label}
      editor={propEditor}
      entityConfig={pluginConfig}
      config={pluginConfig?.getIn(propertyUpdatePath)}
      pageId={pageId}
      pluginId={pluginId}
      configPathSelector={selector}
      inModule={false}
      hideExposePropButton
      sendTileAnalytics
      onChange={onChange}
      isModule
    />
  );
};

const TileSetupComplete = () => {
  return (
    <View style={styles.tileSetupCont}>
      <View style={styles.tileSetup}>
        <View style={styles.tileSetupIcon}>
          <Feather name="check" size={30} color={'#fff'} />
        </View>
        <Text style={commonStyles.heading}>TILE SETUP COMPLETE</Text>
      </View>
    </View>
  );
};

const TileEditorWrapper = ({isDeletable, onDelete}) => {
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const pageId = useSelector(state => selectSelectedPluginPageId(state));

  const editorOpacity = useSharedValue(0);
  useEffect(() => {
    if (!moduleConfig?.id) return;
    editorOpacity.value = withTiming(0.5, {duration: 380, easing: Easing.bezier(0.25, 0.1, 0.25, 1)}, () => {
      editorOpacity.value = withTiming(1, {duration: 380, easing: Easing.bezier(0.25, 0.1, 0.25, 1)});
    });
  }, [moduleConfig?.id, editorOpacity]);
  const editorAnimation = useAnimatedStyle(() => ({opacity: editorOpacity.value}));

  // if (!moduleConfig) return null;
  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  const [tileVariants, setTileVariants] = useState(null);
  const [variantLoading, setVariantLoading] = useState(true);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [currentEditorSection, setCurrentEditorSection] = useState('basics');
  const {moduleAutoFilling} = useSelector((state: EditorRootState) => state?.platform);
  const tiles = moduleAutoFilling[pageId];
  const tileMandatoryStatus = tiles?.[moduleConfig?.id] ?? {};
  const {filled, filling, failed} = tileMandatoryStatus;
  const dispatch = useDispatch();

  useEffect(() => {
    if (moduleUUID) {
      if (!tileVariants || !Array.isArray(tileVariants)) {
        setVariantLoading(true);
        TilesApi.getTileVariants(moduleUUID)
          .then(resp => {
            setTileVariants(resp.data.items);
            setVariantLoading(false);
            const variant =
              moduleInstanceConfig?.get('variantSelected') == 'default'
                ? {id: 'default'}
                : resp.data.items?.find((e: any) => e.id === moduleInstanceConfig?.get('variantSelected'));
            setSelectedVariant(variant);
            if (resp.data.items.length == 0) setSelectedVariant({id: 'no-variants'});
          })
          .catch(() => {
            setTileVariants(null);
            setVariantLoading(false);
            setSelectedVariant({id: 'no-variants'});
          });
      } else {
        const variant =
          moduleInstanceConfig?.get('variantSelected') == 'default'
            ? {id: 'default'}
            : tileVariants?.find((e: any) => e.id === moduleInstanceConfig?.get('variantSelected'));
        setSelectedVariant(variant);
        if (tileVariants.length == 0) setSelectedVariant({id: 'no-variants'});
      }
    }
  }, [moduleInstanceConfig, moduleUUID, tileVariants]);

  useEffect(() => {
    if (filled) {
      dispatch(cleanModuleAutoFill(pageId, moduleConfig?.id));
    }
  }, [filled]);
  return (
    <Animated.View style={[{flex: 1}, editorAnimation]}>
      <TileHeader isDeletable={isDeletable} onDelete={onDelete} />
      {!filled ? (
        <View style={{overflow: 'scroll', flex: 1, paddingRight: 4}}>
          {/* <Tooltip
          toolTipMenuStyles={{width: 270}}
          tooltipWrapperStyles={{bottom: '90%'}}
          tooltip={'Select a tile design to enable this section'}
          visible={!selectedVariant}
          position={'bottom'}> */}
          <View>
            {/* {!selectedVariant && <View style={styles.overlay} />} */}
            <RadioGroupControlV2
              label=""
              options={[
                {text: 'Basics', value: 'basics'},
                {text: 'Settings', value: 'settings'},
                {text: 'Style', value: 'style'},
              ]}
              value={currentEditorSection}
              disableBinding={true}
              onChange={setCurrentEditorSection}
            />
            <TileEditor
              moduleConfig={moduleConfig}
              pageId={pageId}
              key={currentEditorSection}
              location={currentEditorSection}
              disabled={!selectedVariant}
            />
          </View>
          {/* </Tooltip> */}
        </View>
      ) : (
        <TileSetupComplete />
      )}
    </Animated.View>
  );
};

export default TileEditorWrapper;