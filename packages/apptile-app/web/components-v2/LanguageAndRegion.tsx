import theme from '@/root/web/styles-v2/theme';
import _, {debounce} from 'lodash';
import Fuse from 'fuse.js';
import React, {useCallback, useEffect, useState} from 'react';
import {
  Pressable as RNWebPressable,
  PressableProps as RNWebPressableProps,
  StyleSheet,
  View as RNView,
  Image,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import AppConfigApi from '../api/AppConfigApi';
import {useAppBridge} from '@shopify/app-bridge-react';
import {Redirect} from '@shopify/app-bridge/actions';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import AppApiV2 from '../api/AppApiV2';
import Button from '@/root/web/components-v2/base/Button';
import {makeToast} from '../actions/toastActions';
import {DropDown} from '@/root/web/components-v2/DropDown';
import {languageOptions} from '../common/languageForkUtils';
import {checkApptileEmailSelector} from '../selectors/FeatureGatingSelector';
import TextElement from './base/TextElement';
import ModalComponent from './base/Modal';
import PopoverComponent from './base/Popover';
import TextInput from './base/TextInput';
import commonStyles from '../styles-v2/commonStyles';
import {addMultiLanguageSwitchPlugin, fetchAppForks} from '../actions/editorActions';
import {CreateWidget, Icon, MaterialCommunityIcons, DispatchActions} from 'apptile-core';
import CodeInput from '../components/codeEditor/codeInput';
import {ApptileWebIcon} from '../icons/ApptileWebIcon.web';
import Analytics from '@/root/web/lib/segment';

const FUSE_OPTIONS = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  findAllMatches: true,
  minMatchCharLength: 1,
  ignoreLocation: true,
  keys: ['name', 'code'],
};

export type PressableProps = RNWebPressableProps & {
  onHoverIn: (e: MouseEvent) => void;
  onHoverOut: (e: MouseEvent) => void;
};
export function Pressable(props: PressableProps) {
  return <RNWebPressable {...props} />;
}

// Custom View component to fix TypeScript errors with children prop
export const View: React.FC<React.ComponentProps<typeof RNView> & {children?: React.ReactNode}> = props => {
  return <RNView {...props} />;
};

const useGetAppBridge = (isEmbeddedInShopify: boolean): any => {
  const voidFn = () => false;
  const getAppBridge = isEmbeddedInShopify ? useAppBridge : voidFn;
  return getAppBridge();
};

// Language Modal Content Component
type LanguageModalContentProps = {
  languageOptions: Array<{name: string; code: string}>;
  selectedLanguage: string;
  setSelectedLanguage: (value: string) => void;
  allForks: Record<string, any>;
  baseTemplate: string;
  setBaseTemplate: (value: string) => void;
  onAddLanguage: () => void;
  onClose: () => void;
  isLoading: boolean;
  modalState: 'input' | 'progress' | 'success' | 'error';
  currentStep: number;
  completedSteps: number[];
  isForkCreationFailed: boolean;
  onPreview: () => void;
};

const LanguageModalContent: React.FC<LanguageModalContentProps> = ({
  languageOptions,
  selectedLanguage,
  setSelectedLanguage,
  allForks,
  baseTemplate,
  setBaseTemplate,
  onAddLanguage,
  onClose,
  isLoading,
  modalState = 'input',
  currentStep = 0,
  completedSteps = [],
  isForkCreationFailed,
  onPreview,
}) => {
  const [templateDropDown, setTemplateDropDown] = useState(false);
  const [templateDropDownWidth, setTemplateDropDownWidth] = useState(0);

  const [langDropDown, setLangDropDown] = useState(false);
  const [languageDropDownWidth, setLanguageDropDownWidth] = useState(0);

  const [languages, setLanguages] = useState(languageOptions);
  const [langQuery, setLangQuery] = useState('');
  const [fuseSearch, setFuseSearch] = useState(new Fuse(languageOptions ?? [], FUSE_OPTIONS));

  const debouncedSetQuery = debounce(setLangQuery, 100);

  useEffect(() => {
    if (langQuery.trim() === '') {
      setLanguages(languageOptions);
    } else {
      const result = fuseSearch.search(langQuery);
      setLanguages(result.map(r => r.item));
    }
  }, [langQuery, fuseSearch]);

  useEffect(() => {
    const mainFork = _.find(allForks, {forkName: 'main'});
    setBaseTemplate(mainFork.id);
    const firstAvailableLanguage = _.find(languageOptions, o => !_.some(allForks, f => f.title === o.name));
    setSelectedLanguage(firstAvailableLanguage?.code ?? languageOptions[0].code);
  }, []);

  // Translation steps data
  const steps = [
    {id: 1, label: 'Content translated', pendingText: 'Translating content...', completedText: 'Content translated'},
    {
      id: 2,
      label: 'Language settings applied',
      pendingText: 'Applying language settings...',
      completedText: 'Language settings applied',
    },
    {
      id: 3,
      label: 'Text & UI tables updated',
      pendingText: 'Updating text & UI lables...',
      completedText: 'Text & UI lables updated',
    },
    {
      id: 4,
      label: 'Preparing live preview',
      pendingText: 'Preparing live preview...',
      completedText: 'Live preview prepared',
    },
    {
      id: 5,
      label: 'Finalising setup',
      pendingText: 'Finalising setup...',
      completedText: 'Setup finalised',
      failedText: 'Something went wrong!',
    },
  ];

  const handleLanguageSelect = (code: string) => {
    setSelectedLanguage(code);
    setLangDropDown(false);
  };

  const handleTemplateSelect = (forkId: string) => {
    setBaseTemplate(forkId);
    setTemplateDropDown(false);
  };

  const handleDropDownToggle = (type: string) => {
    switch (type) {
      case 'language':
        setLangDropDown(prev => !prev);
        setTemplateDropDown(false);
        break;

      case 'baseTemplate':
        setTemplateDropDown(prev => !prev);
        setLangDropDown(false);
        break;

      default:
        break;
    }
  };

  const renderInputState = () => (
    <View style={styles.languageModalContent}>
      {/* Language Selection */}
      <View style={{...styles.formGroup, zIndex: 2}}>
        <TextElement style={styles.languageModalLabel}>Choose a Language</TextElement>
        <View style={styles.dropdownContainer}>
          <Pressable onPress={() => handleDropDownToggle('language')} style={{backgroundColor: 'white', zIndex: 99}}>
            <View
              onLayout={e => setLanguageDropDownWidth(e.nativeEvent.layout.width)}
              style={[styles.popOverCommonTrigger]}>
              <TextElement style={[commonStyles.baseText, {color: '#000'}]}>
                {(() => {
                  const selectedLang = languageOptions.find(lang => lang.code === selectedLanguage);
                  return selectedLang ? `${selectedLang.name} (${selectedLang.code})` : null;
                })()}
              </TextElement>

              <Icon name="chevron-down" iconType="MaterialCommunityIcons" color="#000" size={16} />
            </View>
          </Pressable>

          {langDropDown && (
            <View style={[styles.popOverCommonChildren, {width: languageDropDownWidth, zIndex: 99}]}>
              <View style={[styles.inputContainer]}>
                <ApptileWebIcon name={'magnify'} size={19} color={theme.CONTROL_INPUT_COLOR} />
                <CodeInput
                  placeholder={'Search language'}
                  singleLine={true}
                  onChange={function (editor: unknown, data: unknown, value: string): void {
                    debouncedSetQuery(value);
                  }}
                  value={langQuery}
                  defaultValue={langQuery}
                />
              </View>
              <div className="popOverCustomScrollBar" style={styles.popOverCommonChildrenContent}>
                {languages.map((lang, i) => {
                  const isLangExist = _.some(allForks, e => _.lowerCase(e.title) === _.lowerCase(lang.name));
                  return (
                    <Pressable
                      key={i}
                      disabled={isLangExist}
                      onPress={() => handleLanguageSelect(lang.code)}
                      onHoverIn={() => {}}
                      onHoverOut={() => {}}
                      style={[
                        styles.popOverCommonChildrenContentItem,
                        lang.code == selectedLanguage && {backgroundColor: '#E6F0FF'},
                        isLangExist && {backgroundColor: '#F3F3F3'},
                      ]}>
                      <TextElement
                        style={[
                          commonStyles.baseText,
                          styles.popOverCommonChildrenContentText,
                          lang.code == selectedLanguage &&
                            !isLangExist &&
                            styles.popOverCommonChildrenContentTextSelected,
                          isLangExist && styles.popOverCommonChildrenContentTextDisabled,
                        ]}>
                        {lang.name}
                      </TextElement>

                      {lang.code == selectedLanguage && !isLangExist && (
                        <MaterialCommunityIcons name="check" size={16} color={theme.PRIMARY_COLOR} />
                      )}
                    </Pressable>
                  );
                })}
              </div>
            </View>
          )}
        </View>
      </View>

      {/* Base Template */}
      <View style={{...styles.formGroup, zIndex: 1}}>
        <TextElement style={styles.languageModalLabel}>Choose a Base Template</TextElement>
        <TextElement style={styles.helperText}>
          Choosing a base template will translate that language template into the new language
        </TextElement>
        <View style={styles.dropdownContainer}>
          <Pressable onPress={() => handleDropDownToggle('baseTemplate')}>
            <View
              onLayout={e => setTemplateDropDownWidth(e.nativeEvent.layout.width)}
              style={[styles.popOverCommonTrigger]}>
              <TextElement style={[commonStyles.baseText, {color: '#000'}]}>
                {(() => {
                  const selectedFork = allForks && baseTemplate ? allForks[baseTemplate].title : null;
                  return selectedFork == 'Main' ? 'English' : selectedFork || 'Unnamed Fork';
                })()}
              </TextElement>

              <Icon name="chevron-down" iconType="MaterialCommunityIcons" color="#000" size={16} />
            </View>
          </Pressable>

          {templateDropDown && (
            <View style={[styles.popOverCommonChildren, {width: templateDropDownWidth}]}>
              <div className="popOverCustomScrollBar" style={styles.popOverCommonChildrenContent}>
                {Object.entries(allForks || {}).map(([forkId, fork], i) => (
                  <Pressable
                    key={i}
                    onPress={() => handleTemplateSelect(forkId)}
                    onHoverIn={() => {}}
                    onHoverOut={() => {}}
                    style={[
                      styles.popOverCommonChildrenContentItem,
                      forkId == baseTemplate && {backgroundColor: '#E6F0FF'},
                    ]}>
                    <TextElement
                      style={[
                        commonStyles.baseText,
                        styles.popOverCommonChildrenContentText,
                        forkId == baseTemplate && styles.popOverCommonChildrenContentTextSelected,
                      ]}>
                      {fork.title == 'Main' ? 'English' : fork.title || 'Unnamed Fork'}
                    </TextElement>

                    {forkId == baseTemplate && (
                      <MaterialCommunityIcons name="check" size={16} color={theme.PRIMARY_COLOR} />
                    )}
                  </Pressable>
                ))}
              </div>
            </View>
          )}
        </View>
      </View>

      {/* Note Section */}
      <View style={styles.noteContainer}>
        <TextElement style={styles.noteTitle}>NOTE -</TextElement>
        <View style={styles.bulletList}>
          <View style={styles.bulletItem}>
            <View style={styles.bullet} />
            <TextElement style={styles.bulletText}>
              Your current changes will be saved when you create the new language template.
            </TextElement>
          </View>
          <View style={styles.bulletItem}>
            <View style={styles.bullet} />
            <TextElement style={styles.bulletText}>
              We recommend completing your app's design in the default language before adding a new language template.
            </TextElement>
          </View>
          <View style={styles.bulletItem}>
            <View style={styles.bullet} />
            <TextElement style={styles.bulletText}>
              Your default language template will be translated only once when creating a new language. After that, any
              further changes must be made individually in each language template.
            </TextElement>
          </View>
        </View>
      </View>

      <View style={styles.languageModalFooter}>
        <Button
          children={<TextElement style={styles.addLanguageButtonText}>Add language</TextElement>}
          onPress={onAddLanguage}
          containerStyles={{
            ...styles.addLanguageModalButtonWrapper,
          }}
          disabled={!selectedLanguage || !baseTemplate || isLoading}
          loading={isLoading}
          color={'CTA'}
        />
      </View>
    </View>
  );

  const renderProgressState = () => (
    <>
      <div className="progressWrapperLanguageModalWrapper">
        <div className="progressBarLanguageModal" style={{width: `${20 * completedSteps.length}%`}}></div>
      </div>
      <View style={[styles.languageModalContent, {padding: 0, height: 430, width: '100%'}]}>
        <View style={styles.stepsWrapper}>
          <View style={styles.stepsContainer}>
            {steps.map(step => {
              const isCompleted = completedSteps.includes(step.id);
              const isInProgress = currentStep === step.id && !isCompleted;

              return (
                <View key={step.id} style={styles.stepItem}>
                  <View
                    style={[
                      styles.stepIcon,
                      isCompleted && styles.stepCompleted,
                      step.id === 5 && isForkCreationFailed && styles.stepFailed,
                    ]}>
                    {step.id === 5 && isForkCreationFailed ? (
                      <MaterialCommunityIcons name="close" size={18} color="#fff" />
                    ) : isCompleted ? (
                      <MaterialCommunityIcons name="check" size={18} color="#fff" />
                    ) : (
                      // <View style={[styles.stepDot, isInProgress && styles.stepInProgress]} />
                      <View />
                    )}
                  </View>
                  <View style={styles.stepTextContainer}>
                    <TextElement style={[styles.stepText, isCompleted && styles.stepTextCompleted]}>
                      {step.id === 5 && isForkCreationFailed
                        ? step.failedText
                        : isCompleted
                        ? step.completedText
                        : step.pendingText}
                    </TextElement>
                  </View>
                </View>
              );
            })}
          </View>
        </View>
      </View>
    </>
  );

  const renderSuccessState = () => (
    <View
      style={[
        styles.languageModalContent,
        {
          height: 430,
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        },
      ]}>
      <View style={styles.successIconContainer}>
        <Image
          source={require('../assets/images/language-success.png')}
          style={{width: 150, height: 150}}
          resizeMode="contain"
        />
      </View>
      <View style={[styles.successTextContainer]}>
        <TextElement style={styles.successTitle}>Your Translated App is Ready to Preview!</TextElement>
        <TextElement style={styles.successMessage}>
          Your new language template is set up! Review translations and make edits if needed before publishing.
        </TextElement>
        {/* <Button children="Close" onPress={onClose} containerStyles={styles.closeSuccessButton} /> */}
        <Button
          children={<TextElement style={styles.addLanguageButtonText}>Preview App</TextElement>}
          onPress={onPreview}
          containerStyles={[styles.addLanguageModalButtonWrapper]}
          loading={isLoading}
          disabled={isLoading}
          color={'CTA'}
        />
      </View>
    </View>
  );

  const renderForkCreationFailureState = () => (
    <View
      style={[
        styles.languageModalContent,
        {
          height: 430,
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        },
      ]}>
      <View style={styles.successIconContainer}>
        <Image
          source={require('../assets/images/something_went_wrong.png')}
          style={{width: 150, height: 150}}
          resizeMode="contain"
        />
      </View>
      <View style={[styles.successTextContainer]}>
        <TextElement style={styles.successTitle}>Something Went Wrong</TextElement>
        <TextElement style={styles.successMessage}>
          We couldn’t complete the setup. Please try again or contact support if the issue persists.
        </TextElement>
        <Button
          children={<TextElement style={styles.addLanguageButtonText}>Close</TextElement>}
          onPress={onClose}
          containerStyles={[styles.addLanguageModalButtonWrapper]}
          // disabled={!selectedLanguage || !baseTemplate || isLoading}
          // loading={isLoading}
        />
      </View>
    </View>
  );

  return (
    <View style={styles.languageModalContainer}>
      <View style={[styles.languageModalHeader, modalState === 'success' && {borderBottomWidth: 0}]}>
        <TextElement style={{...commonStyles.baseText, ...styles.languageModalTitle}}>
          {modalState === 'input' ? 'Add a new language' : modalState === 'progress' ? 'Translating your app' : ''}
        </TextElement>
        {modalState !== 'progress' && (
          <Pressable style={styles.closeButton} onPress={onClose} onHoverIn={() => {}} onHoverOut={() => {}}>
            <MaterialCommunityIcons name="close" size={12} />
          </Pressable>
        )}
      </View>
      <View style={{}}>
        {modalState === 'input' && renderInputState()}
        {modalState === 'progress' && renderProgressState()}
        {modalState === 'success' && renderSuccessState()}
        {modalState === 'error' && renderForkCreationFailureState()}
      </View>
    </View>
  );
};

export const LanguageAndRegion = () => {
  const dispatch = useDispatch();
  const [tooltipVisible, setTooltipVisible] = useState<number | null>(null);
  const apptileState = useSelector(state => state.apptile);

  const platformState = useSelector((state: EditorRootState) => state.platform);
  const {isEmbeddedInShopify} = platformState;

  const appId = apptileState?.appId as string;
  const orgId = apptileState?.orgId as string;

  const forks = useSelector(state => state.forks);
  const [allForks, setAllForks] = useState<Record<string, any>>({});
  const [activeForkId, setActiveForkId] = useState();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [isForkModalOpen, setIsForkModalOpen] = useState(false);

  /* Create Fork States */
  const [isCreateFork, setIsCreateFork] = useState(false);
  const [forkName, setForkName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [forkFrom, setForkFrom] = useState({});
  const [forkOptions, setForkOptions] = useState<any[]>([]);
  const isApptileEmail = useSelector(checkApptileEmailSelector) ?? false;

  /* Language Modal States */
  const [isLanguageModalOpen, setIsLanguageModalOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [baseTemplate, setBaseTemplate] = useState('');
  const [modalState, setModalState] = useState<'input' | 'progress' | 'success' | 'error'>('input');
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [isForkCreationFailed, setIsForkCreationFailed] = useState<boolean>(false);
  const [newFork, setNewFork] = useState<any>();
  const [forkVisibilityInfo, setForkVisibilityInfo] = useState<{[key: number]: boolean}>({});
  const [updatingVisibilityForForkId, setUpdatingVisibilityForForkId] = useState<number | null>(null);
  const [forkCreationAvailable, setForkCreationAvailable] = useState(false);

  useEffect(() => {
    if (apptileState.appSaveId && isLanguageModalOpen && forkCreationAvailable) {
      addLanguage();
      setForkCreationAvailable(false);
    }
  }, [apptileState.appSaveId]);

  const onAddLanguage = () => {
    try {
      Analytics.track('editor:multilanguage_addLanguageClicked');
    } catch (error) {
      console.error('Error tracking event in Multi Language:', error);
    }
    dispatch(addMultiLanguageSwitchPlugin(baseTemplate == activeForkId ? true : false));
    setForkCreationAvailable(true);
  };

  // Using language options from shared utility file

  // Reset modal state when opening
  const openLanguageModal = () => {
    setSelectedLanguage('');
    setBaseTemplate('');
    setModalState('input');
    setCurrentStep(0);
    setCompletedSteps([]);
    setIsLanguageModalOpen(true);
  };

  // Base template options will be populated from forks

  // Function to simulate step progression with a Promise that resolves when ready for final step
  const progressSteps = () => {
    // Start with step 1
    setCurrentStep(1);

    return new Promise<void>(resolve => {
      // Progress through steps 1-4 with 3-second intervals
      const executeSteps = async () => {
        for (let step = 1; step <= 4; step++) {
          await new Promise(r => setTimeout(r, 3000));
          setCompletedSteps(prev => [...prev, step]);
          if (step < 4) {
            setCurrentStep(step + 1);
          } else {
            // We've completed step 4, now set current step to 5
            setCurrentStep(5);
            // Signal that we're ready for final step
            resolve();
          }
        }
      };

      executeSteps();
    });
  };

  const addLanguage = async () => {
    // Get the selected language object
    const selectedLang = languageOptions.find(lang => lang.code === selectedLanguage);
    const selectedFork = allForks && baseTemplate ? allForks[baseTemplate] : null;

    if (!selectedLang || !selectedFork) {
      return; // Button should be disabled in this case, but adding as a safeguard
    }

    // Prepare payload for API call according to required format
    const payload = {
      forkFrom: {
        id: baseTemplate,
      },
      forkName: selectedLang.name.toLowerCase().replace(/\s+/g, '-'),
      forkTitle: selectedLang.name,
      targetLanguage: selectedLang.code,
    };

    // Log the payload
    console.log('Create Language Fork Payload:', payload);

    // Switch to progress state
    setIsLoading(true);
    setModalState('progress');

    const apiPromise = AppApiV2.createLanguageFork(appId, payload);

    // Start progressing through steps 1-4 with 3-second intervals
    // This returns a promise that resolves when we're ready for step 5
    const stepsPromise = progressSteps();

    try {
      // Wait for steps 1-4 to complete
      await stepsPromise;

      // Now at step 5, wait for API to complete if it hasn't already
      const response = await apiPromise;
      console.log('Language Fork API Response:', response);
      setNewFork(response.data);
      dispatch(fetchAppForks(appId));

      try {
        Analytics.track('editor:multilanguage_addLanguageSuccess');
      } catch (error) {
        console.error('Error tracking event in Multi Language:', error);
      }

      // Wait 3 seconds for the final step
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Complete the final step
      setCompletedSteps(prev => [...prev, 5]);

      // Show success state after a brief pause
      setTimeout(() => {
        setModalState('success');
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error creating language fork:', error);

      // Even on error, complete the UI flow for better UX after waiting the 3 seconds
      await new Promise(resolve => setTimeout(resolve, 3000));
      setIsForkCreationFailed(true);
      setCompletedSteps(prev => [...prev, 4]);

      setTimeout(() => {
        setModalState('error');
        setIsLoading(false);
        setIsForkCreationFailed(false);
      }, 1000);
    }
  };

  const onPreview = async () => {
    if (newFork) {
      const forkToSwitch = newFork.forkId;
      onSwitchFork(forkToSwitch, true);
    }
  };

  useEffect(() => {
    fetchForkVisibility();
  }, []);

  const fetchForkVisibility = async () => {
    try {
      const response = await AppApiV2.fetchForksVisibility(appId);
      if (response.data?.success && response.data?.forkInfo?.forks) {
        const visibilityMap = response.data.forkInfo.forks.reduce((acc: {[key: number]: boolean}, fork: any) => {
          acc[fork?.id] = fork?.visible && !!fork?.publishedCommitId;
          return acc;
        }, {});
        setForkVisibilityInfo(visibilityMap);
      }
    } catch (error) {
      console.error('Error fetching fork visibility:', error);
    }
  };

  useEffect(() => {
    const currentForkId = apptileState.forkId;
    const availableForks = forks?.appForksById;

    setAllForks(availableForks);
    setActiveForkId(currentForkId);
    setForkFrom(availableForks[currentForkId]);
    setForkOptions(_.map(availableForks, 'title'));

    // No default selection for base template
  }, [forks, apptileState.forkId]);

  const appBridge = useGetAppBridge(isEmbeddedInShopify);

  const redirectRemote = useCallback(
    (url: string) => {
      if (appBridge) {
        const redirect = Redirect.create(appBridge);
        redirect.dispatch(Redirect.Action.APP, url);
      } else {
        window.location.href = url;
      }
    },
    [appBridge],
  );

  const onSwitchFork = async (forkId: any, isPreview?: boolean) => {
    const {data} = (await AppConfigApi.fetchAppBranches(appId, forkId)) as any;
    const branchName = data.branches?.[0]?.branchName;

    redirectRemote(
      `/dashboard/${orgId}/app/${appId}/f/${forkId}/b/${branchName}/app-editor${isPreview ? '?preview=true' : ''}`,
    );
  };

  /* Create Fork Actions */
  const onForkFromChange = (forkTitle: string) => {
    const forkDetail = _.find(allForks, {title: forkTitle}) as {};
    setForkFrom(forkDetail);
  };
  const debouncedOnValueChange = debounce((newVal, type) => type(newVal), 300);
  const onCreateFork = async () => {
    try {
      if (!forkName || _.isEmpty(forkFrom)) {
        return dispatch(
          makeToast({
            content: 'Please fill in all details!',
            appearances: 'warning',
            duration: 1500,
          }),
        );
      }
      setIsLoading(true);

      const newFork = await AppApiV2.createAppFork(appId, {forkName, forkFrom});
      await onSwitchFork(newFork.data.forkId);
      dispatch(
        makeToast({
          content: 'Fork created successfully',
          appearances: 'success',
          duration: 4000,
        }),
      );
      setIsCreateFork(false);
    } catch (error) {
      logger.error(`Error creating fork`, error);
      dispatch(
        makeToast({
          content: 'Something went wrong. Please try again later!',
          appearances: 'error',
          duration: 2000,
        }),
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="languageAndRegionWrapper" style={styles.forkRoot}>
      <View style={styles.wrapper}>
        <View style={styles.headerContainer}>
          <TextElement style={{...commonStyles.baseText, ...styles.headerText}}>Languages</TextElement>
        </View>
        {!_.isEmpty(allForks) &&
          Object.entries(allForks).map(([index, fork]) => {
            const isVisible = forkVisibilityInfo?.[fork?.id] || false;
            return (
              <Pressable
                key={fork?.id}
                style={({}) => [
                  styles.languageItem,
                  {
                    backgroundColor:
                      activeForkId === `${fork?.id}` ? '#F3F7FD' : fork?.id === hoveredIndex ? '#F5F5F5' : 'white',
                  },
                ]}
                onPress={() => onSwitchFork(fork?.id)}
                onHoverIn={() => {
                  setHoveredIndex(fork?.id);
                }}
                onHoverOut={() => {
                  setHoveredIndex(null);
                }}>
                <View style={styles.languageNameContainer}>
                  <TextElement style={{...commonStyles.baseText, ...styles.languageName}}>
                    {fork?.title === 'Main' ? 'English (Default)' : _.capitalize(fork?.title)}
                  </TextElement>
                  {fork?.publishedCommitId ? (
                    <View style={styles.branchTagWrapper}>
                      <MaterialCommunityIcons name="circle" size={12} color="#3BA720" />
                      <TextElement style={styles.branchTagText}>Live</TextElement>
                    </View>
                  ) : (
                    <TextElement style={{...commonStyles.baseText, ...styles.defaultTag}}> [Draft]</TextElement>
                  )}
                  {/* {fork?.title === 'Main' && (
                    <TextElement style={{...commonStyles.baseText, ...styles.defaultTag}}> [Default]</TextElement>
                  )} */}
                </View>
                <View style={styles.visibleContainer}>
                  {fork?.publishedCommitId ? (
                    <PopoverComponent
                      visible={tooltipVisible === fork.id}
                      onVisibleChange={visible => setTooltipVisible(visible ? fork.id : null)}
                      trigger={
                        <Pressable
                          disabled={updatingVisibilityForForkId !== null}
                          style={{opacity: updatingVisibilityForForkId !== null ? 0.5 : 1}}
                          onPress={async () => {
                            try {
                              setUpdatingVisibilityForForkId(fork.id);
                              const response = await AppApiV2.updateForkVisibility(appId, fork.id, !isVisible);
                              if (response.data?.success && response.data?.forkInfo?.forks) {
                                const visibilityMap = response.data.forkInfo.forks.reduce(
                                  (acc: {[key: number]: boolean}, fork: any) => {
                                    acc[fork?.id] = fork?.visible && !!fork?.publishedCommitId;
                                    return acc;
                                  },
                                  {},
                                );
                                setForkVisibilityInfo(visibilityMap);
                                dispatch(
                                  makeToast({
                                    content: `Language visibility ${!isVisible ? 'enabled' : 'disabled'} successfully`,
                                    appearances: 'success',
                                  }),
                                );
                              }
                            } catch (error) {
                              dispatch(
                                makeToast({
                                  content: 'Failed to update language visibility',
                                  appearances: 'error',
                                }),
                              );
                            } finally {
                              setUpdatingVisibilityForForkId(null);
                            }
                          }}
                          onHoverIn={() => setTooltipVisible(fork.id)}
                          onHoverOut={() => setTooltipVisible(null)}>
                          <MaterialCommunityIcons name={isVisible ? 'eye' : 'eye-off'} size={16} color="#535353" />
                        </Pressable>
                      }>
                      <View style={{paddingBottom: 4}}>
                        <View
                          style={{
                            backgroundColor: '#FFFFFF',
                            padding: 8,
                            borderRadius: 4,
                            borderWidth: 1,
                            borderColor: '#EDEDED',
                            width: 200,
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                          }}>
                          <MaterialCommunityIcons
                            name="information"
                            size={16}
                            color="#D5D5D5"
                            style={{marginRight: 6}}
                          />
                          <TextElement style={{color: '#000000', fontSize: 10, fontWeight: '400', lineHeight: 12}}>
                            {isVisible
                              ? 'Marking this language as hidden will hide it from all users'
                              : 'Marking this language as visible will make it available to all users'}
                          </TextElement>
                        </View>
                      </View>
                    </PopoverComponent>
                  ) : (
                    <PopoverComponent
                      visible={tooltipVisible === fork.id}
                      onVisibleChange={visible => setTooltipVisible(visible ? fork.id : null)}
                      trigger={
                        <Pressable
                          onHoverIn={() => setTooltipVisible(fork.id)}
                          onHoverOut={() => setTooltipVisible(null)}>
                          <MaterialCommunityIcons name="eye-off" size={16} color="#D5D5D5" />
                        </Pressable>
                      }>
                      <View style={{paddingBottom: 4}}>
                        <View
                          style={{
                            backgroundColor: '#FFFFFF',
                            padding: 8,
                            borderRadius: 4,
                            borderWidth: 1,
                            borderColor: '#EDEDED',
                            width: 200,
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                          }}>
                          <MaterialCommunityIcons
                            name="information"
                            size={16}
                            color="#D5D5D5"
                            style={{marginRight: 6}}
                          />
                          <TextElement style={{color: '#000000', fontSize: 10, fontWeight: '400', lineHeight: 12}}>
                            Visibility can only be enabled after publishing the language
                          </TextElement>
                        </View>
                      </View>
                    </PopoverComponent>
                  )}
                </View>
              </Pressable>
            );
          })}
      </View>

      <View style={styles.addLanguageContainer}>
        {/* Language Modal */}
        <ModalComponent
          disableOutsideClick={modalState === 'progress'}
          visible={isLanguageModalOpen}
          onVisibleChange={visible => {
            if (!visible) {
              // Only allow closing if not in progress state
              if (modalState !== 'progress') {
                setIsLanguageModalOpen(false);
              }
            } else {
              setIsLanguageModalOpen(true);
            }
          }}
          content={
            <LanguageModalContent
              languageOptions={languageOptions}
              selectedLanguage={selectedLanguage}
              setSelectedLanguage={setSelectedLanguage}
              allForks={allForks}
              baseTemplate={baseTemplate}
              setBaseTemplate={setBaseTemplate}
              onAddLanguage={onAddLanguage}
              onClose={() => modalState !== 'progress' && setIsLanguageModalOpen(false)}
              isLoading={isLoading}
              modalState={modalState}
              currentStep={currentStep}
              completedSteps={completedSteps}
              isForkCreationFailed={isForkCreationFailed}
              onPreview={onPreview}
            />
          }
        />

        {Object.entries(allForks)?.length < 8 && (
          <Pressable
            style={styles.sidebarAddLanguageButton}
            onPress={openLanguageModal}
            onHoverIn={() => {}}
            onHoverOut={() => {}}>
            <TextElement style={{...commonStyles.baseText, ...styles.addLanguageText}}>+ ADD</TextElement>
          </Pressable>
        )}
      </View>
    </div>
  );
};

const styles = StyleSheet.create({
  forkRoot: {
    flex: 1,
    position: 'relative',
    overflow: 'scroll', // Hide any overflow from the root container
  },
  wrapper: {
    backgroundColor: 'white',
    maxHeight: '100%',
    // overflow: 'scroll', // Keep the original overflow scroll property
  },
  headerContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    // paddingVertical: 12,
    paddingHorizontal: 16,
    height: 52,
    display: 'flex',
    justifyContent: 'center',
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.SECONDARY_COLOR,
  },
  languageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    height: 52,
    // borderBottomWidth: 1,
    // borderBottomColor: '#EEEEEE',
  },
  languageNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageName: {
    fontSize: 14,
    fontWeight: '400',
    color: theme.SECONDARY_COLOR,
  },
  defaultTag: {
    fontSize: 14,
    fontWeight: '400',
    color: '#888888',
  },
  branchTagWrapper: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
    height: 24,
    gap: 4,
    backgroundColor: '#F3F3F3',
    borderRadius: 12,
    marginLeft: 8,
  },
  branchTagText: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: '500',
    color: '#000000',
  },
  menuDots: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  addLanguageContainer: {},
  addLanguageButton: {
    paddingVertical: 8,
  },
  addLanguageText: {
    color: theme.PRIMARY_COLOR,
    fontSize: 14,
    fontWeight: '500',
  },
  // Progress and Success UI styles
  progressContainer: {
    padding: 24,
    alignItems: 'center',
  },
  progressTitle: {
    marginBottom: 16,
    textAlign: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: theme.PRIMARY_COLOR,
  },
  stepsWrapper: {
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepsContainer: {
    width: 250,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  stepIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#9D9D9D',
  },
  stepCompleted: {
    backgroundColor: theme.PRIMARY_COLOR,
    borderWidth: 0,
  },
  stepFailed: {
    backgroundColor: theme.ERROR_BACKGROUND,
    borderWidth: 0,
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#9E9E9E',
  },
  stepInProgress: {
    backgroundColor: theme.PRIMARY_COLOR,
  },
  stepText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '400',
    color: '#000000',
  },
  stepTextCompleted: {
    fontWeight: '500',
  },
  successContainer: {
    padding: 24,
    alignItems: 'center',
  },
  successIconContainer: {
    marginBottom: 16,
  },
  successTextContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    paddingBottom: 24,
    paddingHorizontal: 40,
    marginTop: 20,
  },
  successTitle: {
    marginBottom: 12,
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 20,
    fontWeight: '600',
    color: '#000000',
  },
  successMessage: {
    textAlign: 'center',
    marginBottom: 24,
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400',
    color: '#000000',
  },
  closeSuccessButton: {
    minWidth: 120,
  },
  forkBox: {
    flexShrink: 0,
    flexWrap: 'wrap',
    padding: 11,
    borderWidth: 1,
    borderColor: 'rgb(222, 222, 222)',
    borderRadius: 6,
    margin: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  forkBoxActive: {
    backgroundColor: '#005be40f',
  },
  appTitle: {
    flex: 1,
    fontSize: 16,
  },
  addForkBox: {
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  // Language Modal Styles
  languageModalContainer: {
    width: 608,
    minHeight: 486,
    maxWidth: '100%',
    backgroundColor: 'white',
    borderRadius: 8,
  },
  languageModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 28,
    // paddingVertical: 14,
    height: 48,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  closeButton: {
    padding: 5,
  },
  languageModalTitle: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '600',
    color: theme.SECONDARY_COLOR,
  },
  languageModalContent: {
    paddingHorizontal: 28,
    paddingVertical: 14,
  },
  languageModalLabel: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '500',
    color: theme.SECONDARY_COLOR,
  },
  formGroup: {
    marginBottom: 20,
  },
  dropdownContainer: {
    marginTop: 6,
  },
  helperText: {
    marginTop: 4,
    fontSize: 13,
    lineHeight: 20,
    fontWeight: '400',
    color: theme.DISABLED_COLOR,
  },
  noteContainer: {
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  noteTitle: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '500',
    color: theme.SECONDARY_COLOR,
  },
  bulletList: {
    marginTop: 10,
  },
  bulletItem: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'flex-start',
  },
  bullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#333',
    marginTop: 6,
    marginRight: 10,
  },
  bulletText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '500',
    color: theme.SECONDARY_COLOR,
  },
  languageModalFooter: {
    marginTop: 20,
    marginBottom: 8,
    alignItems: 'flex-end',
  },
  addLanguageModalButtonWrapper: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderWidth: 0,
  },
  addLanguageButtonText: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: '500',
    color: theme.DEFAULT_COLOR,
  },
  // Sidebar add language button styling
  sidebarAddLanguageButton: {
    height: 52,
    paddingHorizontal: 16,
    display: 'flex',
    justifyContent: 'center',
  },
  visibleContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  popOverCommonChildrenContentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  popOverCommonChildrenContentTextSelected: {
    color: theme.PRIMARY_COLOR,
  },
  popOverCommonChildrenContentTextDisabled: {
    color: theme.DISABLED_COLOR,
  },
  inputContainer: {
    paddingVertical: 2,
    paddingHorizontal: 10,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.SECONDARY_BACKGROUND,
  },
  popOverCommonTrigger: {
    backgroundColor: theme.TERTIARY_BACKGROUND,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 8,
    paddingHorizontal: 10,
    borderRadius: 8,
    border: 'none',
  },
  popOverCommonChildren: {
    borderRadius: 8,
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    overflow: 'hidden',
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.10)',
    opacity: 1,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: 'white',
    marginTop: 8,

    position: 'absolute',
    top: '100%',
  },
  popOverCommonChildrenContent: {
    maxHeight: 150,
    padding: 0,
    display: 'flex',
    flexDirection: 'column',
    overflowY: 'scroll',
  },
  popOverCommonChildrenContentText: {
    padding: 8,
    color: 'black',
  },
});

const webStyles = `
.languageAndRegionWrapper::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.popOverCustomScrollBar::-webkit-scrollBar {
  width: 3px;
}
.progressWrapperLanguageModalWrapper {
  width: 100%;
  height: 4px;
  margin-bottom: 10px;
}
.progressBarLanguageModal {
  height: 4px;
  background-color: #1060e0;
  transition: width 0.5s ease-in-out;
}
`;

document.head.insertAdjacentHTML('beforeend', `<style>${webStyles}</style>`);
