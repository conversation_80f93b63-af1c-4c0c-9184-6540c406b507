import {CreateWidget, DragDropItemTypes, Icon} from 'apptile-core';
import useEndDrag from '@/root/app/common/utils/useEndDrag';
import {identity, isEmpty} from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {useDrag} from 'react-dnd';
import {getEmptyImage} from 'react-dnd-html5-backend';
import {findNodeHandle, Image, Text, View, StyleSheet, Pressable} from 'react-native';
import {useDispatch} from 'react-redux';
import {editAITile, fetchTile, openIntegrationModal} from '../actions/editorActions';
import {ITileItem} from '../api/ApiTypes';
import {ApptileWebIcon} from '../icons/ApptileWebIcon.web';
import theme from '../styles-v2/theme';
import {TileHoverStatus, TileAnimation} from './TilesExplorer';
import snippets from '../../app/plugins/widgets/WebViewWidgetV3/snippets.json';
import Analytics from '@/root/web/lib/segment';

import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  withRepeat,
  withSequence,
  cancelAnimation,
  withDelay,
} from 'react-native-reanimated';
import {useNavigate} from '../routing.web';

export interface TileItemProps {
  tile: ITileItem;
  isTileUnLocked: boolean;
  integrationInfo: any;
  appId: string;
  integrationCode: string;
  tileNo: number;
  tileAnimation: TileAnimation;
  tileHover: {
    isTileHovered: TileHoverStatus;
    setTileHover: React.Dispatch<React.SetStateAction<TileHoverStatus>>;
  };
  legacyMode: boolean;
  localDefinition: boolean;
}

const TileItem: React.FC<TileItemProps> = props => {
  const {
    tile,
    isTileUnLocked,
    integrationInfo,
    appId,
    integrationCode,
    tileNo,
    tileAnimation,
    tileHover,
    legacyMode,
    localDefinition = false,
    aiTile = false,
  } = props;
  const {isTileHovered, setTileHover} = tileHover;
  const {hasTileAnimationEverHappened, isTileAnimationHappening, setTileAnimationEverHappend, setTileAnimationStatus} =
    tileAnimation;
  const istileHoverEffectsEnabled = isTileHovered.status && isTileHovered.tileIndex == tileNo;
  const onDragEnd = useEndDrag();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [, dragRef, connectDragPreview] = useDrag({
    type: DragDropItemTypes.WIDGET,
    item: () => {
      const modulePayload = {moduleUUID: tile.id, bFetchTileFromServer: true};
      const dragItem: CreateWidget = {
        type: 'widget',
        action: 'create',
        payload: {
          layout: {},
          pluginType: 'ModuleInstance',
          configType: 'widget',
          ...modulePayload,
        },
      };
      dispatch(fetchTile(tile.id, localDefinition || aiTile));
      logger.info('Begin Drag: ', dragItem);
      return dragItem;
    },
    end: onDragEnd,
  });
  const setDragRefs = useCallback(
    ref => {
      dragRef(ref && findNodeHandle(ref));
    },
    [dragRef],
  );
  useEffect(() => {
    connectDragPreview(getEmptyImage(), {captureDraggingState: true});
  }, [connectDragPreview]);

  useEffect(() => {
    triggerTileAnimation(tileNo, true, 350, 40, 1);
  }, []);

  const [uri, setUri] = useState(
    tile?.coverImage ||
      'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSI6EI5Kbf1xLKFW8J7uXyZCtk6jyVt585FIvoEFdBSLOOVKDQ1M9xk5CxS_F6yw2bfbgg&usqp=CAU',
  );
  const [aspectRatio, setAspectRatio] = useState(1);
  const onImageLoad = useCallback(
    e => {
      Image.getSize(uri, (width, height) => {
        setAspectRatio(width / height);
      });
    },
    [uri],
  );

  const handleIntegrationFlow = () => {
    navigate(`../dashboard/integrations/${integrationInfo.id}`);
  };

  const handleTileInteraction = () => {
    isTileUnLocked ? identity : handleIntegrationFlow();
  };

  function triggerTileAnimation(
    tileNo: number,
    trigger: boolean,
    animationDuration?: number,
    displacement: number = 50,
    animationRepetitions?: number,
  ) {
    displacement = legacyMode ? -1 * displacement : displacement;
    // animation should happen only for the first tile and for the first time only
    if (tileNo !== 0 || hasTileAnimationEverHappened) return;

    if (trigger) {
      setTileAnimationStatus(true);
      offset.value = withDelay(
        700,
        withRepeat(
          withSequence(
            withTiming(displacement, {duration: animationDuration, easing: Easing.bezier(0.42, 0.0, 0.58, 1.0)}),
            withDelay(100, withTiming(0, {duration: animationDuration, easing: Easing.bezier(0.42, 0.0, 0.58, 1.0)})), //to reset the animation back to initial position
          ),
          animationRepetitions,
          false,
          () => {
            setTileAnimationStatus(false);
            setTileAnimationEverHappend(true); // setting this to know wether the animation has already happened so that we dont animate twice to user
          },
        ),
      );
    }
    // else {
    //   // Cancel animation if the user takes mouse out of the tile before animation completes and resetting to initial position
    //   cancelAnimation(offset);
    //   offset.value = withTiming(0, {duration: 0});
    //   setTileAnimationStatus(false)
    //   setTileAnimationEverHappend(true) // setting this to know wether the animation has already happened so that we dont animate twice to user
    // }
  }

  const tileStyles = isTileUnLocked ? {...theme.grabbable} : {background: 'rgba(0, 0, 0, 0.4)', cursor: 'pointer'};
  const iconName = isTileUnLocked ? 'integrations' : 'integration-disconnect';
  const offset = useSharedValue(0);
  const animatedStyles = useAnimatedStyle(() => {
    return {
      // transform: [{ rotate: offset.value }],
      transform: [{translateX: offset.value}],
    };
  });
  const [srcDoc, setSrcDoc] = useState('');
  useEffect(() => {
    if (aiTile) {
      const moduleConfig = tile?.moduleRecord?.moduleConfig?.toJS();
      const webTunnelConfig = moduleConfig?.webTunnel?.config;
      if (webTunnelConfig) {
        let componentProps = {};
        componentProps = Object.fromEntries(
          Object.keys(webTunnelConfig)
            .filter(e => e.startsWith('componentProps-'))
            .map(e => [e.slice(15), webTunnelConfig[e]]),
        );
        const html =
          `<script>window.componentProps=${JSON.stringify(componentProps, null, 2)}</script>` +
          snippets.reactBase.prefix +
          webTunnelConfig.html +
          snippets.reactBase.suffix;
        setSrcDoc(html);
      }
    }
  }, [aiTile]);

  const onAITileEdit = () => {
    dispatch(editAITile('React', tile.id));
    setTimeout(() => {
      navigate('../AI-Tile-Creation');
    }, 300);
    Analytics.track('editor:editor_AiTile_OnTileEditClick', {moduleUUID: tile.id});
  };

  return (
    <View
      ref={isTileUnLocked ? setDragRefs : null}
      onMouseEnter={() => {
        setTileHover({status: true, tileIndex: tileNo});
        // triggerTileAnimation(tileNo, true, 750, 50, 2);
      }}
      onMouseLeave={() => {
        setTileHover({status: false, tileIndex: tileNo});
      }}
      style={[
        {
          borderColor: 'black',
          borderRadius: 2,
        },
      ]}>
      <Animated.View style={[styles.tileItem, tileStyles, animatedStyles]}>
        <View style={{position: 'relative'}}>
          {aiTile && srcDoc ? (
            <View
              style={{
                height: 320,
              }}>
              <iframe style={{borderWidth: 0}} srcdoc={srcDoc} height={'100%'} />
            </View>
          ) : (
            <Image
              style={{
                maxWidth: theme.RIGHT_SIDE_BAR_WIDTH,
                height: 'auto',
                maxHeight: 180,
                aspectRatio,
                opacity: !isTileUnLocked ? 0.1 : 1,
              }}
              source={{uri}}
              resizeMode="contain"
              onLoad={onImageLoad}
            />
          )}
          <View style={[styles.overlay, {display: istileHoverEffectsEnabled || aiTile ? 'block' : 'none'}]} />
          {aiTile && (
            <Pressable
              onPress={onAITileEdit}
              style={{
                position: 'absolute',
                top: 15,
                right: 15,
                height: 28,
                width: 28,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#fff',
                borderRadius: 30,
                zIndex: 10,
              }}>
              <Icon iconType="MaterialCommunityIcons" size={14} name="pencil-outline" />
            </Pressable>
          )}
        </View>
        {integrationCode && (
          <Pressable style={styles.integration} onPress={handleTileInteraction}>
            <ApptileWebIcon name={iconName} size={14} style={styles.tileImg} color={'#444'} />
            <Text style={styles.integrationName}>{integrationInfo?.title || ''}</Text>
          </Pressable>
        )}
        <Text style={[styles.labelStyles]}>{tile?.name}</Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  tileItem: {
    flex: 1,
    borderColor: theme.TILE_BORDER_COLOR,
    backgroundColor: theme.TILE_BACKGROUND,
    overflow: 'hidden',
    position: 'relative',
    marginVertical: 6,
    borderRadius: 12,
    borderWidth: 1,
  },
  labelStyles: {
    paddingTop: 10,
    paddingBottom: 10,
    backgroundColor: theme.TILE_LABEL_BACKGROUND,
    fontFamily: theme.FONT_FAMILY,
    fontSize: 12,
    fontWeight: 500,
    lineHieght: 14,
    textAlign: 'center',
  },
  integration: {
    backgroundColor: '#FFFFFF',
    borderColor: '#e5e5e5',
    borderRadius: 40,
    borderWidth: 1,
    flex: 1,
    flexDirection: 'row',
    left: 0,
    marginHorizontal: 'auto',
    marginVertical: 0,
    paddingHorizontal: 8,
    paddingVertical: 4,
    right: 0,
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 3,
    bottom: -4,
    zIndex: 2,
  },
  integrationName: {
    fontSize: 12,
    lineHeight: 14,
    color: '#444',
  },
  tileImg: {
    marginRight: 4,
  },
  overlay: {
    position: 'fixed',
    width: '100%',
    height: '100%',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.03)',
    zIndex: 5,
    cursor: 'grab',
  },
});

export default TileItem;
