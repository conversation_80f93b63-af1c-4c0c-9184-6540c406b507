import React, {useCallback, useState, useEffect, useMemo} from 'react';
import {ScrollView, TouchableOpacity, View, StyleSheet, Text, ActivityIndicator} from 'react-native';
import CollapsiblePanel from '../components/CollapsiblePanel';
import EditorSectionHeader from '../components/controls/EditorSectionHeader';
import {updateColor} from '../actions/themeActions';
import ColorInputControl from '../components/controls/ColorInputControl';
import {
  addNavigationNav,
  addNavigationPage,
  DispatchActions,
  Icon,
  navComponentDelete,
  navigationConfigUpdatePath,
  NavigatorConfig,
  useTheme,
} from 'apptile-core';
import {navConfigSelector} from '../../../apptile-core/selectors/AppConfigSelector';
import {useDispatch, useSelector} from 'react-redux';
import {selectScreensInNavWithPath} from '../selectors/EditorSelectors';
import {debounce} from 'lodash';
import {makeToast} from '../actions/toastActions';
import {
  EDITOR_SELECT_NAV_COMPONENT,
  EDITOR_SELECTED_PAGE_TYPE,
  navigationReoderingV2,
  softRestartConfig,
} from '../actions/editorActions';
import CodeInput from '../components/codeEditor/codeInput';
import commonStyles from '../styles-v2/commonStyles';
import TileHeader from './TileHeader';
import SortableList from '../components/SortableList';
import _ from 'lodash';
import CheckboxControl from '../components/controls/CheckboxControl';
import {navUpdateName} from '../../../apptile-core/actions/AppConfigActions';
import {getNavigationContext} from 'apptile-core';

const getTabScreens = (ovj: any, mainTab: any) => {
  const mainScreens = ovj.filter(screen => screen.path[1] === mainTab?.name && screen.path.length === 4);
  return mainScreens;
};

const gettopNavigationTabs = (navConfigs: NavigatorConfig) => {
  const rootNavs = navConfigs.screens.flatMap(config => {
    if (config.type === 'navigator') {
      return config.screens;
    }
  });
  const bottomBars = rootNavs.flatMap(config => {
    if (config.type === 'navigator') {
      return config.screens;
    }
  });
  const topNavigationTabs = bottomBars.toJS
    ? Object.entries(bottomBars.toJS()).map(([key, value]) => ({...value, key}))
    : Object.entries(bottomBars).map(([key, value]) => ({...value, key}));
  return topNavigationTabs;
};

const getFirstTopTabScreen = (topTabs: any) => {
  if (topTabs && topTabs.length > 0) return topTabs[0];
  return [];
};

const getFirstBottomTabScreen = (navConfigs: NavigatorConfig) => {
  const bottomBars = navConfigs.screens.flatMap(config => {
    if (config.type === 'navigator') {
      return config.screens;
    }
  });
  const topNavigationTabs = bottomBars.toJS
    ? Object.entries(bottomBars.toJS()).map(([key, value]) => ({...value, key}))
    : Object.entries(bottomBars).map(([key, value]) => ({...value, key}));

  if (topNavigationTabs && topNavigationTabs.length > 0) return topNavigationTabs[0];
  return [];
};

const TopNavigationEditor = () => {
  const navConfigs: NavigatorConfig = useSelector(navConfigSelector)?.get('rootNavigator');
  const mainTab = useMemo(() => {
    if (!navConfigs) return null;
    const navConfigJS = navConfigs.toJS();
    return Object.values(navConfigJS.screens).find(
      screen => screen?.type === 'navigator' && screen?.navigatorType === 'tab',
    );
  }, [navConfigs]);
  const topTab = useMemo(() => {
    if (!mainTab) return null;
    return Object.values(mainTab.screens).find(
      mainScreen => mainScreen?.type === 'navigator' && mainScreen?.navigatorType === 'topTab',
    );
  }, [mainTab]);
  const screens: any = useSelector(selectScreensInNavWithPath(['/']));
  const topNavigationTabs = gettopNavigationTabs(navConfigs);
  const defaultTabNavScreen = getFirstTopTabScreen(topNavigationTabs);
  const defaultBottomNavScreen = getFirstBottomTabScreen(navConfigs);
  const allsCreen = getTabScreens(screens, mainTab);
  const context = getNavigationContext();

  const dispatch = useDispatch();
  const topTabPathSelector = ['/', mainTab?.name];
  const topTabNavSelector = ['/', mainTab?.name, topTab?.name];

  const [showAddNavigationSection, setShowAddNavigationSection] = useState(false);
  const [showUpdateNavigationSection, setShowUpdateNavigationSection] = useState('');
  const [open, setOpen] = useState(true);
  const [closeSectionIndex, setCloseSectionIndex] = useState<Number[]>([]);
  const {themeEvaluator} = useTheme();
  const [iconColor, setIconColor] = useState(themeEvaluator('colors.navText'));
  const [tileColor, setTileColor] = useState(themeEvaluator('colors.navCard'));
  const [currentEditorSection, setCurrentEditorSection] = useState('basics');
  const [showAddPageDialog, setShowAddPageDialog] = useState(true);
  const [newScreenTitle, setNewScreenTitle] = useState('');
  const [TopEnabled, setTopEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showNewScreenContainer, setShowNewScreenContainer] = useState(showAddPageDialog ?? false);

  useEffect(() => {
    if (topNavigationTabs.length !== 0) {
      setTopEnabled(true);
    }
    context && context.navigate('Home');
  }, [topNavigationTabs.length]);

  const [updateNavigationData, setUpdatetNavigationData] = useState({
    oldScreen: '',
    screen: '',
    title: '',
  });

  const debouncedOnValueChange = debounce(value => handleUpdateChangeNavigationData({name: 'title', value}), 1000);

  const handleUpdateChangeNavigationData = ({name, value}) => {
    setUpdatetNavigationData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  function loading(callback, delay = 1000) {
    return setTimeout(callback, delay);
  }

  const onChange = () => {
    showLoadingStatus();
    loading(() => {
      TopEnabled ? ConvertToScreenConfig() : ConvertToNavigatorConfig();
      setIsLoading(false);
    }, 100);
  };

  const ConvertToNavigatorConfig = () => {
    let navName = `Home`;
    if (
      defaultBottomNavScreen.screen !== 'Home' &&
      defaultBottomNavScreen.name !== 'Home' &&
      defaultBottomNavScreen.title !== 'Home'
    ) {
      dispatch(
        makeToast({
          content: 'Please set Home screen to first position to add Tabs',
          duration: 2000,
          appearances: 'warning',
        }),
      );
      return;
    }
    setTopEnabled(!TopEnabled);
    const path = ['/', mainTab?.name, 'Home'];
    dispatch(navComponentDelete([...topTabPathSelector, defaultBottomNavScreen.screen]));
    dispatch(addNavigationNav(topTabPathSelector, navName, 'topTab'));

    let icondata = {
      iconName: defaultBottomNavScreen.iconName || 'home',
      iconType: defaultBottomNavScreen.iconType || 'AntDesign',
    };
    dispatch(navigationConfigUpdatePath([...path], [], icondata));
    const data = {
      screen: defaultBottomNavScreen.screen || 'Home',
      title: defaultBottomNavScreen.title || 'Home',
      iconName: defaultBottomNavScreen.iconName || 'home',
      iconType: defaultBottomNavScreen.iconType || 'AntDesign',
    };
    addPageInTopTab(data);
    dispatch(softRestartConfig());
    dispatch(navigationReoderingV2('UP', [...path], 0));
    dispatch(navUpdateName([...path], 'Home'));
    dispatch({type: EDITOR_SELECT_NAV_COMPONENT, payload: [...path]});
    dispatch({
      type: EDITOR_SELECTED_PAGE_TYPE,
      payload: screens[0].config.type,
    });
  };

  const ConvertToScreenConfig = () => {
    dispatch(navComponentDelete(topTabNavSelector));
    const data = {
      screen: defaultTabNavScreen.screen || 'Home',
      title: 'Home',
      iconName:
        defaultTabNavScreen.iconName != 'file-outline' || !defaultTabNavScreen.iconName
          ? defaultTabNavScreen.iconName
          : 'home',
      iconType:
        defaultTabNavScreen.iconType != 'AntDesign' || !defaultTabNavScreen.iconType
          ? defaultTabNavScreen.iconType
          : 'AntDesign',
    };
    setTopEnabled(!TopEnabled);
    dispatch(addNavigationPage(topTabPathSelector, defaultTabNavScreen.screen));
    dispatch(navigationConfigUpdatePath([...topTabPathSelector, defaultTabNavScreen.screen], [], data));
    dispatch(softRestartConfig());
    const index = 0;
    dispatch(navigationReoderingV2('UP', [...topTabPathSelector, defaultTabNavScreen.screen], index));
    dispatch({type: EDITOR_SELECT_NAV_COMPONENT, payload: ['/', mainTab?.name, undefined]});
    dispatch({
      type: EDITOR_SELECTED_PAGE_TYPE,
      payload: screens[0].config.type,
    });
  };

  const onAddPage = useCallback(() => {
    showLoadingStatus();
    const screenTitle = newScreenTitle;
    if (screenTitle) {
      const screenId = screenTitle.replace(/[^a-z0-9]/gim, '_');
      dispatch({
        type: DispatchActions.ADD_NEW_PAGE,
        payload: {pageId: screenId},
      });
      dispatch({
        type: DispatchActions.ADD_NAVIGATION_PAGE,
        payload: {
          navSelector: ['/'],
          screenName: screenId,
          screenConfig: {
            title: screenTitle,
            screen: screenId,
            showTitleBar: true,
          },
        },
      });
      loading(() => {
        dispatch(softRestartConfig());
        setNewScreenTitle('');
        setShowNewScreenContainer(false);
        const data = {
          screen: screenId,
          title: screenTitle,
          iconName: 'file-outline',
          iconType: 'Material Icon',
        };
        addPageInTopTab(data);
        dispatch(softRestartConfig());
        setIsLoading(false);
      });
    }
  }, [dispatch, newScreenTitle]);

  const addPageInTopTab = (data => {
    let scrName = data.screen;
    const avalaibleScreens = getTabScreens(screens);
    const existed = avalaibleScreens.find(
      (screen: {path: string[]}) =>
        screen?.path[1] === mainTab?.name && screen?.path[3]?.toLowerCase() === scrName?.toLowerCase(),
    );
    if (existed) {
      return;
    }
    const pathSelector = ['/', mainTab?.name, 'Home'];
    const navSelector = ['/', mainTab?.name, 'Home', scrName];
    dispatch(addNavigationPage(pathSelector, scrName));
    dispatch(navigationConfigUpdatePath(navSelector, [], data));
  });

  const updatePage = () => {
    try {
      const navSelector = ['/', mainTab?.name, 'Home', updateNavigationData.screen];
      const {oldScreen, screen, ...data} = updateNavigationData;
      dispatch(navigationConfigUpdatePath(navSelector, [], data));
    } catch (e) {
      console.log(e, 'error in update page');
    }
  };

  const findIndex = (array: any, key: any, originalArray: any) => {
    const arr = array.map(([_, value]) => value.key || value.name);
    const originalKeyOrder = originalArray.map((item: any) => item.key || item.name);

    const idx = arr.indexOf(key);
    const oldIndex = originalKeyOrder.indexOf(key);
    if (idx === oldIndex) return -1;
    return idx;
  };
  const updateNavigationOrder = useCallback(
    (reorderedList: [any, any][], item: any) => {
      if (topNavigationTabs.length >= 2) {
        let operation = 'UP';
        let path = ['/', mainTab?.name, 'Home', item.item.name];
        const index = findIndex(reorderedList, item.item.name, topNavigationTabs);
        dispatch(navigationReoderingV2(operation, path, index));
      }
    },
    [topNavigationTabs],
  );

  function showToaster() {
    dispatch(makeToast({content: 'Minimum 1 navigation item is required', duration: 2000, appearances: 'error'}));
  }

  function showLoadingStatus() {
    setIsLoading(true);
    //dispatch(makeToast({content: 'Loading....', duration: 3000, appearances: 'success'}));
  }

  const renderItem = data => {
    const item = data.itemVal;
    return !(showUpdateNavigationSection === item.name) ? (
      <View key={item.name} style={{marginVertical: 4}}>
        <TouchableOpacity
          onPress={() => {
            setShowUpdateNavigationSection(item.name);
            handleUpdateChangeNavigationData({name: 'oldScreen', value: item.name});
            handleUpdateChangeNavigationData({name: 'screen', value: item.name});
            handleUpdateChangeNavigationData({name: 'title', value: item.title});
            context.navigate(item.name);
          }}>
          <View style={styles.navCardContainer}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text style={styles.navName}>{item.title}</Text>
            </View>
            <TouchableOpacity
              onPress={() => {
                if (topNavigationTabs.length > 1) {
                  dispatch(navComponentDelete(['/', mainTab?.name, 'Home', item.name]));
                  dispatch({type: EDITOR_SELECT_NAV_COMPONENT, payload: ['/', mainTab?.name, topTab?.name]});
                  dispatch({
                    type: EDITOR_SELECTED_PAGE_TYPE,
                    payload: screens[0].config.type,
                  });
                } else {
                  showToaster();
                }
              }}>
              <Icon iconType="ApptileWebIcons" name={'close'} size={16} color="#3C3C3C" />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </View>
    ) : (
      <View key={item.name} style={styles.navUpdateContainer}>
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            marginBottom: 16,
            justifyContent: 'space-between',
          }}>
          <Text style={{fontSize: 11, fontWeight: 600}}>EDITING NAVIGATION ITEM</Text>
          <TouchableOpacity
            onPress={() => {
              setShowUpdateNavigationSection('');
            }}>
            <Icon iconType="ApptileWebIcons" name={'close'} size={16} />
          </TouchableOpacity>
        </View>
        <View style={styles.navTitleCnt}>
          <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>Title</Text>
          <View style={styles.navTitleInput}>
            <CodeInput
              placeholder="value"
              value={updateNavigationData.title}
              onChange={(editor: unknown, data: unknown, value: string) => debouncedOnValueChange(value)}
            />
          </View>
        </View>
        <button
          onClick={() => {
            updatePage();
            setShowUpdateNavigationSection('');
          }}
          style={{
            width: '25%',
            borderRadius: 30,
            padding: 10,
            backgroundColor: 'white',
            border: '1px solid #1060E0',
            color: '#1060E0',
            marginTop: 8,
            cursor: 'pointer',
          }}>
          Update
        </button>
      </View>
    );
  };

  return (
    <View style={{flex: 1, marginTop: 20}}>
      <TileHeader isDeletable={false} />
      <View style={{overflow: 'scroll', flex: 1, paddingRight: 4, marginTop: 8}}>
        <View>
          {/* <RadioGroupControlV2
            label=""
            options={[{text: 'Basics', value: 'basics'}]}
            value={currentEditorSection}
            disableBinding={true}
            onChange={setCurrentEditorSection}
          /> */}
          <View>
            <ScrollView style={{maxHeight: '78vh'}} showsHorizontalScrollIndicator={false}>
              {currentEditorSection === 'basics' ? (
                <CollapsiblePanel
                  isOpen={open}
                  setOpen={(open: boolean) => {
                    setOpen(open);
                  }}
                  backgroundStyle={{borderWidth: 0}}
                  title="section"
                  customHeader={
                    <EditorSectionHeader
                      label={'TOP NAVIGATION ITEMS'}
                      name={'top navigation items'}
                      isPremiumDesabled={false}
                      icon={open ? 'chevron-up' : 'chevron-down'}
                      iconSize={18}
                      iconType={'Material Icon'}
                    />
                  }>
                  {/* {isLoading ? (
                    <View style={styles.activityIndicator}>
                      <ActivityIndicator />
                    </View>
                  ) : ( */}
                  <View>
                    <Text style={styles.navSubHeading}>Must have between 1–5 navigation items</Text>
                    {/* <View style={styles.navigationContainer} /> */}
                    {isLoading ? (
                      <View style={styles.activityIndicator}>
                        <ActivityIndicator />
                      </View>
                    ) : (
                      <View>
                        <CheckboxControl
                          value={TopEnabled}
                          label={'Enable Top Tab'}
                          fullSizeLabel={false}
                          reverse={false}
                          onChange={onChange}
                        />
                        {TopEnabled && (
                          <View>
                            <hr style={{backgroundColor: '#E5E5E5', height: 1, border: 0, width: '100%'}} />
                            {Array.isArray(topNavigationTabs) && (
                              <SortableList
                                dragKey={`1-list-editor`}
                                data={_.toPairs(topNavigationTabs)}
                                onChange={updateNavigationOrder}
                                itemComponent={renderItem}
                                componentProps={{
                                  totalItems: topNavigationTabs?.length,
                                  minLength: parseInt('1', 10),
                                }}
                              />
                            )}
                            {/* <hr style={{backgroundColor: '#E5E5E5', height: 1, border: 0, width: '100%'}} /> */}
                            {!showAddNavigationSection && TopEnabled && (
                              <button
                                disabled={allsCreen.length >= 5}
                                onClick={() => {
                                  setShowAddNavigationSection(true);
                                }}
                                style={
                                  allsCreen.length >= 5 ? styles.enabledAddNavButton : styles.disabledAddNavButton
                                }>
                                {allsCreen.length < 5 ? ' + Add new Tab' : 'Limit reached'}
                              </button>
                            )}
                            {showAddNavigationSection && (
                              <View
                                style={{
                                  border: '1px solid #E5E5E5',
                                  borderRadius: '8px',
                                  paddingVertical: 12,
                                  paddingHorizontal: 8,
                                  marginTop: 8,
                                }}>
                                <View
                                  style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    marginBottom: 12,
                                    justifyContent: 'space-between',
                                  }}>
                                  <Text style={{fontSize: 11, fontWeight: 600}}>ADD NAVIGATION TAB</Text>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setShowAddNavigationSection(false);
                                    }}>
                                    <Icon iconType="ApptileWebIcons" name={'close'} size={16} />
                                  </TouchableOpacity>
                                </View>
                                <View
                                  style={{
                                    flex: 1,
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    marginVertical: 10,
                                    marginHorizontal: 0,
                                  }}>
                                  <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>Title</Text>
                                  <View
                                    style={{
                                      flex: 1,
                                      borderRadius: 5,
                                      backgroundColor: '#F3F3F3',
                                      overflow: 'hidden',
                                    }}>
                                    <CodeInput
                                      placeholder="value"
                                      value={newScreenTitle}
                                      onChange={(editor: unknown, data: unknown, value: string) => {
                                        setNewScreenTitle(value.replace(/[^a-z0-9 ]/gim, ''));
                                      }}
                                    />
                                  </View>
                                </View>
                                <button
                                  // disabled={isAddButtonDisabled()}
                                  onClick={() => {
                                    onAddPage();
                                    setShowAddNavigationSection(false);
                                  }}
                                  style={
                                    false
                                      ? {
                                          width: '20%',
                                          borderRadius: 30,
                                          padding: 10,
                                          backgroundColor: 'white',
                                          border: '1px solid gray',
                                          color: 'gray',
                                          marginTop: 8,
                                          cursor: 'not-allowed',
                                        }
                                      : {
                                          width: '20%',
                                          borderRadius: 30,
                                          padding: 10,
                                          backgroundColor: 'white',
                                          border: '1px solid #1060E0',
                                          color: '#1060E0',
                                          marginTop: 8,
                                          cursor: 'pointer',
                                        }
                                  }>
                                  Add
                                </button>
                              </View>
                            )}
                          </View>
                        )}
                      </View>
                    )}
                  </View>
                  {/* )} */}
                </CollapsiblePanel>
              ) : currentEditorSection === 'settings' ? (
                <View />
              ) : (
                <View>
                  <hr style={{backgroundColor: '#E5E5E5', height: 1, border: 0, width: '100%'}} />
                  <CollapsiblePanel
                    isOpen={!closeSectionIndex.includes(1)}
                    setOpen={(open: boolean) => {
                      if (closeSectionIndex.includes(1)) closeSectionIndex.splice(closeSectionIndex.indexOf(1), 1);
                      else closeSectionIndex.push(1);
                      setCloseSectionIndex([...closeSectionIndex]);
                    }}
                    backgroundStyle={{borderWidth: 0}}
                    title="section"
                    customHeader={
                      <EditorSectionHeader
                        label={'COLOR'}
                        name={'color'}
                        isPremiumDesabled={false}
                        icon={!closeSectionIndex.includes(1) ? 'chevron-up' : 'chevron-down'}
                        iconSize={18}
                        iconType={'Material Icon'}
                      />
                    }>
                    <View>
                      <ColorInputControl
                        inTheme
                        label="Icon & Text"
                        name="navText"
                        value={iconColor}
                        onChange={(value: string) => {
                          setIconColor(value);
                          dispatch(
                            updateColor({
                              colorName: 'navText',
                              colorCode: value,
                              mode: 'light',
                            }),
                          );
                        }}
                      />
                    </View>
                  </CollapsiblePanel>
                  <hr style={{backgroundColor: '#E5E5E5', height: 1, border: 0, width: '100%'}} />
                  <CollapsiblePanel
                    isOpen={!closeSectionIndex.includes(2)}
                    setOpen={(open: boolean) => {
                      if (closeSectionIndex.includes(2)) closeSectionIndex.splice(closeSectionIndex.indexOf(2), 1);
                      else closeSectionIndex.push(2);
                      setCloseSectionIndex([...closeSectionIndex]);
                    }}
                    backgroundStyle={{borderWidth: 0}}
                    title="section"
                    customHeader={
                      <EditorSectionHeader
                        label={'BACKGROUND COLOR'}
                        name={'background color'}
                        isPremiumDesabled={false}
                        icon={!closeSectionIndex.includes(2) ? 'chevron-up' : 'chevron-down'}
                        iconSize={18}
                        iconType={'Material Icon'}
                      />
                    }>
                    <View>
                      <ColorInputControl
                        inTheme
                        label="Background"
                        name="navCardColor"
                        value={tileColor}
                        onChange={(value: string) => {
                          setTileColor(value);
                          dispatch(
                            updateColor({
                              colorName: 'navCard',
                              colorCode: value,
                              mode: 'light',
                            }),
                          );
                        }}
                      />
                    </View>
                  </CollapsiblePanel>
                </View>
              )}
            </ScrollView>
          </View>
          <View />
        </View>
        <hr style={{backgroundColor: '#E5E5E5', height: 1, border: 0, width: '100%'}} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  navigationContainer: {marginBottom: 14},
  navHeading: {fontSize: 11, fontWeight: '600'},
  navSubHeading: {fontSize: 12, marginTop: '-4', color: '#535353', fontWeight: '400'},
  navCardContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'rgb(243, 243, 243);',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  navName: {fontSize: 13, color: '#3C3C3C', fontWeight: '400'},
  navUpdateContainer: {
    border: '1px solid #E5E5E5',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  navTitleCnt: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 10,
    marginTop: '-4',
    marginHorizontal: 0,
  },
  navTitleInput: {
    flex: 1,
    borderWidth: 0,
    borderRadius: 5,
    backgroundColor: '#F3F3F3',
    overflow: 'hidden',
  },
  enabledAddNavButton: {
    width: '50%',
    fontSize: 13,
    borderRadius: 30,
    padding: 10,
    fontWeight: '500',
    backgroundColor: 'white',
    border: '1px solid #00000033',
    color: 'rgba(0, 0, 0, 0.2)',
    marginTop: 8,
    marginBottom: 8,
    cursor: 'not-allowed',
  },
  disabledAddNavButton: {
    width: '50%',
    borderRadius: 30,
    fontSize: 13,
    padding: 10,
    fontWeight: '500',
    backgroundColor: 'white',
    border: '1px solid #1060E0',
    color: '#1060E0',
    marginTop: 8,
    marginBottom: 8,
    cursor: 'pointer',
  },
  horizontalLine: {
    borderWidth: 0,
    backgroundColor: '#E5E5E5',
    margin: '10px 0',
    width: '100%',
  },
  activityIndicator: {
    // position: 'absolute',
    // bottom: 15,
    // right: 15,
    padding: 10,
  },
});

export default TopNavigationEditor;
