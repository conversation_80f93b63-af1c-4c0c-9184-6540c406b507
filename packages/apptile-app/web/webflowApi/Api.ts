import axios, {AxiosRequestConfig, AxiosResponse} from 'axios';
import {WEB_API_SERVER_ENDPOINT} from '../../.env.json';

const axiosInstance = axios.create({withCredentials: true});

export class Api {
  static API_SERVER: string = WEB_API_SERVER_ENDPOINT + '/webflow-proxy/v2';

  static get<T>(url: string, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axiosInstance.get<T>(url, config);
  }
}
