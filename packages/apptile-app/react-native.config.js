const path = require('path');

// For pip don't remove
const enableLivelyPIP = false;
let zegopackage = {};
if (enableLivelyPIP) {
  zegopackage = {
    "zego-express-engine-reactnative": {
      root: path.resolve(__dirname, "../zego-express-engine-reactnative"),
      "platforms": {
        "ios": {
          "podspecPath": path.resolve(__dirname, "../zego-express-engine-reactnative/react-native-zego-express-engine.podspec"),
          "version": "3.14.5",
          "configurations": [],
          "scriptPhases": []
        },
        "android": {
          "sourceDir": path.resolve(__dirname, "../zego-express-engine-reactnative/android"),
          "packageImportPath": "import im.zego.reactnative.RCTZegoExpressEnginePackage;",
          "packageInstance": "new RCTZegoExpressEnginePackage()",
          "buildTypes": [],
          "componentDescriptors": [],
          "cmakeListsPath": path.resolve(__dirname, "../zego-express-engine-reactnative/android/build/generated/source/codegen/jni/CMakeLists.txt")
        }
      }
    }
  };
}

module.exports = {
  "dependencies": {
    "react-native-reanimated": {
      "platforms": {
        "android": null,
        "ios": null
      }
    },
    "lottie-react-native": {
      "platforms": {
        "android": null,
        "ios": null
      }
    },
    "lottie-ios": {
      "platforms": {
        "android": null,
        "ios": null
      }
    },
    "@logrocket/react-native": {
      "platforms": {
        "android": null,
        "ios": null
      }
    },
    ...zegopackage
  }
}
