/**
 * @format
 */

import {shouldInitWebLogrocket} from './web/globalvariables';
// import 'react-native-gesture-handler';
// import 'react-native-console-time-polyfill';
import {AppRegistry, Platform} from 'react-native';

import App from './app/App';
import PIPActivity from './PIPActivityRoot';
import { name as appName, pipactivityname } from './app.json';
import { triggerCustomEventListener } from 'apptile-core';
console.log('glvarcheck', shouldInitWebLogrocket);

triggerCustomEventListener('markStart', 'pre_splash');
AppRegistry.registerComponent(appName, () => App);

if (Platform.OS === "android") {
  AppRegistry.registerComponent(pipactivityname, () => PIPActivity);
}
