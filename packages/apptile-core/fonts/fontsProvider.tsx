import React from 'react';
import {Platform} from 'react-native';
import {useSelector} from 'react-redux';

import {fontsLoader} from './fontsLoader';
import {FontContext} from './context';
import {EMPTY_FONTS_CONFIG, appFontsSelector} from '../selectors/AppConfigSelector';

import {IFontRecord} from '../common/datatypes/types';

export const FontsProvider: React.FC = ({children}) => {
  logger.info('[RENDER] FontsProvider');
  const fontList: Record<string, IFontRecord> = useSelector(appFontsSelector);
  const [loadedFonts, setLoadedFonts] = React.useState<Record<string, string>>(EMPTY_FONTS_CONFIG);

  React.useMemo(() => {
    // Need to show splash screen until font is loaded
    if (Platform.OS === 'web') return;
    logger.info('[DEBUG] Fonts Loader started');
    if (Object.keys(fontList).length)
      fontsLoader(
        (fontObj: Record<string, string>) => setLoadedFonts(fontObj),
        () => setLoadedFonts(EMPTY_FONTS_CONFIG),
        fontList,
      );
    logger.info('[DEBUG] Fonts Loader Finished');
  }, [fontList]);

  return <FontContext.Provider value={{loadedFonts}}>{children}</FontContext.Provider>;
};
