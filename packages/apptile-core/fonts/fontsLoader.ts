import {loadFontFromFile} from 'rn-dynamic-fonts';
import {difference, once} from 'lodash';

import FS from '../common/FileSystem';
import LocalStorage from '../common/LocalStorage';
import {pickWeightAndStyleType} from '../styles/styleUtils';

import {IFontRecord} from '../common/datatypes/types';

export const _fontsLoader = async (
  successCB: (fontObj: Record<string, string>) => void,
  errorCB: () => void,
  fontList: Record<string, IFontRecord>,
) => {
  const id = `fontsLoader_${Math.random()}`;
  logger.time(id);

  try {
    const fontsList: Record<string, string>[] = [];
    const loadedFontsMap: Record<string, string> = {};

    Object.entries(fontList).forEach(([_fontId, fontObj]) => {
      const fileName = fontObj.postScriptName;

      let fileType;
      let fileUrl;
      if (fontObj.provider === 'googleFonts') {
        fileType = fontObj.fileUrl.split('http://fonts.gstatic.com/')[1].split('.')[1];
        fileUrl = fontObj.fileUrl.replace('http://', 'https://');
      } else {
        fileType = 'ttf';
        fileUrl = fontObj.fileUrl;
      }

      fontsList.push({fontFamily: fontObj.postScriptName, fileName, fileType, fileUrl});
    });

    const currentFontFilesPath = fontsList.map(fetchObj => `fonts/${fetchObj.fontFamily}.${fetchObj.fileType}`);
    const previousFontFilesPath = (await LocalStorage.getValue('loadedFontFilesPath')) as string[];

    // remove all unused font files from filesystem
    const unusedFonts = difference(previousFontFilesPath, currentFontFilesPath);
    await Promise.all(unusedFonts.map(async (filePath: string) => await FS.deleteFile(filePath)));

    // downloaded only newly added fonts to reduce Network request
    await Promise.all(
      fontsList.map(async fetchObj => {
        const dir = 'fonts';
        const fileName = `${fetchObj.fontFamily}.${fetchObj.fileType}`;
        const isFileExists = await FS.exists([dir, fileName].join('/'));
        if (!isFileExists) await FS.saveFileFromUrl(dir, fileName, fetchObj.fileUrl);

        const fontName = await loadFontFromFile(fetchObj.fontFamily, FS.getFullPath(dir, fileName));
        loadedFontsMap[fetchObj.fontFamily] = fontName;
      }),
    );

    // Saving file path to local storage for previous load fonts tracking
    await LocalStorage.setValue('loadedFontFilesPath', JSON.stringify(currentFontFilesPath));
    successCB(loadedFontsMap);
  } catch (err) {
    logger.error(err);
    errorCB();
  }

  logger.timeEnd(id);
};

export const fontsLoader = once(_fontsLoader);
