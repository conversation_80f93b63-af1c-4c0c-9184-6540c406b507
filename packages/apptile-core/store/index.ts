import { Platform } from "react-native";
import { createStore, applyMiddleware, compose, Store, AnyAction } from "redux";
import createSagaMiddleware, { SagaMiddlewareOptions } from "redux-saga";
import { createLogger } from "redux-logger";
import { useDispatch } from "react-redux";
// import {routerMiddleware} from 'connected-react-router';
import makeRootReducer, { ApptileStore } from "./RootReducer";
import { rootSaga } from "../sagas";
// import {composeWithDevTools} from '@redux-devtools/extension';

const middleWareOptions: SagaMiddlewareOptions<object> =
  // __DEV__ ? {
  //     sagaMonitor: {
  //       effectTriggered: options => logger.info('Effect Triggered', options),
  //       actionDispatched: action => logger.info('Action Dispatched', action),
  //       effectRejected: (effectId, error) => logger.error('Effect Rejected', error, effectId),
  //       effectCancelled: effectId => logger.warn('Effect Cancelled', effectId),
  //     },
  //   }
  // :
  {};
export const sagaMiddleware = createSagaMiddleware(middleWareOptions);

console.log("Creating App Store.......");

const reduxLogger = createLogger({
  titleFormatter(formattedAction, formattedTime, took) {
    return `action @ ${formattedTime} ${
      formattedAction.type
    } (in ${took.toFixed(2)} ms)`;
  },
});
// let middlewares = [routerMiddleware(history), sagaMiddleware];
let middlewares = [sagaMiddleware];

if (global.ENABLE_REDUX_LOGGER) {
  console.log("unique: add logger");
  middlewares.push(reduxLogger);
}

const store = createStore(
  makeRootReducer(),
  // composeWithDevTools({maxAge: 1000})(applyMiddleware(...middlewares)),
  applyMiddleware(...middlewares)
) as Store<ApptileStore, AnyAction>;
store.addedReducers = {};
window.store = store;

sagaMiddleware.run(rootSaga);

export type AppDispatch = typeof store.dispatch;
export const useAppDispatch = (): ReturnType<typeof useDispatch> =>
  useDispatch<AppDispatch>();

export default store;
