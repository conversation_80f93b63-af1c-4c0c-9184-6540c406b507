import Immutable from 'immutable';
import _, {pick, toInteger, omit} from 'lodash';
import React, {createRef, Ref, useCallback, useEffect, useRef, useState} from 'react';
import {Platform, Pressable, View, Text, Animated} from 'react-native';
import {LayoutRecord} from '../../common/datatypes/types';
import {SafeAny} from '../../common/types';
import {EmptyPlaceholder} from '../../components/common/EmptyPlaceholder';
import {getPlatformStyles} from '../../styles/styleUtils';
import {withModelStyles} from '../../views/containers/ThemeContainer';
import {default as NativeEditableWidget} from './common/components/EditableWidget';
import {getShadowStyle} from './common/shadowUtils';
import {WidgetTreeNode} from './widgetLayout';
import ApptileScrollView from '../../views/screen/ApptileScrollView';
import {performHapticFeedback} from '../state/ApptileGlobalPlugin/ApptileHaptics';
// import useTappableGestureWrapperHook from './common/hooks/useTappableGestureWrapperHook';
// import useReanimatedKeyboardStyles from './common/hooks/useReanimatedKeyboardStyles';

interface ApptileFlexboxInternalProps {
  isEditable: boolean;
  children: WidgetTreeNode[];
  containerId: string;
  layout: LayoutRecord;
  model: Immutable.Map<string, SafeAny>;
  config: Immutable.Map<string, SafeAny>;
  renderWidgetTreeNode: (widget: WidgetTreeNode, instance?: number | undefined) => any;
  instances?: number;
  modelStyles?: Record<string, any>;
  triggerEvent: (event: string) => void;
  forwardedRef: React.Ref<any>;
  message?: string;
}

export interface ApptileFlexboxProps extends ApptileFlexboxInternalProps {
  previewWidgetAdd: (item, monitor) => void;
  previewWidgetMove: (item, monitor) => void;
  undoPreview: (item) => void;
}

const AnimatedApptileScrollView = Animated.createAnimatedComponent(ApptileScrollView);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const _ApptileFlexBox: React.FC<ApptileFlexboxProps> = React.forwardRef((props, forwardedRef) => {
  let EditableWidget = NativeEditableWidget;

  if (global.EditableWidget) {
    // logger.info("Taking the web version of editable widget in apptileflexbox");
    EditableWidget = global.EditableWidget; 
  }

  const {
    children,
    isEditable,
    layout,
    model,
    config,
    modelStyles,
    renderWidgetTreeNode,
    triggerEvent,
    isAnimated,
    animations,
    setShouldHide,
    // runHideAnim
  } = props;
  const showPlaceholder = isEditable && !children.length;
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const isTappable = model.get('isTappable');
  // const makeKeyboardAware = model.get('makeKeyboardAware');
  const detectVisibility = model.get('detectVisibility');
  const enableHaptics = model?.get('enableHaptics');
  const hapticMethod = model?.get('hapticMethod');

  const viewRef = useRef<any>();
  const [viewRefState, setViewRefState] = useState<Ref<any>>(createRef());

  const renderWidgetNode = useCallback(
    (widget: WidgetTreeNode, instance?: number) => {
      let EditableHandlePositionContext = {
        Consumer: ({children}) => (<View>
          <Text>{"This should never be rendered"}</Text>
          {children}
        </View>),
        Provider: ({children}) => (<View>
          <Text>{"This should never be rendered"}</Text>
          {children}
        </View>)
      };
      if (isEditable && global.EditableHandlePositionContext) {
        EditableHandlePositionContext = global.EditableHandlePositionContext;
      } else if (isEditable) {
        logger.error("isEditable is true for ApptileFlexbox but EditableHandlePositionContext is not available on global");
      }

      return isEditable ? (
        <EditableHandlePositionContext.Consumer key={`${widget.id}_-${instance}`}>
          {handlePosition => (
            <EditableHandlePositionContext.Provider value={handlePosition}>
              <EditableWidget {...{widget, isEditable}}>{renderWidgetTreeNode(widget)}</EditableWidget>
            </EditableHandlePositionContext.Provider>
          )}
        </EditableHandlePositionContext.Consumer>
      ) : (
        renderWidgetTreeNode(widget)
      );
    },
    [isEditable, renderWidgetTreeNode],
  );

  const onPress = useCallback(() => {
    if (enableHaptics) performHapticFeedback(hapticMethod);
    triggerEvent('onTap');
  }, [enableHaptics, hapticMethod, triggerEvent]);

  const isScrollView = layoutStyles.overflow === 'scroll';

  let ViewComponentConf: {web: any; default: any;} = {
    web: View,
    default: View
  };

  if (isAnimated) {
    if (isTappable) {
      if (isEditable) {
        ViewComponentConf.web = Animated.View;
      } else {
        ViewComponentConf.web = AnimatedPressable;
      }
      ViewComponentConf.default = AnimatedPressable;
    } else {
      if (isScrollView) {
        ViewComponentConf.web = AnimatedApptileScrollView;
        ViewComponentConf.default = AnimatedApptileScrollView;
      } else {
        ViewComponentConf.web = Animated.View;
        ViewComponentConf.default = Animated.View;
      }
    }
  } else {
    if (isTappable) {
      if (isEditable) {
        ViewComponentConf.web = View;
      } else {
        ViewComponentConf.web = Pressable;
      }
      ViewComponentConf.default = Pressable;
    } else {
      if (isScrollView) {
        ViewComponentConf.web = ApptileScrollView;
        ViewComponentConf.default = ApptileScrollView;
      } else {
        ViewComponentConf.web = View;
        ViewComponentConf.default = View;
      }
    }
  }

  let ViewComponent = Platform.select(ViewComponentConf);

  const pressableProps = isTappable ? {onPress: onPress} : {};
  const contentContainerStyles = ['flexDirection', 'justifyContent', 'alignItems', 'alignContent'];

  const setViewRefs = useCallback(
    ref => {
      viewRef.current = ref;
      if (Platform.OS === 'web') {
        if (!isScrollView && ref !== viewRefState) setViewRefState(ref);
      }
      if (forwardedRef !== null) {
        if (typeof forwardedRef === 'function') {
          forwardedRef(ref);
        } else {
          if (!forwardedRef.hasOwnProperty('current')) {
            logger.error(
              'Unexpected ref object provided for %s. ' + 'Use either a ref-setter function or React.createRef().',
            );
          }
          forwardedRef.current = ref;
        }
      }
    },
    [forwardedRef, isScrollView, viewRefState],
  );
  const setScrollViewParentRef = useCallback(
    ref => {
      if (Platform.OS === 'web' && ref !== viewRefState) setViewRefState(ref);
      viewRef.current = ref;
    },
    [viewRefState, viewRef],
  );

  let scrollViewProps = isScrollView
    ? {
        contentContainerStyle: pick(layoutStyles, contentContainerStyles),
        horizontal: layoutStyles?.flexDirection === 'row',
        showsHorizontalScrollIndicator: false,
        keyboardShouldPersistTaps: 'handled',
        innerViewRef: setScrollViewParentRef,
      }
    : {
      };

  const elevation = toInteger(config.getIn(['config', 'style', 'elevation'], 0));
  const shadowStyles = getPlatformStyles({...getShadowStyle(elevation), elevation});

  const mountMove = useRef(new Animated.Value(0)).current;

  // if (animations?.transitions?.exiting && runHideAnim) {
  //   Animated.timing(
  //       mountMove,
  //       {
  //         toValue: 1000,
  //         duration: 300,
  //         useNativeDriver: true
  //       }
  //     ).start(() => {
  //       setShouldHide(true);
  //     });
  // }

  // useEffect(() => {
  //   if (isAnimated && animations?.transitions?.entering) {
  //     logger.info('[UNIQUE] inside animation trigger code');
  //     Animated.timing(
  //       mountMove,
  //       {
  //         toValue: 0,
  //         duration: 300,
  //         useNativeDriver: true
  //       }
  //     ).start();
  //   }
  // }, [isAnimated]);

  let animatedStyles = {};
  if (isAnimated) {
    // {opacity: fadeAnim}
    animatedStyles = {transform: [{translateY: mountMove}]};
  }

  const childrenDom = children.map((widget, idx) => renderWidgetNode(widget, idx));

  return (
    <ViewComponent
        {...pressableProps}
        {...scrollViewProps}
        ref={setViewRefs}
        style={[
          isScrollView ? omit(layoutStyles, contentContainerStyles) : layoutStyles,
          modelStyles,
          shadowStyles,
          animatedStyles
        ]}>
        {childrenDom}
        {showPlaceholder && <EmptyPlaceholder />}
      </ViewComponent>
  );
});

const ApptileFlexboxWithTheme = withModelStyles(_ApptileFlexBox);
const ConnectedApptileFlexBox = ApptileFlexboxWithTheme;
export default ConnectedApptileFlexBox;