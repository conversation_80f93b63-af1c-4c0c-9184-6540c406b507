import React from 'react';
import _ from 'lodash';

import {Optional} from '../../../common/types';
import {WidgetTreeNode} from '../widgetLayout';
import {ContainerProps} from './containers/containerTypes';
import {ApptileWidget} from './containers/ApptileWidget';
import {ApptileModule} from './containers/ApptileModule';
import {ApptileContainer} from './containers/ApptileContainer';
import {ApptileBottomSheet} from './containers/ApptileBottomSheet';
import {ApptileListView} from './containers/ApptileListView';
import {ApptileModal} from './containers/ApptileModal';

export type RenderWidgetTreeNodeProps = Optional<
  Pick<
    ContainerProps,
    | 'containerId'
    | 'pageId'
    | 'pageKey'
    | 'children'
    | 'model'
    | 'config'
    | 'isEditable'
    | 'previewWidgetAdd'
    | 'previewWidgetMove'
    | 'undoPreview'
  >,
  'model'
>;

export const renderWidgetTreeNode =
  (props: RenderWidgetTreeNodeProps) =>
  (widget: WidgetTreeNode, instance?: number | undefined = props.instance) => {
    if (!widget || widget.hidden) return <></>;
    const {isEditable, pageId} = props;
    const {children, widgetType, id, pageKey, layout, isDynamic, config, cachedModel} = widget;

    const afProps = {
      isEditable,
      children,
      containerId: id,
      id,
      pageId,
      layout,
      config,
      widgetType,
      pageKey,
      isDynamic,
      cachedModel,
    };
    const widgetKey =
      (instance !== undefined ? encodeListViewPlugin(id, instance) : id) + '_:' + (isDynamic ? 'D' : 'S');
    switch (widgetType) {
      case 'ContainerWidget':
        return <ApptileContainer instance={instance} key={widgetKey} {...afProps} />;
      case 'ModalWidget':
        return <ApptileModal instance={instance} key={widgetKey} {...afProps} 
          sentry-label={pageKey + "_" + id + "_" + "modal"}/>;
      case 'BottomSheetWidget':
        return <ApptileBottomSheet instance={instance} key={widgetKey} {...afProps} 
          sentry-label={pageKey + "_" + id + "_" + "bottomsheet"} />;
      case 'ListViewWidget':
        return <ApptileListView key={id} {...afProps} />;
      case 'ModuleInstance':
        return <ApptileModule instance={instance} key={widgetKey} {...afProps} />;
      default:
        return <ApptileWidget 
          key={widgetKey} 
          instance={instance} 
          widget={widget} 
          sentry-label={pageKey + "_" + widgetType + "_" + id}
        />;
    }
  };

export const encodeListViewPlugin = (pluginId: string, instance: number) => {
  return `${pluginId}_-${instance}`;
};
