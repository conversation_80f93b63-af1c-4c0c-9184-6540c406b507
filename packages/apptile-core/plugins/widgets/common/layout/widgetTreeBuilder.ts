import Immutable from 'immutable';
import {WidgetLayout, WidgetTree, WidgetTreeNode} from '../../widgetLayout';

export default function buildWidgetTree(widgets: Immutable.OrderedMap<unknown, WidgetLayout>): WidgetTree {
  const tree: WidgetTree = [];
  const nodes: {[s: string]: WidgetTreeNode} = {};

  widgets.forEach(widget => {
    nodes[widget.id] = {
      ...widget,
      children: [],
    };
  });
  widgets.forEach(widget => {
    const {container} = widget.layout;
    if (widget.hidden) return;

    if (container) {
      const containerNode = nodes[container];
      if (containerNode) {
        nodes[container].children.push(nodes[widget.id]);
      } else {
        logger.info(
          `Component "${widget.id}" in ${widget.pageKey} is orphaned -- it used to be a child of "${container}" which no longer exists.`,
        );
      }
    } else {
      tree.push(nodes[widget.id]);
    }
  });

  for (let index = 0; index < tree.length; ++index) {
    tree[index].depth = 0;  
  }

  return tree;
}

export const buildTilesTree = widgets => {
  return widgets
    .map(w => {
      return {
        ...w,
        children: buildTilesTree(w.children).filter(w => w.children.length || w.widgetType === 'ModuleInstance'),
      };
    })
    .filter(w => ['ModuleInstance', 'ContainerWidget'].includes(w.widgetType));
};
