import React from 'react';
import {GetRegisteredWidget} from '../../../plugin';
import {WidgetTreeNode} from '../../widgetLayout';

type ApptileWidgetProps = React.PropsWithRef<{
  widget: WidgetTreeNode;
  instance?: number | null;
}>;

export const ApptileWidget: React.FC<ApptileWidgetProps> = React.forwardRef(
  ({widget, instance}: ApptileWidgetProps, ref) => {
    const {widgetType, pageKey, pageId, id, isDynamic, config, cachedModel} = widget;
    // const [WidgetClass] = useMemo(() => [GetRegisteredWidget(widgetType)], [widgetType]);
    const WidgetClass = GetRegisteredWidget(widgetType);

    if (!WidgetClass) {
      return null;
    }

    const widgetProps = {
      id,
      pageId,
      pageKey,
      instance,
      isDynamic,
      config,
      cachedModel,
    };

    return <WidgetClass {...widgetProps} ref={ref} />;
  },
);
