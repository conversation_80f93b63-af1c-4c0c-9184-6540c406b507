import React, {Children} from 'react';
import {PluginEditorsConfig} from '../../common/EditorControlTypes';
import {PluginPropertySettings} from '../plugin';
import {renderWidgetTreeNode} from '../widgets/common/containerWrappers';
import {connectWidget} from '../widgets/widget';
import {Plan, allAvailablePlans} from '../../web/common/pricePlanConstants';

export interface IModuleInstanceConfig {
  childNamespace: string;
  moduleUUID: string;
  moduleName: string;
  variantSelected: string;
  changesDone: boolean;
  variantGating: Plan;
}

const ModuleInstanceConfig: IModuleInstanceConfig = {
  childNamespace: '',
  moduleUUID: '',
  moduleName: '',
  variantSelected: 'custom',
  changesDone: false,
  // TODO(gaurav): This probably is wrong. It shouldn't be in core
  variantGating: allAvailablePlans.CORE,
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'module',
  type: 'widget',
  hidden: () => true,
};

const ModuleInstance: React.FC = React.forwardRef((props, ref) => {
  const {instance, children} = props;
  return React.cloneElement(Children.only(renderWidgetTreeNode({...props})(children[0], instance)), {
    ref: ref,
  });
});

const editors: PluginEditorsConfig<typeof ModuleInstanceConfig> = {
  module: [
    {
      type: 'moduleEditor',
      name: 'module',
      props: {
        label: 'Module',
      },
    },
  ],
};
const propertySettings: PluginPropertySettings = {};
const pluginOnUpdate = undefined;

export default connectWidget('ModuleInstance', ModuleInstance, ModuleInstanceConfig, pluginOnUpdate, editors, {
  propertySettings,
  pluginListing,
});
