import {triggerAction, TriggerActionPayload} from '../../../actions/DispatchActions';
import {ApptileDimensions} from '../../../common/apptile/ApptileGlobal/ApptileDimensions';
import {getAppDispatch} from '../../../common/utils/dispatcher';
import {getAppState} from '../../../common/utils/getAppState';
import {APPTILE_GLOBAL_PLUGIN_ID} from '../../../constants/modelConstants';
import {RootState} from '../../../store/RootReducer';
import {Platform} from 'react-native';
import {VERSION_CODE} from '../../../lib/RNApptile';
import {AppPageTriggerOptions, connectPlugin, PluginPropertySettings, TriggerActionIdentifier} from '../../plugin';
import userSigningAction from './actions/userSigningAction';
import {setDisplaySizes} from './ApptileGlobalDimensionActions';
import {triggerHapticFeedback} from './ApptileHaptics';
import {dismissKeyBoard} from './actions/DismissKeyboardAction';
import {openLink} from './actions/OpenLinkAction';
import {shareText} from './actions/ShareTextAction';
import {copyText} from './actions/CopyTextAction';
import {fileDownloadAction} from './actions/fileDownloadAction';
import {switchAppFork} from './actions/SwitchAppForkAction';
import {enableLogrocket} from './actions/EnableLogrocketAction';
import {softRefresh} from './actions/SoftRefreshAction';
import {fetchForkInfoAction} from './actions/FetchForkInfoAction';
import pkg from '../../../package.json';

import {BRAND_SETTINGS_KEY, BRAND_LOGO_ASSET_ID, BRAND_LOGO_WIDTH} from '../../../common/datatypes/BrandSettingsTypes';

import docs from './docs';
import _ from 'lodash';
import { PluginEditorsConfig } from '../../../common/EditorControlTypes';
import { LocalStorage, modelUpdateAction, triggerCustomEventListener } from '../../..';

interface ApptileGlobalPluginConfigParams {
  value: any;
  platform: string;
  screenWidth: number;
  screenHeight: number;
  windowWidth: number;
  windowHeight: number;
  setDisplaySizes: string;
  triggerHapticFeedback: string;
  postSignInAction: string;
  postSignOutAction: string;
  dismissKeyboard: string;
  openLink: string;
  shareText: string;
  copyText: string;
  switchAppFork: string;
  enableLogrocket: string;
  frameworkVersion: string;
  brandLogoAssetId: string;
  brandLogoSize: string;
  appSettings: string;
  versionNumber: number;
  softRefresh: string;
  latestAndroidBuild: number;
  latestIosBuild: number;
  appStorePermaLink: string;
  playStorePermaLink: string;
  forkInfoCDNBaseUrl: string;
  downloadFile: string;
  fetchPushNotificationInfo: string;
  fetchForkInfo: string;
  forkInfo: any;
}

export const ApptileGlobalPluginConfig: ApptileGlobalPluginConfigParams = {
  value: null,
  platform: '',
  screenWidth: 1,
  screenHeight: 1,
  windowWidth: 1,
  windowHeight: 1,
  setDisplaySizes: 'action',
  triggerHapticFeedback: 'action',
  postSignInAction: 'action',
  postSignOutAction: 'action',
  dismissKeyboard: 'action',
  openLink: 'action',
  shareText: 'action',
  copyText: 'action',
  switchAppFork: 'action',
  enableLogrocket: 'action',
  softRefresh: 'action',
  frameworkVersion: '0.0.1',
  brandLogoAssetId: '',
  brandLogoSize: '',
  appSettings: '',
  versionNumber: '',
  latestAndroidBuild: '',
  latestIosBuild: '',
  appStorePermaLink: '',
  playStorePermaLink: '',
  downloadFile:'action',
  fetchPushNotificationInfo: 'action',
  forkInfoCDNBaseUrl: '',
  fetchForkInfo: 'action',
  forkInfo: '',
};

const propertySettings: PluginPropertySettings = {
  value: {
    getValue: (model, renderedValue) => {
      return renderedValue;
    },
  },
  platform: {
    getValue: (model, renderedValue, selector) => {
      return Platform.OS;
    },
  },
  brandLogoAssetId: {
    getValue: (model, renderedValue, selector) => {
      return getBrandLogoAssetId();
    },
  },
  brandLogoSize: {
    getValue: (model, renderedValue, selector) => {
      return getBrandLogoSize();
    },
  },
  appSettings: {
    getValue: (model, renderedValue, selector) => {
      return getAppSettings();
    },
  },
  setDisplaySizes: {
    type: TriggerActionIdentifier,
    getValue(_, renderedValue, selector) {
      return setDisplaySizes;
    },
    actionMetadata: {
      editableInputParams: {
        windowWidth: '',
        windowHeight: '',
        screenWidth: '',
        screenHeight: '',
      },
    },
  },
  triggerHapticFeedback: {
    type: TriggerActionIdentifier,
    getValue(_, renderedValue, selector) {
      return triggerHapticFeedback;
    },
    actionMetadata: {
      editableInputParams: {
        method: [
          'selection',
          'impactLight',
          'impactMedium',
          'impactHeavy',
          'notificationSuccess',
          'notificationWarning',
          'notificationError',
        ],
      },
    },
  },
  postSignInAction: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return userSigningAction.postSignInAction;
    },
    actionMetadata: {
      editableInputParams: {
        platformUserId: '',
      },
    },
  },
  postSignOutAction: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return userSigningAction.postSignOutAction;
    },
  },
  dismissKeyboard: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return dismissKeyBoard;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  openLink: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return openLink;
    },
    actionMetadata: {
      editableInputParams: {
        link: '',
      },
    },
  },
  shareText: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return shareText;
    },
    actionMetadata: {
      editableInputParams: {
        text: '',
      },
    },
  },
  copyText: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return copyText;
    },
    actionMetadata: {
      editableInputParams: {
        text: '',
      },
    },
  },
  softRefresh: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return softRefresh;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  switchAppFork: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return switchAppFork;
    },
    actionMetadata: {
      editableInputParams: {
        forkName: '',
      },
    },
  },
  enableLogrocket: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return enableLogrocket; 
    }
  },
  frameworkVersion: {
    getValue: (model, renderedValue, selector) => {
      return pkg.version;
    },
  },
  versionNumber: {
    getValue: (model, renderedValue, selector) => {
      const vn = isNaN(Number(VERSION_CODE)) ? VERSION_CODE : Number(VERSION_CODE);
      return vn;
    },
  },
  latestAndroidBuild: {
    getValue: (model, renderedValue, selector) => {
      return Number(renderedValue) || 0;
    },
  },
  latestIosBuild: {
    getValue: (model, renderedValue, selector) => {
      return Number(renderedValue) || 0;
    },
  },
  downloadFile: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return fileDownloadAction;
    },
    actionMetadata: {
      editableInputParams: {
        fileUrl: '',
      },
    },
  },
  fetchPushNotificationInfo: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return ()=>{
        console.log("ApptileGetNotificationInfo Triggered");
        triggerCustomEventListener('ApptileGetNotificationInfo')
      };
    }
  },
  fetchForkInfo: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return fetchForkInfoAction;
    },
    actionMetadata: {
      editableInputParams: {
        appUUID: '',
      },
    },
  },
};

const getBrandLogoAssetId = () => {
  const state = getAppState();
  const brandLogoAssetId = state.appConfig.current
    ?.getIn(['settings', BRAND_SETTINGS_KEY])
    ?.getSettingValue(BRAND_LOGO_ASSET_ID);

  if (_.isNil(brandLogoAssetId)) {
    return '';
  }

  return brandLogoAssetId;
};

const getBrandLogoSize = () => {
  const state = getAppState();
  const brandLogoSize = state.appConfig.current
    ?.getIn(['settings', BRAND_SETTINGS_KEY])
    ?.getSettingValue(BRAND_LOGO_WIDTH);

  if (_.isNil(brandLogoSize)) {
    return '';
  }

  return brandLogoSize;
};

const getAppSettings = () => {
  const state = getAppState();
  const appSettings = state.appConfig.current
    ?.get('settings')?.toJS();

  if (_.isNil(appSettings)) {
    return {};
  }

  return appSettings;
};

const onPluginUpdate = function* (
  state: RootState,
  pluginId: string,
  pageKey: string,
  instance: number | undefined,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): any {
  const dispatch = getAppDispatch();
  if (pageLoad) {
    ApptileDimensions.addListener(sizes => {
      // logger.info('Got Sizes', sizes);
      const payload: TriggerActionPayload = {
        pluginConfig: null,
        pluginModel: state.stageModel.getModelValue([pluginId]),
        pluginSelector: [APPTILE_GLOBAL_PLUGIN_ID],
        eventModelJS: {value: 'setDisplaySizes', params: sizes},
      };
      _.debounce(() => dispatch(triggerAction(payload)), 400);
    });
    ApptileDimensions.init();

    const appUUID = state.apptile?.appId;

    dispatch(modelUpdateAction([{
      selector: [APPTILE_GLOBAL_PLUGIN_ID, 'appUUID'],
      newValue: appUUID,
    }], undefined, true));

    // Updating the model with the localStorage value
    const region = yield LocalStorage.getValue('region');
    dispatch(modelUpdateAction([{
      selector: [APPTILE_GLOBAL_PLUGIN_ID, 'region'],
      newValue: region,
    }], undefined, true));
  }

  // const model: any = state.stageModel.getPluginModel(pageKey, pluginId);
  // let modelSelector = [pluginId];
  // if (options?.focusTrigger && !runOnPageFocus) {
  //   return;
  // }
  // if (
  //   !userTriggered &&
  //   !runWhenModelUpdates &&
  //   !(pageLoad && runWhenPageLoads) &&
  //   !(options?.focusTrigger && runOnPageFocus)
  // ) {
  //   // logger.info(
  //   //   `Skipping onPluginUpdate ${pluginId} runOnPageFocus: ${runOnPageFocus} focusTrigger: ${options?.focusTrigger}`,
  //   // );
  //   return;
  // }
  // if (value) {
  //   yield put(triggerPageEvent(pageKey, pluginId, instance, 'onReturnTrue'));
  // } else {
  //   yield put(triggerPageEvent(pageKey, pluginId, instance, 'onReturnFalse'));
  // }
  // let pluginUpdateReturn = {
  //   modelUpdates: [
  //     {
  //       selector: modelSelector.concat(['previousValue']),
  //       newValue: value,
  //     },
  //   ] as PluginModelChange[],
  // };
  // return pluginUpdateReturn;
};

const ApptileGlobalPluginEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'appStorePermaLink',
      props: {
        label: 'App Store Permanent Link',
      }
    },
    {
      type: 'codeInput',
      name: 'playStorePermaLink',
      props: {
        label: 'Play Store Permanent Link',
      }
    },
    {
      type: 'codeInput',
      name: 'forkInfoCDNBaseUrl',
      props: {
        label: 'Fork Info CDN Base URL',
      },
    }
  ],
  advanced: [
    {
      type: 'codeInput',
      name: 'latestAndroidBuild',
      props: {
        label: 'Latest Android Build Number',
      }
    },
    {
      type: 'codeInput',
      name: 'latestIosBuild',
      props: {
        label: 'Latest iOS Build Number',
      }
    }
  ],
}

export default connectPlugin('ApptileGlobal', null, ApptileGlobalPluginConfig, onPluginUpdate, ApptileGlobalPluginEditors, {
  propertySettings,
  pluginListing: undefined,
  docs,
});