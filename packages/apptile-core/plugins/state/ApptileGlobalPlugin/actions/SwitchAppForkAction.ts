import {InteractionManager} from 'react-native';
import { checkAppUpdate, doesManifestCauseUpdate, downloadAppUpdate } from '../../../../common/ApptileUpdate';
import {PluginConfig} from '../../../../common/datatypes/types';
import {Selector} from '../../../../common/DependencyGraph/types';
import {AppDispatch} from '../../../../store';
import { restartAppConfig } from '../../../../actions/AppConfigActions';
import LocalStorage from '../../../../common/LocalStorage';
import { showJSSplashScreen } from '../../../../components/common/JSSplashScreen';

export const switchAppFork = async (
  _dispatch: AppDispatch,
  _config: PluginConfig,
  _model: any,
  _selector: Selector,
  params: any,
) => {
  try {
    const {forkName} = params;
    console.log(`switchAppFork : ${forkName}`);
    if (!forkName)return;
    const currentForkName = await LocalStorage.getValue('activeForkName');
    await LocalStorage.removeItem('shopData');
    await LocalStorage.removeItem('currentCartId');
    await LocalStorage.setValue('region', forkName);
    await LocalStorage.setValue('activeForkName', forkName);
    return checkAppUpdate()
    .then(async (appManifest) => {
      if(appManifest && appManifest.publishedCommitId){
        return doesManifestCauseUpdate(appManifest)
          .then(bDidUpdate => ({bDidUpdate, appManifest}))
      } else {
        await LocalStorage.setValue('activeForkName', currentForkName);
        return {bDidUpdate: false, appManifest};
      }
    }).then(({appManifest, bDidUpdate}) => {
      if (bDidUpdate) {
        showJSSplashScreen();
        downloadAppUpdate(appManifest)
        .then(async (bUpdatedConfig) => {
          if(bUpdatedConfig){
            await InteractionManager.runAfterInteractions();
            logger.info('[APPUPDATE] RESTARTING APP !!!');
            _dispatch(restartAppConfig());
          }
        }).catch(e => {
          logger.warn('[APPUPDATE] Failed updating app', e)
        });
      }
    })
  } catch (e) {
    logger.error(e);
  }
};