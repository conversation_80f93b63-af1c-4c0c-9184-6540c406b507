import {Share} from 'react-native';

import {PluginConfig} from '../../../../common/datatypes/types';
import {Selector} from '../../../../common/DependencyGraph/types';
import {AppDispatch} from '../../../../store';

export const shareText = async (
  _dispatch: AppDispatch,
  _config: PluginConfig,
  _model: any,
  _selector: Selector,
  params: any,
) => {
  try {
    const {text} = params;
    if (!text) throw Error();
    Share.share({message: text});
  } catch (e) {
    toast.show('can not share!', {type: 'error', placement: 'top', duration: 1000});
  }
};
