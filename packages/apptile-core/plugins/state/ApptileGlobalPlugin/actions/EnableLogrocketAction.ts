import { LocalStorage } from 'apptile-core';
let triggerCount = 0;
let timeoutHandle = null;
export function enableLogrocket () {
  if (timeoutHandle) {
    clearTimeout(timeoutHandle);
  }

  if (triggerCount >= 15) {
    triggerCount = 0;
    import('@logrocket/react-native')
      .then(logrocketModule => {
        logrocketModule.default.init('97heiy/lively-selling-apps', {
          network: {
            requestSanitizer: request => {
              if (request.headers['x-auth-token']) {
                request.headers['x-auth-token'] = '';
              }

              return request;
            },
          },
          // connectionType: 'WIFI',
          console: {
            isEnabled: {
              warn: false,
            },
            shouldAggregateConsoleErrors: true,
          },
          redactionTags: ['RedactionString'],
        });
        return LocalStorage.getValue('loggedInUser').then((user) => ({ logrocketModule, user }));
      })
      .then(({logrocketModule, user}) => {
        if (user && user.user_name) {
          const identity = {
            name: user.user_name,
            compan: user.company_name
          };
          
          logrocketModule.default.identify(user.user_id, identity);
        }
        alert("Logrocket initialized");
      })
      .catch(() => {
        alert("Failed to initialize logrocket");
      });
  } else {
    logger.info(`You are ${10 - triggerCount} taps away from enabling logrocket`);
    triggerCount += 1;
    
    timeoutHandle = setTimeout(() => {
      logger.info("Aborting enable of logrocket via timeout");
      triggerCount = 0;
    }, 500);
  }
}