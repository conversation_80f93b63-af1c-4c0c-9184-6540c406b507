import NotificationsApi from '../../api/NotificationsApi';
import {getAppConstants} from '../../constants/api';
import ApptileLocalSettings from './ApptileLocalSettings';
import {getAppBundleId, getUniqueDeviceId} from '../deviceInfo/deviceInfo';
import { triggerCustomEventListener } from '../utils/CustomEventSystem';
// TODO(gaurav) DONE eventbus
// import {registerForMoengageNotification} from '../ApptileAnalytics/moengageAnalytics/useMoEngage';

const updateFCMToken = async () => {
  if (ApptileLocalSettings.pushToken && ApptileLocalSettings.userIdentifier) {
    try {
      logger.info('Update FCM token started!!');
      const appId = getAppConstants().APPTILE_APP_ID as string;
      const token = ApptileLocalSettings.pushToken;
      const userIdentifier = ApptileLocalSettings.userIdentifier;
      if (token) {
        await NotificationsApi.updateFCMToken(userIdentifier, appId, token, getUniqueDeviceId(), getAppBundleId());
        // TODO(gaurav) DONE eventbus
        // registerForMoengageNotification(token);
        triggerCustomEventListener('registerForMoengageNotification', token);
      }
      logger.info('Update FCM token done !!');
    } catch (e) {
      logger.error(e);
    }
  }
};

export default updateFCMToken;
