// import {NormalizedPluginProps, STATIC_PLUGIN_PROPS} from 'src/plugins/Plugin';
import {transform as transformSync, transformFromAst} from '@babel/standalone';
import {NodePath, TransformOptions} from '@babel/core';
import * as t from '@babel/types';
import {HAS_TEMPLATE_REGEX} from './JSBindingTypes';

export const JSBINDING_TEST_REGEX = /.*?({{[\s\S]*?}}).*?/;
export const JSBINDING_MATCH_REGEX = () => new RegExp(/.*?({{[\s\S]*?}}).*?/g);
export const SINGLE_OBJECT_REGEX = /^{{([\s\S]+?)}}$/g;
export const JSBINDING_REPLACE_REGEX = /{{([\s\S]*?)}}/g;

// This regex will match everyhting between two braces like this {{ matched_item }}
// If there are triple braces it will include the innermost braces in the match i.e. in {{{item}}} the match will have {item}
export const REGEX_MATCH_TRIPLE_BRACE = /{{([\s\S]*?}?)}}/g;

export function isJSBinding(value: string): boolean {
  // return JSBINDING_TEST_REGEX.test(value);
  if (typeof value !== 'string') {
    return false;
  }

  const start = value.indexOf('{{');
  const end = value.lastIndexOf('}}');
  return end > start && start >= 0;
}

export const isSingleObjectString = (v: string): boolean => {
  const matches = v.match(HAS_TEMPLATE_REGEX);
  if (matches) {
    if (matches.length > 1) {
      return false;
    }
  }
  return !!v.match(SINGLE_OBJECT_REGEX);
};

const localContextName = '$pageContext';
const globalContextName = '$appJSModel';
const transpiledFunctionArgs = [localContextName, globalContextName, 'i', 'currentPage', '_', 'moment'];

/**
 *  Babel plugin that transforms a binding function to modify all accesses into 
 *  the context to be namespaced
 */
function namespacePlugin(babel: {types: typeof t}, options: { ns: string }) {
  const t = babel.types;

  const nsPrefix = options.ns;
  return {
    visitor: {
      MemberExpression(path: NodePath<t.MemberExpression>) {
        const objectPath = path.get('object');
        if (
          objectPath.isIdentifier() && objectPath.node.name == localContextName
        ) {
          const propertyPath = path.get('property');
          if (propertyPath.isIdentifier()) {
            const propertyName = nsPrefix + '::' + propertyPath.node.name;
            path.replaceWith(
              t.memberExpression(
                t.identifier(localContextName),
                t.stringLiteral(propertyName),
                true
              )              
            );
            path.skip();
          }
        }      
      },
    }
  };
}

/**
 * Babel plugin that transforms jsbindings into functions. Jsbindings
 * '{{ code }} --> function($appmodel, i, currentPage, _, moment) { return code; }'
 * Inside the `code` string, all MemberExpressions as defined by babel are prefixed with $appmodel,
 * except bound references and and references to i, currentPage, _ and moment
 *
 * More information about the concepts used here can be found in the babel handbook:
 * https://github.com/jamiebuilds/babel-handbook/blob/master/translations/en/plugin-handbook.md#toc-inserting-into-a-container
 *
 * For development of transpilation logic astexplorer is a useful tool
 * https://astexplorer.net/
 */
function transformerPlugin(babel: {types: typeof t}, options: { globalScopeIdentifiers: string[], selector: string[] }) {
  const t = babel.types;
  return {
    visitor: {
      Program: {
        // If you need to assign names to the functions at some point use the commented code here
        exit(path: NodePath<t.Program>, state: {filename: string}) {
          let functionName = null;
          if (state && state.filename) {
            const parts = state.filename.split('/');
            functionName = t.identifier(parts[parts.length - 1]);
          }

          if (path.node.body.length == 0) {
            // If a single string is provided as the binding babel will interpret that as a
            // directive.
            const directive = path.node.directives[0];
            path.node.directives = [];
            if (!directive) {
              logger.error('Not a transpilable expression');
            } else {
              const functionBody = t.blockStatement([t.returnStatement(t.stringLiteral(directive.value.value))]);
              const functionParams = transpiledFunctionArgs.map(it => t.identifier(it));
              const functionDecl = t.functionDeclaration(functionName, functionParams, functionBody);
              path.pushContainer('body', functionDecl);
            }
          } else if (path.node.body.length == 1) {
            let functionBody;
            const bodyStatement = path.node.body[0];
            if (t.isExpressionStatement(bodyStatement)) {
              functionBody = t.blockStatement([t.returnStatement(bodyStatement.expression)]);
            } else {
              functionBody = t.blockStatement([
                t.returnStatement(t.stringLiteral('Invalid expression! A binding should be a single expression')),
              ]);
            }

            const functionParams = transpiledFunctionArgs.map(it => t.identifier(it));
            const functionDecl = t.functionDeclaration(functionName, functionParams, functionBody);
            const bodyInternalPath = path.get('body.0');
            if (!Array.isArray(bodyInternalPath)) {
              bodyInternalPath.replaceWith(functionDecl);
            } else {
              logger.error(
                'The expression for binding has more than one statement! only single statements can be transpiled.',
              );
              // TODO(gaurav) DONE NO URGENT NEED do these only when in web
              import('../../store').then(storeMod => {
                const store = storeMod.default;
                store.dispatch({
                  type: 'EDITOR_RECORD_BINDING_ERROR',
                  payload: {
                    binding: "couldn't find binding",
                    error: {
                      transpiledFn: 'could not generate function',
                      error: 'Failure in subpart',
                    },
                  },
                });
              });
            }
          } else {
            logger.error('Cannot transpile expression');
            import('../../store').then(storeMod => {
              const store = storeMod.default;
              store.dispatch({
                type: 'EDITOR_RECORD_BINDING_ERROR',
                payload: {
                  binding: "couldn't find binding",
                  error: {
                    transpiledFn: 'could not generate function',
                    error: 'Failure in subpart',
                  },
                },
              });
            });
          }
        },
      },
      ExpressionStatement(path: NodePath<t.ExpressionStatement>) {
        const expressionPath = path.get('expression');
        const identifierName = expressionPath.node.name;
        if (
          expressionPath.isIdentifier() &&
          !transpiledFunctionArgs.includes(identifierName) &&
          !expressionPath.scope.hasBinding(identifierName)
        ) {
          let contextName = localContextName;
          if (options.globalScopeIdentifiers.includes(identifierName)) {
            contextName = globalContextName;
          }
          expressionPath.replaceWith(t.memberExpression(t.identifier(contextName), path.node.expression));
          expressionPath.skip();
        }
      },
      Property(propertyPath: NodePath<t.Property>) {
        const valuePath = propertyPath.get('value');
        const identifierName = valuePath.node.name;
        if (
          valuePath.isIdentifier() &&
          !transpiledFunctionArgs.includes(identifierName) &&
          !valuePath.scope.hasBinding(identifierName)
        ) {
          let contextName = localContextName;
          if (options.globalScopeIdentifiers.includes(identifierName)) {
            contextName = globalContextName;
          }
          valuePath.replaceWith(t.memberExpression(t.identifier(contextName), valuePath.node));

          valuePath.skip();
        }
      },
      LogicalExpression(expressionPath: NodePath<t.LogicalExpression>) {
        const leftPath = expressionPath.get('left');
        let identifierName = leftPath.node.name;
        if (
          leftPath.isIdentifier() &&
          !transpiledFunctionArgs.includes(identifierName) &&
          !leftPath.scope.hasBinding(identifierName)
        ) {
          let contextName = localContextName;
          if (options.globalScopeIdentifiers.includes(identifierName)) {
            contextName = globalContextName;
          }
          leftPath.replaceWith(t.memberExpression(t.identifier(contextName), leftPath.node));

          leftPath.skip();
        }

        const rightPath = expressionPath.get('right');
        identifierName = rightPath.node.name;
        if (
          rightPath.isIdentifier() &&
          !transpiledFunctionArgs.includes(identifierName) &&
          !rightPath.scope.hasBinding(identifierName)
        ) {
          let contextName = localContextName;
          if (options.globalScopeIdentifiers.includes(identifierName)) {
            contextName = globalContextName;
          }
          rightPath.replaceWith(t.memberExpression(t.identifier(contextName), rightPath.node));

          rightPath.skip();
        }
      },
      OptionalMemberExpression(path: NodePath<t.OptionalMemberExpression>) {
        const objectPath = path.get('object');
        const identifierName = objectPath.node.name;
        if (
          objectPath.isIdentifier() &&
          !transpiledFunctionArgs.includes(identifierName) &&
          !objectPath.scope.hasBinding(identifierName)
        ) {
          let contextName = localContextName;
          if (options.globalScopeIdentifiers.includes(identifierName)) {
            contextName = globalContextName;
          }
          objectPath.replaceWith(t.memberExpression(t.identifier(contextName), path.node.object));
          objectPath.skip();
        }
      },
      MemberExpression(path: NodePath<t.MemberExpression>) {
        const objectPath = path.get('object');
        const identifierName = objectPath.node.name;
        if (
          objectPath.isIdentifier() &&
          !transpiledFunctionArgs.includes(identifierName) &&
          !objectPath.scope.hasBinding(identifierName)
        ) {
          let contextName = localContextName;
          if (options.globalScopeIdentifiers.includes(identifierName)) {
            contextName = globalContextName;
          }
          objectPath.replaceWith(t.memberExpression(t.identifier(contextName), path.node.object));
          objectPath.skip();
        }
      },
    },
  };
}

type ExpressionPart = {type: 'literal' | 'call'; value: string};

function createLeftOfBinaryExpression(types: typeof t, part: ExpressionPart) {
  if (part.type === 'literal') {
    return types.stringLiteral(part.value);
  } else {
    return types.callExpression(
      types.identifier(part.value),
      transpiledFunctionArgs.map(it => types.identifier(it)),
    );
  }
}

function mergeMultiBindingAsts(
  interpFcnAsts: Array<t.File | null | undefined>,
  returnExprParts: Array<ExpressionPart>,
) {
  const functionDecls = interpFcnAsts
    .map(file => {
      if (file) {
        return file.program.body[0];
      } else {
        logger.error('An interpolation did not compile properly!');
        import('../../store').then(storeMod => {
          const store = storeMod.default;
          store.dispatch({
            type: 'EDITOR_RECORD_BINDING_ERROR',
            payload: {
              binding: "couldn't find binding",
              error: {
                transpiledFn: 'could not generate function',
                error: 'Failure in subpart',
              },
            },
          });
        });
        return null;
      }
    })
    .filter(it => !!it);

  const output = transformSync(`const a = function() {}`, {
    ast: true,
    plugins: [
      function (babel) {
        const t = babel.types;

        let returnExpression = createLeftOfBinaryExpression(t, returnExprParts[returnExprParts.length - 1]);
        for (let index = returnExprParts.length - 2; index >= 0; --index) {
          const left = createLeftOfBinaryExpression(t, returnExprParts[index]);
          returnExpression = t.binaryExpression('+', left, returnExpression);
        }

        return {
          visitor: {
            Program: {
              exit(path: NodePath<t.MemberExpression>) {
                const functionBody = t.blockStatement([...functionDecls, t.returnStatement(returnExpression)]);
                const functionParams = transpiledFunctionArgs.map(it => t.identifier(it));
                const functionDecl = t.functionDeclaration(null, functionParams, functionBody);

                const bodyInternalPath = path.get('body.0');
                if (!Array.isArray(bodyInternalPath)) {
                  bodyInternalPath.replaceWith(functionDecl);
                } else {
                  logger.error(
                    'The expression for binding has more than one statement! only single statements can be transpiled.',
                  );
                  import('../../store').then(storeMod => {
                    const store = storeMod.default;
                    store.dispatch({
                      type: 'EDITOR_RECORD_BINDING_ERROR',
                      payload: {
                        binding: 'no code',
                        error: {
                          transpiledFn: 'could not generate function',
                          error:
                            'The expression for binding has more than one statement! only single statements can be transpiled.',
                        },
                      },
                    });
                  });
                }
              },
            },
          },
        };
      },
    ],
  });
  return output.ast;
}

export function transpileJsBinding(code: string, nsPrefix: string, globalScopeIdentifiers: string[], selector: string[]): string {
  // Note: The regex is being recreated here everytime because `matchAll`
  // seems to maintain some state in the regex. This is not a huge problem
  // because this code is only supposed to run in the editor when someone is
  // adding a new binding.
  const reg = new RegExp(REGEX_MATCH_TRIPLE_BRACE);
  const isJsBinding = reg.test(code);
  let transpiled = code;
  if (isJsBinding) {
    const reg = new RegExp(REGEX_MATCH_TRIPLE_BRACE);
    const interpolations = Array.from(code.matchAll(reg));

    let shouldCreateNestedFns = interpolations.length > 1;

    let stringParts = [];
    let codeOffset = 0;
    for (let interpolation of interpolations) {
      if (interpolation.index !== undefined) {
        const stringPart = code.substr(codeOffset, interpolation.index - codeOffset);
        stringParts.push(stringPart);
        codeOffset = interpolation.index + interpolation[0].length;
      } else {
        logger.error('Invalid match: ', interpolation);
      }
    }
    const stringLastPart = code.substr(codeOffset);
    stringParts.push(stringLastPart);

    for (let i = 0; i < stringParts.length && !shouldCreateNestedFns; ++i) {
      shouldCreateNestedFns = stringParts[i].trim() !== '';
    }

    let lastConsumedStringPart = -1;
    if (shouldCreateNestedFns) {
      let interpolatingFns = [];
      let exprParts: Array<ExpressionPart> = [];
      let fnIndex = 0;
      for (let index = 0; index < interpolations.length; ++index) {
        const interpolation = interpolations[index];
        let firstCaptureGroup = interpolation[1];

        // An object expression of the form { "a": 1 } is invalid javascript for babel because {} are also used to demarcate scopes in javascript
        const isSyntacticallyInvalidObjectExpression =
          firstCaptureGroup.trim().startsWith('{') && firstCaptureGroup.endsWith('}');
        if (isSyntacticallyInvalidObjectExpression) {
          firstCaptureGroup = `(${firstCaptureGroup})`;
        }

        try {
          const plugins: TransformOptions['plugins'] = [
            [transformerPlugin, {globalScopeIdentifiers, selector}]
          ];

          if (nsPrefix) {
            plugins.push(
              [ namespacePlugin, { ns: nsPrefix } ]
            );
          }
          const transpiledAst = transformSync(firstCaptureGroup, {
            filename: 'interp' + fnIndex,
            ast: true,
            plugins,
          }).ast;

          interpolatingFns.push(transpiledAst);
          lastConsumedStringPart = index;
          exprParts.push({type: 'literal', value: stringParts[index]});
          exprParts.push({type: 'call', value: 'interp' + fnIndex++});
        } catch (err) {
          logger.error('Failed for: ', code, err);
          import('../../store').then(storeMod => {
            const store = storeMod.default;
            store.dispatch({
              type: 'EDITOR_RECORD_BINDING_ERROR',
              payload: {
                binding: code,
                error: {
                  transpiledFn: 'could not generate function',
                  error: err.toString(),
                },
              },
            });
          });
          return `function() { return; }`; // `function() { return "Invalid binding! Babel threw error"; }`;
        }
      }

      if (lastConsumedStringPart < stringParts.length - 1) {
        lastConsumedStringPart++;
        exprParts.push({type: 'literal', value: stringParts[lastConsumedStringPart]});
        if (lastConsumedStringPart < stringParts.length - 1) {
          logger.error('Some parts of the enclosing string were not included in transpiled function!');
        }
      }
      const finalAst = mergeMultiBindingAsts(interpolatingFns, exprParts);
      transpiled = transformFromAst(finalAst, null, {}).code;
      /*
      let stringPartLiterals = createLiteralAsts(stringParts);
      let functionCalls = createFnCallAsts(interpolatingFns.length);
      let enclosingAst = createFuncDeclAst();
      for (let fn of interpolatingFns) {
        enclosingAst.pushContainer("body", fn);
      }
      let returnExpression = returnConcatenation(stringPartLiterals, functionCalls);
      enclosingAst.pushContainer("body", resultExpression);
      transpiled = transformFromAstSync(enclosingAst, null, {}).code;
      */
    } else {
      try {
        const match = interpolations[0];
        let firstCaptureGroup = match[1];

        // An object expression of the form { "a": 1 } is invalid javascript for babel because {} are also used to demarcate scopes in javascript
        const isSyntacticallyInvalidObjectExpression =
          firstCaptureGroup.trim().startsWith('{') && firstCaptureGroup.endsWith('}');
        if (isSyntacticallyInvalidObjectExpression) {
          firstCaptureGroup = `(${firstCaptureGroup})`;
        }

        const plugins: TransformOptions['plugins'] = [
          [transformerPlugin, {globalScopeIdentifiers, selector}]
        ];

        if (nsPrefix) {
          plugins.push(
            [ namespacePlugin, { ns: nsPrefix } ]
          );
        }
        const transpiledCode = transformSync(firstCaptureGroup, {
          plugins,
        }).code;

        if (typeof transpiledCode === 'string') {
          transpiled = transpiledCode;
        } else {
          transpiled = `function() { return; }`; // `function() { return "Invalid binding!"; }`;
          import('../../store').then(storeMod => {
            const store = storeMod.default;
            store.dispatch({
              type: 'EDITOR_RECORD_BINDING_ERROR',
              payload: {
                binding: code,
                error: {
                  transpiledFn: 'could not generate function',
                  error: 'Unknown error happened',
                },
              },
            });
          });
        }
      } catch (err) {
        logger.error('Failed for: ', code, err);
        transpiled = `function() { return; }`; // `function() { return "Invalid binding!"; }`;
        import('../../store').then(storeMod => {
          const store = storeMod.default;
          store.dispatch({
            type: 'EDITOR_RECORD_BINDING_ERROR',
            payload: {
              binding: code,
              error: {
                transpiledFn: 'could not generate function',
                error: err.toString(),
              },
            },
          });
        });
      }
    }
  } else {
    logger.error('Failed to transpile because there is no js binding');
    import('../../store').then(storeMod => {
      const store = storeMod.default;
      store.dispatch({
        type: 'EDITOR_RECORD_BINDING_ERROR',
        payload: {
          binding: code,
          error: {
            transpiledFn: 'could not generate function',
            error: 'no binding found',
          },
        },
      });
    });
  }

  return transpiled;
}

export function UNSAFE_renameVariables(code: string, varName: any, newName: any) {
  return code.replaceAll(varName, newName);
}
