import {parse} from 'acorn';
import _, {isEqual} from 'lodash';
import moment from 'moment';
import {resolvePluginPropertySettings} from '../../plugins/plugin';
import getModelForNamespace from '../appModel/getNamespaceModel';
import {PluginNamespace, PluginType, AppModelType} from '../datatypes/types';
import {getPluginSelector, getPluginSelectorFromSelector} from '../datatypes/utils';
import {IDependencyGraph, Selector, strsel} from '../DependencyGraph/types';
import {expressions} from './JSASTWalker';
import {HAS_TEMPLATE_REGEX} from './JSBindingTypes';
import {isJSBinding, isSingleObjectString} from './JSBindingUtils';
import Immutable from 'immutable';

type GetFinalValueType = (model: any, value: any) => any;

export function evaluateJSBinding(jsSnippet: string, context: Record<string, unknown>): Promise<unknown> {
  const js = jsSnippet.replace('{{', '').replace('}}', '');
  const evalScript = `function evalFunction(){
    const result = ${js};
    return result;
  }
  evalFunction();
  `;

  const JS_EVAL_CONTEXT: Record<string, unknown> = {};
  Object.keys(context).forEach(key => {
    JS_EVAL_CONTEXT[key] = context[key];
  });

  Object.keys(JS_EVAL_CONTEXT).forEach(key => {
    self[key] = JS_EVAL_CONTEXT[key];
  });

  const evalResult = eval(evalScript);

  Object.keys(JS_EVAL_CONTEXT).forEach(key => {
    delete self[key];
  });

  return evalResult;
}

function renderListViewPlugin(
  selector: Selector,
  modelSelector: Selector,
  instances: any,
  pluginId: string,
  pluginModelSelector: Selector,
  pluginType: PluginType,
  pageKey: string,
  pageId: string,
  rawJsBindingString: unknown,
  model: AppModelType,
  preNamespaceModel: JSModel, //_.PartialDeep<Object>,
  getFinalValue: GetFinalValueType,
  contextId: number | undefined = undefined,
  namespace?: PluginNamespace | undefined,
  fn,
) {
  let listViewParentId = null;
  if (selector.length) {
    listViewParentId = model.dependencyGraph.getAncestorListViewSelector(strsel(selector))!;
  }

  const ancestorListViewSel = getPluginSelector(pageId, listViewParentId);
  // const ancestorListViewNamespace = _model().dependencyGraph.lookupNamespace(ancestorListViewSel);
  // const oldInstances = model.getModelValue([pageKey].concat(ancestorListViewSel.slice(1)).concat(['data']))?.length ?? 0;
  // TODO(gaurav) this is not working
  // const oldInstances2 = _.get(model.jsModel, ancestorListViewSel.concat(['data']))?.length ?? 0;

  var isChangedByInstance = true;

  let newModel: AppModelType = model;
  // for (let i = isChangedByInstance ? 0 : oldInstances; i < instances; i++) {
  for (let i = 0; i < (instances || 1); i++) {
    try {
      let newValue = null;
      let evalValue = rawJsBindingString;
      let jsBindingCheck = false;
      if (i > 0) {
        const instanceModel = _.get(model.jsModel, pluginModelSelector)?.[i];
        if (!instanceModel) {
          // let pluginModelCache = model.dependencyGraph.depGraph.getNodeData(
          //   strsel([pageId].concat(pluginModelSelector.slice(1))),
          // )?._cachedModel;
          // let pluginModelValue = pluginModelCache?.get(0);
          // TODO(samyam) Figure out a way to extract cached model from Appconfig.
          let pluginModelValue = model.getModelValue(pluginModelSelector.concat([0]));
          if (pluginModelValue) {
            if (contextId)
              newModel.evalContextStack
                .getEvaluationContext(contextId)
                .pushEvalResult({selector: pluginModelSelector.concat([i]), value: pluginModelValue});
            if (Immutable.isImmutable(pluginModelValue)) {
              pluginModelValue = pluginModelValue.toJS();
            }
            _.set(newModel.jsModel, pluginModelSelector.concat([i]), pluginModelValue);
          }
        }
      }
      // enforce monomorphic calls to isJSBinding to prevent deoptimization
      if (typeof rawJsBindingString === 'string' && isJSBinding(rawJsBindingString)) {
        jsBindingCheck = isJSBinding(rawJsBindingString);
        /*
        let modelJS =
          pluginType !== 'ModuleProperty' &&
          isEqual(ancestorListViewNamespace?.getNamespace(), namespace?.getNamespace())
            ? getModelForNamespace(preNamespaceModel, namespace, pluginType)
            : getModelForNamespace(preNamespaceModel, namespace, pluginType, i);
        modelJS.i = i;
        */
        // TODO(gaurav): Update jsmodel for index maybe?
        preNamespaceModel.i = i;
        evalValue = evaluateJSTemplate(rawJsBindingString, preNamespaceModel, fn);
      }

      // const pluginValues = model.getModelValue(pluginSelector)?.get(i);
      const pluginValues = _.get(model.jsModel, pluginModelSelector)?.[i];
      newValue = getFinalValue(pluginValues, evalValue);
      // logger.info(selector.join('.'), 'JSBinding: ', rawJsBindingString, ' value: ', newValue, modelJS);
      const propSelector = pluginModelSelector.concat([i]).concat(selector.slice(pluginModelSelector.length));
      const oldValue = _.get(newModel.jsModel, propSelector);
      if (oldValue !== newValue) {
        if (contextId)
          newModel.evalContextStack
            .getEvaluationContext(contextId)
            .pushEvalResult({selector: propSelector, value: newValue});
        if (Immutable.isImmutable(newValue)) {
          newValue = newValue.toJS();
        }
        _.set(newModel.jsModel, propSelector, newValue);
      }
      // newModel = newModel.setModelValue(propSelector, newValue);
    } catch (e) {
      const propSelector = pluginModelSelector.concat([i]).concat(selector.slice(pluginModelSelector.length));
      if (contextId)
        newModel.evalContextStack.getEvaluationContext(contextId).pushEvalResult({selector: propSelector, value: ''});
      _.set(newModel.jsModel, pluginModelSelector.concat([i]).concat(selector.slice(pluginModelSelector.length)), '');
      // newModel = newModel.setModelValue(pluginSelector.concat([i]).concat(selector.slice(pluginSelector.length)), '');
    }
  }
  return newModel;
}

export type JSModel = {
  $global: any;
  unresolved: boolean;
  $context: any;
  hasCurrentPage: boolean;
  currentPage: any;
  hasIndex: boolean;
  i: number;
};

// This is a performance critical function so we avoid creating any new objects that will need to get
// garbage collected here.
export function getModelJSForPlugin(
  model: AppModelType.jsModel,
  pluginSelector: Selector,
  preAllocatedModel: JSModel,
): JSModel {
  // Will need to access page entities while evaluating analytics parameters.
  const pageKey = pluginSelector[0];
  const pageProperty = pluginSelector[1];
  preAllocatedModel.unresolved = false;
  preAllocatedModel.hasIndex = false;
  preAllocatedModel.$global = model;

  if (pluginSelector.length > 2 && (pageProperty == 'plugins' || pageProperty == 'analytics')) {
    // _.get will return undefined if the path given to it doesn't exist in the object so we add
    // the equivalent behaviour using a trycatch
    // const pluginModel = _.get(model.jsModel, pluginSelector.slice(0, 2));

    let pageJsModel;
    try {
      pageJsModel = model[pageKey];
    } catch (err) {
      preAllocatedModel.unresolved = true;
      // logger.error('Undefined jsModel: ', pageKey, pageProperty);
    }

    let pluginJsModel;
    try {
      pluginJsModel = pageJsModel[pageProperty];
    } catch (err) {
      preAllocatedModel.unresolved = true;
      // logger.error('Undefined jsModel: ', pageKey, pageProperty);
    }

    if (!preAllocatedModel.unresolved) {
      preAllocatedModel.hasCurrentPage = true;
      preAllocatedModel.currentPage = pageJsModel;
      preAllocatedModel.$context = pluginJsModel;
    }
  } else {
    preAllocatedModel.hasCurrentPage = false;
    preAllocatedModel.$context = model;
  }

  return preAllocatedModel;
}

function pluginInListViewUtil(
  pageKey: string,
  pageId: string,
  pluginId: string,
  dependencyGraph: IDependencyGraph,
  jsModel: AppModelType.jsModel,
): {inListView: boolean; instances: number} {
  const lvParentId = dependencyGraph?.getAncestorListViewSelector(`${pageId}.plugins.${pluginId}`);

  if (lvParentId) {
    try {
      const instances = _.get(jsModel, [pageKey, 'plugins', lvParentId])?.instances || 0;
      return {inListView: true, instances: Number(instances)};
    } catch (err) {
      return {inListView: true, instances: 0};
    }
  }
  return {inListView: false, instances: 0};
}

export function evaluateJSBindingString(
  selector: Selector,
  modelSelector: Selector,
  rawJsBindingString: unknown,
  dependencyGraph: IDependencyGraph,
  model: AppModelType,
  jsModelScratchMemory: JSModel,
  contextId: number | undefined = undefined,
  namespace?: PluginNamespace | undefined,
  fn?: Function,
): AppModelType {
  if (rawJsBindingString === undefined) {
    return model;
  }
  const pluginSelector = getPluginSelectorFromSelector(selector);
  const pluginModelSelector = getPluginSelectorFromSelector(modelSelector);
  const pageKey = pluginModelSelector.length > 1 ? pluginModelSelector[0] : '';
  const pluginId = pluginModelSelector[pluginModelSelector.length - 1];
  const pageId = pluginSelector.length > 1 ? pluginSelector[0] : '';
  // const pluginValues = _model().hasPlugin(pluginId)
  //   ? _model().getPlugin(pluginId)
  //   : {};
  const pluginType = dependencyGraph.lookupPluginType(pageId ? [pageId, 'plugins', pluginId] : pluginSelector);

  let getFinalValue = (model: unknown, value: unknown): unknown => value;
  let dirtyNodes: unknown = [];

  const propertyConfig =
    resolvePluginPropertySettings(pluginType) && selector.length - 1 === pluginSelector?.length
      ? resolvePluginPropertySettings(pluginType)[selector.slice(-1)]
      : null;
  if (propertyConfig) {
    if (propertyConfig.getValue) {
      getFinalValue = (model: unknown, value: unknown): unknown => {
        if (__DEV__ && model) {
          const readOnlyHandler = {
            get(target, prop) {
              // console.log("Getter triggered: ", prop);
              const res = target[prop];
              if (res && typeof res === "object") {
                return new Proxy(res, readOnlyHandler);
              } else {
                return res;
              }
            },
            set(target, prop, value) {
              // console.log("Setter triggered: ", prop);
              logger.error("Setter triggered during jsbindingevaluation for ", prop, target);
              throw new Error("A plugin may not mutate the model in a property resolver.");
            }
          };
          model = new Proxy(model, readOnlyHandler);
        }

        try {
          return propertyConfig.getValue(model, value, selector);
        } catch (err) {
          logger.error('Problem when trying to get value', err);
          return;
        }
      };
    }

    const updatesPropsAsync = propertyConfig.updatesPropsAsync;
    if (updatesPropsAsync && updatesPropsAsync.length > 0) {
      dirtyNodes = updatesPropsAsync.map(prop => `${selector[0]}.${prop}`);
    }
  }
  // const modelJS = current(_model().getValues());

  const {inListView, instances} = pluginInListViewUtil(pageKey, pageId, pluginId, dependencyGraph, model.jsModel); //  model.pluginInListView(pageKey, pluginId);

  if (inListView) {
    // logger.time(`preNamespaceModel ${pluginSelector}`);
    const preNamespaceModel = getModelJSForPlugin(model.jsModel, pluginModelSelector, jsModelScratchMemory);
    // logger.timeEnd(`preNamespaceModel ${pluginSelector}`);
    let newModel = renderListViewPlugin(
      selector,
      modelSelector,
      instances,
      pluginId,
      pluginModelSelector,
      pluginType,
      pageKey,
      pageId,
      rawJsBindingString,
      model,
      preNamespaceModel,
      getFinalValue,
      contextId,
      namespace,
      fn,
    );
    return newModel;
  } else {
    const pluginValues = _.get(model.jsModel, pluginModelSelector || '') || {};

    try {
      const oldValue = _.get(model.jsModel, modelSelector);
      let newValue = rawJsBindingString;
      // enforce monomorphic calls to isJSBinding to prevent deoptimization
      if (typeof rawJsBindingString === 'string' && isJSBinding(rawJsBindingString)) {
        // logger.time(`preNamespaceModel ${pluginSelector}`);
        const preNamespaceModel: JSModel = getModelJSForPlugin(
          model.jsModel,
          pluginModelSelector,
          jsModelScratchMemory,
        );
        // logger.timeEnd(`preNamespaceModel ${pluginSelector}`);
        // const modelJS = getModelForNamespace(preNamespaceModel, namespace, pluginType);
        newValue = evaluateJSTemplate(rawJsBindingString, preNamespaceModel, fn);
      }
      newValue = getFinalValue(pluginValues, newValue);
      let newModel: AppModelType = model;
      if (model.jsModel == null) {
        newModel = model.set('jsModel', {});
      }

      if (newValue != oldValue && contextId) {
        newModel.evalContextStack
          .getEvaluationContext(contextId)
          .pushEvalResult({selector: modelSelector, value: newValue});
        if (Immutable.isImmutable(newValue)) {
          newValue = newValue.toJS();
        }
        _.set(newModel.jsModel, modelSelector, newValue);
      }
      // newModel = newModel.setModelValue(selector, newValue);
      return newModel;
    } catch (e) {
      logger.error(e);
      let newValue = null;
      newValue = getFinalValue(pluginValues, '');
      let newModel = model;
      if (contextId)
        newModel.evalContextStack.getEvaluationContext(contextId).pushEvalResult({selector: modelSelector, value: ''});
      _.set(newModel.jsModel, modelSelector, newValue);
      // newModel = newModel.setModelValue(selector, newValue);
      return newModel;
    }
  }
}

let storeRef = Promise.resolve();
if (process.env.IS_WEB_ENV) {
  storeRef = import('../../store').then(storeMod => {
    return storeMod.default;
  });
}

function evaluateJSTemplate(code: string, scope: JSModel, fn?: Function) {
  let returnedValue;
  if (fn) {
    try {
      // TODO(gaurav) check that scope's resolved flags are true
      returnedValue = fn(scope?.$context, scope?.$global, scope?.i, scope?.currentPage, _, moment);
    } catch (err) {
      if (process.env.IS_WEB_ENV) {
        if (global.ENABLE_BINDING_ERROR_LOGGING) {
          console.error("Binding evaluation error: ", err);
        }
      }
    }
  } else {
    logger.error(
      'No compiled function was found for code. This fallback will no longer work properly. Fix this: ' + code,
    );
    const compiled = new Function('scope', `with(scope||{}){ return (${code}) }`);
    returnedValue = compiled(scope);
  }

  if (returnedValue instanceof Function) {
    return returnedValue();
  } else {
    return returnedValue;
  }
}

export function getJSBindingVariables(dynamicJSString: string, ignoredGlobals: Set<string> = new Set()): Selector[] {
  const depVars: Selector[] = [];
  dynamicJSString.replace(HAS_TEMPLATE_REGEX, (_, js) => {
    let ast = null;
    try {
      ast = parse(js, {ecmaVersion: 11});
      const parsedExpressions = expressions(ast);
      parsedExpressions.forEach(v => depVars.push(v));
    } catch (e) {
      // Swallow error ??
    }

    return '';
  });
  return depVars;
}
