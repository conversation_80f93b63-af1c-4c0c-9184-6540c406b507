import {parse} from 'acorn';
// import {DepGraph} from 'dependency-graph';
import {DepGraph} from './DepGraph';
import * as Immutable from 'immutable';
import _, {isEqual} from 'lodash';
import {SHARED_GLOBAL_MODEL_PROPERTIES} from '../../constants/modelConstants';
import {MaskResolver, resolvePluginPropertySettings, EventTriggerIdentifier} from '../../plugins/plugin';
import {PageEventsList} from '../datatypes/PageTypes';
import {
  EventHandlerConfig,
  PluginConfig,
  EventHandlerShape,
  PluginNamespace,
  PluginNamespaceImpl,
  NavigationConfig,
  PageConfig,
} from '../datatypes/types';
import {
  addNamespace,
  getDependencyGraphSelector,
  getPluginIdIndexInSelector,
  isPluginSelector,
} from '../datatypes/utils';
import {variableRefs} from '../JSBinding/JSASTWalker';
import {getJSBindingVariables} from '../JSBinding/JSBindingEvaluator';
import {isJSBinding, JSBINDING_REPLACE_REGEX, transpileJsBinding} from '../JSBinding/JSBindingUtils';
import {
  strsel,
  objsel,
  Selector,
  NodeData,
  IDependencyGraph,
  DependencyGraphInterface,
  Binding,
  SerializedBindings,
} from './types';

export class DependencyGraph implements IDependencyGraph {
  depGraph: DepGraph<NodeData> & DependencyGraphInterface<NodeData>;
  topoCache: Selector[] | null;
  evalOrderCache: Selector[] | null;
  _overallOrderCache: string[] | null;
  parentContainers: Map<string, string>;
  private compiledBindingFns: Map<string, Binding>;
  private globalScopeIdentifiers: string[] = [];

  constructor(globalScopeIdentifiers: string[]) {
    this.depGraph = new DepGraph<NodeData>() as DepGraph<NodeData> & DependencyGraphInterface<NodeData>;
    this._overallOrderCache = null;
    this.topoCache = null;
    this.evalOrderCache = null;
    this.parentContainers = new Map();
    this.compiledBindingFns = new Map();
    this.globalScopeIdentifiers = globalScopeIdentifiers;
    window.dependencyGraph = this;
  }

  hydrateCompiledBindings(code: {raw: string[]; transpiled: string[]}) {
    // The reason for the mysterious if check in this function is as follows:
    //
    // Check if the cache rehydrated from compiled function has a smaller size than
    // the number of incoming cache value. When the app starts there are 5 or 6 bindings
    // added to compiledBindingFns. So its size is not 0 at startup and it can change
    // depending on the template. But this check seems to be good enough to determine if
    // hydrateCompiledBindings has been called once. The reason for this check is to
    // prevent recompilation of the code cache on every navigation.
    if (this.compiledBindingFns.size < code.raw.length) {
      const compiled = new Function('return [' + code.transpiled.join(',') + ']')();
      for (let i = 0; i < code.raw.length; ++i) {
        if (!this.compiledBindingFns.has(code.raw[i])) {
          this.compiledBindingFns.set(code.raw[i], {transpiled: code.transpiled[i], compiled: compiled[i]});
        }
      }
    }
  }

  getTranspiledBindings() {
    const serialized: SerializedBindings = {raw: [], transpiled: []};
    for (const [key, value] of this.compiledBindingFns.entries()) {
      serialized.raw.push(key);
      serialized.transpiled.push(value.transpiled);
    }
    return serialized;
  }

  prepareAndMapBinding(code: string, namespace?: PluginNamespace, selector: Selector): Binding | undefined {
    let nsPrefix = '';
    if (namespace) {
      const ns = namespace.getNamespace();
      if (ns.length) {
        nsPrefix = ns.join('::');
      }
    }

    let codeKey = code;
    if (nsPrefix) {
      codeKey = nsPrefix + '::' + codeKey;
    }

    let binding = this.compiledBindingFns.get(codeKey);
    // if (binding) {
    //   return binding;
    // } else {
    // {
    try {
      let globalScopeIdentifiers = this.globalScopeIdentifiers;
      if (nsPrefix) {
        globalScopeIdentifiers = [];
      }
      const transpiledCode = transpileJsBinding(code, nsPrefix, globalScopeIdentifiers, selector);
      const compiledCode = new Function('return ' + transpiledCode)();
      binding = {
        transpiled: transpiledCode,
        compiled: compiledCode,
      };
      this.compiledBindingFns.set(codeKey, binding);
    } catch (err) {
      logger.error('Could not compile expression', err);
    }
    // }
    return binding;
  }

  private clearTopologicalOrderCache() {
    this._overallOrderCache = null;
    this.topoCache = null;
    this.evalOrderCache = null;
  }
  private hasDynamicString(id: string) {
    return this.depGraph.getNodeData(id).dynamicString !== undefined;
  }
  private hasJSBinding(id: string) {
    return !!this.depGraph.getNodeData(id)?.isJSBinding;
  }
  private hasForceEval(id: string) {
    return !!this.depGraph.getNodeData(id)?.forceEvaluation;
  }
  private hasPluginType(id: string): string {
    return this.depGraph.getNodeData(id).pluginType;
  }
  private addNode(selector: Selector, namespace?: PluginNamespace) {
    const id = strsel(selector);
    if (!this.depGraph.hasNode(id)) {
      this.clearTopologicalOrderCache();
      let data: NodeData = {selector};
      if (namespace) data = this.addNamespaceToData(data, namespace);
      this.depGraph.addNode(id, data);
    }
  }

  private addNamespaceToData(data: NodeData, namespace: PluginNamespace): NodeData {
    const nsString = strsel(namespace.getNamespace());
    data.namespace = nsString;
    if (namespace.getPluginId()) data.originalPluginId = namespace.getPluginId();
    return data;
  }

  private mergeNodeData(selector: Selector, update: unknown): void {
    this.clearTopologicalOrderCache();
    const id = strsel(selector);
    this.depGraph.setNodeData(id, {
      ...this.depGraph.getNodeData(id),
      ...(update as Record<string, unknown>),
    });
  }

  private updateNode(selector: Selector, dynamicString: unknown, namespace?: PluginNamespace) {
    const id = strsel(selector);
    let data: NodeData = {
      selector,
      dynamicString,
      isJSBinding: isJSBinding(dynamicString),
    };

    if (data.isJSBinding) {
      this.prepareAndMapBinding(dynamicString, namespace, selector);
    }

    if (namespace) data = this.addNamespaceToData(data, namespace);

    if (!this.depGraph.hasNode(id)) {
      this.clearTopologicalOrderCache();

      this.depGraph.addNode(id, data);
    } else {
      this.mergeNodeData(selector, data);
    }
  }

  private updateChild(
    childSelector: Selector,
    parentSelectors: Selector[],
    dynamicString: unknown,
    namespace?: PluginNamespace,
  ) {
    this.updateNode(childSelector, dynamicString, namespace);
    const id = strsel(childSelector);
    const currentParentIds = this.depGraph.dependenciesOf(id);
    const pluginSelector = this.getPluginSelector(childSelector);

    // Clear out the current dependencies
    this.clearTopologicalOrderCache();
    currentParentIds.forEach(pid => this.depGraph.removeDependency(id, pid));

    // Add the new dependencies
    parentSelectors.forEach(s => this.addDependency(childSelector, s));

    if (namespace !== undefined) {
      const pluginNSSelStr = strsel(pluginSelector.concat(['namespace']));
      if (this.depGraph.nodes.get(pluginNSSelStr) && strsel(childSelector) !== pluginNSSelStr) {
        this.addDependency(childSelector, pluginSelector.concat(['namespace']));
      }
    }

    if (this.parentContainers.get(strsel(pluginSelector))) {
      const container = this.parentContainers.get(strsel(pluginSelector));
      const pagePrefixSel = this.getPagePrefixSelector(childSelector);

      const listviewAncestorId = this.getAncestorListViewSelector(strsel(childSelector));
      if (listviewAncestorId) {
        this.addDependency(childSelector, pagePrefixSel.concat([listviewAncestorId, 'instances']));
      }
      if (this.depGraph.nodes.get(strsel(pagePrefixSel.concat([container])))) {
        this.addDependency(pagePrefixSel.concat([container]), childSelector);
      }
    }

    // Test if cyclic dependency found
    try {
      this.depGraph.dependenciesOf(id);
    } catch (err) {
      logger.error('Cyclic dependency found', err);
      parentSelectors.forEach(s => this.depGraph.removeDependency(id, strsel(s)));
    }
  }

  private addDependency(from: Selector, to: Selector, check = false) {
    if (!from || !to) {
      return;
    }
    this.clearTopologicalOrderCache();
    this.depGraph.addDependency(strsel(from), strsel(to));
    if (check) {
      try {
        this.depGraph.dependenciesOf(strsel(from));
      } catch (err) {
        logger.error('Cyclic dependency found', err);
        // message.error(err.message)
        this.depGraph.removeDependency(strsel(from), strsel(to));
      }
    }
  }

  private copyPlugin(pageKey: string, pluginId: string, newId: string) {
    for (let id of this.depGraph.nodes.keys()) {
      const {selector, ...properties} = this.depGraph.nodes.get(id);
      if ((selector[0] === pageKey && selector[2] == pluginId) || (!pageKey && selector[0] == pluginId)) {
        const newSelector = (pageKey ? [pageKey, 'plugins', newId] : [newId]).concat(
          selector.slice(selector.indexOf(pluginId) + 1),
        );

        const newKey = strsel(newSelector);
        this.depGraph.nodes.set(newKey, {
          selector: newSelector,
          ...properties,
          ...(newSelector[newSelector.length - 1] === 'id' ? {dynamicString: newId} : {}),
          ...(newSelector[newSelector.length - 1] === newId ? {originalPluginId: newId} : {}),
        });
        this.clearTopologicalOrderCache();
        this.depGraph.incomingEdges.set(newKey, this.depGraph.incomingEdges.get(id).slice(0));
        this.depGraph.outgoingEdges.set(newKey, this.depGraph.outgoingEdges.get(id).slice(0));
        for (let inc of this.depGraph.incomingEdges.get(newKey)) {
          this.depGraph.outgoingEdges.get(inc).push(newKey);
        }
        for (let out of this.depGraph.outgoingEdges.get(newKey)) {
          this.depGraph.incomingEdges.get(out).push(newKey);
        }
      }
    }
  }

  hasNode(selector: Selector): boolean {
    return this.depGraph.hasNode(strsel(selector));
  }

  lookupDynamicString(selector: Selector): string | unknown {
    const id = strsel(selector);
    return this.depGraph.getNodeData(id).dynamicString;
  }

  lookupBinding(selector: Selector): {dynamicString: string | unknown; binding: Binding} {
    const id = strsel(selector);
    const node = this.depGraph.getNodeData(id);
    let nsPrefix = node.namespace;
    /*
    if (node.namespace) {
      const namespace = node.namespace.getNamespace().join('::');
      nsPrefix = namespace;
    }
    */

    let lookupKey = node.dynamicString;
    if (nsPrefix) {
      lookupKey = nsPrefix + '::' + lookupKey;
    }

    const binding = this.compiledBindingFns.get(lookupKey);
    return {
      dynamicString: node.dynamicString,
      binding: binding || {compiled: undefined, transpiled: ''},
    };
  }

  isJSBindingString(selector: Selector): boolean {
    const id = strsel(selector);
    return !!this.depGraph.getNodeData(id)?.isJSBinding;
  }

  lookupPluginType(selector: Selector): string {
    const id = strsel(selector);
    return this.depGraph.getNodeData(id).pluginType;
  }

  addEventNodes(events: Immutable.List<EventHandlerConfig>, selector: string[]): void {
    events.map((eventConfig: EventHandlerConfig) => {
      const {label} = eventConfig;
      this.addNode([...selector, label]);
    });
  }
  /**
   * In order to make dep graph works (ie find cyclic dependecy) and resolve the cyclic dependecy correctly following should be true.
   * we have to be very careful with trigger and event names
   * ie. query1.trigger(trigger in query1) -> query2.trigger(method in event1 and pluginId: query2)
   * query2.trigger(trigger in query2) -> query1.trigger (method in event2 and pluginId: query1)
   * @param events
   * @param pageKey
   * @param triggerSelector
   */
  updateEventDependency(events: Immutable.List<EventHandlerConfig>, pageKey: string, triggerSelector: string[]) {
    events.map(eventConfig => {
      // const {pluginId, isGlobalPlugin, label, method} = eventConfig;
      const pluginId = eventConfig.get('pluginId');
      const label = eventConfig.get('label');
      const method = eventConfig.get('method');
      // this.depGraph.dependantsOf()
      let eventSelector: string[] = eventConfig.get('isGlobalPlugin')
        ? [pluginId, method]
        : [pageKey, 'plugins', pluginId, method];

      this.addNode(eventSelector);
      if (
        triggerSelector.indexOf(label) > -1 &&
        eventSelector.length > 0 &&
        this.depGraph.hasNode(strsel(eventSelector))
      ) {
        this.addDependency(eventSelector, triggerSelector, true);
      }
    });
  }

  removeEventDependency(pageKey: string, pluginId: string, eventConfig: EventHandlerShape): void {
    const {isGlobalPlugin, method, label} = eventConfig;

    const pluginSelector = pageKey ? [pageKey, 'plugins', pluginId] : [pluginId];
    const sourceSelector = pluginSelector.concat([label]);
    const eventPluginId = eventConfig.pluginId;
    const eventSelector = isGlobalPlugin ? [eventPluginId, method] : [pageKey, 'plugins', eventPluginId, method];

    if (this.depGraph.hasNode(strsel(sourceSelector)) && this.depGraph.hasNode(strsel(eventSelector))) {
      this.clearTopologicalOrderCache();
      this.depGraph.removeDependency(strsel(eventSelector), strsel(sourceSelector));
    }
  }

  removeEvents(pageKey: string, pluginId: string): void {
    for (let id of this.depGraph.nodes.keys()) {
      const selector = this.depGraph.nodes.get(id).selector;
      if (
        (selector[0] === pageKey && selector[2] == pluginId && selector[3] == 'events') ||
        (!pageKey && selector[0] == pluginId && selector[1] == 'events')
      ) {
        this.clearTopologicalOrderCache();
        this.depGraph.removeNode(id);
      }
    }
  }

  addPlugin(pageKey: string, plugin: PluginConfig, data: unknown): void {
    const selector = pageKey ? [pageKey, 'plugins', plugin.get('id')] : objsel(plugin.get('id'));

    const namespace = plugin.namespace;
    this.addObject(selector, data, namespace);

    //Special case: to add event node in dep graph
    if (plugin.config.get('events') && plugin.config.get('events').size > 0) {
      this.addEventNodes(plugin.config.get('events'), selector);
    }
  }

  updatePlugin(pageKey: string, plugin: PluginConfig, data: unknown, namespace?: PluginNamespaceImpl): void {
    const selector = pageKey ? [pageKey, 'plugins', plugin.get('id')] : objsel(plugin.get('id'));

    const containers = new Set<string>();

    if (plugin.layout) {
      if (plugin.layout.container) {
        containers.add(plugin.layout.container);
      }
    }
    const modelProps = MaskResolver(plugin.subtype)(data) || data;

    const pluginPropertySettings = resolvePluginPropertySettings(plugin.subtype);

    const modelPropsNoEvents = modelProps?.delete('events');
    this.updateObject(selector, modelPropsNoEvents, containers, namespace);

    const events = modelProps.get('events', Immutable.List<EventHandlerConfig>());
    this.updateObject(selector.concat('events'), events, containers, namespace);

    this.mergeNodeData(selector, {
      pluginType: plugin.subtype,
    });

    if (pluginPropertySettings) {
      const keys = Object.keys(pluginPropertySettings);
      for (const key of keys) {
        const keySelector = selector.concat([key]);
        const propertyConfig = pluginPropertySettings[key];
        const eventType = propertyConfig.type || undefined;
        const getValue = propertyConfig.getValue;
        if (eventType === EventTriggerIdentifier) {
          const pluginEvents = events.filter(event => event.get('label') === key);
          if (events && events.size > 0) {
            this.updateEventDependency(pluginEvents, pageKey, keySelector);
          }
          continue;
        }
        if (getValue) {
          this.mergeNodeData(keySelector, {
            hasGetValue: true,
          });
        }
        if (propertyConfig.forceEvaluation || propertyConfig.type === 'ACTION') {
          this.mergeNodeData(keySelector, {
            forceEvaluation: true,
          });
        }

        const updatesProps = propertyConfig.updatesProps || [];
        for (const property of updatesProps) {
          this.addDependency(selector.concat([property]), keySelector, true);
          this.mergeNodeData(selector.concat([property]), {
            forceEvaluation: true,
          });
        }
        this.mergeNodeData(keySelector, {
          updatesProps,
        });

        const updatesPropsAsync = propertyConfig.updatesPropsAsync || [];

        if (updatesPropsAsync && updatesPropsAsync.length > 0) {
          for (const property of updatesPropsAsync) {
            this.addDependency(selector.concat([property]), keySelector, true);
          }
          this.mergeNodeData(keySelector, {
            updatesPropsAsync,
          });
        }
      }
    }
  }

  getPluginSelector(selector: Selector): Selector {
    return selector.indexOf('plugins') > 0 ? selector.slice(0, selector.indexOf('plugins') + 2) : [selector[0]];
  }

  getPagePrefixSelector(selector: Selector): Selector {
    return selector.indexOf('plugins') > 0 ? selector.slice(0, selector.indexOf('plugins') + 1) : [];
  }

  getPageSelector(selector: Selector): Selector {
    return selector.indexOf('plugins') > 0 ? selector.slice(0, selector.indexOf('plugins')) : [];
  }

  updateObject(selector: Selector, data: unknown, containers?: Set<string>, namespace?: PluginNamespace): void {
    if (!this.depGraph.nodes.get(strsel(selector)) && this.depGraph.nodes.get(strsel(selector.slice(0, -1)))) {
      this.addObject(selector, data);
      this.addDependency(selector.slice(0, -1), selector);
    }

    const pluginSelector =
      selector.indexOf('plugins') > 0 ? selector.slice(0, selector.indexOf('plugins') + 2) : [selector[0]];

    containers?.forEach(container => {
      this.parentContainers.set(strsel(pluginSelector), container);
      if (container !== '') {
        const containerSelector = pluginSelector.slice(0, pluginSelector.length - 1).concat([container]);
        if (this.depGraph.nodes.get(strsel(containerSelector.concat(['instances'])))) {
          this.addDependency(selector, containerSelector.concat(['instances']));
        }
        if (this.depGraph.nodes.get(strsel(containerSelector))) {
          this.addDependency(containerSelector, selector);
        }
      }
    });

    if (Immutable.isCollection(data) || Immutable.isRecord(data)) {
      data = Immutable.isRecord(data) ? Immutable.Map(data) : data;
      data.map((v, k) => {
        const childSelector = selector.concat([k as string]);
        const pagePluginsSelector =
          selector.indexOf('plugins') > 0 ? selector.slice(0, selector.indexOf('plugins') + 1) : null;

        // This is to support 'currentPage' special binding variable to bind to current Page Model.
        const pageSelector = pagePluginsSelector?.slice(0, 1);
        const currentPageAdjust = (s: Selector) => {
          if (pageSelector && s[0] === 'currentPage') return pageSelector.concat(s.slice(1));
          else return s;
        };

        const currentNamespace = namespace || this.lookupNamespace(childSelector);
        if (typeof v === 'string') {
          let dependantParents = [];
          // TODO: Compute expression dependencies here.
          dependantParents = getJSBindingVariables(v);

          // Check if any of the dependants' path prefix exists in dep tree.
          // If they do then they are valid.
          // The paths may refer to deep data objects so check all prefixes until we find a valid path.
          const validParents = dependantParents.filter(s => {
            s = currentPageAdjust(s);
            for (let i = 0; i < s.length; i++) {
              const prefixSel = this.appendNamespaceToSelector(s.slice(0, s.length - i), currentNamespace);
              if (
                this.depGraph.hasNode(strsel(prefixSel)) ||
                (pagePluginsSelector && this.depGraph.hasNode(strsel(pagePluginsSelector?.concat(prefixSel))))
              ) {
                return true;
              }
            }
            return false;
          });

          const selectorsToAdd: Selector[] = validParents.map((s): Selector => {
            s = currentPageAdjust(s);
            s = s.filter(key => key !== 'i');
            for (let i = 0; i < s.length; i++) {
              const prefixSel = this.appendNamespaceToSelector(s.slice(0, s.length - i), currentNamespace);
              if (this.depGraph.hasNode(strsel(prefixSel))) return prefixSel;
              if (pagePluginsSelector && this.depGraph.hasNode(strsel(pagePluginsSelector?.concat(prefixSel)))) {
                return pagePluginsSelector?.concat(prefixSel);
              }
            }
          });
          return this.updateChild(childSelector, selectorsToAdd, v, currentNamespace);
        } else if (typeof v === 'object' && Array.isArray(v)) {
          return this.updateChild(childSelector, [], v, currentNamespace);
        } else {
          return this.updateObject(childSelector, v, containers, currentNamespace);
        }
      });
    } else {
      return this.updateChild(selector, [], data, namespace);
    }
  }

  lookupNamespace(selector: Selector): PluginNamespace | undefined {
    const stringSelector = strsel(selector);
    if (isPluginSelector(selector)) {
      if (this.depGraph.hasNode(stringSelector)) {
        const node = this.depGraph.getNodeData(stringSelector);
        const nsString = node.namespace;
        const originalPluginId = node.originalPluginId;
        return nsString ? new PluginNamespaceImpl(objsel(nsString), originalPluginId) : undefined;
      } else {
        return undefined;
      }
    } else {
      if (this.depGraph.hasNode(stringSelector)) {
        const node = this.depGraph.getNodeData(stringSelector);
        const nsString = node.namespace;
        const originalPluginId = node.originalPluginId;
        if (nsString) {
          return new PluginNamespaceImpl(objsel(nsString), originalPluginId);
        } else {
          return this.lookupNamespace(selector.slice(0, -1));
        }
      } else {
        return undefined;
      }
    }
  }

  private appendNamespaceToSelector(pluginId: Selector, namespace?: PluginNamespace) {
    if (!namespace) return pluginId;

    const selector = [...pluginId];
    const isSharedGlobal = SHARED_GLOBAL_MODEL_PROPERTIES.includes(selector[0]);
    if (!isSharedGlobal) {
      const pluginIdIndex = getPluginIdIndexInSelector(selector);
      selector[pluginIdIndex] = addNamespace(namespace, selector[pluginIdIndex]);
    }
    return selector;
  }

  getAncestorListViewSelector(stringSelector: string): string | null {
    let result: string | null = null;

    while (stringSelector) {
      // length of 'plugins.'
      const LENGTH_OF_PLUGINS_STR = 8;

      let pluginSelector = stringSelector;
      const indexOfPlugins = stringSelector.indexOf('plugins');
      if (indexOfPlugins > 0) {
        // in a string like xyz.plugins.abc.efg this is the index of the . after abc
        const indexOfDotAfterPlugin = stringSelector.indexOf('.', indexOfPlugins + LENGTH_OF_PLUGINS_STR);
        if (indexOfDotAfterPlugin >= 0) {
          // this gets the substring xyz.plugins.abc
          pluginSelector = stringSelector.substr(0, indexOfDotAfterPlugin);
        }
      }

      let pagePrefixSel = '';
      if (indexOfPlugins > 0) {
        // LENGTH_OF_PLUGINS_STR -1 because we want to skip the trailing . in 'xyz.plugins.'
        pagePrefixSel = stringSelector.substr(0, indexOfPlugins + LENGTH_OF_PLUGINS_STR - 1);
      }

      const container = this.parentContainers.get(pluginSelector);
      if (container) {
        const containerSel = pagePrefixSel.concat('.', container);
        if (this.depGraph.nodes.get(containerSel.concat('.', 'instances'))) {
          result = container;
          break;
        } else {
          stringSelector = containerSel;
          // result = this.getAncestorListViewSelector(containerSel);
        }
      } else {
        break;
      }
    }

    return result;
  }

  addObject(selector: Selector, object: unknown, namespace?: PluginNamespace): void {
    if (Immutable.isCollection(object) || Immutable.isRecord(object)) {
      this.addNode(selector, namespace);
      object = Immutable.isRecord(object) ? Immutable.Map(object) : object;
      object.map((v, k) => {
        const childSelector = selector.concat([k]);
        this.addObject(childSelector, v);

        // If the child gets updated, then the parent must be marked as dirty
        this.addDependency(selector, childSelector);
      });
    } else {
      this.addNode(selector, namespace);
    }
  }

  deletePlugin(pageKey: string, pluginId: string): void {
    for (let id of this.depGraph.nodes.keys()) {
      const selector = this.depGraph.nodes.get(id).selector;
      if ((selector[0] === pageKey && selector[2] == pluginId) || (!pageKey && selector[0] == pluginId)) {
        this.clearTopologicalOrderCache();
        this.depGraph.removeNode(id);
      }
    }
  }

  renamePlugin(pageKey: string, pluginId: string, newId: string): void {
    this.copyPlugin(pageKey, pluginId, newId);
    this.deletePlugin(pageKey, pluginId);
  }

  topologicalSort(): Selector[] {
    if (!this.topoCache) {
      // logger.time('Dependency graph Sort');
      this._overallOrderCache = this.depGraph.overallOrder();
      this.topoCache = this._overallOrderCache
        .filter(id => this.hasDynamicString(id))
        .map(id => this.depGraph.getNodeData(id).selector);
      this.evalOrderCache = this._overallOrderCache
        .filter(
          id => this.hasForceEval(id) || this.hasPluginType(id) || (this.hasDynamicString(id) && this.hasJSBinding(id)),
        )
        .map(id => this.depGraph.getNodeData(id).selector);
      // logger.timeEnd('Dependency graph Sort');
    }
    return this.topoCache;
  }
  evalOrderSort(): Selector[] {
    this.topologicalSort();
    return this.evalOrderCache;
  }

  getObjectSelectors(objSel: Selector): Selector[] {
    const objSelPrefix = `${strsel(objSel)}.`;
    const result = [];
    for (let sel of this.depGraph.nodes.keys()) {
      if (sel.startsWith(objSelPrefix)) {
        result.push(objsel(sel));
      }
    }
    return result;
  }

  getDependenciesOf(selector: Selector): Set<NodeData> {
    if (selector.length === 0) {
      return new Set();
    }
    const id = strsel(selector);
    if (this.depGraph.hasNode(id)) {
      return new Set(this.depGraph.dependenciesOf(id).map(cid => this.depGraph.getNodeData(cid)));
    }
    return this.getDependenciesOf(selector.slice(0, selector.length - 1));
  }

  getDirectDependenciesOf(selector: Selector): Set<NodeData> {
    if (selector.length === 0) {
      return new Set();
    }
    const id = strsel(selector);
    if (this.depGraph.hasNode(id)) {
      return new Set(this.depGraph.outgoingEdges[id].map(cid => this.depGraph.getNodeData(cid)));
    }

    return this.getDependenciesOf(selector.slice(0, selector.length - 1));
  }

  getDirectDependentsOf(selector: Selector): Set<NodeData> {
    if (selector.length === 0) {
      return new Set();
    }
    const id = strsel(selector);
    if (this.depGraph.hasNode(id)) {
      return new Set(this.depGraph.incomingEdges.get(id).map(cid => this.depGraph.getNodeData(cid)));
    }
    return this.getDirectDependentsOf(selector.slice(0, selector.length - 1));
  }

  getDependantsOf(selector: Selector): Set<NodeData> {
    if (selector.length === 0) {
      return new Set();
    }

    const id = strsel(selector);
    if (this.depGraph.hasNode(id)) {
      return new Set(this.depGraph.dependantsOf(id).map(cid => this.depGraph.getNodeData(cid)));
    }

    return this.getDependantsOf(selector.slice(0, selector.length - 1));
  }

  canReachWithout(start1: Selector, target: Selector, exclude: Selector): boolean {
    const start = getDependencyGraphSelector(start1);
    const end = getDependencyGraphSelector(target);

    const queue = [];
    const visited: {[key: string]: boolean} = {};

    queue.push(start);
    visited[strsel(start)] = true;
    while (queue.length !== 0) {
      const curr = queue.shift();
      const dependents = this.getDirectDependentsOf(curr!); // curr cannot be undefined because of the queue.length != 0 check
      for (const dependent of dependents) {
        const dependentSelector = dependent.selector;
        if (strsel(dependentSelector) === strsel(end)) {
          return true;
        }

        if (!visited[strsel(dependentSelector)] && strsel(dependentSelector) !== strsel(exclude)) {
          visited[strsel(dependentSelector)] = true;
          queue.push(dependentSelector);
        }
      }
    }
    return false;
  }

  addNavigation(navConfig: NavigationConfig): void {
    const selector = ['ApptileNavigation'];
    this.addObject(selector, navConfig.rootNavigator);
  }

  updateNavigation(navConfig: NavigationConfig): void {
    const selector = ['ApptileNavigation'];
    this.updateObject(selector, navConfig.rootNavigator);
  }

  addPage(pageKey: string, pageConfig: PageConfig, data: any): void {
    const selector = [pageKey];
    this.addObject(selector, data);
  }

  updatePage(pageKey: string, pageConfig: PageConfig, data: any): void {
    const selector = [pageKey];

    const pageDataNoEvents = data?.delete('events');
    this.updateObject(selector, pageDataNoEvents);

    const events: Immutable.List<EventHandlerConfig> = data.get('events', Immutable.List<EventHandlerConfig>());
    this.updateObject(selector.concat('events'), events);

    const keys = PageEventsList;
    for (const key of keys) {
      const keySelector = selector.concat([key]);
      const pageEvents = events.filter(event => event.get('label') === key);
      if (events && events.size > 0) {
        this.updateEventDependency(pageEvents, pageKey, keySelector);
      }
      continue;
    }
  }

  serialize(_transpiledBindings: SerializedBindings) {
    // Assign an index to each selector in depgraph.
    // Create a tree to store these indices in the serialized 
    // format where the leaves are '.'
    const nodeDataKeys = new Set<string>();
    const selectorIndices = {};
    const compressedIndices = {};
    let runningIndex = 0;
    for (let selector of this.depGraph.nodes.keys()) {
      selectorIndices[selector] = runningIndex++;
      const keyPath = selector.split('.');
      if (keyPath[0] === "") {
        keyPath[0] = "__blank__";
      }

      let target = compressedIndices;
      for (let step of keyPath) {
        if (!target[step]) {
          target[step] = {};
        }
        target = target[step];
      }
      target['.'] = selectorIndices[selector];
      const node = this.depGraph.nodes.get(selector);
      Object.keys(node).forEach(key => {
        nodeDataKeys.add(key);
      });
    }

    const attrs: string[] = Array.from(nodeDataKeys);
    const indexOfSelector = attrs.indexOf('selector');
    if (indexOfSelector < 0) {
      throw new Error("Every dependencyGraph node must have a 'selector'");
    } else if (indexOfSelector > 0) {
      const temp = attrs[0];
      attrs[0] = attrs[indexOfSelector];
      attrs[indexOfSelector] = temp;
    }
    
    // Compress incoming edges using the indices
    const incomingEdges = [];
    for (let key of this.depGraph.incomingEdges.keys()) {
      const edge = [];
      edge.push(selectorIndices[key]);
      this.depGraph.incomingEdges.get(key)
        .forEach((selector: string) => {
          edge.push(selectorIndices[selector]);
        });
      incomingEdges.push(edge);
    }

    // Compress outgoing edges
    const outgoingEdges = [];
    for (let key of this.depGraph.outgoingEdges.keys()) {
      const edge = [];
      edge.push(selectorIndices[key]);
      this.depGraph.outgoingEdges.get(key)
        .forEach((selector: string) => {
          edge.push(selectorIndices[selector]);
        });
      outgoingEdges.push(edge);
    }

    // Compress nodes
    const nodes = [];
    for (let key of this.depGraph.nodes.keys()) {
      const node = this.depGraph.nodes.get(key);
      let bitfield = 0;
      const vals = [];

      for (let attribute of attrs) {
        const isEncodable = isEncodableValue(node[attribute]);
        if (node.hasOwnProperty(attribute)) {
          if (isEncodable) {
            bitfield = (bitfield << 2) | 0b11;
          } else if (attribute === "dynamicString") {
            if (_transpiledBindings.raw.indexOf(node.dynamicString) >= 0) {
              bitfield = (bitfield << 2) | 0b11;
            } else {
              bitfield = (bitfield << 2) | 0b10;
            }
          } else {
            bitfield = (bitfield << 2) | 0b10;
          }

          if ((attribute === "dynamicString") && node.isJSBinding) {
            const globalFcnIndex = _transpiledBindings.raw.indexOf(node.dynamicString);
            if (globalFcnIndex >= 0) {
              vals.push(globalFcnIndex);
            } else {
              vals.push(node.dynamicString);
            }
          } else if (attribute === "selector") {
            vals.push(selectorIndices[node.selector.join(".")]);
          } else if (isEncodable && attribute !== "dynamicString") {
            if (node[attribute] === true) {
              vals.push('t');
            } else if (node[attribute] === false) {
              vals.push('f');
            } else if (node[attribute] === null) {
              vals.push('n');
            } else if (node[attribute] === undefined) {
              vals.push('u');
            } else {
              throw new Error("Non encodable value encountered");
            }
          } else {
            vals.push(node[attribute]);
          }
        } else {
          bitfield = (bitfield << 2);
        }
      }

      nodes.push(bitfield, ...vals);
    }

    const parentContainers = [];
    for (let key of this.parentContainers.keys()) {
      parentContainers.push([selectorIndices[key], this.parentContainers.get(key)]);
    }

    const _overallOrderCache = this._overallOrderCache.map(it => selectorIndices[it]);
    const topoCache = [];
    for (let index = 0; index < this.topoCache.length; ++index) {
      if (this.topoCache[index] !== null) {
        topoCache.push(
          selectorIndices[this.topoCache[index].join('.')] 
        );
      }
    }

    const evalOrderCache = [];
    for (let index = 0; index < this.evalOrderCache.length; ++index) {
      if (this.evalOrderCache[index] !== null) {
        evalOrderCache.push(selectorIndices[this.evalOrderCache[index].join(".")]);
      }
    }

    return {
      globalScopeIdentifiers: this.globalScopeIdentifiers,
      _transpiledBindings,
      depGraph: {
        incomingEdges,
        outgoingEdges,
        nodeDataKeys: attrs,
        nodes,
        compressedIndices,
      },
      parentContainers,
      _overallOrderCache,
      topoCache,
      evalOrderCache
    }
  }

  equal(graph1: DependencyGraph) {
    const results = [];
    const graph2 = this;
    // Check that depGraph.nodes are same
    const nodes1 = graph1.depGraph.nodes;
    const nodes2 = graph2.depGraph.nodes;
    
    if (nodes1.size !== nodes2.size) {
      console.error("depGraph.nodes are not the same size");
      return false;
    }

    for (let key of nodes1.keys()) {
      const node1 = graph1.depGraph.nodes.get(key);
      const node2 = graph2.depGraph.nodes.get(key);
      if (!_.isEqual(node1, node2)) {
        results.push(`Depgraph equality check error: 
                     Node ${key} are different: 
                     ${JSON.stringify(node1, null, 2)} 
                     ${JSON.stringify(node2, null, 2)}`);
      }
    }

    // Check that depGraph.incomingEdges are same
    let edges1 = graph1.depGraph.incomingEdges;
    let edges2 = graph2.depGraph.incomingEdges;
    if (edges1.size !== edges2.size) {
      results.push(`Depgraph equality check error: 
                   depgraph edges are not the same size`);
    }

    for (let key of edges1.keys()) {
      const edge1 = edges1.get(key);
      const edge2 = edges2.get(key);
      if (!_.isEqual(edge1, edge2)) {
        results.push(`Depgraph equality check error: 
                     Edges ${key} are different: 
                     ${JSON.stringify(edge1, null, 2)} 
                     ${JSON.stringify(edge2, null, 2)}`)
      }
    }

    // Check that depGraph.outgoingEdges are same
    edges1 = graph1.depGraph.outgoingEdges;
    edges2 = graph2.depGraph.outgoingEdges;
    if (edges1.size !== edges2.size) {
      results.push(`Depgraph equality check error: 
                   depgraph edges are not the same size`);
    }

    for (let key of edges1.keys()) {
      const edge1 = edges1.get(key);
      const edge2 = edges2.get(key);
      if (!_.isEqual(edge1, edge2)) {
        results.push(`Depgraph equality check error:  
                     Edges ${key} are different: 
                     ${JSON.stringify(edge1, null, 2)} 
                      ${JSON.stringify(edge2, null, 2)}`
                    )
      }
    }

    // Check that topoCache is same
    // Check that evalOrderCache is same
    // Check that overallOrderCache is same
    for (let key of ['topoCache', 'evalOrderCache', '_overallOrderCache']) {
      if (graph1[key]?.length !== graph2[key]?.length) {
        results.push(`Depgraph equality check error: 
                     ${key} length different`);
      }

      for (let index = 0; index < graph1[key].length; ++index) {
        if (!_.isEqual(graph1[key][index], graph2[key][index])) {
          results.push(`Depgraph equality check error: 
                       ${key} differs at index ${index} 
                       ${graph1[key][index]} 
                       ${graph2[key][index]}`
                      );
        }
      }
    }
    return results;
  }

  deserialize(serializedDepGraph: ReturnType<typeof this.serialize>) {
    this.hydrateCompiledBindings(serializedDepGraph._transpiledBindings);
    const depGraph = serializedDepGraph.depGraph;
    const si = depGraph.compressedIndices;
    const selectorIndices = [];
    unwindCompressedIndices(si, "", selectorIndices);

    const decodeEdges = (serializedEdges: typeof depGraph.incomingEdges | typeof depGraph.outgoingEdges, edgesMap: Map<string, string[]>) => {
      for (let index = 0; index < serializedEdges.length; ++index) {
        const encodedNodes = serializedEdges[index];
        const edgeStartI = encodedNodes[0];
        const source = selectorIndices[edgeStartI];
        const dests = [];
        for (let i = 1; i < encodedNodes.length; ++i) {
          const edgeEndI = encodedNodes[i];
          dests.push(selectorIndices[edgeEndI]);
        }
        edgesMap.set(source, dests);
      }
    }

    decodeEdges(depGraph.incomingEdges, this.depGraph.incomingEdges);
    decodeEdges(depGraph.outgoingEdges, this.depGraph.outgoingEdges);

    const attrs = serializedDepGraph.depGraph.nodeDataKeys;

    const unCompressEncodedVal = (val: 'n'|'t'|'f'|'u') => {
      if (val === 'n') {
        return null;
      } else if (val === 't') {
        return true;
      } else if (val === 'u') {
        return undefined;
      } else {
        return false;
      }
    }

    for (let index = 0; index < depGraph.nodes.length; ) {
      const isEncodable = [];
      const attrExist = [];
      let bitField = depGraph.nodes[index];
      let numAttrsInNode = 1;
      for (let i = 0; i < attrs.length; ++i) {
        const encodable = bitField & 0b1;
        isEncodable.unshift(encodable);
        bitField = bitField >> 1;
        const exist = bitField & 0b1;
        numAttrsInNode += exist;
        attrExist.unshift(exist);
        bitField = bitField >> 1;
      }

      const selector = selectorIndices[depGraph.nodes[index + 1]];
      const parsedSelector = selector.split(".");
      for (let i = 0; i < parsedSelector.length; ++i) {
        const sel = parsedSelector[i];
        if (/^[0-9]+$/.test(sel)) {
          parsedSelector[i] = parseInt(sel);
        } else if (sel === "") {
          parsedSelector[i] = null;
        } 
      }

      const nodeValue: Partial<NodeData> = {
        selector: parsedSelector
      };

      this.depGraph.nodes.set(selector, nodeValue);

      let runningIndex = 1;
      for (let bitI = 1; bitI < isEncodable.length; ++bitI) {
        const attr = attrs[bitI];
        const exists = attrExist[bitI] === 1;
        if (exists) {
          const compressedValue = depGraph.nodes[index + ++runningIndex];
          if (attr !== 'dynamicString' && isEncodable[bitI] === 1) {
            nodeValue[attr] = unCompressEncodedVal(compressedValue);
          } else {
            nodeValue[attr] = compressedValue;
          }
        }
      }

      const indexOfDynamicString = attrs.indexOf('dynamicString');

      if (indexOfDynamicString >= 0 && nodeValue.isJSBinding && (isEncodable[indexOfDynamicString] === 1)) {
        nodeValue.dynamicString = serializedDepGraph._transpiledBindings.raw[nodeValue.dynamicString];
      }

      index += numAttrsInNode;
    }

    for (let index = 0; index < serializedDepGraph.parentContainers.length; ++index) {
      const child = selectorIndices[serializedDepGraph.parentContainers[index][0]];
      const parent = serializedDepGraph.parentContainers[index][1];
      this.parentContainers.set(child, parent);
    }

    if (!this._overallOrderCache && serializedDepGraph._overallOrderCache) {
      this._overallOrderCache = [];
    }

    for (let index = 0; index < serializedDepGraph._overallOrderCache.length; ++index) {
      const selectorI = serializedDepGraph._overallOrderCache[index];
      this._overallOrderCache[index] = selectorIndices[selectorI];
    }

    if (!this.topoCache && serializedDepGraph.topoCache) {
      this.topoCache = [];
    }

    for (let index = 0; index < serializedDepGraph.topoCache.length; ++index) {
      const selectorI = serializedDepGraph.topoCache[index];
      this.topoCache[index] = selectorIndices[selectorI].split(".");
      for (let i = 0; i < this.topoCache[index].length; ++i) {
        if (/^[0-9]+$/.test(this.topoCache[index][i])) {
          this.topoCache[index][i] = parseInt(this.topoCache[index][i]);
        }
      }
    }

    if (!this.evalOrderCache && serializedDepGraph.evalOrderCache) {
      this.evalOrderCache = [];
    }

    for (let index = 0; index < serializedDepGraph.evalOrderCache.length; ++index) {
      const selectorI = serializedDepGraph.evalOrderCache[index];
      this.evalOrderCache[index] = selectorIndices[selectorI].split(".")
      for (let i = 0; i < this.evalOrderCache[index].length; ++i) {
        if (/^[0-9]+$/.test(this.evalOrderCache[index][i])) {
          this.evalOrderCache[index][i] = parseInt(this.evalOrderCache[index][i]);
        }
      }
    }
    return 0;
  }
}

export function renameVariables(code: any, varName: any, newName: any) {
  let ast = parse(code, {ecmaVersion: 11});
  let references = variableRefs(ast, varName);
  let renamedCode = code;
  while (references.length > 0) {
    // logger.info(references);
    renamedCode = renamedCode.substring(0, references[0].start) + newName + renamedCode.substring(references[0].end);
    if (references.length > 1) {
      ast = parse(renamedCode, {ecmaVersion: 11});
      references = variableRefs(ast, varName);
    } else {
      references.pop();
    }
  }
  // Old Discarded Code
  // renamedCode = references.reduce((code: any, {start, end}) => {
  //   return code.substring(0, start) + newName + code.substring(end);
  // }, code);
  return renamedCode;
}

function updateJSBinding(jsString: any, pluginId: any, newId: any) {
  const newJsString = jsString.replace(JSBINDING_REPLACE_REGEX, (matched, code) => {
    let result = code;
    try {
      result = renameVariables(code, pluginId, newId);
    } catch (e) {
      logger.error(e);
    }
    if (result === code) {
      return matched;
    } else {
      return `{{${result}}}`;
    }
  });
  return newJsString;
}

export function updateProperty(jsString: any, oldId: any, newId: any, propertyType: any = '') {
  let newValue: any, hasNewValue: any;
  if (typeof jsString === 'string') {
    const renamedJsString = updateJSBinding(jsString, oldId, newId);
    if (renamedJsString !== jsString) {
      newValue = renamedJsString;
      hasNewValue = true;
    }
  }
  return {newValue, hasNewValue};
}

function isEncodableValue(val: boolean|null) {
  return (val === true) || (val === false) || (val === null) || (val === undefined);
}

function unwindCompressedIndices(indices: object, path: string, result: string[]) {
  for (let key in indices) {
    if (key === ".") {
      result[indices[key]] = path;
    } else {
      let nextpath: string;
      if (path == "__blank__") {
        nextpath = "." + key;
      } else if (path) {
        nextpath = path + "." + key;
      } else {
        nextpath = key;
      }
      unwindCompressedIndices(indices[key], nextpath, result);
    }
  }
}
