import * as Sentry from "@sentry/react";
// TODO(gaurav) DONE
// import { WEB_SENTRY_TRACE_SAMPLE_RATE, WEB_SENTRY_DSN } from '../../../.env.json';
export const routingInstrumentation = {
  registerNavigationContainer() {
    logger.info("No sentry tracking in web");
  },
};
export default class SentryHelper {
  static init() {
    if (!global.ENABLE_REDUX_LOGGER) {
      Sentry.init({
        dsn: global.WEB_SENTRY_DSN,
        integrations: [
          Sentry.browserTracingIntegration(),
          // Sentry.replayIntegration(),
        ],
        // Set `tracePropagationTargets` to control for which URLs trace propagation should be enabled
        tracePropagationTargets: [/^https:\/\/dev-api\.apptile\.io/, /^https:\/\/api\.apptile\.io/],
        // Performance Monitoring
        tracesSampleRate: global.WEB_SENTRY_TRACE_SAMPLE_RATE, // Capture 100% of the transactions, reduce in production!
        // Session Replay
        replaysSessionSampleRate: 0.05, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
        replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
      });
    }
  }

  static startTransaction(name: string) {
    logger.info("[SENTRY_TRANSACTION] " + name);
    return {
      name,
      finish: () => logger.info("[SENTRY_TRANSACTION_FINISH] " + name),
    };
  }

  static startSpan(
    transaction: { name: string },
    op: string,
    description?: string
  ) {
    logger.info(
      "[SENTRY_SPAN]: " + transaction + " : " + op + " : " + description
    );
    return {
      finish: () =>
        logger.info(
          "[SENTRY_SPAN_FINISH]: " +
            transaction.name +
            " : " +
            op +
            " : " +
            description
        ),
      setStatus: (...args: any[]) =>
        logger.info(
          "[SENTRY_SPAN_FINISH]: " +
            transaction.name +
            " : " +
            op +
            " : " +
            description,
          args
        ),
    };
  }
}
