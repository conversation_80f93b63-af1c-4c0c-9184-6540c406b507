import {immutable} from '../../lib/immutable';
var jsanutils = require('../../lib/jsan/utils');
import * as Immutable from 'immutable';
import _ from 'lodash';
import {FlexStyle} from 'react-native';
import {baseGlobalThemeConfig} from '../../styles/theme/global';
import {ITypographyItem} from '../../styles/theme/global/declaration';
import {IThemeDependencyGraph} from '../../styles/theme/themeDependencyGraph/themeDependencyTypes';
import {AnimSelector} from '../Animations/apptileAnimationTypes';
import {ApptileAnalyticsEventType} from '../ApptileAnalytics/ApptileAnalyticsTypes';
import {
  DependencyGraphInterface,
  IDependencyGraph,
  NodeData,
  Selector,
  strsel,
  SerializedBindings,
} from '../DependencyGraph/types';
import {PropertyEditorConfig} from '../EditorControlTypes';
import EvaluationContextStack from '../ExecutionContext/EvaluationContext';
import {EvalResult} from '../ExecutionContext/EvaluationContextTypes';
import {SafeAny} from '../types';
import {SerializationMarker} from '../../lib/immutable/immutable/serialize';

export type PropType<TObj, TProp extends keyof TObj> = TObj[TProp];
export type TupleToObject<T extends [string, any]> = {
  [key in T[0]]: Extract<T, [key, any]>[1];
};
export interface ImmutableMapType<T = any> extends Immutable.Map<string, any> {
  toJS(): T;
  get<K extends keyof T>(key: K): T[K];
}

export type WidgetType =
  | "AnimatedTimerWidget"
  | "ExampleWidget"
  | "TextWidget"
  | "ImageSlider"
  | "ContainerWidget"
  | "ListViewWidget"
  | "PillWidget"
  | "ImageWidget"
  | "TextInputWidget"
  | "CheckBoxWidget"
  | "ButtonWidget"
  | "ModuleInstance"
  | "BadgeWidget"
  | "RichTextWidget"
  | "ModalWidget"
  | "IconWidget"
  | "WebViewWidget"
  | "WebViewWidgetV2"
  |"ResetPasswordWebViewWidget"
  | "ImageSliderWidget"
  | "RatingWidget"
  | "RangeSliderWidget"
  | "VideoPlayerWidget"
  | "CameraWidget"
  | "RadioGroupWidget"
  | "AccordionWidget"
  | "CountdownWidget"
  | "AutoLinkTextWidget"
  | "ProgressBarWidget"
  | "AssistantAIWidget"
  // Custom Plugins
  | "DisplayImageList"
  | "ChatWidget"
  | "BottomSheetWidget"
  | "ScrollBubblesWidget"
  | "StorifyMeWidget"
  | "ShopFloCheckoutWidget"
  | "GoKwikCheckoutWidget"
  | "ShopifyWebCheckoutWidget"
  | "ProfileWidget"
  | "KimiricaFAQWidget"
  | "LivelyWidget"
  | "LivelyStoriesWidget"
  | "CloudSearchHelper"
  | "SwatchWidget"
  | "AnimatedProgressBarWidget"
  | "VideoPlayerWidgetV2"
  | "VideoSeeker"
  | "GorgiasWebViewWidget"
  | "ImageSliderWidgetV2"
  | "ShopifyCustomerLoginWidget"
  | "KippunHaruWebViewWidget"
  | "Livelyshoppablefeed2"
  ;

export type StatePluginSubTypes =
  | 'ApptileGlobal'
  | 'LocalStoragePlugin'
  | 'StatePlugin'
  | 'TimerPlugin'
  | 'PollPlugin'
  | 'LogicalExpressionPlugin'
  | 'CustomList'
  | 'ShopifyPDP_22_10'
  | 'ShopifyPLP_22_10'
  | 'ShopifyCollectionsTree_22_10'
  | 'SearchFiltersPlugin'
  | 'SearchanizeHelper'
  | 'NostoHelper'
  | 'ZapietHelper'
  | 'ApptileColorSwatch'
  | 'CloudSearchHelper'
  | 'ShopifyWishlist_22_10';

export type PluginType =
  | StatePluginSubTypes
  | 'QueryPlugin'
  | 'ShopifyQuery'
  | 'ShopifyGlobal'
  | 'MFAuthQuery'
  | 'MFAuthenticationQuery';

export type ModuleSubTypes = 'ModuleProperty' | 'ModuleOutput';

export type PluginSubType = WidgetType | PluginType | ModuleSubTypes;

export type PluginIdTypePage = {
  id: string;
  pluginType: PluginType;
  pageKey: string;
};
export interface LayoutParams
  extends Pick<
    FlexStyle,
    | 'aspectRatio'
    | 'alignContent'
    | 'alignItems'
    | 'alignSelf'
    | 'flex'
    | 'flexBasis'
    | 'flexDirection'
    | 'flexGrow'
    | 'flexShrink'
    | 'flexWrap'
    | 'height'
    | 'justifyContent'
    | 'maxHeight'
    | 'maxWidth'
    | 'minHeight'
    | 'minWidth'
    | 'overflow'
    | 'width'
    | 'position'
    | 'top'
    | 'bottom'
    | 'left'
    | 'right'
    | 'direction'
  > {
  container: string;
  hidden?: boolean | string;
  __flexProperties?: Record<string, string | number | boolean>;
}

const defaultLayoutParams: LayoutParams = {
  container: '',
  hidden: undefined,
  aspectRatio: undefined!,
  alignContent: undefined!,
  alignItems: undefined!,
  alignSelf: undefined!,
  flex: undefined!,
  flexBasis: undefined!,
  flexDirection: undefined!,
  direction: undefined!,
  flexGrow: undefined!,
  flexShrink: undefined!,
  flexWrap: undefined!,
  height: undefined!,
  justifyContent: undefined!,
  maxHeight: undefined!,
  maxWidth: undefined!,
  minHeight: undefined!,
  minWidth: undefined!,
  overflow: undefined!,
  width: undefined!,
  position: undefined!,
  top: undefined!,
  bottom: undefined!,
  left: undefined!,
  right: undefined!,
  __flexProperties: undefined!,
};
const CONTAINER_LAYOUT_PROPS = [
  'flexDirection',
  'direction',
  'flexWrap',
  'overflow',
  'justifyContent',
  'alignItems',
  'alignSelf',
  'alignContent',
];

export class LayoutRecord extends Immutable.Record(defaultLayoutParams, 'layout') {
  getFlexProperties() {
    if (this.__flexProperties !== undefined) {
      return {...this.__flexProperties};
    } else {
      const {container, hidden, ...flexProperties} = this.toJS();
      let flex = _.omitBy(flexProperties, _.isNil);
      flex = _.omitBy(flex, _.isEmpty);

      const NUMERICFLEX = [
        'aspectRatio',
        'height',
        'width',
        'flex',
        'flexBasis',
        'flexGrow',
        'flexShrink',
        'maxHeight',
        'maxWidth',
        'minHeight',
        'minWidth',
        'top',
        'bottom',
        'left',
        'right',
      ];
      flex = _.mapValues(flex, (val, key) => {
        if (NUMERICFLEX.includes(key)) {
          return _.isNaN(val * 1) ? val : _.toNumber(val);
        }
        return val;
      });

      return flex;
    }
  }

  _generateFlexPropertiesCache() {
    return this.set('__flexProperties', this.getFlexProperties());
  }
  _clearFlexPropertiesCache() {
    return this.set('__flexProperties', undefined);
  }

  getContainerFlexProperties() {
    return _.pick(this.getFlexProperties(), CONTAINER_LAYOUT_PROPS);
  }

  getNonContainerFlexProperties() {
    return _.omit(this.getFlexProperties(), CONTAINER_LAYOUT_PROPS);
  }

  serialize() {
    return _.omitBy(super.toObject(), _.isUndefined);
  }
}

export interface PluginNamespace {
  getNamespace: () => string[];
  getPluginId: () => string;
  getParentNamespace: () => PluginNamespace | undefined;
}

export class PluginNamespaceImpl implements PluginNamespace {
  namespace: string[];
  pluginId: string;
  constructor(namespace: string[], pluginId?: string) {
    this.namespace = namespace;
    if (pluginId) {
      this.pluginId = pluginId;
    }
  }

  getNamespace() {
    return this.namespace;
  }
  getPluginId() {
    return this.pluginId;
  }

  private hasParentNamespace() {
    return this.namespace && this.namespace.length > 1;
  }

  getParentNamespace() {
    if (this.hasParentNamespace()) {
      return new PluginNamespaceImpl(this.namespace.slice(0, -1));
    } else {
      return new PluginNamespaceImpl([]);
    }
  }
}

export interface IAnalyticsConfig {
  enabled: boolean;
  type: ApptileAnalyticsEventType;
  name: string;
  params: Immutable.Map<string, string>;
}
const defaultAnalyticsConfig = {
  enabled: false,
  type: 'track',
  name: '',
  params: Immutable.Map(),
};

export class AnalyticsConfig extends Immutable.Record(defaultAnalyticsConfig, 'analyticsConfig') {
  constructor(params: any) {
    super(params);
  }
}

export interface IAnimatedTransitionModifier {
  modifier: string;
  params: Array<SafeAny>;
}
const defaultTransitionModifier: IAnimatedTransitionModifier = {
  modifier: '',
  params: [],
};
export class AnimatedTransitionModifier extends Immutable.Record(
  defaultTransitionModifier,
  'animatedTransitionModifier',
) {
  constructor(params: any) {
    super(params);
  }
}
export interface IAnimatedTransitionRecord {
  transition: string;
  modifiers: Immutable.List<AnimatedTransitionModifier>;
}
const defaultAnimatedTransition: IAnimatedTransitionRecord = {
  transition: '',
  modifiers: Immutable.List(),
};
export class AnimatedTransitionRecord extends Immutable.Record(defaultAnimatedTransition, 'animatedTransition') {
  constructor(params: any) {
    super(params);
  }
}
export interface ITransitionsConfig {
  entering: AnimatedTransitionRecord | undefined;
  exiting: AnimatedTransitionRecord | undefined;
  layout: AnimatedTransitionRecord | undefined;
}
const defaultTransitionsConfig: ITransitionsConfig = {
  entering: undefined,
  exiting: undefined,
  layout: undefined,
};
export class TransitionsRecord extends Immutable.Record(defaultTransitionsConfig, 'transitions') {
  constructor(params: any) {
    super(params);
  }
}

export interface IAnimationsConfig {
  enabled: boolean;
  transitions: TransitionsRecord;
  references: Immutable.Map<string, AnimSelector | undefined> | undefined;
}
const defaultAnimationsConfig: IAnimationsConfig = {
  enabled: false,
  transitions: new TransitionsRecord({}),
  references: Immutable.Map(),
};

export class AnimationsConfig extends Immutable.Record(defaultAnimationsConfig, 'animationsConfig') {
  constructor(params: any) {
    super(params);
  }
}

export interface PluginConfigParams<ConfigType> {
  id: string;
  type: 'widget' | 'state' | 'query' | 'datasource';
  subtype: PluginSubType;
  config: ConfigType & EventsConfigParams;
  layout: LayoutRecord;
  namespace: PluginNamespace | undefined;
  analytics: AnalyticsConfig | undefined;
  animations: AnimationsConfig | undefined;
  _modelStyles: Record<string, any> | undefined;
  _cachedModel: ImmutableMapType | undefined;
}

export type PluginConfigType<T> = Immutable.Record<PluginConfigParams<T>> & Readonly<PluginConfigParams<T>>;

const defaultPluginConfigValues: PluginConfigParams<any> = {
  id: undefined!,
  type: undefined!,
  subtype: undefined!,
  config: undefined,
  layout: new LayoutRecord(),
  namespace: undefined,
  analytics: undefined,
  animations: undefined,
  _modelStyles: undefined,
  _cachedModel: undefined,
};

export interface WidgetConfigType<T = any> extends PluginConfigType<T> {
  subtype: WidgetType;
  type: 'widget';
}

export class PluginConfig extends Immutable.Record(defaultPluginConfigValues, 'pluginConfig') {
  constructor(params: any) {
    super(params);
  }

  isWidget() {
    return this.type === 'widget';
  }
}

export enum PageTransitions {
  Default = 'Default',
  Instant = 'Instant',
  Fade = 'Fade',
  SlideUp = 'SlideUp',
  SlideDown = 'SlideDown',
  SlideLeft = 'SlideLeft',
  SlideRight = 'SlideRight',
  Scale = 'Scale',
}

export interface PageParamConfigParams {
  name: string;
  isRequired: boolean;
  defaultValue?: string;
}
const defaultPageParamConfig: PageParamConfigParams = {
  name: 'paramName',
  isRequired: true,
  defaultValue: undefined,
};
const defaultPageOptionConfig: PageParamConfigParams = {
  name: 'optionName',
  isRequired: true,
  defaultValue: undefined,
};
export class PageParamConfig extends Immutable.Record(defaultPageParamConfig, 'pageParamConfig') {}
export class PageOptionConfig extends Immutable.Record(defaultPageOptionConfig, 'pageOptionConfig') {}

export type PageTypes = 'Screen' | 'Header' | 'Loader';
export type CachedDependencyGraph = DependencyGraphInterface<NodeData> & {
  parentContainers: Record<string, string>;
};
export interface PageConfigParams extends EventsConfigParams {
  title: string;
  pageId?: string;
  pageKey?: string;
  pageParams: Immutable.Map<string, PageParamConfig>;
  plugins: Immutable.OrderedMap<string, PluginConfig>;
  containerType: 'View' | 'ScrollView';
  disableSafeArea?: boolean;
  disablePageCache?: boolean;
  disableBackground?: boolean;
  type: PageTypes;
  pageOptions?: Immutable.Map<string, PageOptionConfig>;
  analytics: AnalyticsConfig | undefined;
  pageUUID: string | null;
  transition: PageTransitions;
  _transpiledBindings: {raw: string[]; transpiled: string[]};
}

const defaultPageConfig: PageConfigParams = {
  title: 'New Page',
  pageId: undefined!,
  pageKey: undefined!,
  pageParams: Immutable.Map(),
  plugins: Immutable.OrderedMap(),
  containerType: 'ScrollView',
  disableSafeArea: true,
  disablePageCache: false,
  disableBackground: false,
  type: 'Screen',
  pageOptions: Immutable.Map(),
  events: Immutable.List(),
  analytics: undefined,
  pageUUID: null,
  transition: PageTransitions.Default,
  _transpiledBindings: {raw: [], transpiled: []},
};

export class PageConfig extends Immutable.Record(defaultPageConfig, 'pageConfig') {
  getPluginId(id: string): PluginConfigType<any> {
    return this.plugins.get(id)!;
  }

  setPluginId(id: string, pluginConfig: PluginConfigType<any>): PageConfig {
    return this.plugins.set(id, pluginConfig);
  }
}

export type NavigatorType = 'stack' | 'tab' | 'topTab';
export type NavigatorConfigType = 'navigator' | 'screen';
export interface NavigatorConfigBase {
  type: NavigatorConfigType;
}

export interface ScreenConfigParams extends NavigatorConfigBase {
  type: 'screen';
  name: string;
  screen: string;
  title?: string;
  iconType?: string;
  iconName: string;
  showTitleBar?: boolean;
  isModal?: boolean;
  isTransparentModal?: boolean;
  unmountOnBlur?: boolean;
  header?: string | null;
  transition: PageTransitions;
  nativeTemplate?: string;
}
const defaultScreenConfig: ScreenConfigParams = {
  type: 'screen',
  name: 'Screen0',
  screen: '',
  title: '',
  iconType: 'Material Icon',
  iconName: 'file-outline',
  showTitleBar: false,
  isModal: false,
  isTransparentModal: false,
  unmountOnBlur: false,
  header: null,
  transition: PageTransitions.Default,
  nativeTemplate: undefined,
};

export class ScreenConfig extends Immutable.Record(defaultScreenConfig, 'screenConfig') {}
export interface NavigatorConfigParams extends NavigatorConfigBase {
  type: 'navigator';
  name: string;
  navigatorType: NavigatorType;
  iconType?: string;
  iconName?: string;
  detachInActiveScreens?: boolean;
  screens: Immutable.OrderedMap<string, ScreenConfig | NavigatorConfig>;
}
const defaultNavigatorConfig: NavigatorConfigParams = {
  type: 'navigator',
  name: 'Navigator',
  navigatorType: 'stack',
  iconType: 'Material Icon',
  iconName: 'file-outline',
  detachInActiveScreens: false,
  screens: Immutable.OrderedMap(),
};
export class NavigatorConfig extends Immutable.Record(defaultNavigatorConfig, 'navigatorConfig') {}
export interface NavigationConfigParams {
  rootNavigator: NavigatorConfig;
}

const defaultNavigationConfig: NavigationConfigParams = {
  rootNavigator: new NavigatorConfig({
    type: 'navigator',
    name: 'Home',
    navigatorType: 'tab',
    detachInActiveScreens: false,
    screens: Immutable.OrderedMap(),
  }),
};

export class NavigationConfig extends Immutable.Record(defaultNavigationConfig, 'navConfig') {
  setRootNavigator(nav: NavigatorConfig) {
    return this.set('rootNavigator', nav);
  }

  setNavigationProperty(navSelector: any[], propertyName: string, value: any) {
    if (navSelector.length == 1 && navSelector[0] == '/') {
      return this.setIn(['rootNavigator', propertyName], value);
    } else {
      const selPath = navSelector[0] === '/' ? navSelector.slice(1) : navSelector;
      const updatePath = ['rootNavigator'];
      selPath.forEach((val: string) => updatePath.push('screens', val));
      updatePath.push(propertyName);
      return this.setIn(updatePath, value);
    }
  }

  updateNavigationPropertyPath(navSelector: any[], selector: string[], update: any) {
    if (navSelector.length == 1 && navSelector[0] == '/') {
      return this.mergeIn(['rootNavigator'].concat(selector), update);
    } else {
      const selPath = navSelector[0] === '/' ? navSelector.slice(1) : navSelector;
      const updatePath = ['rootNavigator'];
      selPath.forEach((val: string) => updatePath.push('screens', val));
      updatePath.concat(selector);
      return this.mergeIn(updatePath, update);
    }
  }

  setNavigationPropertyValue(navSelector: any[], selector: string[], value: any) {
    if (navSelector.length == 1 && navSelector[0] == '/') {
      return this.setIn(['rootNavigator'].concat(selector), value);
    } else {
      const selPath = navSelector[0] === '/' ? navSelector.slice(1) : navSelector;
      const updatePath = ['rootNavigator'];
      selPath.forEach((val: string) => updatePath.push('screens', val));
      updatePath.concat(selector);
      return this.setIn(updatePath, value);
    }
  }

  setNavigationName(navSelector: any[], newNavName: string) {
    if (navSelector.length == 1 && navSelector[0] == '/') {
      return this.setIn(['rootNavigator', 'name'], newNavName);
    } else {
      const selPath = navSelector[0] === '/' ? navSelector.slice(1) : navSelector;
      const updatePath = ['rootNavigator'];
      selPath.forEach((val: string) => updatePath.push('screens', val));
      const screenCollectionPath = updatePath.slice(0, -1);
      const oldNavName = updatePath[updatePath.length - 1];
      updatePath.push('name');

      const nameUpdatedNav = this.setIn(updatePath, newNavName);
      return nameUpdatedNav.setIn(
        screenCollectionPath,
        nameUpdatedNav.getIn(screenCollectionPath).mapKeys(key => {
          if (key === oldNavName) return newNavName;
          return key;
        }),
      );
    }
  }

  addNavigationScreen(navSelector: any[], screenName: string, screenConfig?: Partial<ScreenConfig>) {
    const newScreenConfig = {name: screenName, ...screenConfig};
    if (navSelector.length == 1 && navSelector[0] == '/') {
      return this.setIn(
        ['rootNavigator', 'screens'],
        this.rootNavigator.screens.set(screenName, new ScreenConfig(newScreenConfig)),
      );
    } else {
      const selPath = navSelector[0] === '/' ? navSelector.slice(1) : navSelector;
      const updatePath = ['rootNavigator'];
      selPath.forEach((val: string) => updatePath.push('screens', val));
      const navToUpdate: NavigatorConfig = this.getIn(updatePath);
      updatePath.push('screens');
      return this.setIn(updatePath, navToUpdate.screens.set(screenName, new ScreenConfig(newScreenConfig)));
    }
  }

  addNavigationNavigator(navSelector: any[], navName: string, navigatorType: string) {
    if (navSelector.length == 1 && navSelector[0] == '/') {
      return this.setIn(
        ['rootNavigator', 'screens'],
        this.rootNavigator.screens.set(navName, new NavigatorConfig({name: navName, navigatorType})),
      );
    } else {
      const selPath = navSelector[0] === '/' ? navSelector.slice(1) : navSelector;
      const updatePath = ['rootNavigator'];
      selPath.forEach((val: string) => updatePath.push('screens', val));
      const navToUpdate: NavigatorConfig = this.getIn(updatePath);
      navToUpdate.screens.set(navName, new NavigatorConfig({name: navName, navigatorType}));
      updatePath.push('screens');
      return this.setIn(
        updatePath,
        navToUpdate.screens.set(navName, new NavigatorConfig({name: navName, navigatorType})),
      );
    }
  }

  deleteNavigationItem(navSelector: any[]) {
    if (navSelector.length == 1 && navSelector[0] == '/') {
      return this; //Cannot delete Root Nav
    } else {
      const selPath = navSelector[0] === '/' ? navSelector.slice(1) : navSelector;
      const updatePath = ['rootNavigator'];
      selPath.forEach((val: string) => updatePath.push('screens', val));
      const navToUpdate: NavigatorConfig = this.getIn(updatePath);
      const screenCollectionPath = updatePath.slice(0, -1);
      const deleteNavName = updatePath[updatePath.length - 1];
      return this.setIn(screenCollectionPath, this.getIn(screenCollectionPath).remove(deleteNavName));
    }
  }

  getConfig(navSelector: any[]): ScreenConfig | NavigatorConfig {
    if (navSelector.length == 1 && navSelector[0] == '/') {
      return this.get('rootNavigator');
    } else {
      const selPath = navSelector[0] === '/' ? navSelector.slice(1) : navSelector;
      const updatePath = ['rootNavigator'];
      selPath.forEach((val: string) => updatePath.push('screens', val));
      return this.getIn(updatePath);
    }
  }
}

export type IThemeColorConfig = Record<'light' | 'dark', Record<string, string>>;

export type IThemeTypographyConfig = Record<'ios' | 'android' | 'web', Record<string, ITypographyItem>>;

export interface ApptileThemeDefinitions {
  colors: Record<string, any>;
  typography: Record<string, any>;
  custom: Record<string, any>;
  tile: Record<string, any>;
}
export interface ApptileThemeConfigParams {
  isDarkModeSupported: boolean;
  version: string;
  colors: IThemeColorConfig;
  typography: IThemeTypographyConfig;
  config: Partial<ApptileThemeDefinitions>;
}

export type ApptileThemeActiveConfig = Omit<ApptileThemeConfigParams, 'colors' | 'typography'>;

const defaultApptileThemeConfig: ApptileThemeConfigParams = {
  isDarkModeSupported: false,
  version: '0.0.2',
  colors: baseGlobalThemeConfig.colors,
  typography: baseGlobalThemeConfig.typography,
  config: {},
};

export class ApptileThemeConfig extends Immutable.Record(defaultApptileThemeConfig, 'themeConfig') {}

export interface ImageConfigParams {
  fileName: string;
  width: number;
  height: number;
  fileUrl: string;
}

export interface ImageRecordShape extends ImageConfigParams {
  variants?: Immutable.OrderedMap<string, ImageConfigParams>;
}

const defaultImageRecord: ImageRecordShape = {
  variants: Immutable.OrderedMap(),
  fileName: '',
  width: 0,
  height: 0,
  fileUrl: '',
};

export type IFontRecord = {
  category: string;
  family: string;
  variant: string; // variant type
  fontWeight: '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
  fontStyle: 'normal' | 'italic';
  postScriptName: string;
  provider: string;
  fileUrl: string;
};

const defaultFontRecord: IFontRecord = {};

export class ImageRecord extends Immutable.Record(defaultImageRecord, 'imageRecord') {}

export class FontRecord extends Immutable.Record(defaultFontRecord, 'fontRecord') {}

export interface SettingsConfigShape {
  values: Immutable.OrderedMap<string, any>;
}
const defaultSettingsConfig: SettingsConfigShape = {
  values: Immutable.OrderedMap<string, any>(),
};

export type ApptileSettings = Immutable.Map<string, SettingsConfig>;

export class SettingsConfig extends Immutable.Record(defaultSettingsConfig, 'settingsConfig') {
  getSettingValue(key: string): SafeAny | unknown {
    return this.getIn(['values', key]);
  }
  setSettingValue(key: string, value: SafeAny): SettingsConfig {
    return this.setIn(['values', key], value);
  }
}

export interface AppConfigParams {
  title: string;
  pages: Immutable.Map<string, PageConfig>;
  navigation: NavigationConfig;
  plugins: Immutable.Map<string, PluginConfig>;
  modules: ModuleRecords;
  images: Immutable.Map<string, ImageRecord>;
  fonts: Immutable.Map<string, FontRecord>;
  theme: ApptileThemeConfig;
  settings: ApptileSettings;
  blueprintUUID: string | null;
  _transpiledBindings: SerializedBindings;
  _cachedDependencyGraph: any;
}

const defaultAppConfig: AppConfigParams = {
  title: 'New App',
  pages: Immutable.Map(),
  navigation: new NavigationConfig(defaultNavigationConfig),
  plugins: Immutable.Map(),
  modules: Immutable.Map(),
  images: Immutable.Map(),
  fonts: Immutable.Map(),
  theme: new ApptileThemeConfig(defaultApptileThemeConfig),
  settings: Immutable.Map(),
  blueprintUUID: null,
  _transpiledBindings: {raw: [], transpiled: []},
  _cachedDependencyGraph: undefined,
};

export class AppConfig extends Immutable.Record(defaultAppConfig, 'appConfig') {
  getPage(id: string): PageConfig {
    return this.pages.get(id)!;
  }

  addPage(title: string, pageConfig: PageConfig) {
    return this.set('pages', this.pages.set(title, pageConfig));
  }

  setNavigation(nav: NavigationConfig) {
    return this.set('navigation', nav);
  }

  getPlugin(id: string): PluginConfig {
    return this.plugins.get(id)!;
  }

  getGlobalPlugins(): Immutable.Map<string, PluginConfig> {
    return this.plugins as Immutable.Map<string, PluginConfig>;
  }

  getPagePlugins(pageId: string): Immutable.Map<string, PluginConfig> {
    return this.pages.getIn([pageId, 'plugins']) as Immutable.Map<string, PluginConfig>;
  }

  addPlugin(pluginId: string, pluginConfig: PluginConfig) {
    return this.set('plugins', this.plugins.set(pluginId, pluginConfig));
  }

  setPluginId(id: string, pluginConfig: PluginConfigType<any>): AppConfig {
    return this.setIn(['plugins', id], pluginConfig);
  }

  setTheme(theme: ApptileThemeConfig) {
    return this.set('theme', theme);
  }

  getTheme() {
    return this.get('theme');
  }

  getImageId(imageId: string): ImageRecord {
    return this.getIn(['images', imageId]) as ImageRecord;
  }

  getFonts() {
    return this.get('fonts');
  }

  setFonts(fonts: Immutable.Map<string, FontRecord>) {
    return this.set('fonts', fonts);
  }

  getSettingsFor(settingsKey: string): SettingsConfig {
    return this.settings.get(settingsKey, new SettingsConfig(defaultSettingsConfig));
  }

  setSettingsFor(settingsKey: string, settingsConfig: SettingsConfig) {
    return this.setIn(['settings', settingsKey], settingsConfig);
  }
}

export type EventType = 'query' | 'widget' | 'page' | 'action';
export type MethodType =
  | 'executeQuery'
  | 'getNextPage'
  | 'setValue'
  | 'navigate'
  | 'navigateReset'
  | 'navigateBack'
  | 'setPageTitle'
  | 'triggerToast'
  | 'triggerAction'
  | 'forwardModuleEvent'
  | 'sendPageAnalytics'
  | 'sendTrackAnalytics';

export interface EventRouteParam {
  routeParams: Immutable.Map<string, string>;
}

export interface EventHandlerShape {
  label: string;
  type: EventType;
  method: MethodType;
  pluginId: string;
  isGlobalPlugin: boolean;
  screenName?: string;
  prop?: string;
  value?: any;
  params: Immutable.Map<string, any>;
  hasCondition?: boolean;
  condition?: string | null | boolean;
}

const defaultEventHandler: EventHandlerShape = {
  label: '',
  type: 'query',
  method: 'trigger',
  pluginId: '',
  isGlobalPlugin: true,
  screenName: '',
  prop: '',
  value: null,
  params: Immutable.Map(),
  hasCondition: false,
  condition: '',
};

export interface EventsConfigParams {
  events?: Immutable.List<EventHandlerConfig>;
}

export class EventHandlerConfig extends Immutable.Record(defaultEventHandler, 'eventConfig') {
  constructor(params: any) {
    super(params);
  }
}

export class ModuleEventHandlerConfig extends Immutable.Record(
  {
    eventHandler: defaultEventHandler,
    isExposed: false,
    name: '',
    type: 'both',
  },
  'moduleEventConfig',
) {
  constructor(params: any) {
    super(params);
  }
}

// Module property-types type and ENUM array;
export const MODULE_PROPERTY_TYPES = ['property', 'query'];
export type ModulePropertyType = 'property' | 'query';

export type ModuleEditorLocationType = 'module' | 'style' | 'basic';
export interface ModuleEditorConfig {
  selector: Selector;
  value: any;
  label: string;
  editorType: PropertyEditorConfig<any> | undefined | null;
  mandatory: boolean;
  advanceProperty: boolean;
  basePlan: string;
}
export const defaultModuleEditorConfig: ModuleEditorConfig = {
  selector: [],
  value: undefined,
  label: '',
  editorType: null,
  mandatory: false,
  advanceProperty: false,
  basePlan: 'CORE',
};
export class ModuleEditorRecord extends Immutable.Record(defaultModuleEditorConfig, 'moduleEditorRecord') {
  constructor(params: any) {
    super(params);
  }
}

export interface ModuleRecordShape {
  moduleUUID: string;
  moduleSaveId: string | undefined;
  moduleName: string;
  inputs: string[];
  outputs: string[];
  queries: string[];
  events: string[];
  tags: string[];
  persistInputBindings: boolean;
  inputBindings: Immutable.Map<string, string>;
  editors: Immutable.OrderedMap<string, ModuleEditorConfig>;
  basicEditors: Immutable.OrderedMap<string, ModuleEditorConfig>;
  styleEditors: Immutable.OrderedMap<string, ModuleEditorConfig>;
  defaultEventHandlers: Immutable.List<ModuleEventHandlerConfig>;
  moduleConfig: Immutable.OrderedMap<string, PluginConfig>;
  isMovable: string;
  isDeletable: string;
  isRootLevelTile: boolean;
  isAITile: boolean;
}
export const defaultModuleRecord: ModuleRecordShape = {
  moduleUUID: '',
  moduleSaveId: undefined,
  moduleName: '',
  inputs: [],
  outputs: [],
  queries: [],
  events: [],
  tags: [],
  persistInputBindings: false,
  inputBindings: Immutable.Map(),
  defaultEventHandlers: Immutable.List(),
  editors: Immutable.OrderedMap(),
  basicEditors: Immutable.OrderedMap(),
  styleEditors: Immutable.OrderedMap(),
  moduleConfig: Immutable.OrderedMap(),
  isMovable: 'CORE',
  isDeletable: 'CORE',
  isRootLevelTile: false,
  isAITile: false,
};
export class ModuleRecord extends Immutable.Record(defaultModuleRecord, 'moduleRecord') {
  constructor(params: any) {
    super(params);
  }
  getTagsString() {
    return this.tags.join(', ');
  }
  setTagsString(tagsStr: string) {
    return this.set(
      'tags',
      tagsStr.split(',').map(str => str.trim()),
    );
  }
}
export type ModuleRecords = Immutable.Map<string, ModuleRecord>;

const customReplacer = (key, value, defaultReplacer) => {
  if (
    key === 'layout' &&
    typeof value === 'object' &&
    typeof value.namespace === 'object' //&&
    //typeof value.pluginId === 'string'
  ) {
    defaultReplacer(key, _.omitBy(value, _.isUndefined));
  }
  return defaultReplacer(key, value);
};

const customReviver = (key, value, defaultReviver) => {
  if (
    key === 'namespace' &&
    typeof value === 'object' &&
    typeof value.namespace === 'object' //&&
    //typeof value.pluginId === 'string'
  ) {
    return new PluginNamespaceImpl(value.namespace, value.pluginId);
  }
  if (typeof value === 'object' && value !== null && '$jsan' in value) return jsanutils.restore(value.$jsan);
  return defaultReviver(key, value);
};

// Note: never change the order serilizer it should same as AppConfigParams
export const SerializableRecordRefs = [
  PluginConfig,
  PageConfig,
  NavigationConfig,
  AppConfig,
  LayoutRecord,
  ApptileThemeConfig,
  EventHandlerConfig,
  ScreenConfig,
  NavigatorConfig,
  PageParamConfig,
  PluginNamespaceImpl,
  ModuleRecord,
  ImageRecord,
  FontRecord,
  SettingsConfig,
  ModuleEditorRecord,
  AnalyticsConfig,
  AnimationsConfig,
  TransitionsRecord,
  AnimatedTransitionRecord,
  AnimatedTransitionModifier,
];
const {stringify, parse, serialize} = immutable(Immutable, SerializableRecordRefs, customReplacer, customReviver);
export const RecordSerializer = {stringify, parse};

export type AppModelValues = Immutable.Map<string, any>;

interface AppModelShape {
  jsModel: any;
  values: AppModelValues;
  pageKeysToId: Immutable.Map<string, string>;
  pageKeysBooted: Immutable.Map<string, boolean>;
  dependencyGraph: IDependencyGraph;
  modelInitialized: boolean;
  evalContextStack: EvaluationContextStack;
  lastCommitTimestamp: number;
}

const defaultAppModelShape: AppModelShape = {
  jsModel: null,
  values: Immutable.Map<string, any>(),
  pageKeysToId: Immutable.Map<string, string>(),
  pageKeysBooted: Immutable.Map<string, boolean>(),
  dependencyGraph: null!,
  modelInitialized: false,
  evalContextStack: new EvaluationContextStack(),
  lastCommitTimestamp: 0,
};

export class AppModelRecord extends Immutable.Record(defaultAppModelShape, 'appModel') {
  constructor(params: Partial<AppModelShape>) {
    super(params);
  }

  initializeModel(): AppModelRecord {
    return this.set('modelInitialized', true);
  }

  setPageKeyToId(pageKey: string, pageId: string) {
    return this.set('pageKeysToId', this.pageKeysToId.set(pageKey, pageId));
  }

  setPageKeyBooted(pageKey: string, bBooted: boolean) {
    return this.set('pageKeysBooted', this.pageKeysBooted.set(pageKey, bBooted));
  }

  deletePageKeyToId(pageKey: string) {
    return this.set('pageKeysToId', this.pageKeysToId.remove(pageKey));
  }

  getPageKeysForId(pageId: string): Immutable.List<string> {
    return this.pageKeysToId
      .filter(val => val === pageId)
      .keySeq()
      .toList();
  }

  getPageId(pageKey: string) {
    return this.pageKeysToId.get(pageKey);
  }

  setModelValue(selector: any[], value: any): AppModelType {
    // logger.time('setModelValue');
    var newModel = this.set('values', this.values.setIn(selector, value));
    var jsModel = this.get('jsModel');
    if (jsModel === null || jsModel === undefined) {
      jsModel = this.values.toJS();
    }
    let jsModelValue = value;
    if (Immutable.isImmutable(value)) {
      jsModelValue = value.toJS();
    }
    newModel = newModel.set('jsModel', _.set(jsModel, selector, jsModelValue));
    // logger.timeEnd('setModelValue');
    return newModel;
  }

  setAllValues(evalResults: Array<{selector: string[]; value: any}>) {
    const newSelf = this.withMutations(self => {
      for (let result of evalResults) {
        self = self.set('values', self.values.setIn(result.selector, result.value));
        var jsModel = self.get('jsModel');
        if (jsModel === null || jsModel === undefined) {
          jsModel = self.values.toJS();
        }

        let jsModelValue = result.value;
        if (Immutable.isImmutable(result.value)) {
          jsModelValue = result.value.toJS();
        } else {
          jsModelValue = result.value;
        }
        self = self.set('jsModel', _.set(jsModel, result.selector, jsModelValue));
      }
    });
    return newSelf;
  }

  setAllValuesNoJSModel(evalResults: Array<{selector: string[]; value: any}>) {
    var jsModel = this.get('jsModel');
    let modelValues = this.get('values');
    modelValues = modelValues.withMutations(mutableValues => {
      for (let index = 0; index < evalResults.length; ++index) {
        const result = evalResults[index];
        mutableValues = mutableValues.setIn(result.selector, result.value);
      }
    });
    return this.set('values', modelValues);
  }

  mergeEvalResults(results: EvalResult[]) {
    // var jsModel = this.get('jsModel');
    let modelValues = this.get('values');
    modelValues = modelValues.withMutations(mutableValues => {
      for (let index = 0; index < results.length; ++index) {
        const result = results[index];
        mutableValues.setIn(result.selector, result.value);
        // _.set(jsModel, result.selector, Immutable.isImmutable(result.value) ? result.value.toJS() : result.value);
      }
    });
    return this.set('values', modelValues); // .set('jsModel', jsModel);
  }

  getModelValue(selector: any[]): any {
    return this.values.getIn(selector);
  }

  // deleteModelValue(selector: any[]): AppModelType {
  //   let newModel = this.set('values', this.values.deleteIn(selector));
  //   return newModel.set('jsModel', newModel.get('values').toJS());
  // }

  deleteModelValue(selector: any[]): AppModelType {
    let newModel = this.set('values', this.values.deleteIn(selector));
    var jsModel = this.get('jsModel');
    _.unset(jsModel, selector);
    return newModel.set('jsModel', jsModel);
  }

  updateApptileNavigation(navigation: any): AppModelType {
    let newModel = this.set('values', this.values.set('ApptileNavigation', navigation));
    return newModel.setIn(['jsModel', 'ApptileNavigation'], newModel.getIn(['values', 'ApptileNavigation']).toJS());
  }

  setDependencyGraph(depGraph: IDependencyGraph) {
    return this.set('dependencyGraph', depGraph);
  }

  getPluginModel(pageKey: string, pluginId: string) {
    if (pageKey) {
      return this.getModelValue([pageKey, 'plugins', pluginId]);
    }
    return this.getModelValue([pluginId]);
  }

  pluginParent(pageKey: string, pluginId: string) {
    if (!this.dependencyGraph) {
      return null;
    }
    return (this.dependencyGraph as any).parentContainers.get(strsel([pageKey, 'plugins', pluginId]));
  }

  pluginInContainer(pageKey: string, pluginId: string, containerId: string): boolean {
    const parent = this.pluginParent(pageKey, pluginId);
    if (parent) {
      const inContainerId = parent === containerId;
      if (inContainerId) {
        return true;
      }
      return this.pluginInContainer(pageKey, parent, containerId);
    }
    return false;
  }

  pluginInListView(pageKey: string, pluginId: string): {inListView: boolean; instances: number | null} {
    const lvParentId = this.dependencyGraph?.getAncestorListViewSelector(`${pageKey}.plugins.${pluginId}`);
    // const parent = this.pluginParent(pageKey, pluginId);
    // if (parent) {
    //   // const inListView = this.getModelValue([pageKey, 'plugins', parent])?.get('pluginType') === 'ListViewWidget';

    //   // recursively check if we have a listview ancestor
    //   return this.pluginInListView(pageKey, parent);
    // }

    if (lvParentId) {
      try {
        const instances = (this.getModelValue([pageKey, 'plugins', lvParentId]) as any).get('instances') || '0';
        return {inListView: true, instances: Number(instances)};
      } catch (err) {
        return {inListView: true, instances: 0};
      }
    }
    return {inListView: false, instances: null};
  }

  updateTimestamp(): AppModelType {
    return this.set('lastCommitTimestamp', Date.now());
  }
}

export type AppModelType = Omit<
  AppModelRecord,
  | 'set'
  | 'update'
  | 'merge'
  | 'mergeDeep'
  | 'mergeWith'
  | 'mergeDeepWith'
  | 'delete'
  | 'setIn'
  | 'updateIn'
  | 'mergeIn'
  | 'mergeDeepIn'
  | 'deleteIn'
  | 'remove'
  | 'removeIn'
>;

interface ApptileThemeModelShape {
  isDark: boolean;
  isDarkModeSupported: boolean;
  dependencyGraph: IThemeDependencyGraph;
  version: string;
  activeThemeConfig: ApptileThemeActiveConfig;
  themeValue: Partial<ApptileThemeDefinitions>;
  modelInitialized: boolean;
}

const defaultApptileThemeModelShape: ApptileThemeModelShape = {
  isDark: false,
  isDarkModeSupported: false,
  dependencyGraph: null!,
  version: '',
  activeThemeConfig: {
    version: '',
    isDarkModeSupported: false,
    config: {},
  },
  themeValue: {},
  modelInitialized: false,
};

export class ApptileThemeModelRecord extends Immutable.Record(defaultApptileThemeModelShape, 'themeModel') {
  constructor(params: Partial<ApptileThemeModelShape>) {
    super(params);
  }

  initializeModel(): ApptileThemeModelType {
    return this.set('modelInitialized', true);
  }

  setModelValue(selector: any[], value: any): ApptileThemeModelType {
    var themeValue = this.get('themeValue');
    return this.set('themeValue', _.set(themeValue, selector, value));
  }

  getModelValue(selector: any[]): any {
    return _.get(selector, this.themeValue);
  }

  deleteModelValue(selector: any[]): ApptileThemeModelType {
    return this.set('themeValue', _.unset(this.themeValue, selector));
  }

  clearThemeValue(): ApptileThemeModelType {
    return this.set('themeValue', {});
  }

  setDependencyGraph(depGraph: IThemeDependencyGraph): ApptileThemeModelType {
    return this.set('dependencyGraph', depGraph);
  }

  setActiveThemeConfig(activeThemeConfig: ApptileThemeActiveConfig) {
    return this.set('activeThemeConfig', activeThemeConfig);
  }
}

export type ApptileThemeModelType = Omit<
  ApptileThemeModelRecord,
  | 'set'
  | 'update'
  | 'merge'
  | 'mergeDeep'
  | 'mergeWith'
  | 'mergeDeepWith'
  | 'delete'
  | 'setIn'
  | 'updateIn'
  | 'mergeIn'
  | 'mergeDeepIn'
  | 'deleteIn'
  | 'remove'
  | 'removeIn'
>;

export interface AppPageQuerySettings {
  runWhenPageLoads: boolean;
  runOnPageFocus: boolean;
  runWhenModelUpdates: boolean;
  loading: boolean;
  // queryRefreshTime: string;
  // queryThrottleTime: string;
  // queryTimeout: string;
  // queryTriggerDelay: string;
}

export interface AppPageQueryTriggers {
  // transformer: string;
  // enableTransformer: boolean;
  // errorTransformer: string;
  // enableErrorTransformer: boolean;
}

export interface AppPageQueryData {
  data: any;
  metadata: any;
  rawData: any;
  timestamp: number;
  errors: any;
  hasError: boolean;
}

export type AppPageQueryConfigParams = AppPageQuerySettings & AppPageQueryTriggers & AppPageQueryData;

export interface ModuleCreationParams {
  pageId?: string;
  pageKey?: string;
  pluginId?: string;
  variablesBySelector: Record<string, {bindingVars: Selector[]; bindingString: string}>;
  eventHandlersMap: Record<string, Record<string, Array<{index: number; event: EventHandlerConfig}>>>;
  inputSelectorStrings: string[];
  modulePluginIds: string[];
  moduleParentContainerId?: string;
  isOpen: boolean;
}

export type BindingError = {
  transpiledFn: string;
  error: string;
};

export interface Toast {
  id: string;
  content: string;
  appearances: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  cancellable?: boolean;
}
