import React from 'react';
import {WidgetProps} from '../../plugins/widgets/widget';
import useApptileAnimatedStyles from './useApptileAnimatedStyles';

export const connectAnimatedWidget = (Widget: React.ComponentType<WidgetProps>): React.FC<any> => {
  const WidgetWithAnimations: React.FC<WidgetProps> = React.forwardRef((props: WidgetProps, ref) => {
    const {id, pageKey, instance, config} = props;
    const animConfig = config?.get('animations');
    const isAnimated = !!animConfig?.enabled;
    const animationWidgetProps = useApptileAnimatedStyles(pageKey, id, instance, animConfig, config);

    return <Widget {...props} isAnimated={isAnimated} animations={animationWidgetProps} ref={ref} />;
  });
  return WidgetWithAnimations;
};
