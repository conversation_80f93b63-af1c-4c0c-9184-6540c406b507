import AsyncStorage from '@react-native-async-storage/async-storage';

class LocalStorage {
  namespace?: string;

  setNamespace = (namespace: string) => {
    this.namespace = namespace;
  };
  getKey = (key: string) => {
    return [this.namespace, key].filter(Boolean).join('_');
  };
  setValue = async (key: string, value: string | Record<string, any>) => {
    // logger.time(`setValue_${key}`);
    if (!key) return;
    let finalValue = '';
    try {
      if (value) {
        finalValue = typeof value === 'string' ? value : JSON.stringify(value);
      }
      await AsyncStorage.setItem(this.getKey(key), finalValue);
    } catch (e) {
      logger.error(`setValue_${key}`, e);
    } finally {
      // logger.timeEnd(`setValue_${key}`, value);
    }
  };
  getValue = async (key: string): Promise<string | number | Record<string, any> | any[] | null> => {
    // logger.time(`getValue_${key}`);
    if (!key) return null;
    let value = await AsyncStorage.getItem(this.getKey(key));
    try {
      value = value && JSON.parse(value);
    } catch (e) {
      // not a json, IGNORE
    } finally {
      // logger.timeEnd(`getValue_${key}`, value);
    }
    return value;
  };

  removeItem = async (key: string) => {
    // logger.time(`setValue_${key}`);
    if (!key) {
      logger.error(`removeItem_${key}`);
      return
    };
    try {
      await AsyncStorage.removeItem(this.getKey(key));
    } catch (e) {
      logger.error(`removeItem_${key}`, e);
    } finally {
      // logger.timeEnd(`setValue_${key}`, value);
    }
  };

  // Code used to divide sting in chunks and save in local-storage to avoid 6mb limit on android
  // BUT should be avoided and use Filesystem instead
  // async setValueInChunks(key: string, value: string): Promise<void> {
  //   for (let i = 0; i < 4; i++) {
  //     await this.setValue(`${key}_chunk${i}`, value.slice((i * value.length) / 4, ((i + 1) * value.length) / 4));
  //   }
  // }
  // async getValueFromChunks(key: string): Promise<string> {
  //   let value = '';
  //   for (let i = 0; i < 4; i++) {
  //     const str = await this.getValue(`${key}_chunk${i}`);
  //     if (str) value += str;
  //   }
  //   return value;
  // }
}

const localStorage = new LocalStorage();
export default localStorage;
