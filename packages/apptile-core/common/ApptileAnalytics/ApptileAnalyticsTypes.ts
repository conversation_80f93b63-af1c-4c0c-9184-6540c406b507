export interface IApptileAnalyticsAppDetails {
  name: string;
  version: string;
  build: string;
}
export interface IApptileAnalyticsDeviceDetails {
  type: string;
  id: string;
  advertisingId: string;
  manufacturer: string;
  model: string;
  name: string;
}
export interface IApptileAnalyticsNetworkDetails {
  bluetooth: boolean;
  carrier: string;
  cellular: boolean;
  wifi: boolean;
}
export interface IApptileAnalyticsOSDetails {
  name: string;
  version: string;
}
export interface IApptileAnalyticsPlatformDetails {
  bundleVersion: string;
  appId: string;
  appSaveId: string;
}
export interface IApptileAnalyticsScreenDetails {
  density: number;
  height: number;
  width: number;
}
export interface IApptileAnalyticsContext {
  app: IApptileAnalyticsAppDetails;
  device: IApptileAnalyticsDeviceDetails;
  locale: string;
  network: IApptileAnalyticsNetworkDetails;
  os: IApptileAnalyticsOSDetails;
  apptilePlatform: IApptileAnalyticsPlatformDetails;
  screen: IApptileAnalyticsScreenDetails;
}

export const ApptileAnalyticsEventTypes = ['page', 'track'] as const;
export type ApptileAnalyticsEventType = typeof ApptileAnalyticsEventTypes[number];

export interface IApptileAnalyticsEvent {
  userId?: string;
  context: IApptileAnalyticsContext;
  event: string;
  properties: Record<string, any>;
  timestamp: string;
  type: ApptileAnalyticsEventType;
}
