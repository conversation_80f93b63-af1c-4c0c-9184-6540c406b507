import {OrderedMap} from 'immutable';
import _ from 'lodash';
import {createSelector, createSelectorCreator, defaultMemoize} from 'reselect';
import {PluginConfig} from '../datatypes/types';

export const createDeepEqualSelector: typeof createSelector = createSelectorCreator(
  defaultMemoize,
  (a: any, b: any) => {
    const isEq = _.isEqual(a, b);
    // if (!isEq) logger.info('DeepEquals', isEq, a ? Object.keys(a) : a, b ? Object.keys(b) : b);
    return isEq;
  },
);

export const immutableDeepEqualsFn = (a: any, b: any) => {
  /*
  const isEq = a === b || (a && b && _.isEqual(a.toIndexedSeq().toJS(), b.toIndexedSeq().toJS()));
  // if (!isEq) logger.info('ImmutableDeepEquals', isEq, a, b);
  return isEq;
  */
  if (a === b) {
    return true;
  } else if (!a || !b) {
    return false;
  } else {
    return a.equals(b);
  }
};

export const createImmutableDeepEqualSelector: typeof createSelector = createSelectorCreator(
  defaultMemoize,
  immutableDeepEqualsFn,
);

export const immutableEqualsFn = (a: Immutable.Collection<any, any>, b: Immutable.Collection<any, any>) => {
  const isEq = a ? a.equals(b) : false;
  // if (!isEq) logger.info('immutableEqualsFn', isEq, a, b);
  return isEq;
};

export const createImmutableEqualitySelector: typeof createSelector = createSelectorCreator(
  defaultMemoize,
  immutableEqualsFn,
);

// const hashFn = (...args) => args.reduce((acc, val) => acc + '-' + JSON.stringify(val), '');
// export const createUnboundCacheSelector: typeof createSelector = createSelectorCreator(_.memoize, hashFn);

export const filterAndSortWidgets = (
  plugins?: OrderedMap<string, PluginConfig>,
): OrderedMap<string, PluginConfig> | undefined => {
  return plugins?.filter(plugin => plugin.type === 'widget');
};

export const makeBoolean = (val: unknown): boolean => {
  return val === 'false' || val === '0' || (_.isObjectLike(val) && _.isEmpty(val)) ? false : !!val;
};
