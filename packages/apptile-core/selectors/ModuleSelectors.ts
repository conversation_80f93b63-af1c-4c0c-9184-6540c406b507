import Immutable from 'immutable';
import _ from 'lodash';
import {createSelector} from 'reselect';
import {AppConfig, ModuleRecord, PageConfig, PluginConfig} from '../common/datatypes/types';
import {RootState} from '../store/RootReducer';
import {selectAppConfig} from './AppConfigSelector';

export const selectModulesCache = createSelector(selectAppConfig, (appConfig: AppConfig) => appConfig?.modules);

export const selectModuleByUUID = createSelector(
  selectAppConfig,
  (state: RootState, moduleUUID: string) => moduleUUID,
  (appConfig: AppConfig, moduleUUID: string) => appConfig?.modules?.get(moduleUUID),
);
