import Immutable from 'immutable';
import _ from 'lodash';
import {mapValues} from 'lodash';
import {createSelector} from 'reselect';
import {selectEditorState} from '.';
import {AppModelValues, PluginConfig, PluginConfigType} from '../common/datatypes/types';
import {
  createImmutableDeepEqualSelector,
  immutableEqualsFn,
  immutableDeepEqualsFn,
  filterAndSortWidgets,
} from '../common/utils';
import {propsSelectorWithProps} from '../plugins/widgets/common/containers/containerTypes';
import {WidgetLayout, WidgetLayoutMap} from '../plugins/widgets/widgetLayout';
import {RootState} from '../store/RootReducer';
import {selectAppModel} from './AppModelSelector';
import {isJSBinding} from '../common/JSBinding/JSBindingUtils';
import {selectPageConfigForPage} from './PageSelector';
import {getCachedPluginModelsFromPageConfig} from '../sagas/AppModelSaga';

// const selectPluginsConfigForPage = (state: RootState, ownProps: {pageId: string; pageKey: string}) =>
//   state.appConfig?.current?.pages.get(ownProps.pageId)?.plugins;

// const selectPluginModelsForPage = (state: RootState, ownProps: {pageId: string; pageKey: string}) => {
//   logger.info(`selectPluginModelsForPage ${ownProps.pageKey}`);
//   return state.appModel?.getModelValue([ownProps.pageKey, 'plugins']);
// };

const selectPageKeyFromPropsArg = (_, pageId, pageKey) => pageKey;
const selectPageIdFromPropsArg = (_, pageId, pageKey) => pageId;
const selectEditorSelectedPluginSel = state => state.editor?.selectedPluginConfigSel;

const selectPluginsConfigByPageId = createSelector(
  (state: RootState) => state,
  selectPageIdFromPropsArg,
  (state, pageId) => {
    // logger.info(`selectPluginsConfigByPageId ${pageId}`);
    return state.appConfig?.current?.pages.get(pageId)?.plugins;
  },
  {
    memoizeOptions: {
      maxSize: 64,
    },
  },
);

const selectPluginsModelMapByPageKey = createSelector(
  (state: RootState) => state,
  selectPageKeyFromPropsArg,
  (state, pageKey) => {
    // logger.info(`selectPluginsModelMapByPageKey ${pageKey}`);
    return state.appModel?.getModelValue([pageKey, 'plugins']);
  },
  {
    memoizeOptions: {
      maxSize: 64,
    },
  },
);

const filteredWidgetsSelector = createSelector(
  selectPluginsConfigByPageId,
  pluginTemplates => {
    // logger.info(`filteredWidgetsSelector`);
    return filterAndSortWidgets(pluginTemplates);
  },
  {
    memoizeOptions: {
      maxSize: 64,
      resultEqualityCheck: immutableDeepEqualsFn,
    },
  },
);

type WidgetModelLayoutValues = {
  [key: string]: WidgetLayout;
};

const widgetLayoutMapCreator = (
  pageConfig,
  pluginConfigs,
  selectedPluginConfigSel,
  pageId,
  pageKey,
): WidgetLayoutMap => {
  // logger.time(`widgetLayoutMapCreator ${pageKey}`);
  let values: WidgetLayoutMap = Immutable.OrderedMap();

  pluginConfigs?.forEach((widget: PluginConfigType<any>, id) => {
    // const model: Map<string, unknown> = pageModel?.get(id);
    if (!widget) return;

    // // Hiding list view children is handled downstream by the list view since a given
    // // plugin isn't necessarily hidden/not hidden in all cell instances at once
    let hidden = false;
    // if (!appModel?.pluginInListView(id)?.inListView) {
    // hidden = !!model.get('hidden');

    // // Hide the plugin if there is a non-empty hidden plugin template and it has not yet been evalutated (to prevent flashing hidden plugins)
    // const hiddenTemplate = pluginConfigs.get(id)?.config.get('hidden');

    // const hasHiddenTemplate = !_.isEmpty(hiddenTemplate);
    // if (hasHiddenTemplate && !model.has('hidden')) {
    //   hidden = true;
    // }
    // }

    const {subtype: widgetType, layout, config, namespace} = widget;
    const selected: boolean =
      selectedPluginConfigSel && selectedPluginConfigSel[0] === pageKey && selectedPluginConfigSel[2] === id;
    // // modals manually handle dynamic heights, so we don't include the container key
    // const isModalLike = widgetTypeIsModalLike(widgetType)
    const events = config?.get('events');
    let areEventsDynamic = false;
    if (events) {
      events.map(eventHandler => {
        if (areEventsDynamic) return;

        if (typeof eventHandler?.condition === 'string' && isJSBinding(eventHandler?.condition)) {
          areEventsDynamic = true;
        }

        eventHandler?.params?.map(val => {
          if (typeof val === 'string' && isJSBinding(val)) {
            areEventsDynamic = true;
          }
        });
      });
    }
    var isDynamic: boolean = config?.filter(val => typeof val === 'string' && isJSBinding(val)).size > 0;
    isDynamic =
      isDynamic ||
      areEventsDynamic ||
      (typeof layout.hidden === 'string' && isJSBinding(layout.hidden)) ||
      widgetType === 'ListViewWidget' ||
      widgetType === 'ModalWidget';

    values = values.set(id, {
      id,
      pageId,
      pageKey: pageKey,
      widgetType,
      layout,
      config: widget,
      hidden,
      selected,
      isDynamic,
      inModule: namespace !== undefined,
      cachedModel: widget._cachedModel,
    });
  });
  // logger.timeEnd(`widgetLayoutMapCreator ${pageKey}`);
  return values;
};

// const _modelLayoutValuesSelector = createSelector(
//   filteredWidgetsSelector,
//   selectEditorSelectedPluginSel,
//   selectPageIdFromPropsArg,
//   selectPageKeyFromPropsArg,
//   widgetLayoutMapCreator,
//   {
//     memoizeOptions: {
//       resultEqualityCheck: immutableDeepEqualsFn,
//     },
//   },
// );

export const makePageWidgetLayoutsSelector = () => {
  // logger.info(`MAKING makePageWidgetLayoutsSelector`);
  return createSelector(
    selectPageConfigForPage,
    filteredWidgetsSelector,
    selectEditorSelectedPluginSel,
    selectPageIdFromPropsArg,
    selectPageKeyFromPropsArg,
    widgetLayoutMapCreator,
    {
      memoizeOptions: {
        resultEqualityCheck: immutableDeepEqualsFn,
      },
    },
  );
};

// export const makePageWidgetLayoutsSelector = () => {
//   // logger.info(`MAKING makePageWidgetLayoutsSelector`);
//   return createImmutableDeepEqualSelector(_modelLayoutValuesSelector, (v: WidgetLayoutMap) => v);
// };

const unfilteredPluginsSelector = createSelector(
  selectPluginsConfigByPageId,
  selectPluginsModelMapByPageKey,
  (pluginTemplates, appModel) => {
    return pluginTemplates?.filter(widget => appModel?.get(widget.id));
  },
);

const _modelPluginLayoutValuesSelector = createSelector(
  selectPageConfigForPage,
  unfilteredPluginsSelector,
  selectEditorSelectedPluginSel,
  selectPageIdFromPropsArg,
  selectPageKeyFromPropsArg,
  widgetLayoutMapCreator,
);

// const modelPluginLayoutValuesSelector = createImmutableDeepEqualSelector(
//   _modelPluginLayoutValuesSelector,
//   (v: WidgetLayoutMap) => v,
// );

export const selectPagePluginLayouts = createSelector(_modelPluginLayoutValuesSelector, pluginLayoutValues =>
  pluginLayoutValues.mapEntries(([id, pluginLayout]) => {
    return [id, {...pluginLayout, hidden: false}];
  }),
);
