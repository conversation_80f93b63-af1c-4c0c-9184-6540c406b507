import React, {useEffect, useState} from 'react';
import {Platform, View, StyleSheet, Modal} from 'react-native';
import { NativeModules } from 'react-native';
import { ImageComponent } from 'apptile-core';

const {RNApptile} = NativeModules;

import RNGetValues from '../../lib/RNGetValues';

const styles = StyleSheet.create({
  root: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  textWrapper: {
    position: 'absolute',
    right: 0,
    bottom: '18%',
    left: 0,
  },
  text: {
    textAlign: 'center',
  },
});

export let hideJSSplashScreen: () => void;
export let showJSSplashScreen: () => void;
export let setSplashScreenMessage: (msg: string) => void;


// ForNativesplash (Don't remove)
let isSplashHiddenAtStart = false;

const JSSplashScreen: React.FC = ({children, splashImageSource}) => {
  const [isRendered, setIsRendered] = useState(false);
  const [isHidden, setIsHidden] = useState(isSplashHiddenAtStart);
  const [message, setMessage] = useState('Loading 1s and 0s...');

  useEffect(() => {
    hideJSSplashScreen = () => {
      // debugger
      setIsHidden(true);
      RNApptile.notifyJSReady();
    }
    showJSSplashScreen = () => setIsHidden(false);
    setSplashScreenMessage = msg => setMessage(msg);
    setIsRendered(true);

    return () => {
      hideJSSplashScreen = () => {};
    };
  }, []);

  // const translateY = useSharedValue(0);
  // useEffect(() => {
  //   translateY.value = withDelay(
  //     240,
  //     withRepeat(
  //       withSequence(
  //         withTiming(-12, {duration: 740, easing: Easing.inOut(Easing.ease)}),
  //         withTiming(0, {duration: 740, easing: Easing.bounce}),
  //       ),
  //       Infinity,
  //       true,
  //     ),
  //   );
  // }, [translateY]);
  // const animatedStyles = useAnimatedStyle(() => ({transform: [{translateY: translateY.value}]}));

  const [isPreviewApp, setIsPreviewApp] = useState(false);
  useEffect(() => {
    RNGetValues('APPTILE_IS_DISTRIBUTED_APP').then(isDistributedApp => {
      if (!__DEV__ && !isDistributedApp) setIsPreviewApp(true);
    });
  }, []);

  return (
    <>
      {isRendered && children}
      {!isHidden && (
        <Wrapper>
          <View style={styles.container}>
            {/* {isPreviewApp && <ActivityIndicator size="large" />} */}
            {(!isPreviewApp) && (
              <ImageComponent style={styles.image} source={splashImageSource} resizeMode="cover" />
            )}
          </View>
        </Wrapper>
      )}
    </>
  );
};

const Wrapper: React.FC = ({children}) => {
  return Platform.OS === 'android' ? (
    <Modal statusBarTranslucent transparent>
      {children}
    </Modal>
  ) : (
    <View style={styles.root}>{children}</View>
  );
};

export default JSSplashScreen;
