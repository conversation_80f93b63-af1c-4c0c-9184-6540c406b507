import { uniqueId } from 'lodash';
import React from 'react';
import {View, StyleSheet} from 'react-native';

import Svg, {Rect, G, Defs, LinearGradient, Stop} from 'react-native-svg';

const ApptileLoadingStatePlaceHolder = props => {
  const {layoutStyles} = props;
  const rid = uniqueId();

  return (
    <View style={[layoutStyles, {flex: 1, borderRadius: 4, overflow: 'hidden'}]}>
      <View style={[StyleSheet.absoluteFill]}>
        <Svg height="100%" width="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
          <Defs>
            <LinearGradient id={`grad-${rid}`} x1="0" y1="0" x2="1" y2="0">
              <Stop offset="0" stopColor="#ddd" stopOpacity="1" />
              <Stop offset="0.5" stopColor="#f8f8f8" stopOpacity="1" />
              <Stop offset="1" stopColor="#ddd" stopOpacity="1" />
            </LinearGradient>
          </Defs>
          <G>
            <Rect x="0" y="0" width="200" height="100" stroke="none" strokeWidth="0" fill={`url(#grad-${rid})`} />
          </G>
        </Svg>
      </View>
    </View>
  );
};

export default ApptileLoadingStatePlaceHolder;
