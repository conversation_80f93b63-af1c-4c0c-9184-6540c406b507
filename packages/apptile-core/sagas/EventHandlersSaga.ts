import {put, call, all, takeEvery, select, take, spawn} from 'redux-saga/effects';
import {
  AnalyticsEventPayload,
  commitStageModel,
  DispatchAction,
  DispatchActions,
  DispatchEmptyAction,
  ForwardModuleEventPayload,
  NavigateToScreenPayload,
  TriggerActionPayload,
  triggerPageEvent,
  TriggerQueryPayload,
} from '../actions/DispatchActions';
import {SagaIterator} from '@redux-saga/types';
import {apptileNavigationSelector, selectStageModel, selectAppModel} from '../selectors/AppModelSelector';
import {execOnPluginUpdate} from './AppModelSaga';
import _ from 'lodash';
import {goBack, navDispatch, navigate} from '../views/navigation/ApptileRootNavigationHelpers';
import {CommonActions} from '@react-navigation/core';
import {getAppDispatch} from '../common/utils/dispatcher';
import {
  getScreenNamePaths,
  getScreenNavigationParams,
  makeResetStateFromPath,
} from '../common/navigation/NavigationHelpers';
import {selectAppConfig} from '../selectors/AppConfigSelector';
import {AppConfig, AppModelType} from '../common/datatypes/types';
import {NAMESPACE_SPERATOR} from '../constants/modelConstants';
import { triggerCustomEventListener } from '../common/utils/CustomEventSystem';
// TODO(gaurav) DONE eventbus
// import {ApptileAnalytics} from '../common/ApptileAnalytics';

function* triggerQuerySaga(action: DispatchAction<TriggerQueryPayload>): SagaIterator {
  const {selector, options} = action.payload;

  try {
    // const pageModels = yield select(selectPageModels);
    const stageModel = yield select(selectStageModel);
    const pageModels = stageModel?.getModelValue([]);

    const queryModel = pageModels.getIn(selector);
    if (!queryModel) throw 'triggerQuerySaga: Query Not Found.';
    const pluginType = queryModel?.get('pluginType');

    yield call(
      execOnPluginUpdate,
      {
        id: queryModel?.get('id'),
        pluginType: pluginType,
        pageKey: queryModel?.get('pageKey'),
      },
      null,
      true,
      false,
      options,
    );
    yield put({
      type: DispatchActions.APPPAGE_TRIGGER_QUERY_FINISHED,
    });
  } catch (e) {
    yield put({
      type: DispatchActions.APPPAGE_TRIGGER_QUERY_ERROR,
      payload: {
        e,
        selector,
      },
    });
  }
}

function* navigationGoBack(action: DispatchEmptyAction): SagaIterator {
  goBack();
}

function* navigateToScreen(action: DispatchAction<NavigateToScreenPayload>): SagaIterator {
  try {
    const {screenName, params} = action.payload;
    const apptileNavigation = yield select(apptileNavigationSelector);
    const rootScreens = apptileNavigation?.screens;
    logger.info('Navigating to Screen: ', screenName, params);
    if (!rootScreens) {
      throw 'Navigation screens not found in model !';
    }
    const screenPaths = _.fromPairs(getScreenNamePaths(rootScreens));
    const screenPath = screenPaths[screenName];
    if (!screenPath) {
      throw 'Navigation screen not found in navigation !';
    }
    if (screenPath.length === 1) navigate(screenPath[0], _.isEmpty(params) ? undefined : params);
    else {
      navigate(screenPath[0], getScreenNavigationParams(screenPath, params));
    }
  } catch (e) {
    logger.error(e);
  }
}

function* handleNavigateToScreenReset(action: DispatchAction<NavigateToScreenPayload>): SagaIterator {
  try {
    const {screenName, params} = action.payload;
    const appModel: AppModelType = yield select(selectAppModel);
    const stageModel: AppModelType = yield select(selectStageModel);
    if (appModel.lastCommitTimestamp < stageModel.lastCommitTimestamp) {
      yield put(commitStageModel());
      yield take(DispatchActions.FINALIZE_COMMIT_APP_MODEL);
    }
    const apptileNavigation = yield select(apptileNavigationSelector);
    const rootScreens = apptileNavigation?.screens;
    logger.info('Navigating to Screen Reset: ', screenName, params);
    if (!rootScreens) {
      throw 'Navigation screens not found in model !';
    }
    const screenPaths = _.fromPairs(getScreenNamePaths(rootScreens));
    const screenPath = screenPaths[screenName];
    if (!screenPath) {
      throw 'Navigation screen not found in navigation !';
    }
    // logger.info('Navigation Reset Screen path', screenPath);
    navDispatch(CommonActions.reset(makeResetStateFromPath(screenPath, params)));
  } catch (e) {
    logger.error(e);
  }
}

function* handleTriggerActionSaga(action: DispatchAction<TriggerActionPayload>): SagaIterator {
  const {pluginConfig, pluginModel, pluginSelector, eventModelJS} = action.payload;
  const {value, params} = eventModelJS;
  const appModel: AppModelType = yield select(selectStageModel);
  const appConfig: AppConfig = yield select(selectAppConfig);
  const actionName = value;

  const actionHandler = pluginModel.get(actionName);
  const dispatch = getAppDispatch();
  if (actionHandler) {
    // logger.info('calling the function');
    yield spawn(function* () {
      yield call(actionHandler, dispatch, pluginConfig, pluginModel, pluginSelector, params, appConfig, appModel);
    });
    // logger.info('after fork');
  }
}

function* handleForwardModuleEvent(action: DispatchAction<ForwardModuleEventPayload>): SagaIterator {
  const {pageKey, pluginId, instance, eventModelJS} = action.payload;
  const {value, params} = eventModelJS;
  const eventName = value;
  const appConfig: AppConfig = yield select(selectAppConfig);
  const appModel: AppModelType = yield select(selectStageModel);
  const pageId = appModel.getPageId(pageKey);

  const pluginConfig: PluginConfigType = pageId
    ? appConfig?.getIn(['pages', pageId, 'plugins', pluginId])
    : appConfig?.getIn([pluginId]);

  if (pluginConfig && !_.isEmpty(pluginConfig.namespace?.getNamespace())) {
    const namespaceArr = pluginConfig.namespace?.getNamespace();
    const namespacePrefix = namespaceArr.join(NAMESPACE_SPERATOR);
    const pagePlugins = appConfig?.getPagePlugins(pageId);
    for (let [pagePluginId, pagePluginConfig] of pagePlugins) {
      if (
        pagePluginConfig.subtype === 'ModuleInstance' &&
        pagePluginConfig.config.get('childNamespace') === _.last(namespaceArr)
      ) {
        const {inListView} = appModel.pluginInListView(pageKey, pagePluginConfig.id);
        yield put(triggerPageEvent(pageKey, pagePluginConfig.id, inListView ? instance : undefined, eventName, params));
      }
    }
  }
  return [];
}

export function* handleAnalyticsEvent(action: DispatchAction<AnalyticsEventPayload>): SagaIterator {
  const {type, name, data} = action.payload;
  // TODO(gaurav) DONE eventbus
  // ApptileAnalytics.sendEvent(type, name, data);
  triggerCustomEventListener('ApptileAnalyticsSendEvent', type, name, data);
}

export default function* eventHandlersSaga(): SagaIterator {
  yield all([
    takeEvery(DispatchActions.APPPAGE_TRIGGER_QUERY, triggerQuerySaga),
    takeEvery(DispatchActions.APPNAV_GO_BACK, navigationGoBack),
    takeEvery(DispatchActions.APPNAV_NAVIGATE, navigateToScreen),
    takeEvery(DispatchActions.APPNAV_NAVIGATE_RESET, handleNavigateToScreenReset),
    takeEvery(DispatchActions.APPPAGE_TRIGGER_ACTION, handleTriggerActionSaga),
    takeEvery(DispatchActions.APPTILE_ANALYTICS_EVENT, handleAnalyticsEvent),
    takeEvery(DispatchActions.FORWARD_MODULE_EVENT, handleForwardModuleEvent),
  ]);
}
