import {SagaIterator} from '@redux-saga/types';
import Immutable from 'immutable';
import _, {} from 'lodash';
import {all, call, put, putResolve, select, spawn, take, takeEvery, takeLatest} from 'redux-saga/effects';
import {
  FetchAppConfigPayload, fetchAppConfig,
} from '../actions/AppConfigActions';
import {
  DispatchAction,
  DispatchActions,
} from '../actions/DispatchActions';
import AppConfigApi, { FetchAppResponse } from '../api/AppConfigApi';
import {
  AppConfig,
  EventHandlerConfig,
  ModuleRecord,
  NavigatorConfig,
  PageConfig,
  PluginConfig,
  ScreenConfig,
} from '../common/datatypes/types';
// TODO(gaurav) DONE move to eventbus
// import {makeToast} from '../../web/actions/toastActions';

import {
  GetRegisteredConfig,
  GetRegisteredPluginConfigMigrationFn,
  PluginUpdateConfig,
} from '../plugins/plugin';
import {selectAppConfig} from '../selectors/AppConfigSelector';
import {
  initGlobalPageModels,
} from './AppModelSaga';
import {APPTILE_GLOBAL_PLUGIN_ID} from '../constants/modelConstants';
import {ApptileGlobalPluginConfig} from '../plugins/state/ApptileGlobalPlugin';
// TODO(gaurav) DONE figure out how to fix this
// import {selectSelectedPluginSelector} from '../../web/selectors/EditorSelectors';
import {InteractionManager, Platform} from 'react-native';
import { apptileStateSelector } from '../selectors/ApptileStateSelectors';

export function* fetchAppConfigSaga(action: DispatchAction<FetchAppConfigPayload>): SagaIterator {
  try {
    if(Platform.OS === 'web') return;

    const appConfig: Promise<FetchAppResponse> = yield call(
      AppConfigApi.fetchAppData,
      action.payload.appId,
      action.payload.appFork,
    );
    logger.info(`[DEBUG] fetchAppConfigSaga: Processing AppConfig`);
    logger.time(`fetchAppConfigSaga: Processing AppConfig`);
    const versionMatchedAppConfig = versionMatchAppConfig(appConfig);
    logger.timeEnd(`fetchAppConfigSaga: Processing AppConfig`);


    yield put({
      type: DispatchActions.FETCH_APPCONFIG_FINISHED,
      payload: {
        appId: action.payload.appId,
        appConfig: versionMatchedAppConfig,
      },
    });

    logger.info(`[DEBUG] fetchAppConfigSaga: Dispatching INIT_APP_MODEL`);
    yield put({
      type: DispatchActions.INIT_APP_MODEL,
    });
    yield spawn(function* () {
      yield take(DispatchActions.INIT_UPDATE_PAGE_MODEL);
      yield call(InteractionManager.runAfterInteractions);
      yield call(initGlobalPageModels);
    });
  } catch (e) {
    yield put({
      type: DispatchActions.INIT_APP_MODEL_ERROR,
      payload: {
        e,
      },
    });
  }
}

// export function* restartAppConfig(): SagaIterator {
//   // const apptileState = yield select(apptileStateSelector);
//   try {
//     yield putResolve({
//       type: DispatchActions.DESTROY_APP_CONFIG,
//     });
//     yield putResolve({
//       type: DispatchActions.DESTROY_APP_MODEL,
//     });
//     // yield put(fetchAppConfig(apptileState.appId, apptileState.appFork));
//   } catch (e) {
//     logger.error(e);
//   }
// }

// AppConfig loaded cann be from older versionn
// This routine adds any missing fields or migrates olderr fields
export function versionMatchAppConfig(appConfig: AppConfig): AppConfig {
  const versionMatchPluginConfig = (pluginConfig: PluginConfig): PluginConfig => {
    const pluginType = pluginConfig.get('subtype');
    const configGen = GetRegisteredConfig(pluginType);
    let mergedConfig = configGen(pluginConfig.config);
    const events = mergedConfig.get('events', null);
    if (events) {
      const updatedEvents = events.map(event => {
        if (!Immutable.isRecord(event)) return new EventHandlerConfig(event);
        return event;
      });
      mergedConfig = mergedConfig.set('events', updatedEvents);
    }
    const inputVars = mergedConfig.get('inputVariables', null);
    if (inputVars) {
      let inputVariables = inputVars;
      inputVariables = !Immutable.isImmutable(inputVars) ? Immutable.Map(inputVars) : inputVariables;
      mergedConfig = mergedConfig.set('inputVariables', inputVariables);
    }
    pluginConfig = pluginConfig.set('config', mergedConfig);
    const migrationFn = GetRegisteredPluginConfigMigrationFn(pluginType);
    if(migrationFn) pluginConfig = migrationFn(pluginConfig);
    return pluginConfig;
  };

  const updateNavRecords = (nav: NavigatorConfig | ScreenConfig) => {
    if (!Immutable.isRecord(nav)) nav = nav.type === 'screen' ? new ScreenConfig(nav) : new NavigatorConfig(nav);
    if (nav.type === 'navigator') {
      nav = nav.update('screens', screens => screens.map(screenOrNav => updateNavRecords(screenOrNav)));
    }
    return nav;
  };

  // FIXME: Add Migrations for AppConfig members as well.
  let updatedAppConfig = appConfig;
  // Make sure Navigation has records not objects.
  updatedAppConfig = updatedAppConfig.setIn(
    ['navigation', 'rootNavigator'],
    updateNavRecords(updatedAppConfig.navigation.rootNavigator),
  );
  const globalPlugins = appConfig.get('plugins');
  // Update Global plugin configs with migartionns and defaults
  let updatedGlobalPlugins = globalPlugins;
  globalPlugins.forEach((plugin, pluginId) => {
    updatedGlobalPlugins = updatedGlobalPlugins.set(pluginId, versionMatchPluginConfig(plugin));
  });
  // Check for Apptile Global Plugin. Add if it does not exist.
  let apptileGlobalPlugin = updatedGlobalPlugins
    .filter(plugin => plugin.subtype === 'ApptileGlobal')
    ?.get(APPTILE_GLOBAL_PLUGIN_ID);
  if (!apptileGlobalPlugin) {
    updatedGlobalPlugins = updatedGlobalPlugins.set(
      APPTILE_GLOBAL_PLUGIN_ID,
      new PluginConfig({
        id: APPTILE_GLOBAL_PLUGIN_ID,
        type: 'state',
        subtype: 'ApptileGlobal',
        config: Immutable.Map(ApptileGlobalPluginConfig),
      }),
    );
  }

  updatedAppConfig = updatedAppConfig.set('plugins', updatedGlobalPlugins);

  // Update Page plugin configs with migartionns and defaults
  const pageConfigs = appConfig.get('pages');
  let updatedPageConfigs = pageConfigs;
  pageConfigs.forEach((pageConfig, pageId) => {
    // FIXME: Add Migrations for PageConfigs as well.
    const plugins = pageConfig.get('plugins');
    let updatedPlugins = plugins;
    plugins.forEach((plugin, pluginId) => {
      updatedPlugins = updatedPlugins.set(pluginId, versionMatchPluginConfig(plugin));
    });
    updatedPageConfigs = updatedPageConfigs.set(pageId, pageConfig.set('plugins', updatedPlugins));
  });

  // FIXME: Add Migrations for Other Configs/themes as well.
  return updatedAppConfig.set('pages', updatedPageConfigs);
}

export function* pluginUpdateConfig(action: DispatchAction<PluginUpdateConfig>): SagaIterator {
  try {
    const {selector, pluginConfig} = action.payload;
    const pluginId = selector.length == 1 ? selector[0] : selector[2];
    const pageId = selector.length == 1 ? null : selector[0];

    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    if (pageId) {
      newAppConfig = newAppConfig.setIn(['pages', pageId, 'plugins', pluginId], pluginConfig);
    } else {
      newAppConfig = newAppConfig.setPluginId(pluginId, pluginConfig);
    }

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
  } catch (e) {}
}

export function getPluginsContained(pluginId: string, pageConfig: PageConfig): Immutable.OrderedSet<PluginConfig> {
  let pluginsList: Immutable.OrderedSet<PluginConfig> = Immutable.OrderedSet();
  pageConfig.plugins.forEach((childConfig: PluginConfig) => {
    if (childConfig.layout?.container === pluginId) {
      pluginsList = pluginsList.add(childConfig);
      pluginsList = pluginsList.merge(getPluginsContained(childConfig.id, pageConfig));
    }
  });

  return pluginsList;
}

export function getModuleDependencies(plugins: PluginConfig[], appConfig: AppConfig): Immutable.Set<ModuleRecord> {
  let modulesList: Immutable.Set<ModuleRecord> = Immutable.Set();
  plugins.forEach((pluginConfig: PluginConfig) => {
    if (pluginConfig.subtype === 'ModuleInstance') {
      const moduleUUID = pluginConfig.config.get('moduleUUID');
      const moduleRecord = appConfig.modules.get(moduleUUID);
      if (moduleRecord) modulesList = modulesList.add(moduleRecord);
    }
  });
  return modulesList;
}

export default function* appConfigSagas(): SagaIterator {
  yield all([
    takeLatest(DispatchActions.FETCH_APPCONFIG, fetchAppConfigSaga),
    takeEvery(DispatchActions.PLUGIN_UPDATE_CONFIG, pluginUpdateConfig),
    // takeLatest(DispatchActions.RESTART_APPCONFIG, restartAppConfig),
  ]);
}
