import {SagaIterator} from '@redux-saga/types';
import {spawn, all, call} from 'redux-saga/effects';
import appConfigSagas from './AppConfigSaga';
import appModelSagas, {appEventsHandlerSagas, appModelPageSagas, appModelTriggerSagas} from './AppModelSaga';
import apptileSaga from './ApptileSaga';
import apptileThemeSagas from './ApptileThemeSaga';
import eventHandlersSaga from './EventHandlersSaga';

const sagas = [
  apptileSaga,
  appConfigSagas,
  appModelPageSagas,
  appModelTriggerSagas,
  appEventsHandlerSagas,
  appModelSagas,
  eventHandlersSaga,
  apptileThemeSagas,
];

export function* rootSaga(childSagas = sagas): SagaIterator {
  yield all(
    childSagas.map(saga =>
      spawn(function* () {
        while (true) {
          try {
            yield call(saga);
            break;
          } catch (e) {
            logger.error(e);
          }
        }
      }),
    ),
  );
}
