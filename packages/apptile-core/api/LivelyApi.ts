import { Api } from './Api';

export default class LivelyApi {
  static baseURL = 'https://api.lively.li';
  static pollBaseURL = 'https://event.lively.li';
  static getCommunicationUrl() {
    return LivelyApi.baseURL;
  }
  static getPollingUrl() {
    return this.pollBaseURL + '/api/live-streaming' + '/poll';
  }
  static generateZeegoToken<T>(userId: string, streamingId: string) {
    return Api.post<T>(LivelyApi.getCommunicationUrl() +
      `/live-streaming/token`, {
          user_id: userId,
          streaming_id: streamingId,
      },
      {
        withCredentials: false
      },
    );
  }
  static getCompanyInfo<T>(brandId: string) {
    return Api.get<T>(LivelyApi.getCommunicationUrl() +
      `/conferenceUsers/getCompanyInfo?company_id=${brandId}`,
      {
        withCredentials: false
      },
    );
  }
  static pollVote<T>(streamingId: string, companyId: string, pollId: string, viewerId: string, optionId: string) {
    return Api.post<T>(LivelyApi.getPollingUrl() + 
      `/vote`,
      {
        streaming_id: streamingId,
        company_id: companyId,
        poll_uuid: pollId,
        viewer_id: viewerId,
        option_id: optionId
      },
      {
        withCredentials: false
      }
    )
  }
}
