import axios, {AxiosRequestConfig, AxiosResponse} from 'axios';
import {Platform} from 'react-native';

import {getApiEndpoint, getUpdateEndpoint} from '../constants/api';
// TODO(gaurav) DONE this has to be referenced on global now
// import {WEB_API_SERVER_ENDPOINT} from '../../.env.json';

if (__DEV__) {
  const progressTracker = (direction: string, method?: string, url?: string) => (progressEvent: any) => {
    const total = progressEvent.total;
    const current = progressEvent.loaded;
    let progress = total ? ((current / total) * 100).toFixed(2) + '%' : (current / 1000000).toFixed(2) + 'mb';
    // logger.info(`${direction} ${method?.toUpperCase()} ${url} | Progress: ${progress}`);
  };

  // axios.interceptors.request.use(
  //   function (config) {
  //     if (config.url?.includes('https://apptile-api.loca.lt')) config.headers['Bypass-Tunnel-Reminder'] = 1;
  //     config.onUploadProgress = progressTracker(' ⬆ ', config.method, config.url);
  //     config.onDownloadProgress = progressTracker(' ⬆ ', config.method, config.url);
  //     return config;
  //   },
  //   function (error) {
  //     return Promise.reject(error);
  //   },
  // );

  axios.interceptors.response.use(
    function (response) {
      logger.info(' ⬇ ', response.status, response.config.method, response.config.url);
      return response;
    },
    function (error) {
      return Promise.reject(error);
    },
  );
}

export class Api {
  static get API_SERVER(): string {
    return Platform.select({
      android: getApiEndpoint(),
      ios: getApiEndpoint(),
      default: global.WEB_API_SERVER_ENDPOINT,
    });
  }
  static get UPDATE_SERVER(): string {
    return Platform.select({
      android: getUpdateEndpoint(),
      ios: getUpdateEndpoint(),
      default: global.WEB_API_SERVER_ENDPOINT,
    });
  }

  static get<T>(url: string, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axios.get<T>(url, config);
  }
  static post<T>(url: string, data?: unknown, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axios.post<T>(url, data, config);
  }
  static put<T>(url: string, data?: unknown, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axios.put<T>(url, data, config);
  }

  static patch<T>(url: string, data?: unknown, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axios.patch<T>(url, data, config);
  }

  static delete<T>(url: string, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axios.delete<T>(url, config);
  }
}
