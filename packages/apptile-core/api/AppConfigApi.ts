import {InteractionManager, Platform} from 'react-native';
import {AxiosPromise} from 'axios';
import semver from 'semver';
// TODO(gaurav) DONE this is broken on main too
// import {AppConfigResponse} from 'apptile-server';
type AppConfigResponse = any;

import {Api} from './Api';
import RNGetValues from '../lib/RNGetValues';
import {getActiveBundle} from '../lib/RNDynamicBundle';
import LocalStorage from '../common/LocalStorage';
import FS from '../common/FileSystem';
import {setSplashScreenMessage} from '../components/common/JSSplashScreen';
import {AppConfig, RecordSerializer} from '../common/datatypes/types';
import SentryHelper from '../common/Sentry';
import _ from 'lodash';
import { triggerCustomEventListener } from '../common/utils/CustomEventSystem';
import { getAppConstants } from '../constants/api';

export type FetchAppResponse = {
  appConfig: AppConfig;
  apiResponse: any;
};
export default class AppConfigApi {
  static baseURL = '/app';
  static getApiUrl() {
    return Api.UPDATE_SERVER + AppConfigApi.baseURL;
  }
  
  static async fetchAppData(appId: string | number, appFork: string): Promise<FetchAppResponse> {
    triggerCustomEventListener('markStart', 'fetchAppConfig');
    const transaction = SentryHelper.startTransaction("fetchAppData");
    logger.info('[DEBUG] fetchAppData: Called');
    logger.time('fetchAppData');
    setSplashScreenMessage?.('Checking for updates...');
    let fetchAppDataUrl = Api.API_SERVER + '/api/v2/app' + `/${appId}`;

    logger.info('[APPCONFIG] start from ' + fetchAppDataUrl);
    try {
      const isDistributedApp = await RNGetValues('APPTILE_IS_DISTRIBUTED_APP');
      if (!isDistributedApp) {
        const forkId = getAppConstants()?.DEV_FORK_ID;
        const branchName = getAppConstants()?.DEV_BRANCH_NAME;
        logger.info(`non distributed app forkId ${forkId} ${branchName}`);
        // console.log('FORK: ', forkId);
        // console.log('BRANCH: ', branchName);
        const functionSpan = SentryHelper.startSpan(transaction, "function", "nondistributed app appconfig load");
        try {
          const fetchSpan = SentryHelper.startSpan(transaction, "http", "make api call for appconfig");
          const response = await Api.get(`${fetchAppDataUrl}/${forkId}/${branchName}`)
            .catch(e => logger.error(`Unable to load App from ${fetchAppDataUrl}/${forkId}/${branchName}, ERROR: ${e.message}`, e.response))
            .finally(() => fetchSpan.finish());
          const parseSpan = SentryHelper.startSpan(transaction, "function", "parse data");
          const appConfig = RecordSerializer.parse(response.data);
          parseSpan.finish();
          functionSpan.finish();
          transaction.finish();
          return appConfig;
        } catch (e) {
          logger.error(e);
        }
      }

      // Load new app config immediately
      let activeAppSaveId = await LocalStorage.getValue('activeAppSaveId');
      const configFromFs = (async () => {
        const span = SentryHelper.startSpan(transaction, "function", "load appconfig from fs");
        try {
          let appConfig, fsResponse;
          if (activeAppSaveId) {
            logger.info(`[DEBUG] fetchAppData: Loading FS App Save.`);
            const configSpan = SentryHelper.startSpan(transaction, "fs", "active get config file");
            appConfig = await loadSavedAppConfigFile();
            configSpan.finish();
          } else {
            const bundledSpan = SentryHelper.startSpan(transaction, "fs", "read bundled config");
            logger.info(`[DEBUG] fetchAppData: Loading Bundled App Save.`);
            fsResponse = await FS.getBundledAppConfig();
            bundledSpan.finish();
            logger.info(`[DEBUG] fetchAppData: Processing saved AppConfig`);
            const parseSpan = SentryHelper.startSpan(transaction, "function", "inactive parse config");
            appConfig = RecordSerializer.parse(fsResponse);
            parseSpan.finish();
            // TODO: Add a build time variable to retrieve and set appId here.
            saveAppConfigCache(fsResponse, 1);
          }
          span.finish();
          return appConfig;
        } catch (e) {
          logger.error(e);
          throw e;
        }
      })();
      return await configFromFs;
    } catch (err) {
      transaction.description = "Error while getting appconfig";
      transaction.finish();
      console.error('[APPCONFIG] failed to get appconfig', err);
      throw err;
    }
  }

  static async fetchAppManifest(appId: string, forkName: string, fwVersion: string) {
    const updateEndpoint = (Api.UPDATE_SERVER || (await RNGetValues('APPTILE_UPDATE_ENDPOINT'))) + AppConfigApi.baseURL;
    const manifestUrl = updateEndpoint + `/${appId}/${forkName}/manifest?frameworkVersion=${fwVersion}`;
    logger.info(manifestUrl);
    return Api.get(manifestUrl);
  }

  static saveAppData(appId: number, appSaveId: number, data: unknown): AxiosPromise<AppConfigResponse> {
    return Api.post(AppConfigApi.getApiUrl() + `/${appId}`, {appSaveId, data}, {withCredentials: true});
  }

  static fetchCachedPages() {
    const fetchPromise = new Promise(resolve => loadSavedAppConfigPages().then(val => resolve(val)));
    InteractionManager.runAfterInteractions({name: '', gen: () => fetchPromise});
    return fetchPromise;
  }
  static async getUpdatedAppConfig(url: string, appSaveId: number) {
    logger.info('[APPUPDATE] getUpdatedAppConfig: Called', appSaveId, url);
    return Api.get(url).then(res => {
      return saveAppConfigCache(res.data as string, appSaveId);
    });
 }
}

const saveAppConfigCache = async (appConfigString: string, appSaveId: number) => {
    return FS.saveFile(`appSaves`, `${appSaveId}.serialized.json`, appConfigString)
    .then(() => LocalStorage.setValue('activeAppSaveId', appSaveId))
    // .then(() => FS.listFilesInDir(FS.getFullPath('appSaves')))
    // .then(files => {
    //   for (let index = 0; index < files.length; ++index) {
    //     const filename = files[index];
    //     logger.info("Checking file for deletion: ", filename);
    //     if (![`${appSaveId}.serialized.json`, `${appSaveId}.json`].includes(filename)) {
    //       logger.info("Deleting file: ", filename);
    //       FS.deleteFile('appSaves', filename);
    //     }
    //   }
    // })
    .catch(e => logger.error(e));
};


const loadSavedAppConfigFile = async () => {
  let activeAppSaveId = await LocalStorage.getValue('activeAppSaveId');
  let promise1 = (async () => {
    let baseFile = await FS.getFile(`appSaves/${activeAppSaveId}.serialized.json`);
    logger.info(`[DEBUG] loadSavedAppConfigFile: Decoding Serialized Config`);
    logger.time(`loadSavedAppConfigFile: Decoding Serialized Config`);
    if (baseFile) baseFile = RecordSerializer.parse(baseFile);
    logger.timeEnd(`loadSavedAppConfigFile: Decoding Serialized Config`);
    return baseFile;
  })();
  let savedAppConfig: any = await promise1;
  return savedAppConfig;
};
