// TODO(gaurav) DONE broken on main
// import {AppConfigResponse} from 'apptile-server';
type AppConfigResponse = any;
import {AxiosPromise} from 'axios';

import {Api} from './Api';

export default class AssetsApi {
  static baseURL = '/api/asset-manager';
  static getApiUrl() {
    return Api.API_SERVER + AssetsApi.baseURL;
  }

  static fetchAppAssets(appId: string, page: number, limit: number): AxiosPromise<AppConfigResponse> {
    return Api.get(AssetsApi.getApiUrl() + `/apps/${appId}/assets?page=${page}&limit=${limit}`);
  }

  static getIconsForCurrentApp(appId: string): AxiosPromise<AppConfigResponse> {
    const assetAPIUrl = AssetsApi.getApiUrl() + `/icons/${appId}`;
    logger.info('getIconsForCurrentApp AssetsApi URL', assetAPIUrl);
    return Api.get(assetAPIUrl);
  }
}
