import {AppConfig, PluginConfig, ScreenConfig} from '../common/datatypes/types';
import {Selector} from '../common/DependencyGraph/types';
import {PluginUpdateConfig} from '../plugins/plugin';
import {DispatchAction, DispatchActions, DispatchEmptyAction} from './DispatchActions';

export interface FetchAppConfigPayload {
  appId: string | number;
  appFork: string;
  appSaveId: string | number;
  isPublished: boolean;
}

export interface AppConfigLoadedPayload extends FetchAppConfigPayload {
  appConfig: AppConfig;
  isLegacy: boolean;
}

export function fetchAppConfig(
  appId: string | number,
  appFork: string,
): DispatchAction<FetchAppConfigPayload> {
  return {
    type: DispatchActions.FETCH_APPCONFIG,
    payload: {appId, appFork},
  };
}

export function restartAppConfig(): DispatchEmptyAction {
  return {
    type: DispatchActions.RESTART_APPCONFIG,
  }
}

export function pluginUpdateConfigAction(
  selector: string[],
  pluginConfig: PluginConfig,
): DispatchAction<PluginUpdateConfig> {
  return {
    type: DispatchActions.PLUGIN_UPDATE_CONFIG,
    payload: {
      selector,
      pluginConfig,
    },
  };
}

export interface PluginConfigUpdate {
  pluginId: string;
  pageId: string;
  update?: any;
}

export interface BulkPluginUpdate {
  updates: any[];
}

export interface PluginConfigValueUpdate {
  pluginId: string;
  pageId: string;
  propName: string;
  value?: any;
}
export interface PluginConfigUpdatePath {
  pluginId: string;
  pageId: string;
  selector: Selector;
  update?: any;
  remove?: string[];
}
export interface PluginConfigSetPathValue {
  pluginId: string;
  pageId: string;
  selector: Selector;
  value?: any;
}

export const pluginConfigUpdatePath = (
  pluginId: string,
  pageId: string,
  selector: Selector,
  update: any,
  remove: string[] | undefined = undefined,
): DispatchAction<PluginConfigUpdatePath> => {
  return {
    type: DispatchActions.PLUGIN_UPDATE_CONFIG_PATH,
    payload: {
      pluginId,
      pageId,
      selector,
      update,
      remove,
    },
  };
};

export const pluginConfigSetPathValue = (
  pluginId: string,
  pageId: string,
  selector: Selector,
  value: any,
): DispatchAction<PluginConfigSetPathValue> => {
  return {
    type: DispatchActions.PLUGIN_SET_CONFIG_PATH_VALUE,
    payload: {
      pluginId,
      pageId,
      selector,
      value,
    },
  };
};

export const pluginConfigUpdate = (
  pluginId: string,
  pageId: string,
  update: any,
): DispatchAction<PluginConfigUpdate> => {
  return {
    type: DispatchActions.PLUGIN_CONFIG_UPDATE,
    payload: {
      pluginId,
      pageId,
      update,
    },
  };
};

export const bulkPluginUpdates = (updates: any): DispatchAction<BulkPluginUpdate> => {
  return {
    type: DispatchActions.BULK_PLUGIN_UPDATE,
    payload: {
      updates,
    },
  };
};

export const pluginConfigValueSetRaw = (
  pluginId: string,
  pageId: string,
  propName: string,
  value: any,
): DispatchAction<PluginConfigValueUpdate> => {
  return {
    type: DispatchActions.PLUGIN_CONFIG_SET_PROP_VALUE,
    payload: {
      pluginId,
      pageId,
      propName,
      value,
    },
  };
};
export type PluginLayoutUpdate = PluginConfigUpdate;
export const pluginLayoutUpdate = (
  pluginId: string,
  pageId: string,
  update: any,
): DispatchAction<PluginLayoutUpdate> => {
  return {
    type: DispatchActions.PLUGIN_LAYOUT_UPDATE,
    payload: {
      pluginId,
      pageId,
      update,
    },
  };
};

export type PluginStyleUpdate = PluginConfigUpdate;
export const pluginStyleUpdate = (pluginId: string, pageId: string, update: any): DispatchAction<PluginStyleUpdate> => {
  return {
    type: DispatchActions.PLUGIN_STYLE_UPDATE,
    payload: {
      pluginId,
      pageId,
      update,
    },
  };
};

export interface PluginConfigEventDelete {
  pluginId: string;
  pageId: string;
  selector?: string[];
}

export const pluginConfigDeleteEvent = (
  pluginId: string,
  pageId: string,
  selector: any,
): DispatchAction<PluginConfigEventDelete> => {
  return {
    type: DispatchActions.PLUGIN_CONFIG_EVENT_DELETE,
    payload: {
      pluginId,
      pageId,
      selector,
    },
  };
};

export const pluginConfigEventUpdate = (
  pluginId: string,
  pageId: string,
  update: any,
): DispatchAction<PluginConfigUpdate> => {
  return {
    type: DispatchActions.PLUGIN_CONFIG_EVENT_UPDATE,
    payload: {
      pluginId,
      pageId,
      update,
    },
  };
};

export interface NavConfigUpdate {
  navSelector: string[];
  propertyName: string;
  value?: any;
}
export const navConfigUpdate = (
  navSelector: string[],
  propertyName: string,
  value: any,
): DispatchAction<NavConfigUpdate> => {
  return {
    type: DispatchActions.UPDATE_NAVIGATION_PROPERTY,
    payload: {
      navSelector,
      propertyName,
      value,
    },
  };
};

export interface NavigationConfigUpdatePath {
  navSelector: string[];
  selector: Selector;
  update?: any;
}
export interface NavigationConfigSetPathValue {
  navSelector: string[];
  selector: Selector;
  value?: any;
}

export const navigationConfigUpdatePath = (
  navSelector: string[],
  selector: Selector,
  update: any,
): DispatchAction<NavigationConfigUpdatePath> => {
  return {
    type: DispatchActions.UPDATE_NAVIGATION_CONFIG_PATH,
    payload: {
      navSelector,
      selector,
      update,
    },
  };
};

export const navigationConfigSetPathValue = (
  navSelector: string[],
  selector: Selector,
  value: any,
): DispatchAction<NavigationConfigSetPathValue> => {
  return {
    type: DispatchActions.SET_NAVIGATION_CONFIG_PATH_VALUE,
    payload: {
      navSelector,
      selector,
      value,
    },
  };
};

export interface NavUpdateName {
  navSelector: string[];
  newNavName: string;
}
export const navUpdateName = (navSelector: string[], newNavName: string): DispatchAction<NavUpdateName> => {
  return {
    type: DispatchActions.UPDATE_NAVIGATION_NAME,
    payload: {
      navSelector,
      newNavName,
    },
  };
};

export interface AddPage {
  pageId?: string;
}

export interface AddNavigationPage {
  navSelector: string[];
  screenName: string;
  screenConfig?: Partial<ScreenConfig>;
}
export const addNavigationPage = (
  navSelector: string[],
  screenName: string,
  screenConfig: Partial<ScreenConfig> = {},
): DispatchAction<AddNavigationPage> => {
  return {
    type: DispatchActions.ADD_NAVIGATION_PAGE,
    payload: {
      navSelector,
      screenName,
      screenConfig,
    },
  };
};

export interface AddNavigationNav {
  navSelector: string[];
  navName: string;
  type: string;
}
export const addNavigationNav = (
  navSelector: string[],
  navName: string,
  type: string,
): DispatchAction<AddNavigationNav> => {
  return {
    type: DispatchActions.ADD_NAVIGATION_NAVIGATOR,
    payload: {
      navSelector,
      navName,
      type,
    },
  };
};

export interface NavComponentDelete {
  navSelector: string[];
}
export const navComponentDelete = (navSelector: string[]): DispatchAction<NavComponentDelete> => {
  return {
    type: DispatchActions.DELETE_NAVIGATION_COMPONENT,
    payload: {
      navSelector,
    },
  };
};

export interface UpdatePageId {
  pageId: string;
  newPageId: string;
}
export const updatePageId = (pageId: string, newPageId: string): DispatchAction<UpdatePageId> => {
  return {
    type: DispatchActions.UPDATE_PAGE_ID,
    payload: {
      pageId,
      newPageId,
    },
  };
};
export interface UpdatePageConfig {
  pageId: string;
  update: string;
}
export const updatePageConfig = (pageId: string, update: any): DispatchAction<UpdatePageConfig> => {
  return {
    type: DispatchActions.UPDATE_PAGE_CONFIG,
    payload: {
      pageId,
      update,
    },
  };
};
export interface PageConfigUpdatePath {
  pageId: string;
  selector: Selector;
  update?: any;
}
export interface PageConfigSetPathValue {
  pageId: string;
  selector: Selector;
  value?: any;
}

export const pageConfigUpdatePath = (
  pageId: string,
  selector: Selector,
  update: any,
): DispatchAction<PageConfigUpdatePath> => {
  return {
    type: DispatchActions.UPDATE_PAGE_CONFIG_PATH,
    payload: {
      pageId,
      selector,
      update,
    },
  };
};

export const pageConfigSetPathValue = (
  pageId: string,
  selector: Selector,
  value: any,
): DispatchAction<PageConfigSetPathValue> => {
  return {
    type: DispatchActions.SET_PAGE_CONFIG_PATH_VALUE,
    payload: {
      pageId,
      selector,
      value,
    },
  };
};

export interface DeletePageConfig {
  pageId: string;
}
export const deletePageConfig = (pageId: string): DispatchAction<DeletePageConfig> => {
  return {
    type: DispatchActions.DELETE_PAGE_CONFIG,
    payload: {
      pageId,
    },
  };
};
