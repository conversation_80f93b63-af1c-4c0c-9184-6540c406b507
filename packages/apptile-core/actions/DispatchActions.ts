import Immutable from 'immutable';
import {ApptileAnalyticsEventType} from '../common/ApptileAnalytics/ApptileAnalyticsTypes';
import {CreateWidgetPayload} from '../common/datatypes/dndTypes';
import {
  AppModelType,
  ApptileThemeActiveConfig,
  ApptileThemeModelType,
  EventHandlerConfig,
  ModuleCreationParams,
  ModulePropertyType,
  PluginConfig,
  PluginIdTypePage,
  PluginNamespace,
  PluginNamespaceImpl,
  SettingsConfig,
} from '../common/datatypes/types';
import {Selector} from '../common/DependencyGraph/types';
import {EvalResult} from '../common/ExecutionContext/EvaluationContextTypes';
import {AppPageTriggerOptions} from '../plugins/plugin';

export type ActionType = string;
export enum DispatchActions {
  // Platform Actions
  APPTILE_INIT = 'APPTILE_INIT',
  APPTILE_INIT_DONE = 'APPTILE_INIT_DONE',
  APPTILE_INIT_ERROR = 'APPTILE_INIT_ERROR',
  APPTILE_ACTIVE_PAGE = 'APPTILE_ACTIVE_PAGE',
  DESTROY_APPTILE_CONFIG = 'DESTROY_APPTILE_CONFIG',
  APPTILE_FOCUS_PAGE = 'APPTILE_FOCUS_PAGE',

  INIT_APPTILE_THEME = 'INIT_APPTILE_THEME',
  INIT_APPTILE_THEME_ERROR = 'INIT_APPTILE_THEME_ERROR',
  UPDATE_APPTILE_THEME = 'UPDATE_APPTILE_THEME',
  SET_APPTILE_THEME_ACTIVE_CONFIG = 'SET_APPTILE_THEME_ACTIVE_CONFIG',
  SET_APPTILE_THEME_ACTIVE_CONFIG_ERROR = 'SET_APPTILE_THEME_ACTIVE_CONFIG_ERROR',
  DESTROY_APPTILE_THEME = 'DESTROY_APPTILE_THEME',

  PLATFORM_INIT = 'PLATFORM_INIT',
  PLATFORM_INIT_ERROR = 'PLATFORM_INIT_ERROR',
  PLATFORM_INIT_FETCHED = 'PLATFORM_INIT_FETCHED',

  REQUEST_ORGS = 'REQUEST_ORGS',
  REQUEST_ORGS_FINISHED = 'REQUEST_ORGS_FINISHED',
  REQUEST_ORGS_ERROR = 'REQUEST_ORGS_ERROR',

  CREATE_ORG_PAGE = 'CREATE_ORG_PAGE',
  CREATE_ORG_PAGE_FINISHED = 'CREATE_ORG_PAGE_FINISHED',
  CREATE_ORG_PAGE_ERROR = 'CREATE_ORG_PAGE_ERROR',

  //User Actions
  REQUEST_USER = 'REQUEST_USER',
  REQUEST_USER_FINISHED = 'REQUEST_USER_FINISHED',
  REQUEST_USER_ERROR = 'REQUEST_USER_ERROR',

  UPDATE_USER = 'UPDATE_USER',
  UPDATE_USER_FINISHED = 'UPDATE_USER_FINISHED',

  SIGNUP_USER = 'SIGNUP_USER',
  SIGNUP_USER_FINISHED = 'SIGNUP_USER_FINISHED',
  SIGNUP_USER_ERROR = 'SIGNUP_USER_ERROR',

  LOGIN_USER = 'LOGIN_USER',
  LOGIN_USER_FINISHED = 'LOGIN_USER_FINISHED',

  RECOVER_PASSWORD_USER = 'RECOVER_PASSWORD_USER',
  RECOVER_PASSWORD_USER_FINISHED = 'RECOVER_PASSWORD_USER_FINISHED',

  RESET_PASSWORD_USER = 'RESET_PASSWORD_USER',
  RESET_PASSWORD_USER_FINISHED = 'RESET_PASSWORD_USER_FINISHED',

  VERIFY_EMAIL_USER = 'VERIFY_EMAIL_USER',
  VERIFY_EMAIL_USER_FINISHED = 'VERIFY_EMAIL_USER_FINISHED',

  LOGOUT_USER = 'LOGOUT_USER',
  LOGOUT_USER_FINISHED = 'LOGOUT_USER_FINISHED',

  //AppConfig Actions
  FETCH_APPCONFIG = 'FETCH_APPCONFIG',
  FETCH_APPCONFIG_FINISHED = 'FETCH_APPCONFIG_FINISHED',
  FETCH_APPCONFIG_ERROR = 'FETCH_APPCONFIG_ERROR',
  IS_APP_SAVED = 'IS_APP_SAVED',
  IS_APP_SAVING = 'IS_APP_SAVING',
  IS_APP_SAVE_FAILED = 'IS_APP_SAVE_FAILED',

  RESTART_APPCONFIG='RESTART_APPCONFIG',
  CHANGE_APPCONFIG = 'CHANGE_APPCONFIG',
  CHANGE_APPFORK = 'CHANGE_APPFORK',

  APP_UPDATE_ISEDITABLE = 'APP_UPDATE_ISEDITABLE',
  APP_UPDATE_ISPREVIEW = 'APP_UPDATE_ISPREVIEW',
  APP_UPDATE_ISTILESONLY = 'APP_UPDATE_ISTILESONLY',

  // App Settings Actions
  SET_APP_SETTINGS = 'SET_APP_SETTINGS',
  UPDATE_APP_SETTINGS_VALUE = 'UPDATE_APP_SETTINGS_VALUE',
  DELETE_APP_SETTINGS = 'DELETE_APP_SETTINGS',

  UPDATE_APP_CONFIG = 'UPDATE_APP_CONFIG',
  DESTROY_APP_CONFIG = 'DESTROY_APP_CONFIG',
  SET_APP_NAVIGATION = 'SET_APP_NAVIGATION',
  UPDATE_NAVIGATION_PROPERTY = 'UPDATE_NAVIGATION_PROPERTY',
  UPDATE_NAVIGATION_NAME = 'UPDATE_NAVIGATION_NAME',
  ADD_NAVIGATION_PAGE = 'ADD_NAVIGATION_PAGE',
  ADD_NAVIGATION_NAVIGATOR = 'ADD_NAVIGATION_NAVIGATOR',
  DELETE_NAVIGATION_COMPONENT = 'DELETE_NAVIGATION_COMPONENT',
  UPDATE_NAVIGATION_CONFIG_PATH = 'UPDATE_NAVIGATION_CONFIG_PATH',
  SET_NAVIGATION_CONFIG_PATH_VALUE = 'SET_NAVIGATION_CONFIG_PATH_VALUE',

  //AppPage Actions
  FETCH_PAGE = 'FETCH_PAGE',
  FETCH_PAGE_FINISHED = 'FETCH_PAGE_FINISHED',
  FETCH_PAGE_ERROR = 'FETCH_PAGE_ERROR',
  ADD_NEW_PAGE = 'ADD_NEW_PAGE',
  ADD_NEW_PAGE_DONE = 'ADD_NEW_PAGE_DONE',
  UPDATE_PAGE_ID = 'UPDATE_PAGE_ID',
  UPDATE_PAGE_ID_DONE = 'UPDATE_PAGE_ID_DONE',
  UPDATE_PAGE_CONFIG = 'UPDATE_PAGE_CONFIG',
  DELETE_PAGE_CONFIG = 'DELETE_PAGE_CONFIG',
  DELETE_PAGE_CONFIG_DONE = 'DELETE_PAGE_CONFIG_DONE',
  UPDATE_PAGE_CONFIG_PATH = 'UPDATE_PAGE_CONFIG_PATH',
  SET_PAGE_CONFIG_PATH_VALUE = 'SET_PAGE_CONFIG_PATH_VALUE',

  UNLOAD_ACTIVE_PAGE = 'UNLOAD_ACTIVE_PAGE',

  APPPAGE_SAVE = 'APPPAGE_SAVE',
  APPPAGE_SAVE_FINISHED = 'APPPAGE_SAVE_FINISHED',

  APPPAGE_SAVEPROPS = 'APPPAGE_SAVEPROPS',
  APPPAGE_SAVEPROPS_FINISHED = 'APPPAGE_SAVEPROPS_FINISHED',
  APPPAGE_SAVEPROPS_ERROR = 'APPPAGE_SAVEPROPS_ERROR',

  APPPAGE_TRIGGER_QUERY = 'APPPAGE_TRIGGER_QUERY',
  APPPAGE_TRIGGER_QUERY_FINISHED = 'APPPAGE_TRIGGER_QUERY_FINISHED',
  APPPAGE_TRIGGER_QUERY_ERROR = 'APPPAGE_TRIGGER_QUERY_ERROR',

  APPPAGE_EVENT_HANDLER = 'APPPAGE_EVENT_HANDLER',
  APPPAGE_EVENT_HANDLER_FINISHED = 'APPPAGE_EVENT_HANDLER_FINISHED',
  APPPAGE_EVENT_HANDLER_ERROR = 'APPPAGE_TRIGGER_QUERY_ERROR',

  APPPAGE_TRIGGER_ACTION = 'APPPAGE_TRIGGER_ACTION',

  APPTILE_ANALYTICS_EVENT = 'APPTILE_ANALYTICS_EVENT',

  //Plugin Operations
  ADD_PLUGIN = 'ADD_PLUGIN',
  ADD_DATASOURCE_PLUGIN = 'ADD_DATASOURCE_PLUGIN',
  ADD_PLUGIN_DONE = 'ADD_PLUGIN_DONE',
  SELECT_PLUGIN = 'SELECT_PLUGIN',
  MULTISELECT_PLUGIN = 'MULTISELECT_PLUGIN',
  DESELECT_PLUGINS = 'DESELECT_PLUGINS',
  DELETE_PLUGIN = 'DELETE_PLUGIN',
  DELETE_SELECTED_PLUGINS = 'DELETE_SELECTED_PLUGINS',

  /////////////////////////////////////////////////////////
  // TODO: Delete specialized events and move to PLUGIN_UPDATE_CONFIG_PATH & PLUGIN_SET_CONFIG_PATH_VALUE
  PLUGIN_UPDATE_PROPERTY = 'PLUGIN_UPDATE_PROPERTY',
  PLUGIN_UPDATE_PROPERTY_VALUE = 'PLUGIN_UPDATE_PROPERTY_VALUE',
  PLUGIN_TRIGGER_ON_UPDATE = 'PLUGIN_TRIGGER_ON_UPDATE',
  FLUSH_ON_PLUGIN_TRIGGERS = 'FLUSH_ON_PLUGIN_TRIGGERS',
  PLUGIN_UPDATE_EVENT = 'PLUGIN_UPDATE_EVENT',
  PLUGIN_UPDATE_LAYOUT = 'PLUGIN_UPDATE_LAYOUT',
  PLUGIN_UPDATE_STYLE = 'PLUGIN_UPDATE_STYLE',
  PLUGIN_DELETE_EVENT_PROPERTY = 'PLUGIN_DELETE_EVENT_PROPERTY',
  PLUGIN_CONFIG_UPDATE = 'PLUGIN_CONFIG_UPDATE',
  PLUGIN_CONFIG_SET_PROP_VALUE = 'PLUGIN_CONFIG_SET_PROP_VALUE',
  PLUGIN_LAYOUT_UPDATE = 'PLUGIN_LAYOUT_UPDATE',
  PLUGIN_STYLE_UPDATE = 'PLUGIN_STYLE_UPDATE',
  BULK_PLUGIN_UPDATE = 'BULK_PLUGIN_UPDATE',
  // TODO: Delete specialized events and move to PLUGIN_UPDATE_CONFIG_PATH & PLUGIN_SET_CONFIG_PATH_VALUE
  /////////////////////////////////////////////////////////
  PLUGIN_UPDATE_CONFIG_PATH = 'PLUGIN_UPDATE_CONFIG_PATH',
  PLUGIN_SET_CONFIG_PATH_VALUE = 'PLUGIN_SET_CONFIG_PATH_VALUE',
  PLUGIN_UPDATE_CONFIG = 'PLUGIN_UPDATE_CONFIG', // Use only for pageLoad Config updates.
  PLUGIN_CONFIG_EVENT_UPDATE = 'PLUGIN_CONFIG_EVENT_UPDATE',
  PLUGIN_CONFIG_EVENT_DELETE = 'PLUGIN_CONFIG_EVENT_DELETE',

  //Module Operations
  FORWARD_MODULE_EVENT = 'FORWARD_MODULE_EVENT',
  UPDATE_PLUGIN_PROPERTY_NAMESPACE = 'UPDATE_PLUGIN_PROPERTY_NAMESPACE',
  BATCH_CREATE_MODULE_PLUGINS = 'BATCH_CREATE_MODULE_PLUGINS',
  CREATE_MODULE_PROPERTY = 'CREATE_MODULE_PROPERTY',
  CREATE_MODULE_OUTPUT = 'CREATE_MODULE_OUTPUT',
  CREATE_MODULE_EVENT = 'CREATE_MODULE_EVENT',
  UPDATE_MODULE_PROPERTY = 'UPDATE_MODULE_PROPERTY',
  UPDATE_MODULE_OUTPUT = 'UPDATE_MODULE_OUTPUT',
  DELETE_MODULE_PROPERTY = 'DELETE_MODULE_PROPERTY',
  DELETE_MODULE_OUTPUT = 'DELETE_MODULE_OUTPUT',
  DELETE_MODULE_EVENT = 'DELETE_MODULE_EVENT',
  RENAME_MODULE_PROPERTY = 'RENAME_MODULE_PROPERTY',
  RENAME_MODULE_OUTPUT = 'RENAME_MODULE_OUTPUT',
  RENAME_MODULE_EVENT = 'RENAME_MODULE_EVENT',
  PERSIST_MODULE_INPUTS = 'PERSIST_MODULE_INPUTS',
  CREATE_MODULE = 'CREATE_MODULE',
  REMAP_MODULE_INSTANCE = 'REMAP_MODULE_INSTANCE',

  PLUGIN_RENAME = 'PLUGIN_RENAME',
  PLUGIN_MOVE = 'PLUGIN_MOVE',
  PLUGIN_MOVE_DONE = 'PLUGIN_MOVE_DONE',

  //Schema Actions
  UPDATE_SCHEMA_TREE = 'UPDATE_SCHEMA_TREE',
  SET_SCHEMA_TREE = 'SET_SCHEMA_TREE',

  //AppModel Actions
  INIT_APP_MODEL = 'INIT_APP_MODEL',
  INIT_APP_MODEL_ERROR = 'INIT_APP_MODEL_ERROR',
  RESET_APP_MODEL = 'RESET_APP_MODEL',
  UPDATE_STAGE_MODEL = 'UPDATE_STAGE_MODEL',
  UPDATE_STAGE_MODEL_WITH_RESULTSET = 'UPDATE_STAGE_MODEL_WITH_RESULTSET',
  COMMIT_APP_MODEL = 'COMMIT_APP_MODEL',
  FINALIZE_COMMIT_APP_MODEL = 'FINALIZE_COMMIT_APP_MODEL',
  INITIALIZE_STAGE_MODEL = 'INITIALIZE_STAGE_MODEL',
  UPDATE_PREINIT_APP_MODEL = 'UPDATE_PREINIT_APP_MODEL',
  DESTROY_APP_MODEL = 'DESTROY_APP_MODEL',
  INIT_PAGE_MODEL = 'INIT_PAGE_MODEL',
  INIT_UPDATE_PAGE_MODEL = 'INIT_UPDATE_PAGE_MODEL',
  INIT_PAGE_MODEL_ERROR = 'INIT_PAGE_MODEL_ERROR',
  INIT_PAGE_BOOTED = 'INIT_PAGE_BOOTED',
  DESTROY_PAGE_MODEL = 'DESTROY_PAGE_MODEL',
  DESTROY_PAGE_MODEL_ERROR = 'DESTROY_PAGE_MODEL_ERROR',
  STAGE_APP_MODEL_SCHEMA = 'STAGE_APP_MODEL_SCHEMA',
  PLUGIN_MODEL_UPDATE = 'PLUGIN_MODEL_UPDATE',

  SELECT_QUERY_PLUGIN = 'SELECT_QUERY_PLUGIN',
  DESELECT_QUERY_PLUGIN = 'DESELECT_QUERY_PLUGIN',
  DELETE_QUERY_PLUGIN = 'DELETE_QUERY_PLUGIN',
  TOGGLE_QUERY_PANEL = 'TOGGLE_QUERY_PANEL',
  TOGGLE_QUERY_PREVIEW = 'TOGGLE_QUERY_PREVIEW',

  //Navigation Actions
  APPNAV_GO_BACK = 'APPNAV_GO_BACK',
  APPNAV_NAVIGATE = 'APPNAV_NAVIGATE',
  APPNAV_NAVIGATE_RESET = 'APPNAV_NAVIGATE_RESET',

  //Cleanup Actions
  CLEAN_UP_APP = 'CLEAN_UP_APP',
}

export interface DispatchAction<T> {
  type: DispatchActions | ActionType;
  payload: T;
}

export interface UpdateAppModelBasePayload {
  appModel: AppModelType;
}
export interface UpdateStageModelWithResultsPayload {
  results: EvalResult[];
}
export interface UpdateAppModelMetaExtensions extends UpdateAppModelBasePayload {
  desc: string;
  meta?: Record<string, any>;
}

export interface UpdateAppModel extends UpdateAppModelBasePayload, UpdateAppModelMetaExtensions {}
export interface FinalizeCommitAppModelPayload extends UpdateAppModelBasePayload {}
export interface UpdateStageModelWithResults extends UpdateStageModelWithResultsPayload {}

export function updateStageModelWithResults(results: EvalResult[]): DispatchAction<UpdateStageModelWithResults> {
  return {
    type: DispatchActions.UPDATE_STAGE_MODEL_WITH_RESULTSET,
    payload: {
      results,
    },
  };
}

export function commitStageModel() {
  return {
    type: DispatchActions.COMMIT_APP_MODEL,
  };
}

export type DispatchEmptyAction = Pick<DispatchAction<undefined>, 'type'>;

export interface FetchPageActionPayload {
  pageId: number;
}

export interface UpdateApptileTheme {
  apptileTheme: ApptileThemeModelType;
}

export interface SetApptileThemeActiveConfig {
  activeConfig: ApptileThemeActiveConfig;
}

export function setApptileThemeActiveConfig(
  activeConfig: ApptileThemeActiveConfig,
): DispatchAction<SetApptileThemeActiveConfig> {
  return {
    type: DispatchActions.SET_APPTILE_THEME_ACTIVE_CONFIG,
    payload: {
      activeConfig,
    },
  };
}

export interface InitPageBootedPayload {
  pageKey: string;
  pageId: string;
}
export function pageBooted(pageKey: string, pageId: string): DispatchAction<InitPageBootedPayload> {
  return {
    type: DispatchActions.INIT_PAGE_BOOTED,
    payload: {
      pageKey,
      pageId,
    },
  };
}
export interface ApptilePageFocusPayload {
  activePageKey: string;
  activePageId: string;
}
export function pageFocussed(pageKey: string, pageId: string): DispatchAction<ApptilePageFocusPayload> {
  return {
    type: DispatchActions.APPTILE_FOCUS_PAGE,
    payload: {
      activePageKey: pageKey,
      activePageId: pageId,
    },
  };
}

export interface TriggerQueryPayload {
  selector: string[];
  options: AppPageTriggerOptions;
}
export function triggerPageQuery(payload: TriggerQueryPayload): DispatchAction<TriggerQueryPayload> {
  return {
    type: DispatchActions.APPPAGE_TRIGGER_QUERY,
    payload,
  };
}

export interface SelectPluginPayload {
  pluginSel: string[] | null;
}

export function selectPlugin(pluginSel: string[] | null): DispatchAction<string[]> {
  return {
    type: DispatchActions.SELECT_PLUGIN,
    payload: pluginSel,
  };
}

export interface TriggerOnPluginUpdatePayload {
  plugin: PluginIdTypePage;
  instance: number | null;
  userTriggered: boolean;
  pageLoad: boolean;
  options?: AppPageTriggerOptions;
}

export function triggerOnPluginUpdate(
  plugin: PluginIdTypePage,
  instance: number | null,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
): DispatchAction<TriggerOnPluginUpdatePayload> {
  return {
    type: DispatchActions.PLUGIN_TRIGGER_ON_UPDATE,
    payload: {
      plugin,
      instance,
      userTriggered,
      pageLoad,
      options,
    },
  };
}

export interface TriggerPageEventPayload {
  pageKey: string;
  pluginId: string;
  instance: number | null;
  event: string;
  eventParams?: Record<string, any>;
}

export function triggerPageEvent(
  pageKey: string,
  pluginId: string,
  instance: number | null,
  event: string,
  eventParams?: Record<string, any>,
): DispatchAction<TriggerPageEventPayload> {
  return {
    type: DispatchActions.APPPAGE_EVENT_HANDLER,
    payload: {pluginId, pageKey, instance, event, eventParams},
  };
}

export interface PluginRenamePayload {
  pluginId: string;
  pageId: string;
  newPluginId: string;
  namespace?: PluginNamespace;
}

export interface PluginDeletePayload {
  pluginIds: string[];
  pageId: string;
}

export function addPlugin(createWidgetPayload: CreateWidgetPayload): DispatchAction<CreateWidgetPayload> {
  return {
    type: DispatchActions.ADD_PLUGIN,
    payload: createWidgetPayload,
  };
}

export function addDatasourcePlugin(createWidgetPayload: CreateWidgetPayload): DispatchAction<CreateWidgetPayload> {
  return {
    type: DispatchActions.ADD_DATASOURCE_PLUGIN,
    payload: createWidgetPayload,
  };
}

export interface ChangeAppConfigPayload {
  appId: string;
  orgId: string;
  forkId: string | number;
  branchName?: string;
}

export const changeAppConfig = (appId: string, orgId: string, forkId: string | number, branchName?: string): DispatchAction<ChangeAppConfigPayload> => {
  return {
    type: DispatchActions.CHANGE_APPCONFIG,
    payload: {appId, orgId, forkId, branchName},
  };
};

export interface ChangeAppForkPayload {
  appId: string;
  forkId: string | number;
}

export const changeAppFork = (appId: string, forkId: string | number): DispatchAction<ChangeAppForkPayload> => {
  return {
    type: DispatchActions.CHANGE_APPCONFIG,
    payload: {appId, forkId},
  };
};

export interface PluginPropertyNamespaceUpdate {
  pageKey: string;
  pluginId: string;
  update: any;
  namespace?: PluginNamespaceImpl;
}

export function updatePluginTemplate(
  pluginPropNSUpdate: PluginPropertyNamespaceUpdate,
): DispatchAction<PluginPropertyNamespaceUpdate> {
  return {
    type: DispatchActions.UPDATE_PLUGIN_PROPERTY_NAMESPACE,
    payload: pluginPropNSUpdate,
  };
}

//////////////////////
// Settings Actions
//////////////////////
export interface SetAppSettingsPayload {
  settingsKey: string;
  settings: SettingsConfig;
}
export function setAppSettings(settingsKey: string, settings: SettingsConfig): DispatchAction<SetAppSettingsPayload> {
  return {
    type: DispatchActions.SET_APP_SETTINGS,
    payload: {
      settingsKey,
      settings,
    },
  };
}

export interface DeleteAppSettingsPayload {
  settingsKey: string;
}
export function deleteAppSettings(settingsKey: string): DispatchAction<DeleteAppSettingsPayload> {
  return {
    type: DispatchActions.DELETE_APP_SETTINGS,
    payload: {
      settingsKey,
    },
  };
}

export interface UpdateAppSettingsValuePayload {
  settingsKey: string;
  key: string;
  value: any;
}
export function updateSettingsValue(
  settingsKey: string,
  key: string,
  value: any,
): DispatchAction<UpdateAppSettingsValuePayload> {
  return {
    type: DispatchActions.UPDATE_APP_SETTINGS_VALUE,
    payload: {
      settingsKey,
      key,
      value,
    },
  };
}

//////////////////////
// Navigation Actions
//////////////////////

export interface NavigateToScreenPayload {
  screenName: string;
  params: Record<string, any> | null;
}
export function navigateToScreen(
  screenName: string,
  params: Record<string, any>,
): DispatchAction<NavigateToScreenPayload> {
  return {
    type: DispatchActions.APPNAV_NAVIGATE,
    payload: {
      screenName,
      params,
    },
  };
}
export function navigateToScreenReset(
  screenName: string,
  params: Record<string, any>,
): DispatchAction<NavigateToScreenPayload> {
  return {
    type: DispatchActions.APPNAV_NAVIGATE_RESET,
    payload: {
      screenName,
      params,
    },
  };
}

//////////////////////
// Trigger Actions
//////////////////////
export interface TriggerActionPayload {
  pluginConfig: PluginConfig;
  pluginModel: any;
  pluginSelector: Selector;
  eventModelJS: any;
}

export function triggerAction(payload: TriggerActionPayload): DispatchAction<TriggerActionPayload> {
  return {
    type: DispatchActions.APPPAGE_TRIGGER_ACTION,
    payload: payload,
  };
}

export interface AnalyticsEventPayload {
  type: ApptileAnalyticsEventType;
  name: string;
  data: Record<string, string>;
}

export function sendAnalyticsEvent(
  type: ApptileAnalyticsEventType,
  name: string,
  data: Record<string, string>,
): DispatchAction<AnalyticsEventPayload> {
  return {
    type: DispatchActions.APPTILE_ANALYTICS_EVENT,
    payload: {
      type,
      name,
      data,
    },
  };
}
//////////////////////
// Module Actions
//////////////////////
export interface ForwardModuleEventPayload {
  pluginConfig: PluginConfig;
  pageKey: string;
  pluginId: string;
  instance: number | null;
  eventModelJS: any;
}

export function forwardModuleEvent(payload: ForwardModuleEventPayload): DispatchAction<ForwardModuleEventPayload> {
  return {
    type: DispatchActions.FORWARD_MODULE_EVENT,
    payload: payload,
  };
}

export interface BatchCreateModulePluginsAction {
  pageId: string;
  moduleId: string;
  plugins: Immutable.OrderedMap<string, PluginConfig>;
}

export function batchCreateModulePlugins(
  pageId: string,
  moduleId: string,
  plugins: Immutable.OrderedMap<string, PluginConfig>,
): DispatchAction<BatchCreateModulePluginsAction> {
  return {
    type: DispatchActions.BATCH_CREATE_MODULE_PLUGINS,
    payload: {pageId, moduleId, plugins},
  };
}

export interface CreateModuleProperty {
  moduleUUID: string;
  propertyName: string;
  propertyType: string;
  value: any;
}

export function createModuleProperty(
  moduleUUID: string,
  propertyName: string,
  propertyType: string,
  value: any,
): DispatchAction<CreateModuleProperty> {
  return {
    type: DispatchActions.CREATE_MODULE_PROPERTY,
    payload: {moduleUUID, propertyName, propertyType, value},
  };
}

export interface CreateModuleOutput {
  moduleUUID: string;
  propertyName: string;
  value: any;
}

export function createModuleOutput(
  moduleUUID: string,
  propertyName: string,
  value: any,
): DispatchAction<CreateModuleOutput> {
  return {
    type: DispatchActions.CREATE_MODULE_OUTPUT,
    payload: {moduleUUID, propertyName, value},
  };
}

export interface CreateModuleEvent {
  moduleUUID: string;
  eventName: string;
}

export function createModuleEvent(moduleUUID: string, eventName: string): DispatchAction<CreateModuleEvent> {
  return {
    type: DispatchActions.CREATE_MODULE_EVENT,
    payload: {moduleUUID, eventName},
  };
}

export interface UpdateModuleProperty {
  moduleUUID: string;
  propertyName: string;
  propertyType: string;
  value: any;
}

export function updateModuleProperty(
  moduleUUID: string,
  propertyName: string,
  propertyType: string,
  value: any,
): DispatchAction<UpdateModuleProperty> {
  return {
    type: DispatchActions.UPDATE_MODULE_PROPERTY,
    payload: {moduleUUID, propertyName, propertyType, value},
  };
}

export interface UpdateModuleOutput {
  moduleUUID: string;
  propertyName: string;
  value: any;
}

export function updateModuleOutput(
  moduleUUID: string,
  propertyName: string,
  value: any,
): DispatchAction<UpdateModuleOutput> {
  return {
    type: DispatchActions.UPDATE_MODULE_OUTPUT,
    payload: {moduleUUID, propertyName, value},
  };
}

export interface DeleteModuleProperty {
  moduleUUID: string;
  propertyName: string;
  propertyType: string;
}

export function deleteModuleProperty(
  moduleUUID: string,
  propertyName: string,
  propertyType: string,
): DispatchAction<DeleteModuleProperty> {
  return {
    type: DispatchActions.DELETE_MODULE_PROPERTY,
    payload: {moduleUUID, propertyName, propertyType},
  };
}

export interface DeleteModuleOutput {
  moduleUUID: string;
  propertyName: string;
}

export function deleteModuleOutput(moduleUUID: string, propertyName: string): DispatchAction<DeleteModuleOutput> {
  return {
    type: DispatchActions.DELETE_MODULE_OUTPUT,
    payload: {moduleUUID, propertyName},
  };
}

export interface DeleteModuleEvent {
  moduleUUID: string;
  eventName: string;
}

export function deleteModuleEvent(moduleUUID: string, eventName: string): DispatchAction<DeleteModuleEvent> {
  return {
    type: DispatchActions.DELETE_MODULE_EVENT,
    payload: {moduleUUID, eventName},
  };
}

export interface RenameModuleProperty {
  moduleUUID: string;
  propertyType: ModulePropertyType;
  oldName: string;
  newName: string;
}

export function renameModuleProperty(
  moduleUUID: string,
  propertyType: ModulePropertyType,
  oldName: string,
  newName: string,
): DispatchAction<RenameModuleProperty> {
  return {
    type: DispatchActions.RENAME_MODULE_PROPERTY,
    payload: {moduleUUID, propertyType, oldName, newName},
  };
}

export interface RenameModuleOutput {
  moduleUUID: string;
  oldName: string;
  newName: string;
}

export function renameModuleOutput(
  moduleUUID: string,
  oldName: string,
  newName: string,
): DispatchAction<RenameModuleOutput> {
  return {
    type: DispatchActions.RENAME_MODULE_OUTPUT,
    payload: {moduleUUID, oldName, newName},
  };
}

export interface RenameModuleEvent {
  moduleUUID: string;
  oldName: string;
  newName: string;
}

export function renameModuleEvent(
  moduleUUID: string,
  oldName: string,
  newName: string,
): DispatchAction<RenameModuleEvent> {
  return {
    type: DispatchActions.RENAME_MODULE_EVENT,
    payload: {moduleUUID, oldName, newName},
  };
}

export interface CreateModule
  extends Omit<ModuleCreationParams, 'isOpen' | 'inputSelectorStrings' | 'eventHandlersMap'> {
  inputs: Record<string, string>;
  events: Record<
    string,
    {pluginId: string; eventLabel: string; handlers: Array<{index: number; event: EventHandlerConfig}>}
  >;
  moduleName: string;
}
export function createModule(createModulePayload: CreateModule): DispatchAction<CreateModule> {
  return {
    type: DispatchActions.CREATE_MODULE,
    payload: createModulePayload,
  };
}

export interface PersistModuleInputs {
  moduleUUID: string;
  pageId: string;
  moduleInstanceId: string;
  bSave: boolean;
}

export function persistModuleInputs(
  moduleUUID: string,
  pageId: string,
  moduleInstanceId: string,
  bSave: boolean,
): DispatchAction<PersistModuleInputs> {
  return {
    type: DispatchActions.PERSIST_MODULE_INPUTS,
    payload: {moduleUUID, pageId, moduleInstanceId, bSave},
  };
}
