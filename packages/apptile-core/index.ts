// apptilesdk exports
export {
  getPlatformStyles,
  mergeWithDefaultStyles,
  evaluateStylesMap,
  toRespectiveValue,
  pickWeightAndStyleType,
  isTypographyStyleSheet,
  normalizeFonts,
  alterFontFamily,
  generateTypographyByPlatform,
  IOS_REMOVE_STYLES,
  ANDROID_REMOVE_STYLES,
  fontWeightMap,
} from './styles/styleUtils';

export { ApptileAnalyticsEventTypes, IApptileAnalyticsEvent } from './common/ApptileAnalytics/ApptileAnalyticsTypes';

export { makeBoolean, immutableDeepEqualsFn, createDeepEqualSelector } from './common/utils';
export { getAppDispatch } from './common/utils/dispatcher';
export { getShadowStyle } from './plugins/widgets/common/shadowUtils';
export {
  JSBINDING_TEST_REGEX,
  isJSBinding,
  UNSAFE_renameVariables,
  isSingleObjectString,
} from './common/JSBinding/JSBindingUtils';
export {
  getModelJSForPlugin,
  evaluateJSBindingString,
  getJSBindingVariables,
  JSModel,
} from './common/JSBinding/JSBindingEvaluator';
export { HAS_TEMPLATE_REGEX } from './common/JSBinding/JSBindingTypes';
export { LOADER_SETTINGS_GLOBAL_APPLOADER_KEY, GLOBAL_LOADER_SETTINGS_KEY } from './common/datatypes/GlobalSettingsTypes';

export { modelUpdateAction, destroyPageModel } from './actions/AppModelActions';

export {
  initApptileIsEditable,
  initApptileTilesMode,
  initApptileIsPreview,
  apptileSetActivePage,
} from './actions/ApptileActions';

export { PropertyEditorControlsList } from './common/EditorControlTypes';

// type exports
export type {
  PropertyEditorBaseProps,
  PropertyEditorTypes,
  PropertyEditorCustomProps,
  PropertyEditorConfig,
  PluginEditor,
  Editors,
  PluginEditorsConfig,
  EntityEditorProps,
  ThemeEntityEditorProps,
  EditorProps,
} from './common/EditorControlTypes';

export type {
  ImmutableMapType,
  WidgetType,
  StatePluginSubTypes,
  PluginType,
  ModuleSubTypes,
  PluginSubType,
  EventType,
  MethodType,
  EventHandlerShape,
  PluginNamespace,
  IAnalyticsConfig,
  IAnimatedTransitionModifier,
  LayoutParams,
  PropType,
  AppModelType,
  TupleToObject,
  PluginIdTypePage,
  WidgetConfigType,
  PageParamConfigParams,
  PageTypes,
  CachedDependencyGraph,
  PageConfigParams,
  NavigatorType,
  NavigatorConfigType,
  NavigatorConfigBase,
  ScreenConfigParams,
  NavigatorConfigParams,
  IThemeColorConfig,
  IThemeTypographyConfig,
  ApptileThemeDefinitions,
  ApptileThemeConfigParams,
  ApptileThemeActiveConfig,
  ImageConfigParams,
  ImageRecordShape,
  IFontRecord,
  AppConfigParams,
  EventRouteParam,
  ModulePropertyType,
  ModuleEditorLocationType,
  ModuleEditorConfig,
  ModuleRecordShape,
  AppModelValues,
  ApptileThemeModelType,
  AppPageQuerySettings,
  AppPageQueryTriggers,
  AppPageQueryData,
  AppPageQueryConfigParams,
  ModuleCreationParams,
  BindingError,
  Toast,
} from './common/datatypes/types';
export { 
  ApptileThemeModelRecord,
  AppModelRecord,
  RecordSerializer,
  ModuleRecord,
  ModuleRecords,
  defaultModuleRecord,
  ModuleEditorRecord,
  MODULE_PROPERTY_TYPES,
  EventHandlerConfig,
  EventsConfigParams,
  AnalyticsConfig,
  AnimatedTransitionModifier,
  AnimatedTransitionRecord,
  TransitionsRecord,
  AnimationsConfig,
  LayoutRecord,
  PluginConfig,
  IAnimatedTransitionRecord,
  ITransitionsConfig,
  IAnimationsConfig,
  PluginConfigParams,
  SettingsConfigShape,
  ApptileSettings,
  SettingsConfig,
  PluginConfigType,
  PluginNamespaceImpl,
  PageParamConfig,
  PageOptionConfig,
  PageConfig,
  ScreenConfig,
  NavigatorConfig,
  NavigationConfig,
  ApptileThemeConfig,
  ImageRecord,
  FontRecord,
  AppConfig,
  PageTransitions,
  ModuleEventHandlerConfig,
} from './common/datatypes/types';
export type { WidgetStyleEditorOptions } from './styles/types';
export type { ApptileAnalyticsEventType } from './common/ApptileAnalytics/ApptileAnalyticsTypes';
export type { AnimSelector } from './common/Animations/apptileAnimationTypes';
export type { SafeAny } from './common/types';
export type { PluginModelType, PluginModelChange, PluginListingSection } from './plugins/plugin';
export type { WidgetProps } from './plugins/widgets/widget';
export type { Selector, ModelChange, SerializedBindings } from './common/DependencyGraph/types';
export { objsel, strsel, strsel_index } from './common/DependencyGraph/types';
export { updateProperty, DependencyGraph } from './common/DependencyGraph/DependencyGraph';
export type { IActionMetadata } from './plugins/triggerAction';
export type { RootState } from './store/RootReducer';
export { injectReducer } from './store/RootReducer';
export type { Position } from './plugins/widgets/common/components/CustomModal';
export { default as CustomModal } from './plugins/widgets/common/components/CustomModal';
export type { ModelUpdateAction } from './actions/AppModelActions';
export type {
  ActionType,
  DispatchAction,
  DispatchEmptyAction,
  UpdateAppSettingsValuePayload,
  CreateModule,
  BatchCreateModulePluginsAction,
  CreateModuleEvent,
  CreateModuleOutput,
  CreateModuleProperty,
  DeleteModuleEvent,
  DeleteModuleOutput,
  DeleteModuleProperty,
  PersistModuleInputs,
  // TODO(gaurav): import this type
  // RemapModuleInstance,
  RenameModuleEvent,
  RenameModuleOutput,
  RenameModuleProperty,
  UpdateModuleOutput,
  UpdateModuleProperty,
} from './actions/DispatchActions';

export {
  DispatchActions,
  selectPlugin,
  updateSettingsValue,
  changeAppConfig,
  changeAppFork,
  setAppSettings,
  addDatasourcePlugin,
  createModule,
  deleteAppSettings,
  triggerPageQuery,
  deleteModuleEvent,
  renameModuleEvent,
  deleteModuleOutput,
  renameModuleOutput,
  updateModuleOutput,
  createModuleEvent,
  createModuleOutput,
  createModuleProperty,
  deleteModuleProperty,
  renameModuleProperty,
  updateModuleProperty,
  persistModuleInputs,
  navigateToScreen,
  triggerAction,
  triggerPageEvent,
  sendAnalyticsEvent,
  commitStageModel,
  triggerOnPluginUpdate,
} from './actions/DispatchActions';

export {
  AppConfigLoadedPayload,
  pluginConfigUpdatePath,
  NavComponentDelete,
  NavUpdateName,
  pluginConfigEventUpdate,
  pluginConfigUpdate,
  UpdatePageId,
  pluginConfigDeleteEvent,
  pluginLayoutUpdate,
  pluginStyleUpdate,
  navConfigUpdate,
  navUpdateName,
  navComponentDelete,
  updatePageId,
  updatePageConfig,
  deletePageConfig,
  navigationConfigUpdatePath,
  navigationConfigSetPathValue,
  pageConfigUpdatePath,
  pageConfigSetPathValue,
  pluginConfigSetPathValue,
  addNavigationNav,
  addNavigationPage,
  pluginConfigValueSetRaw,
  pluginUpdateConfigAction,
  bulkPluginUpdates,
} from './actions/AppConfigActions';

export { usePlaceHolder } from './components/common/ApptileLoadingStatePlaceholder/usePlaceHolder';
export { default as useIsEditable } from './common/utils/useIsEditable';
export { default as ApptileLoadingStatePlaceholder } from './components/common/ApptileLoadingStatePlaceholder/ApptileLoadingStatePlaceholder';
export { EmptyPlaceholder } from './components/common/EmptyPlaceholder';
export {
  datasourceTypeModelSel,
  selectAppModel,
  selectStageModel,
  selectPreInitModel,
  selectPageModelInitState,
  apptileNavigationSelector,
  shopifyProductCacheSelector,
  selectPluginStageModel,
  datasourceByIdModelSel,
  selectPageIdForPageKey,
} from './selectors/AppModelSelector';
export { default as useTappableGestureWrapperHook } from './plugins/widgets/common/hooks/useTappableGestureWrapperHook';

export { default as useEndDrag } from './common/utils/useEndDrag';
export {
  selectAppConfig,
  appFontsSelector,
  pagePluginsSelector,
  selectPluginConfig,
  selectGlobalPlugins,
  selectLoaderPageConfigs,
  pageConfigsSelector,
} from './selectors/AppConfigSelector';
export { selectAppSettingsForKey } from './selectors/AppSettingsSelector';
export { selectModuleByUUID, selectModulesCache } from './selectors/ModuleSelectors';
export { selectPageConfigs, selectPageConfigForPage } from './selectors/PageSelector';
export { selectAppState, selectEditorState } from './selectors';
export { MaterialCommunityIcons, Feather, Ionicons, iconList } from './icons/icons';
export { default as Icon } from './icons';
export { ITypographyItem } from './styles/theme/global/declaration';
export { baseGlobalThemeConfig, defaultTypography } from './styles/theme/global';
export * as BrandSettingsTypes from './common/datatypes/BrandSettingsTypes';
export * as HeaderSettingsTypes from './common/datatypes/HeaderSettingsTypes';
export { useTheme } from './styles/theme/context';
export {
  isContainerTypeWidget,
  getOptimalImageSize,
  getWidgetsWithThemes,
  resolveWidgetThemeKeySelector,
} from './plugins/widgets/widget';
export { default as useCallbackRef } from './common/utils/useCallBackRef';
export {
  resolvePluginActions,
  resolvePluginAnimations,
  GetRegisteredPluginInfo,
  GetRegisteredPlugin,
  resolvePluginEditorsConfig,
  resolvePluginListing,
  getEventsEditorConfig,
  GetRegisteredConfig,
  GetRegisteredOnPluginUpdate,
  COMMONLY_USED_PLUGIN_TYPES,
  CommonPluginsType,
  RegisteredPlugins,
  resolvePluginPropertyEditor,
  resolvePluginDocs,
  PLUGIN_LISTING_SECTIONS,
  EventTriggerIdentifier,
  TriggerActionIdentifier,
  PluginListingSettings,
  PluginPropertySetting,
  PluginPropertySettings,
  AppPageTriggerOptions,
  connectPlugin,
  connectConfig,
  onPluginUpdateFn,
  CachePolicy,
  makeActionSelectorConfig,
  registerPlugin,
} from './plugins/plugin';
export { createReducer } from './store/ReducerUtils';

export {
  CreateWidget,
  DragDropItemTypes,
  isMoveWidget,
  MoveWidget,
  isCreateWidget,
  CreateWidgetPayload,
  MoveWidgetPayload,
} from './common/datatypes/dndTypes';

export { connectWidget } from './plugins/widgets/widget';
export { default as LocalStorage } from './common/LocalStorage';

export * from './constants/modelConstants';
export { getPluginModelSelectorFromSelector, getPluginSelector, addNamespace } from './common/datatypes/utils';

export type { EditorStyleItem } from './plugins/widgets/common/widgetEditors';
export {
  hapticEditors,
  defaultEditors,
  defaultStyleEditors,
  containerLayoutEditors,
  layoutEditors,
  defaultTypographyEditors,
} from './plugins/widgets/common/widgetEditors';

export { CurrentScreenContext, getNavigationContext, setNavigationContextForApp } from './views/navigationContext';
export {
  useWidgetRefContext,
  WidgetRefContext,
  WidgetRefRegistry,
  WidgetRefContextProvider,
} from './plugins/widgets/common/WidgetRef/WidgetRefContext';
export { default as ThemeContainer, getPluginModelStylesFromConfig } from './views/containers/ThemeContainer';
export { default as store, AppDispatch, sagaMiddleware } from './store';
export { ApptileCanvasScaleContext } from './components/common/ApptileCanvasScaleContext';
export { selectPagePluginLayouts } from './selectors/selectPageWidgetLayouts';
export {
  apptileStateSelector,
  selectAppIsEditable,
  selectAppIsPreview,
  selectAppIsTilesOnly,
} from './selectors/ApptileStateSelectors';
export {
  selectApptileThemeModel,
  selectActiveThemeConfig,
  selectThemeInitialized,
} from './selectors/ApptileThemeSelectors';
export { activeNavigationSelector, selectScreens } from './selectors/ActiveNavigationSelectors';

export { default as buildWidgetTree } from './plugins/widgets/common/layout/widgetTreeBuilder';

export { PageEventsList, PageEventType } from './common/datatypes/PageTypes';

export {
  LINKING_SETTINGS_KEY,
  LINKING_SETTINGS_PREFIXES_KEY,
  LINKING_SETTINGS_LINKS_KEY,
  ILinkingItem,
  ILinkingConfig,
} from './common/datatypes/LinkingTypes';
export { getPluginsContained, getModuleDependencies } from './sagas/AppConfigSaga';
export {
  getPluginsContained,
  getModuleDependencies,
  versionMatchAppConfig,
} from './sagas/AppConfigSaga';
export {
  hydratePluginModelCachesForPage,
  generatePurePageModelFromConfig, 
  initAppModel, 
  initGlobalPageModels,
  sortPluginsByContainerDepth,
  modelUpdateSaga,
  recalculatePluginConfig,
  addPluginDone,
  batchInitModelForPlugins,
} from './sagas/AppModelSaga';
export { rootSaga } from './sagas';
export { default as ViewerWidgetCanvas } from './plugins/widgets/common/components/WidgetCanvas';

export { validateNaming } from './common/helpers/validateNaming';
export { LogRocket } from './platformSplitters/logrocket';

export { routingInstrumentation, default as SentryHelper } from './common/Sentry';

export { default as logger } from './common/logger';
export { setAppConstants, getAppConstants, ApptileConstantsType } from './constants/api';
export { default as CustomIconProvider } from './components/common/CustomIconProvider';
export { default as CustomIconContext } from './components/common/CustomIconContext';
export { default as useIsPreview } from './common/utils/useIsPreview.web';

export { default as tileCollection } from './styles/theme/tile';
export { toastEditorInputConfig } from './styles/theme/tile/toast';
export { pressable, PressableEditorInputConfig } from './styles/theme/tile/pressable';
export { themeEvaluator } from './styles/theme/themeEvaluator';

export {
  EnteringAnimationNames,
  ExitingAnimationNames,
  LayoutAnimationNames,
  TransitionAnimationType,
} from './common/Animations/apptileAnimationTypes';

export { editableTags } from './plugins/widgets/RichTextWidget/config';
export { scrapeEmptyValues } from './common/helpers/scrapeEmptyValues';

export { default as useHoverHook } from './common/utils/useHover';

export {
  ApptileAnimationsRegistry,
  ApptileAnimationsContext,
  ApptileAnimationsContextProvider,
  useApptileAnimationsContext,
} from './common/Animations/apptileAnimationRegistry';
export {
  LabelTemplateConfig,
  LabelControlComponent,
  labelEditorsConfig,
} from './plugins/widgets/common/controls/LabelComponent';
export { IDefaultFontContext, FontContext, useLoadedFonts } from './fonts/context';
export { IDefaultThemeContext } from './styles/theme/context';
export { FontsProvider } from './fonts/fontsProvider';
export { performHapticFeedback } from './plugins/state/ApptileGlobalPlugin/ApptileHaptics';
export { default as ApptileGlobalPlugin } from './plugins/state/ApptileGlobalPlugin';
export { encodeListViewPlugin, renderWidgetTreeNode } from './plugins/widgets/common/containerWrappers';
export { default as ApptileFlexbox } from './plugins/widgets/ApptileFlexbox';
export { default as KeyboardAwareApptileFlexbox } from './plugins/widgets/KeyboardAwareApptileFlexbox';
export { default as Placeholder } from './components/common/ApptileLoadingStatePlaceholder/ApptileLoadingStatePlaceholder';
export { default as PreviewAppOverlay } from './components/common/PreviewAppOverlay';
export { default as JSSplashScreen, setSplashScreenMessage } from './components/common/JSSplashScreen';
export { default as BottomSheet } from './plugins/widgets/common/components/BottomSheet';
export { default as ModuleOutput } from './plugins/module/ModuleOutput';

export { default as ModuleProperty } from './plugins/module/ModuleProperty';
export { default as ModuleInstance } from './plugins/module/moduleInstance';
export { default as AppContainer } from './views/containers/AppContainer';
export { ActionHandler } from './plugins/triggerAction';
export { default as CustomList } from './plugins/state/CustomList';
export { getUniqueDeviceId } from './common/deviceInfo/deviceInfo';
export { default as getConfigValue } from './lib/RNGetValues';
export { default as ApptileLocalSettings } from './common/ApptileLocalSettings/ApptileLocalSettings';
export { default as initApptileUserIdentifier } from './common/ApptileLocalSettings/initApptileUserIdentifier';
export { default as updateFCMToken } from './common/ApptileLocalSettings/updateFCMToken';
export { default as NativeEditableWidget } from './plugins/widgets/common/components/EditableWidget';
export { default as FileSystem } from './common/FileSystem';
export { default as BundleApi } from './api/BundleApi';
export { default as LivelyApi} from './api/LivelyApi';
export {restartAppConfig} from './actions/AppConfigActions';
export {
  setActiveBundle, 
  registerBundle, 
  reloadBundle, 
  getActiveBundle
} from './lib/RNDynamicBundle';
export { addCustomEventListener, triggerCustomEventListener } from './common/utils/CustomEventSystem';
export {
  APPTILE_NEW_WIDGET_IDENTIFIER,
  WidgetLayout,
  WidgetTreeNode,
  WidgetTree,
  WidgetLayoutMap,
} from './plugins/widgets/widgetLayout';
export { goBack , navigateToRoot} from './views/navigation/ApptileRootNavigationHelpers';
export { default as EmbeddedAppPageContainer } from './views/screen/AppScreenContainer';
export { default as ApptileScrollView } from './views/screen/ApptileScrollView';
export { apptileState } from './store/ApptileReducer';

export * from './styles/theme/context';
export * from './common/datatypes/GlobalSettingsTypes';
export * from './web/common/pricePlanConstants';

import type Toast from 'react-native-toast-notifications';
import type {Logger} from './common/logger';
export {default as AppUpdaterOverlay} from './components/common/AppUpdaterOverlay';

export { getUrlFromRemoteMessage } from './common/utils/mobile-only'

export { default as ImageComponent } from './plugins/widgets/ImageWidget/ImageComponent';

declare global {
  var toast: Toast;
  var logger: Logger;
}

export { DEFAULT_BRANCH_NAME, DEFAULT_FORK_NAME } from './common/SnapshotConstants'