{"name": "apptile-core", "version": "0.18.20", "description": "", "main": "index.ts", "repository": {"type": "git", "url": "git+https://github.com/clearsight-dev/ReactNativeTSProjeect.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/clearsight-dev/ReactNativeTSProjeect/issues"}, "homepage": "https://github.com/clearsight-dev/core-utils#readme", "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-export-namespace-from": "^7.23.4", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.6.3", "babel-loader": "^8.2.3", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.66.2", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "terser-webpack-plugin": "^5.3.10", "typescript": "5.0.4", "url-loader": "^4.1.1", "webpack": "^5.51.1", "webpack-cli": "^4.8.0"}, "peerDependencies": {"@apollo/client": "^3.10.7", "@babel/plugin-syntax-typescript": "7.20.0", "@babel/polyfill": "7.12.1", "@babel/runtime": "7.21.0", "@babel/standalone": "7.12.9", "@gorhom/portal": "1.0.14", "@react-native-async-storage/async-storage": "^1.22.3", "@react-native-clipboard/clipboard": "^1.14.0", "@react-native-firebase/messaging": "15.0.0", "@react-navigation/bottom-tabs": "6.5.7", "@react-navigation/drawer": "6.6.2", "@react-navigation/native": "^6.0.8", "@react-navigation/native-stack": "6.9.12", "@react-navigation/stack": "^6.3.21", "@sentry/core": "^8.13.0", "@sentry/react-native": "^5.24.1", "@types/jsan": "^3.1.5", "@types/redux-logger": "^3.0.12", "@types/redux-saga": "^0.10.5", "@types/tinycolor2": "^1.4.6", "acorn-walk": "8.2.0", "axios": "1.7.2", "babel-plugin-module-resolver": "4.1.0", "babel-plugin-transform-inline-environment-variables": "0.4.4", "immutability-helper": "3.1.1", "immutable": "4.2.4", "jsan": "3.1.14", "lodash-es": "4.17.21", "logrocket": "6.0.0", "moment": "2.29.4", "react": "18.2.0", "react-dnd": "14.0.5", "react-dnd-html5-backend": "14.1.0", "react-dom": "18.2.0", "react-native": "0.73.8", "react-native-device-info": "9.0.2", "react-native-gesture-handler": "^2.14.0", "react-native-haptic-feedback": "1.14.0", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-safe-area-context": "^4.8.2", "react-native-svg": "^15.2.0", "react-native-toast-notifications": "3.3.1", "react-native-vector-icons": "9.0.0", "react-native-web": "0.17.5", "react-redux": "7.2.9", "redux-logger": "3.0.6", "redux-saga": "1.2.2", "reselect": "4.1.7", "rn-dynamic-fonts": "0.1.2", "rn-fetch-blob": "git+ssh://**************/clearsight-dev/rn-fetch-blob.git#2654eff9b6393eeeef8c1198652373845a948fa8", "tinycolor2": "1.6.0", "validate-color": "2.2.4"}, "engines": {"node": ">=18"}, "overrides": {"@react-native-clipboard/clipboard": {"react-native": "~0.73.0", "react-native-windows": "~0.73.0"}}}