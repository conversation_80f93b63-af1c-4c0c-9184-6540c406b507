import React, {useEffect, useMemo} from 'react';
import {useColorScheme} from 'react-native';
import {useSelector} from 'react-redux';
import {UnknownModel} from '../../plugins/plugin';
import {GetWidgetStyleProps} from '../../plugins/widgets/widget';
import {themeConfigSelector} from '../../selectors/AppConfigSelector';
import {IDefaultThemeContext, ThemeProvider, useTheme} from '../../styles/theme/context';
import {getActiveGlobalConfig} from '../../styles/theme/global/utils';
import {baseGlobalThemeConfig} from '../../styles/theme/global';
import {themeEvaluator} from '../../styles/theme/themeEvaluator';
import _, {defaultsDeep} from 'lodash';
import {selectApptileThemeModel, selectThemeInitialized} from '../../selectors/ApptileThemeSelectors';
import {useAppDispatch} from '../../store';
import {DispatchActions, setApptileThemeActiveConfig} from '../../actions/DispatchActions';
import {ApptileThemeConfigParams} from '../../common/datatypes/types';
import {IDefaultFontContext, useLoadedFonts} from '../../fonts/context';

export const usePluginModelStyles = (config: UnknownModel) => {
  const themeContext = useTheme();
  const pluginType = config?.get('subtype') as string;
  const widgetStyles = config?.getIn(['config', 'style']);

  return React.useMemo(
    () => GetWidgetStyleProps(pluginType, themeContext?.themeEvaluator, widgetStyles),
    [pluginType, themeContext, widgetStyles],
  );
};

export const getPluginModelStylesFromConfig = (
  themeEval: IDefaultThemeContext['themeEvaluator'],
  config: UnknownModel,
) => {
  const pluginType = config?.get('subtype') as string;
  const widgetStyles = config?.getIn(['config', 'style']);

  return GetWidgetStyleProps(pluginType, themeEval, widgetStyles);
};

export const withModelStyles = Component =>
  React.forwardRef((props, ref) => {
    const themeContext = useTheme();
    const {loadedFonts} = useLoadedFonts();
    const {model, config} = props as any;
    // const pluginType = model?.get('pluginType') as string;
    // const widgetStyles = model?.get('style');
    const pluginType = config?.get('subtype') as string;
    const widgetStyles = config?.getIn(['config', 'style']);

    const modelStyles = React.useMemo(
      () => GetWidgetStyleProps(pluginType, themeContext?.themeEvaluator, widgetStyles),
      [pluginType, themeContext, widgetStyles],
    );

    return (
      <Component key={props.containerId + props.instance} ref={ref} {...{...props, theme: themeContext, modelStyles}} />
    );
  });

const ThemeContainer: React.FC = ({children}) => {
  logger.info('[RERENDER] Theme Container');
  const dispatch = useAppDispatch();
  const themeConfig = useSelector(themeConfigSelector);
  const isThemeInitialized = useSelector(selectThemeInitialized);
  const apptileThemeModel = useSelector(selectApptileThemeModel);
  const {activeThemeConfig, themeValue} = apptileThemeModel;
  const osColorScheme = useColorScheme();

  useMemo(() => {
    if (!isThemeInitialized && themeConfig) {
      logger.info('[DEBUG] Theme Container Dispatch INIT_APPTILE_THEME');
      setTimeout(() => dispatch({type: DispatchActions.INIT_APPTILE_THEME}), 0);
    }
  }, [isThemeInitialized, themeConfig, dispatch]);

  useEffect(() => {
    const updateActiveTheme = async () => {
      logger.time('[TIMER] Theme Processing');
      const themeFromAppConfig = themeConfig?.toJS() ?? {};
      defaultsDeep(themeFromAppConfig, baseGlobalThemeConfig);
      const globalFromAppConfig = themeFromAppConfig as ApptileThemeConfigParams;

      const isDark = globalFromAppConfig.isDarkModeSupported && osColorScheme === 'dark';
      const activeGlobal = getActiveGlobalConfig(globalFromAppConfig, isDark);
      logger.timeEnd('[TIMER] Theme Processing');
      if (!_.isEqual(activeGlobal, activeThemeConfig)) {
        logger.info('[DEBUG] Theme Container Dispatch setApptileThemeActiveConfig');
        dispatch(setApptileThemeActiveConfig(activeGlobal));
      }
    };
    updateActiveTheme();
  }, [isThemeInitialized, activeThemeConfig, dispatch, themeConfig, osColorScheme]);

  const ThemeContextValue = useMemo(() => {
    return {
      value: themeValue,
      themeEvaluator: themeEvaluator(themeValue),
    };
  }, [themeValue]);

  return <ThemeProvider value={ThemeContextValue}>{children}</ThemeProvider>;
};

export default ThemeContainer;
