import React, {useCallback, useEffect, useMemo, useState, useRef} from 'react';
import {DefaultTheme, LinkingOptions, NavigationContainer, Theme, useNavigationState} from '@react-navigation/native';
import {ActivityIndicator, View, Platform, Linking, BackHandler} from 'react-native';
import {PortalProvider} from '@gorhom/portal';
import {useSelector} from 'react-redux';
import _ from 'lodash';
import validateColor from 'validate-color';

import {fetchAppConfig} from '../../actions/AppConfigActions';
import {initApptileConstants} from '../../actions/ApptileActions';
import {
  appConfigReduxStateSelector,
  navConfigSelector,
  pageConfigsSelector,
} from '../../selectors/AppConfigSelector';
import {useAppDispatch} from '../../store/index';
import {RootState} from '../../store/RootReducer';
import {useTheme} from '../../styles/theme/context';
import {createNavigatorsFromConfig} from '../navigation';
import {apptileNavigationRef} from '../navigation/ApptileRootNavigationHelpers';
import {ToastProvider} from '../../components/common/appToast';
import {apptileNavigationSelector} from '../../selectors/AppModelSelector';
import {apptileStateSelector} from '../../selectors/ApptileStateSelectors';
import {ILinkingItem, LINKING_SETTINGS_KEY, LINKING_SETTINGS_LINKS_KEY} from '../../common/datatypes/LinkingTypes';
import {selectAppSettingsForKey} from '../../selectors/AppSettingsSelector';
import {getLinkingConfig} from '../../common/navigation/NavigationHelpers';
import {
  getInitialNotification,
  getUrlFromRemoteMessage,
  onNotificationOpenedApp,
  isDistributedApp,
} from '../../common/utils/mobile-only';
import {addCustomEventListener, triggerCustomEventListener} from '../../common/utils/CustomEventSystem';
import {WidgetRefContextProvider} from '../../plugins/widgets/common/WidgetRef/WidgetRefContext';
import {GlobalComponentsContextProvider} from '../../components/ApptileGlobalComponents/ApptileGlobalComponents';
import LoaderComponentsBuilder from './LoaderComponentsBuilder';
import {hideJSSplashScreen} from '../../components/common/JSSplashScreen';
import '../../common/logger';
import SentryHelper, {routingInstrumentation} from '../../common/Sentry';

type NavigationThemeColors = 'primary' | 'background' | 'card' | 'text' | 'border' | 'notification';

const appModelInitializedSelector = (state: RootState) => state.appModel.modelInitialized;
const appLinkingSettingSelector = (state: RootState) => selectAppSettingsForKey(state, LINKING_SETTINGS_KEY);


const BackHandlerManager = ({navigationRef}) => {
  const routes = useNavigationState(state => state?.routes);
  const isRootScreen = routes?.length === 1;

  const handleBackPress = useCallback(() => {
    const currentRoute = navigationRef.current?.getCurrentRoute();
    if (isRootScreen) {
      return routes?.[0]?.state?.type != 'tab'; // Prevent default back behavior
    }
    return false; // Allow normal navigation behavior
  }, [isRootScreen, routes]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      handleBackPress
    );

    return () => backHandler.remove(); // Cleanup listener on unmount
  }, [handleBackPress]);

  return null;
};

const AppContainer = ({apptileInit = false}) => {
  logger.info(`[DEBUG] RENDER AppContainer`);
  const dispatch = useAppDispatch();
  const {themeEvaluator} = useTheme();

  useMemo(() => {
    if (apptileInit) {
      logger.info('[DEBUG Dispatching initApptileConstants]');
      setTimeout(() => {dispatch(initApptileConstants())}, 0);
    }
  }, [apptileInit, dispatch]);
  const appModelInitialized = useSelector(appModelInitializedSelector);
  const apptileState = useSelector(apptileStateSelector);
  const appConfigReduxState = useSelector(appConfigReduxStateSelector);
  const navConfig = useSelector(navConfigSelector);
  const pages = useSelector(pageConfigsSelector);
  const apptileNavigation = useSelector(apptileNavigationSelector);
  const rootNavigator = navConfig?.get('rootNavigator');
  const linkingSettings = useSelector(appLinkingSettingSelector);
  const [isLinkingLoaded, setIsLinkingLoaded] = useState(false);
  const [linking, setLinking] = useState<Pick<LinkingOptions<ReactNavigation.RootParamList>, 'prefixes' | 'config'>>();

  useMemo(() => {
    if (!appConfigReduxState.isFetched && !appConfigReduxState.isFetching && apptileState.isInitialized) {
      logger.info(
        '[DEBUG] AppContainer: Fetching AppConfig',
        JSON.stringify(appConfigReduxState),
        apptileState.appId,
        apptileState.appFork,
      );
      setTimeout(() => dispatch(fetchAppConfig(apptileState.appId, apptileState.appFork)), 0);
    }
  }, [appConfigReduxState, apptileState.appId, apptileState.appFork, apptileState.isInitialized, dispatch]);

  const generateAndSetLinkingConfig = useCallback(
    (linkingPrefixes: string[]) => {
      if (!apptileNavigation) return;
      const linkingLinks = linkingSettings.getSettingValue(LINKING_SETTINGS_LINKS_KEY) as ILinkingItem[];
      const linkingConfig = getLinkingConfig(apptileNavigation, linkingPrefixes, linkingLinks);
      setLinking(_linking => (_.isEqual(linkingConfig, _linking) ? _linking : linkingConfig));
      setIsLinkingLoaded(true);
    },
    [apptileNavigation, linkingSettings],
  );

  useEffect(() => {
    (async () => {
      // if (!(await isDistributedApp())) return;
      logger.info('NAVIGATION UPDATE in distributed app!!');
      // TODO(gaurav) DONE this should come from host
      logger.info("Got host value for getLinkingPrefixesInDistributedApp: ", global.getLinkingPrefixesInDistributedApp);
      const linkingPrefixes = global?.getLinkingPrefixesInDistributedApp ? (await global?.getLinkingPrefixesInDistributedApp() ?? []) as string[]: [];
      logger.info('Linking Prefixes: ', linkingPrefixes);
      generateAndSetLinkingConfig(linkingPrefixes);
    })();
  }, [generateAndSetLinkingConfig]);

  useEffect(() => {
    if (appModelInitialized) {
      // ApptileAnalytics.initialize();
      if (Platform.OS !== 'web' && !__DEV__) SentryHelper.addMetadataTags();
    }
  }, [appModelInitialized]);

  useEffect(() => {
    triggerCustomEventListener('markEnd', 'pre_splash');
    triggerCustomEventListener('markStart', 'splash_visible');
  }, []);

  const navigationTheme: Record<NavigationThemeColors, string> = {
    primary: themeEvaluator('colors.navPrimary'),
    background: themeEvaluator('colors.navBackground'),
    card: themeEvaluator('colors.navCard'),
    text: themeEvaluator('colors.navText'),
    border: themeEvaluator('colors.navBorder'),
    notification: themeEvaluator('colors.navBadge'),
  };

  for (const key in navigationTheme) {
    const accessor = key as NavigationThemeColors;
    navigationTheme[accessor] = validateColor(navigationTheme[accessor])
      ? navigationTheme[accessor]
      : DefaultTheme.colors[accessor];
  }

  const navStyle = {
    ...DefaultTheme,
    // dark: global.isDark as boolean,
    colors: {
      ...DefaultTheme.colors,
      ...navigationTheme,
    },
  };

  let navigationContainerProps: {
    theme?: Theme;
    linking?: LinkingOptions<ReactNavigation.RootParamList>;
    onReady?: () => void;
  } = {theme: navStyle};

  const routeNameRef = useRef("initial");

  if (['android', 'ios'].includes(Platform.OS)) {
    navigationContainerProps = {
      theme: navStyle,
      onStateChange: state => {
        const currentRoute = state.routes[state.index].name;      
        triggerCustomEventListener('markStart', 'navigation_to_focus', {routechange_src: routeNameRef.current + "_" + currentRoute});
        routeNameRef.current = currentRoute;
      },
      linking: {
        ...(linking ? linking : {}),
        config: {
          ...(linking ? linking?.config : {}),
          initialRouteName: "Main",
        },
        async getInitialURL() {
          const url = await Linking.getInitialURL();
          if (url) return url;
          const remoteMessage = await getInitialNotification();
          if (remoteMessage) return getUrlFromRemoteMessage(remoteMessage);
        },
        subscribe(listener) {
          let timeoutId: NodeJS.Timeout;
          const delayedListener = (url: string) => {
            timeoutId = setTimeout(() => {
              listener(url);
              logger.info('redirected to', url);
            }, 2000);
          };

          const customListener = addCustomEventListener('deeplink_request', (url: string) => delayedListener(url));
          const linkingListener = Linking.addEventListener('url', ({url}) => {
            logger.info('RECIEVED DEEP-LINK URL: ', url);
            return listener(url)
          });
          const unsubscribeNotificationListener = onNotificationOpenedApp(remoteMessage => {
            const url = getUrlFromRemoteMessage(remoteMessage);
            if (url) listener(url);
          });
          return () => {
            if (timeoutId) clearTimeout(timeoutId);
            customListener.remove();
            linkingListener.remove();
            unsubscribeNotificationListener();
          };
        },
      },
    };
  }

  const childNavigators = useMemo(() => {
    return !!rootNavigator && createNavigatorsFromConfig(rootNavigator, apptileNavigation, {}, pages);
  }, [rootNavigator, apptileNavigation, pages]);

  return appModelInitialized && isLinkingLoaded ? (
    <PortalProvider>
      <GlobalComponentsContextProvider>
        <WidgetRefContextProvider>
          <LoaderComponentsBuilder />
          <NavigationContainer
            ref={apptileNavigationRef}
            {...navigationContainerProps}
            onReady={() => {
              hideJSSplashScreen?.();
              triggerCustomEventListener('markEnd', 'splash_visible');
              routingInstrumentation.registerNavigationContainer(apptileNavigationRef);
            }}>
            {childNavigators}

          <BackHandlerManager navigationRef={apptileNavigationRef} />
          </NavigationContainer>
          <ToastProvider />
        </WidgetRefContextProvider>
      </GlobalComponentsContextProvider>
    </PortalProvider>
  ) : (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      {/* <ActivityIndicator size="large" /> */}
    </View>
  );
};

export default AppContainer;
