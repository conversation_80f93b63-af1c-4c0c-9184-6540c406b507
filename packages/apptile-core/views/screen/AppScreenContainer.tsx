import {useTheme} from '../../styles/theme/context';
import {PortalHost} from '@gorhom/portal';
import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useEffect, useMemo, useRef} from 'react';
import {Platform, ScrollViewProps, StatusBar, StyleSheet, View, ViewProps, useColorScheme} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useDispatch, useStore} from 'react-redux';
import {destroyPageModel, initPageModel} from '../../actions/AppModelActions';
import {apptileSetActivePage} from '../../actions/ApptileActions';
import {pageFocussed} from '../../actions/DispatchActions';
// TODO(gaurav) DONE eventbus
// import {ApptileAnalytics} from '../../common/ApptileAnalytics';
import {PageConfig} from '../../common/datatypes/types';
import useCallbackRef from '../../common/utils/useCallBackRef';
import EditorWidgetCanvas from '../../plugins/widgets/common/components/EditorWidgetCanvas';
import WidgetCanvas from '../../plugins/widgets/common/components/WidgetCanvas';
import {PageModelChannel} from '../../sagas/AppModelSaga';
import {AppDispatch} from '../../store';
import {RootState} from '../../store/RootReducer';
import {CurrentScreenContext, setNavigationContextForApp} from '../navigationContext';
import ApptileScrollView from './ApptileScrollView';
import tinycolor from 'tinycolor2';
import { triggerCustomEventListener } from '../../common/utils/CustomEventSystem';

const mapStateToProps = (state: RootState, ownProps) => {
  return {
    isEditable: state.apptile.isEditable,
  };
};

const Colors = {
  primary: '#1292B4',
  white: '#FFF',
  lighter: '#F3F3F3',
  light: '#DAE1E7',
  dark: '#444',
  darker: '#222',
  black: '#000',
};

const styles = StyleSheet.create({
  sectionContainer: {
    marginTop: 32,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
  },
  sectionDescription: {
    marginTop: 8,
    fontSize: 18,
    fontWeight: '400',
  },
  highlight: {
    fontWeight: '700',
    fontStyle: 'italic',
  },
});

interface IAppScreenContainerProps {
  dispatch: AppDispatch;
}

interface IAppScreeContainerState {
  num: number;
}

// function getRouteModelForName(navModel, name) {
//   if (!navModel) return;
//   if (navModel?.name === name) return navModel;
//   if (navModel.screens) {
//     for (var screen of Object.values(navModel.screens)) {
//       var retVal = getRouteModelForName(screen, name);
//       if (retVal) return retVal;
//     }
//   }
// }
// const pageModelSelector = state => pageKey => state.appModel.getModelValue([])?.get(pageKey);

const AppPageContainer: React.FC<any> = ({navigation, route, isEditable, screen}) => {
  logger.info(`[DEBUG] RERENDER AppPageContainer`);
  const {themeEvaluator} = useTheme();
  const store = useStore();
  const dispatch = useDispatch();

  const pageId = screen?.screen;
  const pageKey = route.key;
  const path = route.path;
  const params = {...(route.params ?? {}),path};
  // const pageModelSel = useMemo(() => state => selectPageModelInitState(state, pageKey), [pageKey]);
  // const pageModelInitState = useSelector(pageModelSel, _.isEqual);
  const pageConfig: PageConfig = store.getState()?.appConfig.current?.pages?.get(pageId);
  const attachmentOptions = {navTitle: screen?.title};
  const isModal: boolean = screen?.isModal;

  useMemo(() => {
    // setTimeout(() => {
    //   PageInitEmitter.initPage(initPageModel(pageId, pageKey, {params}));
    // }, 0);
    // setTimeout(() => {
    //   PageModelChannel.put(initPageModel(pageId, pageKey, {params}));
    // }, 4000);
    // const modelInit = async () => PageModelChannel.put(initPageModel(pageId, pageKey, {params}));
    // modelInit();
    // InteractionManager.runAfterInteractions(() => {
    logger.info('[LIFECYCLE] PAGE initPageModel');
    PageModelChannel.put(initPageModel(pageId, pageKey, {params}));
    // });
    logger.info(`Inited ${pageKey}`);

    // logger.info('[LIFECYCLE] PAGE initPageModel');
    // setTimeout(() => {
    //   dispatch(initPageModel(pageId, pageKey, {params}));
    // }, 0);
    setNavigationContextForApp(navigation);
    // logger.info('[LIFECYCLE] PAGE dispatched initPageModel');
  }, []);

  useEffect(
    useCallbackRef(() => {
      return () => {
        setTimeout(() => {
          dispatch(destroyPageModel(pageId, pageKey));
          logger.info(`Removing ${pageKey}`);
        }, 800);
      };
    }),
    [],
  );

  const focused = useRef(false);

  const pageBootFocusCallback = useCallback(() => {
    const doFocusAsync = async () => {
      // logger.info('[LIFECYCLE] PAGE BOOT FOCUS CALLBACK');
      logger.info('[LIFECYCLE] FOCUS CALLBACK');
      setNavigationContextForApp(navigation);
      const activeNavigation = store.getState()?.activeNavigation;
      if (!(activeNavigation?.activePageKey === pageKey && activeNavigation?.activePageId === pageId)) {
        logger.info('[LIFECYCLE] Set ACTIVE PAGE');
        setTimeout(() => {
          dispatch(apptileSetActivePage(pageKey, pageId))
          triggerCustomEventListener('markEnd', 'navigation_to_focus', {routechange_dest: pageId});
        }, 0);
        // TODO(gaurav) DONE move to eventbus
        // FIXME: This needs to happen when we can actually evaluate analytics params once model is inited.
        // For now just passing page params is supported.
        /*
        if (pageConfig?.analytics && pageConfig?.analytics?.enabled) {
          ApptileAnalytics.sendEvent(
            pageConfig?.analytics.type,
            pageConfig?.analytics.name,
            pageConfig?.analytics.params,
          );
        }
        */
        if (pageConfig?.analytics?.enabled) {
          triggerCustomEventListener('ApptileAnalyticsSendEvent', pageConfig.analytics.type, pageConfig.analytics.name, pageConfig.analytics.params);
        }
        // const screenHeader = _.get(screen, 'header', '');
        // if (screenHeader) {
        //   let pageHeaderModelUpdate = Object.entries(attachmentOptions).map(([paramName, paramValue]) => {
        //     return {
        //       selector: [screen.header, 'options', paramName],
        //       newValue: paramValue,
        //     };
        //   });
        //   dispatch(modelUpdateAction(pageHeaderModelUpdate));
        // }
      }
      logger.info('[LIFECYCLE] Set PAGE FOCUS');
      focused.current = true;
      setTimeout(() => dispatch(pageFocussed(pageKey, pageId)), 0);
    };
    if (!focused.current) {
      doFocusAsync();
    }
    // }
    return () => {
      focused.current = false;
    };
  }, [dispatch, navigation, pageConfig?.analytics, pageId, pageKey, store]);
  useFocusEffect(pageBootFocusCallback);

  const containerType = pageConfig?.get('containerType');
  const disableSafeArea: boolean = pageConfig?.get('disableSafeArea', false);
  const disableBackground: boolean = pageConfig?.get('disableBackground', false);

  const backgroundStyle = disableBackground
    ? {}
    : {
        backgroundColor: themeEvaluator('colors.background') as string,
      };
  const isScrollView = containerType === 'ScrollView';
  const Container = isScrollView ? ApptileScrollView : View;
  const viewProps: ViewProps = {style: {...backgroundStyle, flex: 1}};
  const scrollViewProps: ScrollViewProps = {
    contentInsetAdjustmentBehavior: 'automatic',
    showsVerticalScrollIndicator: false,
    style: {...backgroundStyle, flex: 1},
    contentContainerStyle: {flexGrow: 1},
    keyboardShouldPersistTaps: 'handled',
  };

  const navBackgroundColor = themeEvaluator('colors.navBackground');

  const tinyNavBackground = tinycolor(navBackgroundColor ?? '#fff');

  const osColorScheme = useColorScheme();
  const androidStatusBarStyle =
    osColorScheme === 'dark'
      ? {barStyle: 'light-content', backgroundColor: '#000000'}
      : {barStyle: 'dark-content', backgroundColor: navBackgroundColor as string};

  const child = (
    <>
      <Container {...(isScrollView ? scrollViewProps : viewProps)}>
        {isEditable ? <EditorWidgetCanvas {...{pageId, pageKey}} /> : <WidgetCanvas {...{pageId, pageKey, isModal}} />}
      </Container>
      <PortalHost key={pageKey} name={pageKey} />
    </>
  );

  return (
    <>
      {!disableSafeArea && (
        <SafeAreaView style={{flex: 1}} edges={['top', 'left', 'right']}>
          {/* TODO: Make this match with BG colors, or when we enable dark support*/}
          <StatusBar
            barStyle={tinyNavBackground?.isDark() ? 'light-content' : 'dark-content'}
            backgroundColor={navBackgroundColor}
          />
          {/* {Platform.OS === 'android' ? (
            <StatusBar {...androidStatusBarStyle} />
          ) : (
            <StatusBar barStyle={tinyNavBackground?.isDark() ? 'light-content' : 'dark-content'} />
          )} */}

          {child}
        </SafeAreaView>
      )}
      {disableSafeArea && (
        <>
          {/* TODO: Make this match with BG colors, or when we enable dark support*/}
          {Platform.OS === 'android' ? (
            <StatusBar {...androidStatusBarStyle} />
          ) : (
            <StatusBar barStyle={'dark-content'} />
          )}

          {child}
        </>
      )}
    </>
  );
};

export const EmbeddedAppPageContainer = connect(mapStateToProps)(AppPageContainer);

const AppScreenContainer: React.FC<any> = props => {
  const {navigation, route, screen} = props;
  // const pageId = screen?.screen;
  // const pageKey = route.key;
  // const params = {...(route.params ?? {})};
  // const [frozen, setFrozen] = useState(true);

  // const focusCallback = React.useCallback(() => {
  //   // setFrozen(false);
  // }, []);
  // useFocusEffect(focusCallback);
  // const blurFreeze = React.useCallback(() => {
  //   const unsubscribe = navigation.addListener('blur', () => {
  //     // setFrozen(true);
  //   });
  //   return unsubscribe;
  // }, [navigation]);
  // // eslint-disable-next-line react-hooks/exhaustive-deps
  // useEffect(blurFreeze, []);

  // useFocusEffect(focusCallback);
  return (
    <CurrentScreenContext.Provider value={screen}>
      {/* <Freeze freeze={frozen}> */}
      {/* <AppPageBootListener pageKey={pageKey} pageId={pageId} params={params} /> */}
      <EmbeddedAppPageContainer {...props} />
      {/* </Freeze> */}
    </CurrentScreenContext.Provider>
  );
};

export default AppScreenContainer;
