import React from 'react';
import {Platform, StatusBar} from 'react-native';
import {createMaterialTopTabNavigator, MaterialTopTabNavigationOptions} from '@react-navigation/material-top-tabs';

import {NavigatorCreator} from './types';
import {createNavigatorsFromConfig} from './index';
import {createScreenFromConfig} from './Screen';
import TopTabHeader from './header';
import { useTheme } from '../../styles/theme/context';
import { generateTypographyByPlatform } from '../../styles/styleUtils';
import {SafeAreaView} from 'react-native-safe-area-context';

const TopTabNavigator = createMaterialTopTabNavigator();

export const createTopTabNavigator: NavigatorCreator = (navigatorConfig, navigatorModel, props = {}, pages) => {
  const {themeEvaluator} = useTheme();
  const textColor = themeEvaluator('colors.navText');
  const primaryColor = themeEvaluator('colors.navPrimary');
  const backgroundColor = themeEvaluator('colors.navCard');
  const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'));
  const tabCount = navigatorConfig?.screens?.size;
  const isScrollable = tabCount >= 4;
  let navigatorOptions: Record<string, any> = {
    tabBarOptions: {
        scrollEnabled: isScrollable,
        tabStyle: isScrollable
        ? { width: 'auto', paddingHorizontal: 12 }
        : { flex: 1 }
    },
    screenOptions: {
      tabBarLabelStyle: {fontSize: 12},
      tabBarStyle: {backgroundColor: backgroundColor},
      tabBarShowIcon: false,
      tabBarActiveTintColor: primaryColor,
      tabBarInactiveTintColor: textColor,
    } as MaterialTopTabNavigationOptions,
  };
  if (Platform.OS !== 'web') navigatorOptions = {...navigatorOptions, detachInactiveScreens: false};

  return (
    <SafeAreaView style={{flex : 1}}>
      <TopTabHeader />
      <TopTabNavigator.Navigator lazy={true} id={navigatorConfig.name} {...navigatorOptions} {...props}>
        {navigatorConfig.screens
          .map(config => {
            const screenModal = navigatorModel?.screens[config.name];
            return config.type === 'navigator' ? (
              <TopTabNavigator.Screen
                name={config.name}
                key={config.name}
                navigationKey={config.name}
                options={{tabBarLabel: config.name}}>
                {screenProps => createNavigatorsFromConfig(config, screenModal, screenProps, pages)}
              </TopTabNavigator.Screen>
            ) : (
              createScreenFromConfig(TopTabNavigator, config, screenModal, pages)
            );
          })
          .toList()
          .toJS()}
      </TopTabNavigator.Navigator>
    </SafeAreaView>
  );
};