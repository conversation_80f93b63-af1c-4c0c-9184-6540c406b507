import type {AppConfigParams, NavigatorConfig, NavigatorConfigParams} from '../../common/datatypes/types';

import {createDrawerNavigatorFromConfig} from './DrawerNavigator';
import {createBottomTabNavigatorFromConfig} from './BottomTabNavigator';
import {createStackNavigatorFromConfig} from './StackNavigator';
import { createTopTabNavigator } from './TopTabNavigator';

export function createNavigatorsFromConfig(
  navConfig: NavigatorConfig,
  navModel: NavigatorConfigParams,
  props = {},
  pages: AppConfigParams['pages'],
) {
  switch (navConfig.navigatorType) {
    case 'tab':
      return createBottomTabNavigatorFromConfig(navConfig, navModel, props, pages);
    case 'topTab':
      return createTopTabNavigator(navConfig, navModel, props, pages);
    case 'stack':
    default:
      return createStackNavigatorFromConfig(navConfig, navModel, props, pages);
  }
}
