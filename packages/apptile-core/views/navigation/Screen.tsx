import React from 'react';
import {Platform, View} from 'react-native';
import get from 'lodash/get';
import {StackCardStyleInterpolator} from '@react-navigation/stack';

import {GetScreenId, GetTabBarIcon, ScreenCreator} from './types';
import {ScreenConfigParams} from '../../common/datatypes/types';
import Icon, {MaterialCommunityIcons} from '../../icons';
import CustomIcon from '../../components/common/CustomIcon';
import AppScreenContainer from '../screen/AppScreenContainer';

import slide from './animations/slide';
import scale from './animations/scale';
import none from './animations/none';
import fade from './animations/fade';
// TODO(gaurav) DONE should come from host
// import {GetRegisteredNativePage} from '../prebuilt';
import {useTheme} from '../../styles/theme/context';
import tinycolor from 'tinycolor2';

const transitions: {
  [K in Exclude<ScreenConfigParams['transition'], 'Default'>]: StackCardStyleInterpolator;
} = {
  Instant: none,
  Fade: fade,
  SlideUp: slide('up'),
  SlideDown: slide('down'),
  SlideLeft: slide('left'),
  SlideRight: slide('right'),
  Scale: scale,
};

export const createScreenFromConfig: ScreenCreator = (nav, screenConfig, screenModel, pages) => {
  const isModal = screenConfig?.isModal;
  const isTransparentModal = isModal && screenConfig?.isTransparentModal;
  const screenType = isModal ? (isTransparentModal ? 'transparentModal' : 'modal') : 'card';
  const idGenerator = getScreenId(screenConfig, screenModel);
  const iconGenerator = getTabBarIcon(screenConfig);
  const screenHeader = get(screenModel, 'header', '');
  const pageTransition = pages.get(screenConfig.get('screen'))?.get('transition');
  const screenTransition = screenConfig.get('transition');
  const transition = screenTransition && screenTransition !== 'Default' ? screenTransition : pageTransition;
  const nativeTemplate = screenConfig.get('nativeTemplate');
  const NativeScreenTemplate = (nativeTemplate ? global.GetRegisteredNativePage(nativeTemplate) : undefined) ?? undefined;

  var screenOptions: any = {};
  if (transition && transition !== 'Default') {
    screenOptions = {
      ...screenOptions,
      headerMode: 'screen',
      cardStyleInterpolator: transitions[transition],
    };
  }
  if (isTransparentModal) {
    screenOptions = {
      ...screenOptions,
      headerShown: false,
      cardOverlayEnabled: true,
      // FIXME: This causes scroll issues with modals that have scrollable items in them.
      // gestureEnabled: true,
      // gestureDirection: 'vertical',
    };
  }

  return (
    <nav.Screen
      name={screenConfig.name}
      key={`${screenConfig.name}_${screenModel?.screen}`}
      navigationKey={`${screenConfig.name}_${screenModel?.screen}`}
      getId={idGenerator}
      options={() => ({
        headerShown: !!screenConfig.showTitleBar,
        title: screenConfig.title ?? screenConfig.name,
        presentation: screenType,
        detachInactiveScreens: false,
        headerBackImage: HeaderBackImage,
        tabBarIcon: iconGenerator,
        // ...(screenHeader
        //   ? {
        //       header: (headerProps: any) => {
        //         const headerHeight = Platform.OS === 'ios' ? 100 : Platform.OS === 'android' ? 65 : 'auto';
        //         return (
        //           <View style={{height: headerHeight}}>
        //             <AppHeaderContainer
        //               key={`${screenConfig.name}_${screenModel?.screen}`}
        //               screen={screenModel}
        //               {...headerProps}
        //             />
        //           </View>
        //         );
        //       },
        //     }
        //   : {}),
        ...screenOptions,
      })}>
      {
        (screenProps: any) =>
          // screenConfig.name === 'Product' ? (
          //   <StandardPDP key={`${screenConfig.name}_${screenModel?.screen}`} screen={screenModel} {...screenProps} />
          // ) : screenConfig.name === 'Collection' ? (
          //   <StandardPLP key={`${screenConfig.name}_${screenModel?.screen}`} screen={screenModel} {...screenProps} />
          // ) : (
          nativeTemplate && NativeScreenTemplate ? (
            <NativeScreenTemplate
              key={`${screenConfig.name}_${screenModel?.screen}`}
              screen={screenModel}
              {...screenProps}
            />
          ) : (
            <AppScreenContainer
              key={`${screenConfig.name}_${screenModel?.screen}`}
              screen={screenModel}
              {...screenProps}
            />
          )
        // )
      }
    </nav.Screen>
  );
};

// Utils
const HeaderBackImage = () => {
  const {themeEvaluator} = useTheme();
  const navtText = tinycolor(themeEvaluator('colors.navText'));
  const iconColor = navtText.isDark() ? navtText.lighten(20).toHex8String() : navtText.darken(20).toHex8String();
  return (
    <View style={{...(Platform.OS === 'ios' ? {minWidth: 36, paddingRight: 8} : {})}}>
      <MaterialCommunityIcons name="chevron-left" size={32} color={iconColor} />
    </View>
  );
};

const getTabBarIcon: GetTabBarIcon =
  screenConfig =>
  ({color, size}) => {
    if (screenConfig.iconType === 'Custom Icon')
      return <CustomIcon name={screenConfig.iconName} size={size} color={color} />;
    return <Icon iconType={screenConfig.iconType} name={screenConfig.iconName} size={size} color={color} />;
  };

const getScreenId: GetScreenId =
  (screenConfig, screenModel) =>
  ({params}) => {
    return params
      ? `${screenConfig.name}_${screenModel?.screen}` +
          JSON.stringify(
            Object.keys(params)
              .sort()
              .map(k => params[k]),
          )
      : `${screenConfig.name}_${screenModel?.screen}`;
  };
