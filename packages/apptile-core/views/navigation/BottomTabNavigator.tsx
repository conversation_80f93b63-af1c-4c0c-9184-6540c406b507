import React from 'react';
import {Platform} from 'react-native';
import {BottomTabNavigationOptions, createBottomTabNavigator} from '@react-navigation/bottom-tabs';

import {GetTabBarIcon, NavigatorCreator} from './types';
import {createNavigatorsFromConfig} from './index';
import {createScreenFromConfig} from './Screen';
import CustomIcon from '../../components/common/CustomIcon';
import Icon from '../../icons';

const getTabBarIcon: GetTabBarIcon =
  screenConfig =>
  ({color, size}) => {
    if (screenConfig?.iconType === 'Custom Icon')
      return <CustomIcon name={screenConfig?.iconName} size={size} color={color} />;
    return <Icon iconType={screenConfig?.iconType} name={screenConfig?.iconName} size={size} color={color} />;
  };

const BottomTabNavigator = createBottomTabNavigator();

export const createBottomTabNavigatorFromConfig: NavigatorCreator = (
  navigatorConfig,
  navigatorModel,
  props = {},
  pages,
) => {
  let navigatorOptions: Record<string, any> = {
    screenOptions: {
      tabBarLabelPosition: 'below-icon',
    } as BottomTabNavigationOptions,
  };
  if (Platform.OS !== 'web') navigatorOptions = {...navigatorOptions, detachInactiveScreens: !!navigatorConfig?.detachInActiveScreens};

  return (
    <BottomTabNavigator.Navigator id={navigatorConfig.name} {...navigatorOptions} {...props}>
      {navigatorConfig.screens
        .map(config => {
          const screenModal = navigatorModel?.screens[config.name];
          const iconGenerator = getTabBarIcon(config);
          return config.type === 'navigator' ? (
            <BottomTabNavigator.Screen
              name={config.name}
              key={config.name}
              navigationKey={config.name}
              options={
                {
                  headerShown: false,
                  tabBarIcon: iconGenerator,
                }
              }>
              {screenProps => createNavigatorsFromConfig(config, screenModal, screenProps, pages)}
            </BottomTabNavigator.Screen>
          ) : (
            createScreenFromConfig(BottomTabNavigator, config, screenModal, pages)
          );
        })
        .toList()
        .toJS()}
    </BottomTabNavigator.Navigator>
  );
};
